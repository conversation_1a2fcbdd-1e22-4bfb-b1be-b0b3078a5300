import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'creative-request-form',
  name: 'Creative Request Form',
  description:
    "This template is designed to help teams effortlessly manage and organize creative requests. Whether it's design, video production, or other creative projects, users can fill in essential information in a clear interface. With this template, project details, deadlines, and priorities can be effectively recorded and managed, enhancing the team's workflow efficiency. Additionally, when a new creative request or work order is submitted, an email notification will be sent to keep everyone informed and ensure timely processing.",
  cover: '/assets/template/creative-request-form/creative-request-form.png',
  author: '<PERSON> <<EMAIL>>',
  category: ['project'],
  detach: false,
  visibility: 'SPACE',
  schemaVersion: 'v1',
  version: '0.1.3',
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoMZ5XqyKf8dw6CSwXcCXdF',
      name: 'Auto Email Notification',
      triggers: [
        {
          triggerType: 'RECORD_CREATED',
          templateId: 'trg8mL2z2kRSIIBtC8nR1ve6',
          description: 'The workflow is triggered when a new creative request record is created in the system.',
          input: {
            type: 'DATABASE',
            databaseTemplateId: 'datTNGrENMqmaPbvS0FvzOXZ',
          },
        },
      ],
      actions: [
        {
          templateId: 'act93gKGWizkP4O3OEkx12iq',
          description:
            'An automatic email notification will be sent to the relevant personnel, informing them of the new creative request pending for review.',
          actionType: 'SEND_EMAIL',
          input: {
            subject: 'You have a new creative request pending',
            body: {
              markdown:
                'A new creative request is pending. Please review the details of the request below:\n' +
                '- **Request**: <%= _triggers.trg8mL2z2kRSIIBtC8nR1ve6.record.cells.fld2FmQ8foPpCl1lg3dkKk4z.value %>\n' +
                '- **Description**: <%= _triggers.trg8mL2z2kRSIIBtC8nR1ve6.record.cells.fldNUN0YHI0Ie2gmYVirQhwK.value %>\n' +
                '- **Priority**: <%= JSON.stringify(_triggers.trg8mL2z2kRSIIBtC8nR1ve6.record.cells.fldhQA91HcTdHqs62rejoSDp.value) %>\n' +
                '- **Due Date**: <%= _triggers.trg8mL2z2kRSIIBtC8nR1ve6.record.cells.fld8lZoPrPhxwUg5ktoheJlO.value %>\n' +
                '- **Type**: <%= JSON.stringify(_triggers.trg8mL2z2kRSIIBtC8nR1ve6.record.cells.fldA5RncmJumpqI2nTwl7h9O.value) %>\n' +
                '- **Submitter Email**: <%= _triggers.trg8mL2z2kRSIIBtC8nR1ve6.record.cells.fldF7FdVfVFou5aMpPYDSX1j.value %>  \n' +
                '  \n' +
                'Please review and address this request as soon as possible. For further information, please contact the relevant personnel. Thank you!',
              json: {
                type: 'doc',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'A new creative request is pending. Please review the details of the request below: ',
                        type: 'text',
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: '- **Request**: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trg8mL2z2kRSIIBtC8nR1ve6',
                            'record',
                            'cells',
                            'fld2FmQ8foPpCl1lg3dkKk4z',
                            'value',
                          ],
                          tips: '',
                          names: ['触发器', '有新的记录创建时', '记录', '单元格', 'Request', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: '- **Description**: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trg8mL2z2kRSIIBtC8nR1ve6',
                            'record',
                            'cells',
                            'fldNUN0YHI0Ie2gmYVirQhwK',
                            'value',
                          ],
                          tips: '',
                          names: ['触发器', '有新的记录创建时', '记录', '单元格', 'Description', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: '- **Priority**: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: 'JSON.stringify(_triggers.trg8mL2z2kRSIIBtC8nR1ve6.record.cells.fldhQA91HcTdHqs62rejoSDp.value)',
                          tips: '',
                          names: ['触发器', '有新的记录创建时', '记录', '单元格', 'Priority', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: '- **Due Date**: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trg8mL2z2kRSIIBtC8nR1ve6',
                            'record',
                            'cells',
                            'fld8lZoPrPhxwUg5ktoheJlO',
                            'value',
                          ],
                          tips: '',
                          names: ['触发器', '有新的记录创建时', '记录', '单元格', 'Due Date', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: '- **Type**: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: 'JSON.stringify(_triggers.trg8mL2z2kRSIIBtC8nR1ve6.record.cells.fldA5RncmJumpqI2nTwl7h9O.value)',
                          tips: '',
                          names: ['触发器', '有新的记录创建时', '记录', '单元格', 'Type', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: '- **Submitter Email**: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trg8mL2z2kRSIIBtC8nR1ve6',
                            'record',
                            'cells',
                            'fldF7FdVfVFou5aMpPYDSX1j',
                            'value',
                          ],
                          tips: '',
                          names: ['触发器', '有新的记录创建时', '记录', '单元格', 'Email address', 'value'],
                        },
                      },
                      {
                        type: 'hardBreak',
                      },
                      {
                        type: 'hardBreak',
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Please review and address this request as soon as possible. For further information, please contact the relevant personnel. Thank you!',
                        type: 'text',
                      },
                    ],
                  },
                ],
              },
            },
            to: [
              {
                type: 'EMAIL_STRING',
                email: '<EMAIL>',
              },
            ],
            senderName: '',
            cc: [],
            bcc: [],
            replyTo: [],
            type: 'SERVICE',
          },
        },
      ],
    },
    {
      resourceType: 'FORM',
      templateId: 'fomfjfSQm0w0ZCdUprrs24pe',
      name: 'Creative request form',
      formType: 'DATABASE',
      databaseTemplateId: 'datTNGrENMqmaPbvS0FvzOXZ',
      metadata: {
        type: 'VIEW',
        viewTemplateId: 'viwP9iRxF3QM09tsclWgVlWz',
      },
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datTNGrENMqmaPbvS0FvzOXZ',
      name: 'Creative request data',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwP9iRxF3QM09tsclWgVlWz',
          name: 'Requests',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fld2FmQ8foPpCl1lg3dkKk4z',
              width: 150,
            },
            {
              templateId: 'fldNUN0YHI0Ie2gmYVirQhwK',
              hidden: false,
            },
            {
              templateId: 'fldhQA91HcTdHqs62rejoSDp',
              hidden: false,
            },
            {
              templateId: 'fld8lZoPrPhxwUg5ktoheJlO',
              hidden: false,
            },
            {
              templateId: 'fldurKNOWJ7ExjbnqajPAwL9',
              hidden: false,
            },
            {
              templateId: 'fldF7FdVfVFou5aMpPYDSX1j',
              hidden: false,
            },
            {
              templateId: 'fldA5RncmJumpqI2nTwl7h9O',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fld2FmQ8foPpCl1lg3dkKk4z',
          privilege: 'TYPE_EDIT',
          name: 'Request',
          primary: true,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldNUN0YHI0Ie2gmYVirQhwK',
          privilege: 'NAME_EDIT',
          name: 'Description',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldhQA91HcTdHqs62rejoSDp',
          privilege: 'NAME_EDIT',
          name: 'Priority',
          property: {
            options: [
              {
                id: 'optnvmLsQghp0S1qeOPgMrGx',
                name: 'High',
                color: 'pink5',
              },
              {
                id: 'optSodMa8h8mQ5anYc3qXrIr',
                name: 'Medium',
                color: 'indigo',
              },
              {
                id: 'optEmK5VLhPw2vXvFJ1G0S7P',
                name: 'Low',
                color: 'teal5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fld8lZoPrPhxwUg5ktoheJlO',
          privilege: 'NAME_EDIT',
          name: 'Due Date',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldurKNOWJ7ExjbnqajPAwL9',
          privilege: 'NAME_EDIT',
          name: 'Status',
          property: {
            options: [
              {
                id: 'opt4dyrH1ieSeahcTQS6c09j',
                name: 'Open',
                color: 'deepPurple',
              },
              {
                id: 'optG9xSHkjUFDuDdz8CCm6ip',
                name: 'In process',
                color: 'indigo5',
              },
              {
                id: 'opt3rGoXchizG88fvKjpxP29',
                name: 'Completed',
                color: 'green5',
              },
              {
                id: 'optq0hX2bnJSfz2dzlqsBnfi',
                name: 'Cancelled',
                color: 'red5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'EMAIL',
          templateId: 'fldF7FdVfVFou5aMpPYDSX1j',
          privilege: 'NAME_EDIT',
          name: 'Email address',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldA5RncmJumpqI2nTwl7h9O',
          privilege: 'NAME_EDIT',
          name: 'Type',
          property: {
            options: [
              {
                id: 'optS36oIA74mlJ7bXNNCUu1X',
                name: 'Social Media post',
                color: 'deepPurple',
              },
              {
                id: 'optB4m1bcvPbXnNF8XPOJGPM',
                name: 'Blog post',
                color: 'indigo',
              },
              {
                id: 'optJKtp7w1aFywVfpBn1T6qW',
                name: 'Email',
                color: 'blue',
              },
              {
                id: 'optfz4zJGAtmV7aPrHOHM7Zm',
                name: 'Landing page',
                color: 'teal',
              },
              {
                id: 'opt2jYMVYs70HdZuHtsYQ6ND',
                name: 'Ebook',
                color: 'green',
              },
              {
                id: 'optlOi3ToLVEUB9ukqH1aEXT',
                name: 'Webinar',
                color: 'yellow',
              },
              {
                id: 'optY0hwfXiHBttp0LjAX8Va2',
                name: 'Whitepaper',
                color: 'orange',
              },
              {
                id: 'optjbvzBs9Xof8LNky6Gcq6m',
                name: 'Case study',
                color: 'tangerine',
              },
              {
                id: 'optmkCUdqXwJRonvm3JokoLC',
                name: 'Other',
                color: 'pink',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recz97fw0x79t1Z9UqeiNfWz',
          data: {
            fld2FmQ8foPpCl1lg3dkKk4z: 'AI Technology Whitepaper',
            fld8lZoPrPhxwUg5ktoheJlO: '2024-10-19T16:00:00.000Z',
            fldA5RncmJumpqI2nTwl7h9O: ['optY0hwfXiHBttp0LjAX8Va2'],
            fldF7FdVfVFou5aMpPYDSX1j: '<EMAIL>',
            fldNUN0YHI0Ie2gmYVirQhwK: 'Write a whitepaper on the future of AI technology.',
            fldhQA91HcTdHqs62rejoSDp: ['optEmK5VLhPw2vXvFJ1G0S7P'],
            fldurKNOWJ7ExjbnqajPAwL9: ['opt3rGoXchizG88fvKjpxP29'],
          },
          values: {
            fld2FmQ8foPpCl1lg3dkKk4z: 'AI Technology Whitepaper',
            fld8lZoPrPhxwUg5ktoheJlO: '2024-10-19',
            fldA5RncmJumpqI2nTwl7h9O: ['Whitepaper'],
            fldF7FdVfVFou5aMpPYDSX1j: '<EMAIL>',
            fldNUN0YHI0Ie2gmYVirQhwK: 'Write a whitepaper on the future of AI technology.',
            fldhQA91HcTdHqs62rejoSDp: ['Low'],
            fldurKNOWJ7ExjbnqajPAwL9: ['Completed'],
          },
        },
        {
          templateId: 'recEg1y0W2sP3s3HUX0XPC9i',
          data: {
            fld2FmQ8foPpCl1lg3dkKk4z: 'Instagram Post for Product Launch',
            fld8lZoPrPhxwUg5ktoheJlO: '2024-10-23T16:00:00.000Z',
            fldA5RncmJumpqI2nTwl7h9O: ['optS36oIA74mlJ7bXNNCUu1X'],
            fldF7FdVfVFou5aMpPYDSX1j: '<EMAIL>',
            fldNUN0YHI0Ie2gmYVirQhwK:
              'Create an engaging post to announce the product launch. Highlight key features, use eye-catching visuals, and include a compelling call-to-action to encourage followers to learn more or make a purchase.',
            fldhQA91HcTdHqs62rejoSDp: ['optnvmLsQghp0S1qeOPgMrGx'],
            fldurKNOWJ7ExjbnqajPAwL9: ['opt4dyrH1ieSeahcTQS6c09j'],
          },
          values: {
            fld2FmQ8foPpCl1lg3dkKk4z: 'Instagram Post for Product Launch',
            fld8lZoPrPhxwUg5ktoheJlO: '2024-10-23',
            fldA5RncmJumpqI2nTwl7h9O: ['Social Media post'],
            fldF7FdVfVFou5aMpPYDSX1j: '<EMAIL>',
            fldNUN0YHI0Ie2gmYVirQhwK:
              'Create an engaging post to announce the product launch. Highlight key features, use eye-catching visuals, and include a compelling call-to-action to encourage followers to learn more or make a purchase.',
            fldhQA91HcTdHqs62rejoSDp: ['High'],
            fldurKNOWJ7ExjbnqajPAwL9: ['Open'],
          },
        },
        {
          templateId: 'recG9shinaD1HGiKjuBn1CdK',
          data: {
            fld2FmQ8foPpCl1lg3dkKk4z: 'Blog Post on Remote Work Benefits',
            fld8lZoPrPhxwUg5ktoheJlO: '2024-10-29T16:00:00.000Z',
            fldA5RncmJumpqI2nTwl7h9O: ['optB4m1bcvPbXnNF8XPOJGPM'],
            fldF7FdVfVFou5aMpPYDSX1j: '<EMAIL>',
            fldNUN0YHI0Ie2gmYVirQhwK: 'Write a blog post on the benefits of remote work.',
            fldhQA91HcTdHqs62rejoSDp: ['optSodMa8h8mQ5anYc3qXrIr'],
            fldurKNOWJ7ExjbnqajPAwL9: ['optG9xSHkjUFDuDdz8CCm6ip'],
          },
          values: {
            fld2FmQ8foPpCl1lg3dkKk4z: 'Blog Post on Remote Work Benefits',
            fld8lZoPrPhxwUg5ktoheJlO: '2024-10-29',
            fldA5RncmJumpqI2nTwl7h9O: ['Blog post'],
            fldF7FdVfVFou5aMpPYDSX1j: '<EMAIL>',
            fldNUN0YHI0Ie2gmYVirQhwK: 'Write a blog post on the benefits of remote work.',
            fldhQA91HcTdHqs62rejoSDp: ['Medium'],
            fldurKNOWJ7ExjbnqajPAwL9: ['In process'],
          },
        },
        {
          templateId: 'recfW3CupKG88PqEzOvpqyB8',
          data: {
            fld2FmQ8foPpCl1lg3dkKk4z: 'Digital Marketing Ebook',
            fld8lZoPrPhxwUg5ktoheJlO: '2024-10-30T16:00:00.000Z',
            fldA5RncmJumpqI2nTwl7h9O: ['opt2jYMVYs70HdZuHtsYQ6ND'],
            fldF7FdVfVFou5aMpPYDSX1j: '<EMAIL>',
            fldNUN0YHI0Ie2gmYVirQhwK: 'Create an ebook on digital marketing strategies.',
            fldhQA91HcTdHqs62rejoSDp: ['optnvmLsQghp0S1qeOPgMrGx'],
            fldurKNOWJ7ExjbnqajPAwL9: ['opt4dyrH1ieSeahcTQS6c09j'],
          },
          values: {
            fld2FmQ8foPpCl1lg3dkKk4z: 'Digital Marketing Ebook',
            fld8lZoPrPhxwUg5ktoheJlO: '2024-10-30',
            fldA5RncmJumpqI2nTwl7h9O: ['Ebook'],
            fldF7FdVfVFou5aMpPYDSX1j: '<EMAIL>',
            fldNUN0YHI0Ie2gmYVirQhwK: 'Create an ebook on digital marketing strategies.',
            fldhQA91HcTdHqs62rejoSDp: ['High'],
            fldurKNOWJ7ExjbnqajPAwL9: ['Open'],
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
