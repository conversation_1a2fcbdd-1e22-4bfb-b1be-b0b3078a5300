---
title: ToolSDK.ai 是什么？
slug: toolsdk.ai/help/en/tutorials/getting-start
sidebar_position: 0
---


# ToolSDK.ai 是什么？

<img src="https://book.bika.ai/bika-content/assets/YwAJbSQlIo6AFCxsfxrlqWBZgxh.png" src-width="1432" src-height="794" align="center"/>

# What is ToolSDK.ai?

ToolSDK.ai is an open-source AI Tools SDK for Hosted MCP Servers.

ToolSDK.ai hosted 5000+ MCP servers, MCP-as-a-service.

It also provides an MCP hub server, which as a proxy to connects all ToolSDK.ai - hosted MCP servers.  1 MCP server to rule other all MCP servers all.

# Why ToolSDK.ai? 

## For MCP Clients and AI Tools

Over 100 MCP clients and applications, , such as Claude Desktop, OpenAI Desktop, Cursor, Bika.ai, Cherry Studio, etc., can instant access to remote 5000+ hosted MCP server with easy hosted http link configuraiton.

## For Developers of AI Agents and SaaS Apps

在很多情况下，研发团队开发成熟的 SaaS 软件时，往往要耗费 70% 的时间，用于配置和集成不同的第三方软件，以此实现更丰富的功能。而现在，借助 ToolSDK.ai，所有的对接工作可能仅需一天就能完成。

If you are a developer of AI agents and SaaS apps, use ToolSDK connect and integrate to 5,000 third-party apps and use AI tools via the MCP Servers Ecosystem.

如果你是那个第三方的 MCP Server 的开发者，你也可以那一个提交你的 MCP Server，帮你进行托管，迅速的接入到 tool SDK 的平台里。

# Features

- <b>Easy Connection</b>: 🚀 Connect to 100+ hosted MCP Servers without running local code. No hassle! 😌
- <b>Remote</b> <b>MCP Server</b>: 🔗 Link your AI to any app using 1 MCP Server in MCP Clients like Claude or Cursor. Connect AI apps all! 🤝
- <b>Zapier Alternative</b>: 🛠️ Build Zapier, Make.com-like integrations platform. Rely on the MCP server ecosystem to cut dependencies. Be independent! 💪
- <b>React Form Support</b>: 🎨 Turn MCP Server tools into form inputs for React form apps. Make it look good! 😍
- <b>Extensible Architecture</b>: 🔧 The architecture is easy to expand. It supports ToolApp(Zapier App 100% compatible) and MCP Server packages. Adaptable! 👍
- <b>Rich Ecosystem</b>: 📦 Get access to many tools, including AI and OpenAI SDKs. So many choices! 😃
- <b>Secure Use</b>: 🔐 Log in securely with credentials. Keep accounts separate. Stay safe! 👮
- <b>MCP Hub Server</b>: MCP of others MCPs, one MCP server to rule them all.
- <b>Usage Analytics</b>: 📈 Check how the tools are being used. Know your data! 🧐

If you want to learn more or request new features, head over to https://github.com/toolsdk-ai/toolsdk/community

## 
# For AI MCP Clients

ToolSDK.ai 这个网站有三个作用：

## <b>MCP 服务器检索</b>

在此平台上，您能够搜索并找到所需的 MCP Server。它如同一个热门 MCP Server 排行榜，您可以清晰查看哪些 MCP Server 获得较多点赞（star）、被频繁调用。随后，您可自由前往其 Github 仓库，自行部署运行 Python、Node.js、Java 版本的本地 MCP Server。

## <b>便捷配置 MCP 客户端</b>

您可直接使用网站提供的 MCP Server 配置。只需将 remote url 复制到您的 MCP Client（如 Cursor、OpenAI Desktop、Claude Desktop）中，即可轻松完成配置。

<img src="https://book.bika.ai/bika-content/assets/SpBJbCzNJoTzlSxW4SylQib3gAg.png" src-width="1278" src-height="650"/>

## <b>MCP Router Server路由</b>

如今 MCP server 数量众多，可能多达数千个。

但实际上，质量参差不齐，很多时候你并不需要这么多，或许你真正想要的只是 5 到 10 个高质量的 MCP Server，

ToolSDK.ai提供了 MCP Router Server。它类似于一个中枢路由系统，能够自动路由并调用其他 MCP Server。

您只需配置一个 MCP Server，就能够便捷地调用您之前收藏（点赞）过的其他 MCP Server 。

它就如同一个中间商，将多个 MCP Server 组合成一个集合（collection）。你只需要对这一个 MCP 路由进行一次配置，当你的 Client 调用这个集合时，它便会自动选择，进而自动访问你收藏的那 10 个 MCP Server。

这种情况下，你可以登录 ToolSDK.ai ，找到心仪的 MCP Server 并点击收藏（点一个星星） 。我们的路由服务器会发挥作用。

<img src="https://book.bika.ai/bika-content/assets/A5AwblBfYoph9txYqE4lnVUBg6c.png" src-width="2068" src-height="1078" align="center"/>

# For Developer SDK

## Example Project

我们提供了一个 example project，演示 DEMO 关于如何使用 ToolSDK.ai，包括了AI SDK 调用的 chatbot 和嵌入表单层，把 MCP Server 的 tool 嵌入层表单选择器进行配置保存、AI SDK的调用等等，你可以直接访问网址来查看这些 example 例子：

https://example.toolsdk.ai

<img src="https://book.bika.ai/bika-content/assets/KZLib25yqogBZ2xKbmJlDo0JgkS.png" src-width="1562" src-height="1066" align="center"/>

此外，你也可以在本地运行这个 example 例子，我也强烈建议你。在 Git clone 这个项目之后，先执行这一个东西。

Run example project to see how to use:

```bash
git clone https://github.com/toolsdk-ai/toolsdk
npm install
npm run example
```

## Installation

```bash
npm install toolsdk
# or
yarn add toolsdk
```

## AI SDK + ToolSDK

如果你使用的是 Vercel AI SDK，你可以：

手工复制 ToolSDK.ai 上的 Hosted MCP Server 配置：

```js
import { experimental_createMCPClient, generateText } from 'ai';
import { openai } from '@ai-sdk/openai';


try {
  const client = await experimental_createMCPClient({
    transport: {
      type: 'sse',
      command: <b>'https://toolsdk.ai/api/server/{slack_mcp_server_id}'</b>,
    },
  });
  const tools = await client.listTools();
  const response = await generateText({
    model: openai('gpt-4o-mini'),
    tools,
    messages: [{ role: 'user', content: 'Query the data' }],
  });

  console.log(response);
} finally {
  await client.close();
}
```

手工复制 ToolSDK.ai 上的 MCP Router Server配置，developer ID：

<img src="https://book.bika.ai/bika-content/assets/Vn55byELloVGs0xHxfRl7IVIgFh.png" src-width="1914" src-height="1062" align="center"/>

手工复制 ToolSDK.ai 上的 MCP Router Server配置：

```js
import { experimental_createMCPClient, generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { ToolSDK } 


try {
  const client = await experimental_createMCPClient({
    transport: {
      type: 'sse',
      command: '<b>https://toolsdk.ai/api/router/{YOUR_DEVELOPER_ID}</b>',
      env: {
        'TOOLSDK_SECRET_KEY': '{YOUR_TOOLSDK_SECRET_KEY',
      }
    },
  });

  const tools = await client.listTools();

  const response = await generateText({
    model: openai('gpt-4o-mini'),
    tools,
    messages: [{ role: 'user', content: 'Query the data' }],
  });

  console.log(response);
} finally {
  await client.close();
}
```

调用 ToolSDK，动态获取 托管的 Tools

```js
import { experimental_createMCPClient, generateText } from 'ai';
import { openai } from '@ai-sdk/openai';
import { ToolSDK } from '@toolsdk/api';


try {
  const toolsdk = new ToolSDK({secretKey: 'xxx'});
  const myMCPServers = await toolsdk.myServers();
  const mcpClient = myMCPServers[0].createMCPClient();
  
  const tools = await mcpClient.listTools();

  const response = await generateText({
    model: openai('gpt-4o-mini'),
    tools,
    messages: [{ role: 'user', content: 'Query the data' }],
  });

  console.log(response);
} finally {
  await client.close();
}
```

Reference: https://sdk.vercel.ai/docs/reference/ai-sdk-core/create-mcp-client

Documentation:https://sdk.vercel.ai/docs/ai-sdk-core/tools-and-tool-calling#mcp-tools

Cookbook: https://sdk.vercel.ai/cookbook/node/mcp-tools

## React  + ToolSDK React

就是在不同的软件当中，我们经常要开发出来一个表单，这个表单给到我们的软件的用户去填入一些配置，比如说你是一个做那个会议软件的一个团队，你的用户在这个录入完这一个会议记录之后，你希望他可以迅速的发到 Google Docs 里面去形成一篇文档。对不对？你会让你的用户去配置 Google Docs 的那一个 key 配置，那这时候就要大量的表单。而你现在的话，你可以利用 tool SDK 迅速的获取到对应的这个第三方 tool，还有形成一个表单。

```js
'use server';

import { ToolSDK } from 'toolsdk/api';

export default async function YourClientReactComponent() {
   const toolsdk = new ToolSDK({apiKey:'x'});
   const accountToken = await toolsdk.account('abc').token();
   
   return  <YourClientReactComponent accountToken={accountToken} />
}
```

```js
'use client';

import { MCPServerInput } from 'toolsdk/react';

export function YourClientReactComponent() {
   return  <MCPServerInput
      options={{ scope: 'DEVELOPER_STAR' }}
      accountToken={accountToken}
      consumerKey={'example'}
      defaultValue={{ inputData: {} }}
    />
}
```

# 常见问题解答

## What ToolSDK.ai different from mcp.so and smithery.ai?

While mcp.so and smithery.ai are MCP server directory that for users to discover  thousands of MCP servers and projects, and they hosted some MCP server.

ToolSDK 则提供了一个 SDK，来更方便地接入，你可以直接使用 AI SDK 和 OpenAI SDK 中调用 MCP 服务。

注重质量的接入，Developer 需要 star 自己喜爱的 MCP Servers 或 ToolApp，SDK 中才可以被调用，确保“质量比数量更重要”。

## Can ToolSDK.ai host MCP server?

Yes. ToolSDK.ai also provide a high availability Kubernetes environment to host MCP servers.

Currently MCP server is run in our shared Container process.

## Can I submit more MCP client on ToolSDK.ai?

aaa

# 
