import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: 'Bika.ai',
  templateId: '@vika/rotating-duty-reminder-wecom',
  name: {
    en: 'Rotating duty reminder',
    ja: 'Rotating duty reminder',
    'zh-TW': '輪流值班提醒',
    'zh-CN': '轮流值班提醒',
  },
  cover: '/assets/template/template-cover-no-image-color.png',
  visibility: 'PRIVATE',
  description: {
    en: 'A template for storing duty information',
    ja: '値勤情報を保存するためのテンプレート',
    'zh-TW': '用於存儲值班信息的模板',
    'zh-CN': '用于存储值班信息的模板',
  },
  personas: {
    // 4 roles at least
    en: 'Product manager, Frontend engineer, Backend engineer, Operation and maintenance engineer, Designer, Tester',
    'zh-CN': '产品经理, 前端工程师, 后端工程师, 运维工程师, 设计师, 测试工程师',
    'zh-TW': '產品經理, 前端工程師, 後端工程師, 運維工程師, 設計師, 測試工程師',
    ja: 'プロダクトマネージャー, フロントエンドエンジニア, バックエンドエンジニア, 運用エンジニア, デザイナー, テスター',
  },
  useCases: {
    en: 'Morning meeting rotation host, 24-hour customer service, IT operations holiday response, financial timely settlement, logistics facility maintenance, security patrol scheduling, seamless content updates, creative topic scheduling, social media platform duty, technical maintenance support, public opinion monitoring response, event execution coordination, holiday duty, medical room duty, student activity scheduling, dormitory management scheduling, night patrol scheduling, venue facility duty, elderly care handover, property maintenance duty, household cleaning duty, hotel front desk scheduling, entertainment venue scheduling, supermarket staff scheduling',
    'zh-CN':
      '晨会轮值主持, 24小时客服, IT运维假期响应, 财务准时结算, 后勤设施维护, 安保巡逻排班, 内容无缝更新, 创意选题排期, 社媒平台值守, 技术维护保障, 舆情监控响应, 活动执行协调, 节假日值班, 医务室值班, 学生活动排班, 宿管人员排班, 夜间巡逻排班, 场馆设施值班, 养老看护交接, 物业维修值班, 家庭打扫值日, 酒店前台排班, 娱乐场所排班, 超市人员排班',
    'zh-TW':
      '晨會輪值主持, 24 小時客服, IT 運維假期響應, 財務準時結算, 後勤設施維護, 安保巡邏排班, 內容無縫更新, 創意選題排期, 社媒平台值守, 技術維護保障, 輿情監控響應, 活動執行協調, 節假日值班, 醫務室值班, 學生活動排班, 宿管人員排班, 夜間巡邏排班, 場館設施值班, 養老看護交接, 物業維修值班, 家庭打掃值日, 酒店前台排班, 娛樂場所排班, 超市人員排班',
    ja: '朝会当番司会, 24時間カスタマーサービス, IT運用休暇対応, 財務時間通り決算, 後勤施設保守, 警備パトロールシフト, コンテンツのシームレス更新, クリエイティブなトピックスケジュール, ソーシャルメディアプラットフォームの当番, 技術保守サポート, 世論監視対応, イベント実行調整, 休日当番, 医務室当番, 学生活動シフト, 寮管理当番, 夜間パトロールシフト, 施設設備当番, 高齢者ケア引き継ぎ, 物件保守当番, 家庭掃除当番, ホテルフロントデスクシフト, 娯楽施設シフト, スーパーマーケットスタッフシフト',
  },

  version: '0.0.17',

  category: 'project',
  initMissions: [
    {
      name: '💡轮流值班提醒初始化任务 1：请设置本周值班信息',
      description: '请设置本周值班人员，用于发送值班提醒消息',
      type: 'CREATE_RECORD',
      databaseTemplateId: 'duty_roster',
      buttonText: '填写值班信息',
      assignType: 'SHARE',
      to: [
        {
          type: 'ADMIN',
        },
      ],
      canCompleteManually: true,
    },
    {
      name: '💡轮流值班提醒初始化任务2：设置自动化提醒',
      description: '请设置企业微信的 Webhook URL 并启用自动化提醒，用于向特定的企业微信群中发送值班提醒消息',
      type: 'REDIRECT_SPACE_NODE',
      nodeTemplateId: 'duty_reminder',
      assignType: 'SHARE',
      to: [
        {
          type: 'ADMIN',
        },
      ],
      canCompleteManually: true,
    },
  ],
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'duty_roster',
      databaseType: 'DATUM',
      name: {
        en: 'Duty roster',
        'zh-CN': '值班记录表',
      },
      description: {
        en: 'Storing duty information',
        'zh-CN': '存储值班信息',
      },
      permissions: [],
      fields: [
        {
          templateId: 'start_date',
          name: 'Start date',
          description: 'Start date of the duty period',
          type: 'DATETIME',
          property: {
            autofill: false,
            timeZone: 'Asia/Shanghai',
            dateFormat: 'YYYY/MM/DD',
            includeTime: false,
          },
        },
        {
          templateId: 'end_date',
          name: 'End date',
          description: 'Start date of the duty period',
          type: 'DATETIME',
          property: {
            autofill: false,
            timeZone: 'Asia/Shanghai',
            dateFormat: 'YYYY/MM/DD',
            includeTime: false,
          },
        },
        {
          templateId: 'product_duty_member',
          name: '产品值班人员',
          type: 'MEMBER',
          required: true,
          description: '产品值班人员',
          property: {
            many: false,
          },
        },
        {
          templateId: 'frontend_duty_member',
          name: '前端值班人员',
          type: 'MEMBER',
          required: true,
          description: '前端值班人员',
          property: {
            many: false,
          },
        },
        {
          templateId: 'backend_duty_member',
          name: '后端值班人员',
          type: 'MEMBER',
          required: true,
          description: '后端值班人员',
          property: {
            many: false,
          },
        },
        {
          templateId: 'om_duty_member',
          name: '运维值班人员',
          type: 'MEMBER',
          required: true,
          description: '运维值班人员',
          property: {
            many: false,
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'duty_reminder',
      name: 'Duty reminder',
      description: '每周一上午 10:00 根据当周的值班成员数据发送提醒',
      status: 'INACTIVE',
      permissions: [],
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'scheduler',
          description: '每周一上午 10:00 触发',
          input: {
            type: 'SCHEDULER',
            scheduler: {
              timezone: 'Asia/Hong_Kong',
              datetime: {
                type: 'TODAY',
                hour: 10,
                minute: 0,
              },
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['MON'],
                },
              },
            },
          },
        },
      ],
      actions: [
        {
          actionType: 'FIND_RECORDS',
          templateId: 'find_records',
          description: '查询当周的值班成员数据',
          input: {
            type: 'DATABASE_WITH_FILTER',
            databaseTemplateId: 'duty_roster',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldType: 'DATETIME',
                  fieldTemplateId: 'start_date',
                  clause: {
                    operator: 'Is',
                    value: ['ThisWeek'],
                  },
                },
              ],
            },
          },
          // 匹配的每一条记录，都单独执行 actions，如果某天没有值班记录，则不会发送提醒
          actions: [
            {
              actionType: 'WECOM_WEBHOOK',
              input: {
                type: 'WECOM_WEBHOOK',
                urlType: 'URL',
                url: 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8439408c-a6a1-4c85-89e8-8bd2465119c2',
                data: {
                  msgtype: 'markdown',
                  markdown: {
                    content: `**本周发版负责人通知**\n
  >值班周期：<%= _item.cells.start_date.value %>~<%= _item.cells.end_date.value %>
  >产品负责人：<%= _item.cells.product_duty_member.value[0] %>
  >前端负责人：<%= _item.cells.frontend_duty_member.value[0] %>
  >后端负责人：<%= _item.cells.backend_duty_member.value[0] %>
  >运维负责人：<%= _item.cells.om_duty_member.value[0] %>`,
                  },
                },
              },
            },
          ],
        },
      ],
    },
  ],
};

export default template;
