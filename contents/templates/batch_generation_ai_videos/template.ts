import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'batch_generation_ai_videos',
  name: {
    en: 'Batch generation of HeyGen AI videos',
    'zh-CN': '批量生成 HeyGen AI 视频',
  },
  description: {
    en: 'Simply enter the script and video parameters in Bika to quickly generate AI videos in HeyGen and return the videos to Bika, greatly simplifying your video making and creation process.',
    'zh-CN':
      '只需在 Bika中简单输入脚本、视频参数，即可在HeyGen中快速生成AI视频，并将视频返回Bika，极大简化你的视频制作和创作流程。',
  },
  cover: '/assets/template/batch_generation_of_heyGen_videos/cover.png',
  author: 'lujun <<EMAIL>>',
  category: ['ai', 'script'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.4',
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'datTUplw18O8EBWzjNSCYLaJ',
      name: {
        en: 'HeyGen Videos Database',
        'zh-CN': 'HeyGen 视频数据库',
      },
      description: {
        en: 'Manage the parameters required for HeyGen to generate videos, and the information of generated videos',
        'zh-CN': '管理 HeyGen 生成视频所需的参数和已生成的视频',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwGPRMvUfoBz9IDaksTB5pP',
          name: {
            en: 'All',
            'zh-CN': '全部',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldCr5wuAR3IlxT5Yaut7TJS',
              hidden: false,
              width: 256,
            },
            {
              templateId: 'fldP7YKG45NtMilMyHq0nNsT',
              hidden: false,
            },
            {
              templateId: 'fld8pkIEZLp3Z8MeXErTEVjr',
              hidden: false,
            },
            {
              templateId: 'fld2f33pgod4HoMUN65eEk5t',
              hidden: false,
            },
            {
              templateId: 'fld7VyTftxdN2YUxRqtzIIKv',
              hidden: false,
              width: 272,
            },
            {
              templateId: 'fldWbqEOpum3Yw3P07WweJry',
              hidden: false,
            },
            {
              templateId: 'fldP4Wxb3xI6RBeWFjnix6yK',
              hidden: false,
            },
            {
              templateId: 'fldYK6lqMzM8ca82A9zI2PQk',
              hidden: false,
            },
            {
              templateId: 'fldPiPiyla7ZOjSUlYvGOUW1',
              hidden: false,
            },
            {
              templateId: 'fldzJs2bRtngIWJO1uK25Btz',
              hidden: false,
            },
            {
              templateId: 'fldMW58e4sYfNaULx13AowUW',
              hidden: false,
            },
            {
              templateId: 'fld27Jhl2BRrI7ufuPXf4Rxw',
              hidden: false,
            },
            {
              templateId: 'fldrM1ZsxbgOtzW3nVEEuVgQ',
              hidden: false,
            },
            {
              templateId: 'fldSt3XYAUz0I6djjrTy7vcc',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwwFf963bZsqmXXROECLlFZ',
          name: {
            en: 'To be generated',
            'zh-CN': '待生成视频',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fld27Jhl2BRrI7ufuPXf4Rxw',
                fieldType: 'SINGLE_SELECT',
                clause: {
                  operator: 'Is',
                  value: 'optIr9TmToQXzsIAVGDI554V',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldCr5wuAR3IlxT5Yaut7TJS',
              hidden: false,
            },
            {
              templateId: 'fld7VyTftxdN2YUxRqtzIIKv',
              hidden: false,
            },
            {
              templateId: 'fldP7YKG45NtMilMyHq0nNsT',
              hidden: false,
            },
            {
              templateId: 'fld8pkIEZLp3Z8MeXErTEVjr',
              hidden: false,
            },
            {
              templateId: 'fld2f33pgod4HoMUN65eEk5t',
              hidden: false,
            },
            {
              templateId: 'fldWbqEOpum3Yw3P07WweJry',
              hidden: false,
            },
            {
              templateId: 'fldP4Wxb3xI6RBeWFjnix6yK',
              hidden: false,
            },
            {
              templateId: 'fldzJs2bRtngIWJO1uK25Btz',
              hidden: false,
            },
            {
              templateId: 'fldYK6lqMzM8ca82A9zI2PQk',
              hidden: false,
            },
            {
              templateId: 'fldPiPiyla7ZOjSUlYvGOUW1',
              hidden: false,
            },
            {
              templateId: 'fldMW58e4sYfNaULx13AowUW',
              hidden: false,
            },
            {
              templateId: 'fld27Jhl2BRrI7ufuPXf4Rxw',
              hidden: false,
            },
            {
              templateId: 'fldrM1ZsxbgOtzW3nVEEuVgQ',
              hidden: false,
            },
            {
              templateId: 'fldSt3XYAUz0I6djjrTy7vcc',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwxXipPNT8JPFweoJ2ZirM9',
          name: {
            en: 'Generated video',
            'zh-CN': '已生成的视频',
          },
          filters: {
            conjunction: 'Or',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fld27Jhl2BRrI7ufuPXf4Rxw',
                fieldType: 'SINGLE_SELECT',
                clause: {
                  operator: 'Is',
                  value: 'optAiNoP72bmGtZn20rOgxHG',
                },
              },
              {
                fieldTemplateId: 'fldSt3XYAUz0I6djjrTy7vcc',
                fieldType: 'URL',
                clause: {
                  operator: 'IsNotEmpty',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldCr5wuAR3IlxT5Yaut7TJS',
              hidden: false,
            },
            {
              templateId: 'fld7VyTftxdN2YUxRqtzIIKv',
              hidden: false,
            },
            {
              templateId: 'fldP7YKG45NtMilMyHq0nNsT',
              hidden: true,
            },
            {
              templateId: 'fld8pkIEZLp3Z8MeXErTEVjr',
              hidden: true,
            },
            {
              templateId: 'fld2f33pgod4HoMUN65eEk5t',
              hidden: false,
            },
            {
              templateId: 'fld27Jhl2BRrI7ufuPXf4Rxw',
              hidden: false,
            },
            {
              templateId: 'fldrM1ZsxbgOtzW3nVEEuVgQ',
              hidden: false,
            },
            {
              templateId: 'fldSt3XYAUz0I6djjrTy7vcc',
              hidden: false,
            },
            {
              templateId: 'fldYK6lqMzM8ca82A9zI2PQk',
              hidden: true,
            },
            {
              templateId: 'fldzJs2bRtngIWJO1uK25Btz',
              hidden: true,
            },
            {
              templateId: 'fldMW58e4sYfNaULx13AowUW',
              hidden: true,
            },
            {
              templateId: 'fldWbqEOpum3Yw3P07WweJry',
              hidden: true,
            },
            {
              templateId: 'fldP4Wxb3xI6RBeWFjnix6yK',
              hidden: true,
            },
            {
              templateId: 'fldPiPiyla7ZOjSUlYvGOUW1',
              hidden: true,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldCr5wuAR3IlxT5Yaut7TJS',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Video title',
            'zh-CN': '视频标题',
          },
          primary: true,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fld7VyTftxdN2YUxRqtzIIKv',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Script content',
            'zh-CN': '脚本内容',
          },
          primary: false,
        },
        {
          type: 'NUMBER',
          templateId: 'fldP7YKG45NtMilMyHq0nNsT',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Video width',
            'zh-CN': '视频宽度',
          },
          description: {
            en: 'For the width of the video to be generated,  Required to be greater than 128 and less than 4096',
            'zh-CN': '要生成的视频的宽度，要求大于128 且小于4096',
          },
          property: {
            precision: 0,
            commaStyle: 'thousand',
            symbol: 'px',
            symbolAlign: 'right',
          },
          primary: false,
        },
        {
          type: 'NUMBER',
          templateId: 'fld8pkIEZLp3Z8MeXErTEVjr',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Video height',
            'zh-CN': '视频高度',
          },
          description: {
            en: 'For the height of the video to be generated,  Required to be greater than 128 and less than 4096',
            'zh-CN': '要生成的视频高度，要求大于128 且小于4096',
          },
          required: false,
          property: {
            precision: 0,
            commaStyle: 'thousand',
            symbol: 'px',
            symbolAlign: 'right',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fld2f33pgod4HoMUN65eEk5t',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Dimension',
            'zh-CN': '输出分辨率',
          },
          required: false,
          property: {
            expressionTemplate: '{fldP7YKG45NtMilMyHq0nNsT} +“x” + {fld8pkIEZLp3Z8MeXErTEVjr}',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fld27Jhl2BRrI7ufuPXf4Rxw',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Status',
            'zh-CN': '状态',
          },
          property: {
            options: [
              {
                id: 'optikmTFoUqW1',
                name: 'Awaited',
                color: 'indigo',
              },
              {
                id: 'optIr9TmToQXzsIAVGDI554V',
                name: 'To be generated',
                color: 'deepPurple',
              },
              {
                id: 'optAiNoP72bmGtZn20rOgxHG',
                name: 'Completed',
                color: 'teal4',
              },
            ],
            defaultValue: 'optikmTFoUqW1',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldrM1ZsxbgOtzW3nVEEuVgQ',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Video ID',
            'zh-CN': '视频 ID',
          },
          description: {
            en: 'No need to fill in, it will be automatically filled in after the video is generated',
            'zh-CN': '无需填写，视频生成后将自动填写',
          },
          primary: false,
        },
        {
          type: 'URL',
          templateId: 'fldSt3XYAUz0I6djjrTy7vcc',
          privilege: 'NAME_EDIT',
          name: {
            en: 'The link to the generated video',
            'zh-CN': '已生成视频的链接',
          },
          description: {
            en:
              'Generate a preview link for the video\n' +
              'No need to fill in, the link data will be automatically returned after the video is generated.',
            'zh-CN': '生成视频的预览链接\n无需填写，视频生成后将自动返回链接数据',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldYK6lqMzM8ca82A9zI2PQk',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Voice ID',
            'zh-CN': '声音 ID',
          },
          required: false,
          property: {
            relatedLinkFieldTemplateId: 'fldWbqEOpum3Yw3P07WweJry',
            lookupTargetFieldTemplateId: 'fldCysEbYwHkV3M2DcEE2QgU',
            lookupTargetFieldType: 'SINGLE_TEXT',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldzJs2bRtngIWJO1uK25Btz',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Avatar ID',
            'zh-CN': '角色 ID',
          },
          required: false,
          property: {
            relatedLinkFieldTemplateId: 'fldWbqEOpum3Yw3P07WweJry',
            lookupTargetFieldTemplateId: 'fldCvlX63FxenoZjZ1BoEOqg',
            lookupTargetFieldType: 'SINGLE_TEXT',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldMW58e4sYfNaULx13AowUW',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Video background color',
            'zh-CN': '视频背景色',
          },
          description: {
            en:
              'Generate the background color of the video\n' +
              'Fill in the color value in hexadecimal format. Default is #FFFFFF',
            'zh-CN': '生成视频的背景颜色，填写十六进制格式的颜色值。默认为 #FFFFFF',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldWbqEOpum3Yw3P07WweJry',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Choose a character',
            'zh-CN': '选择人物',
          },
          property: {
            foreignDatabaseTemplateId: 'datM9B8augvwtDkvsDUiAXup',
            brotherFieldTemplateId: 'fld4RgRRKtAYcY3kXDKWCkXm',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldP4Wxb3xI6RBeWFjnix6yK',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Gender',
            'zh-CN': '性别',
          },
          required: false,
          property: {
            relatedLinkFieldTemplateId: 'fldWbqEOpum3Yw3P07WweJry',
            lookupTargetFieldTemplateId: 'fldRMirWwnfVractCjvVCRxs',
            lookupTargetFieldType: 'SINGLE_SELECT',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldPiPiyla7ZOjSUlYvGOUW1',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Language',
            'zh-CN': '语言',
          },
          required: false,
          property: {
            relatedLinkFieldTemplateId: 'fldWbqEOpum3Yw3P07WweJry',
            lookupTargetFieldTemplateId: 'fldZbA7VTDsvDme1Uvw1qTcF',
            lookupTargetFieldType: 'SINGLE_SELECT',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recmB82tRQuKWtOB54yVXwFK',
          data: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['optAiNoP72bmGtZn20rOgxHG'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: 'Hello, today I introduce a new product to you: Bika.ai!',
            fld8pkIEZLp3Z8MeXErTEVjr: 720,
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 5',
            fldMW58e4sYfNaULx13AowUW: '#FFFFFF',
            fldP4Wxb3xI6RBeWFjnix6yK: null,
            fldP7YKG45NtMilMyHq0nNsT: 1280,
            fldPiPiyla7ZOjSUlYvGOUW1: null,
            fldSt3XYAUz0I6djjrTy7vcc: 'www.heygen.com',
            fldWbqEOpum3Yw3P07WweJry: ['recFqcozrjZ74l2QQHIN9pw6'],
            fldYK6lqMzM8ca82A9zI2PQk: null,
            fldrM1ZsxbgOtzW3nVEEuVgQ: 'bd35041059854',
            fldzJs2bRtngIWJO1uK25Btz: null,
          },
          values: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['Completed'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: 'Hello, today I introduce a new product to you: Bika.ai!',
            fld8pkIEZLp3Z8MeXErTEVjr: '720px',
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 5',
            fldMW58e4sYfNaULx13AowUW: '#FFFFFF',
            fldP4Wxb3xI6RBeWFjnix6yK: ['Male'],
            fldP7YKG45NtMilMyHq0nNsT: '1280px',
            fldPiPiyla7ZOjSUlYvGOUW1: ['English'],
            fldSt3XYAUz0I6djjrTy7vcc: 'www.heygen.com',
            fldWbqEOpum3Yw3P07WweJry: ['Conrad'],
            fldYK6lqMzM8ca82A9zI2PQk: ['b419a6ca5fb448c9a8ffdd1db0230c16'],
            fldrM1ZsxbgOtzW3nVEEuVgQ: 'bd35041059854',
            fldzJs2bRtngIWJO1uK25Btz: ['Conrad_standing_sofa_side'],
          },
        },
        {
          templateId: 'recV0nbEN8WNEIdHqpjwoGBV',
          data: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['optAiNoP72bmGtZn20rOgxHG'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: 'Hello, today I introduce a new product to you: Bika.ai!',
            fld8pkIEZLp3Z8MeXErTEVjr: 720,
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 4',
            fldMW58e4sYfNaULx13AowUW: '#FFFFFF',
            fldP4Wxb3xI6RBeWFjnix6yK: null,
            fldP7YKG45NtMilMyHq0nNsT: 1280,
            fldSt3XYAUz0I6djjrTy7vcc: 'www.heygen.com',
            fldWbqEOpum3Yw3P07WweJry: ['recmpa6C5mKjsAKnqkmJKBkD'],
            fldYK6lqMzM8ca82A9zI2PQk: null,
            fldrM1ZsxbgOtzW3nVEEuVgQ: 'bd35041059854',
            fldzJs2bRtngIWJO1uK25Btz: null,
          },
          values: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['Completed'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: 'Hello, today I introduce a new product to you: Bika.ai!',
            fld8pkIEZLp3Z8MeXErTEVjr: '720px',
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 4',
            fldMW58e4sYfNaULx13AowUW: '#FFFFFF',
            fldP4Wxb3xI6RBeWFjnix6yK: ['Male'],
            fldP7YKG45NtMilMyHq0nNsT: '1280px',
            fldPiPiyla7ZOjSUlYvGOUW1: ['English'],
            fldSt3XYAUz0I6djjrTy7vcc: 'www.heygen.com',
            fldWbqEOpum3Yw3P07WweJry: ['Raul'],
            fldYK6lqMzM8ca82A9zI2PQk: ['35659e86ce244d8389d525a9648d9c4a'],
            fldrM1ZsxbgOtzW3nVEEuVgQ: 'bd35041059854',
            fldzJs2bRtngIWJO1uK25Btz: ['Raul_sitting_casualsofawithipad_side'],
          },
        },
        {
          templateId: 'recIJ8QAJsPwNvjA2pm2SKha',
          data: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['optikmTFoUqW1'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: "Bonjour, aujourd'hui je vous présente un nouveau produit : Bika.ai!",
            fld8pkIEZLp3Z8MeXErTEVjr: 720,
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 3',
            fldMW58e4sYfNaULx13AowUW: '#9F7DCB',
            fldP4Wxb3xI6RBeWFjnix6yK: null,
            fldP7YKG45NtMilMyHq0nNsT: 1280,
            fldWbqEOpum3Yw3P07WweJry: ['recK5ZBXgWycAymbUkpjVBV2'],
            fldYK6lqMzM8ca82A9zI2PQk: null,
            fldzJs2bRtngIWJO1uK25Btz: null,
          },
          values: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['Awaited'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: "Bonjour, aujourd'hui je vous présente un nouveau produit : Bika.ai!",
            fld8pkIEZLp3Z8MeXErTEVjr: '720px',
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 3',
            fldMW58e4sYfNaULx13AowUW: '#9F7DCB',
            fldP4Wxb3xI6RBeWFjnix6yK: ['Female'],
            fldP7YKG45NtMilMyHq0nNsT: '1280px',
            fldPiPiyla7ZOjSUlYvGOUW1: ['French'],
            fldWbqEOpum3Yw3P07WweJry: ['Masha'],
            fldYK6lqMzM8ca82A9zI2PQk: ['024bdf73411946c4ac4672df51056cf1'],
            fldzJs2bRtngIWJO1uK25Btz: ['Masha_sitting_office_front'],
          },
        },
        {
          templateId: 'recMHaJSEjt6tdEswtW6eoee',
          data: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['optikmTFoUqW1'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: '你好，今天我向大家介绍一款新产品：Bika.ai ！',
            fld8pkIEZLp3Z8MeXErTEVjr: 720,
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 2',
            fldMW58e4sYfNaULx13AowUW: '#FFFFFF',
            fldP4Wxb3xI6RBeWFjnix6yK: null,
            fldP7YKG45NtMilMyHq0nNsT: 1280,
            fldWbqEOpum3Yw3P07WweJry: ['rec8K4QZdopE3O9EMDww48mI'],
            fldYK6lqMzM8ca82A9zI2PQk: null,
            fldzJs2bRtngIWJO1uK25Btz: null,
          },
          values: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['Awaited'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: '你好，今天我向大家介绍一款新产品：Bika.ai ！',
            fld8pkIEZLp3Z8MeXErTEVjr: '720px',
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 2',
            fldMW58e4sYfNaULx13AowUW: '#FFFFFF',
            fldP4Wxb3xI6RBeWFjnix6yK: ['Female'],
            fldP7YKG45NtMilMyHq0nNsT: '1280px',
            fldPiPiyla7ZOjSUlYvGOUW1: ['Chinese'],
            fldWbqEOpum3Yw3P07WweJry: ['Gala'],
            fldYK6lqMzM8ca82A9zI2PQk: ['02880d1c6fd94b7799d91135581ed810'],
            fldzJs2bRtngIWJO1uK25Btz: ['Gala_lying_sofa_front'],
          },
        },
        {
          templateId: 'reci75ygO7tQ7AFaJkay6gS4',
          data: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['optAiNoP72bmGtZn20rOgxHG'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: "Bonjour, aujourd'hui je vous présente un nouveau produit : Bika.ai!",
            fld8pkIEZLp3Z8MeXErTEVjr: 720,
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 1',
            fldMW58e4sYfNaULx13AowUW: '#7D9CCB',
            fldP4Wxb3xI6RBeWFjnix6yK: null,
            fldP7YKG45NtMilMyHq0nNsT: 1280,
            fldSt3XYAUz0I6djjrTy7vcc: 'www.heygen.com',
            fldWbqEOpum3Yw3P07WweJry: ['recq2dYVhAl1sllqYEVu40mT'],
            fldYK6lqMzM8ca82A9zI2PQk: null,
            fldrM1ZsxbgOtzW3nVEEuVgQ: 'bd35041059854',
            fldzJs2bRtngIWJO1uK25Btz: null,
          },
          values: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['Completed'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv: "Bonjour, aujourd'hui je vous présente un nouveau produit : Bika.ai!",
            fld8pkIEZLp3Z8MeXErTEVjr: '720px',
            fldCr5wuAR3IlxT5Yaut7TJS: 'Introduce video 1',
            fldMW58e4sYfNaULx13AowUW: '#7D9CCB',
            fldP4Wxb3xI6RBeWFjnix6yK: ['Male'],
            fldP7YKG45NtMilMyHq0nNsT: '1280px',
            fldPiPiyla7ZOjSUlYvGOUW1: ['French'],
            fldSt3XYAUz0I6djjrTy7vcc: 'www.heygen.com',
            fldWbqEOpum3Yw3P07WweJry: ['Brent'],
            fldYK6lqMzM8ca82A9zI2PQk: ['01d674cfd32b4728a3fddd21b7e7d543'],
            fldrM1ZsxbgOtzW3nVEEuVgQ: 'bd35041059854',
            fldzJs2bRtngIWJO1uK25Btz: ['Brent_sitting_sofa_side'],
          },
        },
        {
          templateId: 'recBKyY5SWRm2qOe6Hd3iVYW',
          data: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['optIr9TmToQXzsIAVGDI554V'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv:
              'Creating videos has never been easier! With Bika, just set your script and parameters, and automatically send them to Heygen. Instantly generate engaging video content, simplifying your entire production workflow.',
            fld8pkIEZLp3Z8MeXErTEVjr: 720,
            fldCr5wuAR3IlxT5Yaut7TJS: '1-Click Create Videos with Bika!',
            fldMW58e4sYfNaULx13AowUW: '#7b67ee',
            fldP7YKG45NtMilMyHq0nNsT: 1280,
            fldWbqEOpum3Yw3P07WweJry: ['recmpa6C5mKjsAKnqkmJKBkD'],
          },
          values: {
            fld27Jhl2BRrI7ufuPXf4Rxw: ['To be generated'],
            fld2f33pgod4HoMUN65eEk5t: '1280x720',
            fld7VyTftxdN2YUxRqtzIIKv:
              'Creating videos has never been easier! With Bika, just set your script and parameters, and automatically send them to Heygen. Instantly generate engaging video content, simplifying your entire production workflow.',
            fld8pkIEZLp3Z8MeXErTEVjr: '720px',
            fldCr5wuAR3IlxT5Yaut7TJS: '1-Click Create Videos with Bika!',
            fldMW58e4sYfNaULx13AowUW: '#7b67ee',
            fldP4Wxb3xI6RBeWFjnix6yK: ['Male'],
            fldP7YKG45NtMilMyHq0nNsT: '1280px',
            fldPiPiyla7ZOjSUlYvGOUW1: ['English'],
            fldWbqEOpum3Yw3P07WweJry: ['Raul'],
            fldYK6lqMzM8ca82A9zI2PQk: ['35659e86ce244d8389d525a9648d9c4a'],
            fldzJs2bRtngIWJO1uK25Btz: ['Raul_sitting_casualsofawithipad_side'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datM9B8augvwtDkvsDUiAXup',
      name: {
        en: 'HeyGen Characters',
        'zh-CN': 'HeyGen 人物表',
      },
      description: {
        en: "Customize commonly used characters through HeyGen's image and voice",
        'zh-CN': '通过 HeyGen 的形象和声音，定制常用的人物形象',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwuyeD8uqeLGhTU5wan4msg',
          name: {
            en: 'Default',
            'zh-CN': '默认',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldzXTun6CVIt6M1DbEBKdMD',
              hidden: false,
            },
            {
              templateId: 'fldCysEbYwHkV3M2DcEE2QgU',
              hidden: false,
            },
            {
              templateId: 'fldCvlX63FxenoZjZ1BoEOqg',
              hidden: false,
            },
            {
              templateId: 'fld4RgRRKtAYcY3kXDKWCkXm',
              hidden: false,
            },
            {
              templateId: 'fldRMirWwnfVractCjvVCRxs',
              hidden: false,
            },
            {
              templateId: 'fldZbA7VTDsvDme1Uvw1qTcF',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldzXTun6CVIt6M1DbEBKdMD',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Name',
            'zh-CN': '名称',
          },
          primary: true,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldCysEbYwHkV3M2DcEE2QgU',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Voice ID',
            'zh-CN': '声音 ID',
          },
          description: {
            en: 'Select or build your own sound from HeyGen and copy the sound ID here',
            'zh-CN': ' 从 HeyGen 中选择或自建声音，并复制声音 ID 到此处',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldCvlX63FxenoZjZ1BoEOqg',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Avatar ID',
            'zh-CN': '人物 ID',
          },
          description: {
            en: 'Select or create your own character from HeyGen and copy the avatar ID here',
            'zh-CN': ' 从 HeyGen 中选择或自建人物形象，并复制人物 ID 到此处',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld4RgRRKtAYcY3kXDKWCkXm',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Video database',
            'zh-CN': '视频数据表',
          },
          property: {
            foreignDatabaseTemplateId: 'datTUplw18O8EBWzjNSCYLaJ',
            brotherFieldTemplateId: 'fldWbqEOpum3Yw3P07WweJry',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldRMirWwnfVractCjvVCRxs',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Gender',
            'zh-CN': '性别',
          },
          property: {
            options: [
              {
                id: 'opt2dfcsgq2tvOPWowaBVK9p',
                name: 'Male',
                color: 'deepPurple',
              },
              {
                id: 'optoi8OqH4hf1HTE27Mq976e',
                name: 'Female',
                color: 'pink4',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldZbA7VTDsvDme1Uvw1qTcF',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Language',
            'zh-CN': '语言',
          },
          property: {
            options: [
              {
                id: 'optaAXfdgsdZIkLlxqa5eNkt',
                name: 'English',
                color: 'deepPurple',
              },
              {
                id: 'opt2Tzu5h8Uz9S8pdL3fVwYH',
                name: 'Chinese',
                color: 'indigo',
              },
              {
                id: 'optmf8Bv2yg3bW243RrMSeZq',
                name: 'French',
                color: 'blue',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recFqcozrjZ74l2QQHIN9pw6',
          data: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['recmB82tRQuKWtOB54yVXwFK'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Conrad_standing_sofa_side',
            fldCysEbYwHkV3M2DcEE2QgU: 'b419a6ca5fb448c9a8ffdd1db0230c16',
            fldRMirWwnfVractCjvVCRxs: ['opt2dfcsgq2tvOPWowaBVK9p'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['optaAXfdgsdZIkLlxqa5eNkt'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Conrad',
          },
          values: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['Introduce video 5'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Conrad_standing_sofa_side',
            fldCysEbYwHkV3M2DcEE2QgU: 'b419a6ca5fb448c9a8ffdd1db0230c16',
            fldRMirWwnfVractCjvVCRxs: ['Male'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['English'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Conrad',
          },
        },
        {
          templateId: 'recmpa6C5mKjsAKnqkmJKBkD',
          data: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['recV0nbEN8WNEIdHqpjwoGBV', 'recBKyY5SWRm2qOe6Hd3iVYW'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Raul_sitting_casualsofawithipad_side',
            fldCysEbYwHkV3M2DcEE2QgU: '35659e86ce244d8389d525a9648d9c4a',
            fldRMirWwnfVractCjvVCRxs: ['opt2dfcsgq2tvOPWowaBVK9p'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['optaAXfdgsdZIkLlxqa5eNkt'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Raul',
          },
          values: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['Introduce video 4', '1-Click Create Videos with Bika!'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Raul_sitting_casualsofawithipad_side',
            fldCysEbYwHkV3M2DcEE2QgU: '35659e86ce244d8389d525a9648d9c4a',
            fldRMirWwnfVractCjvVCRxs: ['Male'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['English'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Raul',
          },
        },
        {
          templateId: 'rec8K4QZdopE3O9EMDww48mI',
          data: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['recMHaJSEjt6tdEswtW6eoee'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Gala_lying_sofa_front',
            fldCysEbYwHkV3M2DcEE2QgU: '02880d1c6fd94b7799d91135581ed810',
            fldRMirWwnfVractCjvVCRxs: ['optoi8OqH4hf1HTE27Mq976e'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['opt2Tzu5h8Uz9S8pdL3fVwYH'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Gala',
          },
          values: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['Introduce video 2'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Gala_lying_sofa_front',
            fldCysEbYwHkV3M2DcEE2QgU: '02880d1c6fd94b7799d91135581ed810',
            fldRMirWwnfVractCjvVCRxs: ['Female'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['Chinese'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Gala',
          },
        },
        {
          templateId: 'recK5ZBXgWycAymbUkpjVBV2',
          data: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['recIJ8QAJsPwNvjA2pm2SKha'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Masha_sitting_office_front',
            fldCysEbYwHkV3M2DcEE2QgU: '024bdf73411946c4ac4672df51056cf1',
            fldRMirWwnfVractCjvVCRxs: ['optoi8OqH4hf1HTE27Mq976e'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['optmf8Bv2yg3bW243RrMSeZq'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Masha',
          },
          values: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['Introduce video 3'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Masha_sitting_office_front',
            fldCysEbYwHkV3M2DcEE2QgU: '024bdf73411946c4ac4672df51056cf1',
            fldRMirWwnfVractCjvVCRxs: ['Female'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['French'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Masha',
          },
        },
        {
          templateId: 'recq2dYVhAl1sllqYEVu40mT',
          data: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['reci75ygO7tQ7AFaJkay6gS4'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Brent_sitting_sofa_side',
            fldCysEbYwHkV3M2DcEE2QgU: '01d674cfd32b4728a3fddd21b7e7d543',
            fldRMirWwnfVractCjvVCRxs: ['opt2dfcsgq2tvOPWowaBVK9p'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['optmf8Bv2yg3bW243RrMSeZq'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Brent',
          },
          values: {
            fld4RgRRKtAYcY3kXDKWCkXm: ['Introduce video 1'],
            fldCvlX63FxenoZjZ1BoEOqg: 'Brent_sitting_sofa_side',
            fldCysEbYwHkV3M2DcEE2QgU: '01d674cfd32b4728a3fddd21b7e7d543',
            fldRMirWwnfVractCjvVCRxs: ['Male'],
            fldZbA7VTDsvDme1Uvw1qTcF: ['French'],
            fldzXTun6CVIt6M1DbEBKdMD: 'Brent',
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'automation_generation_video',
      name: {
        en: 'Batch generation of HeyGen videos',
        'zh-CN': '批量生成 HeyGen 视频',
      },
      description: {
        en: 'Automatic batch generation of Heygen videos based on data table content',
        'zh-CN': '基于数据表内容批量自动化生成 Heygen 视频',
      },
      triggers: [
        {
          triggerType: 'MANUALLY',
          templateId: 'confirm_generation',
          description: {
            en: 'Confirm batch generation of videos',
            'zh-CN': '确认批量生成视频',
          },
          input: {
            fields: [],
          },
        },
      ],
      actions: [
        {
          templateId: 'find_video_records',
          description: {
            en: 'Find the record to be generated',
            'zh-CN': '查找待生成的记录',
          },
          actionType: 'FIND_RECORDS',
          input: {
            interruptIfNoRecord: true,
            type: 'DATABASE_VIEW',
            viewTemplateId: 'viwwFf963bZsqmXXROECLlFZ',
            databaseTemplateId: 'datTUplw18O8EBWzjNSCYLaJ',
          },
        },
        {
          templateId: 'generate_videos_sequen',
          description: {
            en: 'Generate videos sequentially',
            'zh-CN': '依次生成视频',
          },
          actionType: 'LOOP',
          input: {
            type: 'PREV_ACTION',
            actionTemplateId: 'find_video_records',
            path: 'records',
          },
          actions: [
            {
              description: {
                en: 'Start generating video',
                'zh-CN': '开始生成视频',
              },
              actionType: 'WEBHOOK',
              templateId: 'send_request',
              input: {
                type: 'WEBHOOK',
                method: 'POST',
                headers: [
                  {
                    key: 'accept',
                    value: 'application/json',
                  },
                  {
                    key: 'content-type',
                    value: 'application/json',
                  },
                  {
                    key: 'x-api-key',
                    value: 'YOUR-API-KEY',
                  },
                ],
                url: 'https://api.heygen.com/v2/video/generate',
                body: {
                  type: 'raw',
                  format: 'json',
                  data: {
                    title: '<%= _item.cells.fldCr5wuAR3IlxT5Yaut7TJS.value %>',
                    caption: 'False',
                    dimension: {
                      width: '<%= _item.cells.fldP7YKG45NtMilMyHq0nNsT.data %>',
                      height: '<%= _item.cells.fld8pkIEZLp3Z8MeXErTEVjr.data %>',
                    },
                    video_inputs: [
                      {
                        voice: {
                          type: 'text',
                          voice_id: '<%= _item.cells.fldYK6lqMzM8ca82A9zI2PQk.value %>',
                          input_text: '<%= _item.cells.fld7VyTftxdN2YUxRqtzIIKv.value %>  ',
                        },
                        character: {
                          type: 'avatar',
                          scale: 1,
                          matting: false,
                          avatar_id: '<%= _item.cells.fldzJs2bRtngIWJO1uK25Btz.value %>',
                          avatar_style: 'normal',
                        },
                        background: {
                          type: 'color',
                          value: '<%= _item.cells.fldMW58e4sYfNaULx13AowUW.value %>',
                        },
                      },
                    ],
                  },
                },
              },
            },
            {
              description: {
                en: 'Backfill video information',
                'zh-CN': '回填视频信息',
              },
              actionType: 'UPDATE_RECORD',
              templateId: 'update_video_id',
              input: {
                type: 'SPECIFY_RECORD_BODY',
                recordId: '<%= _item.id %>',
                fieldKeyType: 'TEMPLATE_ID',
                data: {
                  fld27Jhl2BRrI7ufuPXf4Rxw: ['optAiNoP72bmGtZn20rOgxHG'],
                  fldSt3XYAUz0I6djjrTy7vcc:
                    'https://app.heygen.com/videos/<%= _itemActions.send_request.body.data.video_id %>',
                  fldrM1ZsxbgOtzW3nVEEuVgQ: '<%= _itemActions.send_request.body.data.video_id %>',
                },
                databaseTemplateId: 'datTUplw18O8EBWzjNSCYLaJ',
              },
            },
          ],
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
