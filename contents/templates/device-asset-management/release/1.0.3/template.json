{"templateId": "device-asset-management", "name": "Device Asset Management", "description": "A comprehensive Device Assets Management Template designed to track devices, manage repair service requests, and streamline client support. With an integrated form for clients to submit service requests, automated technician assignment, and customer notification upon task completion, this template simplifies device maintenance and customer communication", "cover": "/assets/template/device-asset-management/cover.png", "author": "<PERSON><PERSON> <<EMAIL>>", "category": ["technology"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.3", "resources": [{"resourceType": "DATABASE", "templateId": "dat7FgMJpjtnZdjOGSBVcOep", "name": "<PERSON><PERSON> Asset Tracking", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwB2pUVBjYLTqNreMC6LvYO", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldWchEQhWnfJSwwhVFjBCej", "width": 194}, {"templateId": "fldEs25NqAv9EFuErU2smr8E", "width": 271}, {"templateId": "fldIUowGldnpkBYpNefzdNP5"}, {"templateId": "fldYsl5wsqQaAnbiJqOEaL1w"}, {"templateId": "fldxz8lhQfgj6aZ92sj0lQh3"}, {"templateId": "fldj5j2j9ydcmoXmcNPfGale"}, {"templateId": "fldgfadj3XEMqtZT4xZHZcdo"}, {"templateId": "fldk3tqhIkgioQSRpwlbiHRd", "width": 224}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldWchEQhWnfJSwwhVFjBCej", "privilege": "TYPE_EDIT", "name": "Serial Number", "primary": true}, {"type": "FORMULA", "templateId": "fldEs25NqAv9EFuErU2smr8E", "privilege": "NAME_EDIT", "name": "Id", "property": {"expressionTemplate": "{fldIUowGldnpkBYpNefzdNP5}+“ - ”+{fldWchEQhWnfJSwwhVFjBCej}"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldIUowGldnpkBYpNefzdNP5", "privilege": "NAME_EDIT", "name": "Model", "property": {"options": [{"id": "optcYOZaETBDZDOvlnWje0oH", "name": "X150 Mini", "color": "deepPurple"}, {"id": "optEInCdMcvCN7EnyyGFMSbg", "name": "Z300 Advanced", "color": "indigo"}, {"id": "optE5OmPgGtiIaRUMnaTQ5mN", "name": "Y200 Standard", "color": "blue"}, {"id": "opt1nhjTfvhpTx9EOYPnDw1H", "name": "X100 Pro", "color": "teal"}], "defaultValue": ""}, "primary": false}, {"type": "DATETIME", "templateId": "fldYsl5wsqQaAnbiJqOEaL1w", "privilege": "NAME_EDIT", "name": "Delivery Date", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "LINK", "templateId": "fldxz8lhQfgj6aZ92sj0lQh3", "privilege": "NAME_EDIT", "name": "Customer", "property": {"foreignDatabaseTemplateId": "datxVclZPJDgdUWNdR2BWuJH", "brotherFieldTemplateId": "fldUJj0bcioO6wKEatIQtV4Y"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldj5j2j9ydcmoXmcNPfGale", "privilege": "NAME_EDIT", "name": "Current Status", "property": {"options": [{"id": "optnNP3JDNWgaYHeiZh1lrIu", "name": "Under Repair", "color": "deepPurple"}, {"id": "optggJjXmDuEVXRkg4Xk7TAU", "name": "Returned", "color": "indigo"}, {"id": "optB5BuVuwv2RyZtPRChygll", "name": "In Service", "color": "blue"}, {"id": "optS6pyMYsKfp7Rz5SF5K2ZH", "name": "Active", "color": "teal"}], "defaultValue": ""}, "primary": false}, {"type": "ATTACHMENT", "templateId": "fldgfadj3XEMqtZT4xZHZcdo", "privilege": "NAME_EDIT", "name": "Attached Files", "primary": false}, {"type": "LINK", "templateId": "fldk3tqhIkgioQSRpwlbiHRd", "privilege": "NAME_EDIT", "name": "Service Requests", "property": {"foreignDatabaseTemplateId": "dat3v1uPkP44Cfw053M2pwVs", "brotherFieldTemplateId": "fldoEFNaFGClrCXxsipU1Egb"}, "primary": false}], "records": [{"templateId": "recpBqRz2nHm33qQgJUWtx6g", "data": {"fldEs25NqAv9EFuErU2smr8E": "SN-9JK78MN5", "fldIUowGldnpkBYpNefzdNP5": ["optcYOZaETBDZDOvlnWje0oH"], "fldWchEQhWnfJSwwhVFjBCej": "SN-9JK78MN5", "fldYsl5wsqQaAnbiJqOEaL1w": "2023-04-19T16:00:00.000Z", "fldj5j2j9ydcmoXmcNPfGale": ["optnNP3JDNWgaYHeiZh1lrIu"], "fldk3tqhIkgioQSRpwlbiHRd": ["recbZxyhFoPWz2ZfHZ2drUJl"], "fldxz8lhQfgj6aZ92sj0lQh3": ["recbVlFITIUSoIZ2akcQYUWX"]}, "values": {"fldEs25NqAv9EFuErU2smr8E": "X150 Mini - SN-9JK78MN5", "fldIUowGldnpkBYpNefzdNP5": ["X150 Mini"], "fldWchEQhWnfJSwwhVFjBCej": "SN-9JK78MN5", "fldYsl5wsqQaAnbiJqOEaL1w": "2023-04-19", "fldj5j2j9ydcmoXmcNPfGale": ["Under Repair"], "fldk3tqhIkgioQSRpwlbiHRd": ["SN-9JK78MN5 - 20241218"], "fldxz8lhQfgj6aZ92sj0lQh3": ["Healthcare Systems Co."]}}, {"templateId": "recLyqUxmUVwCax0WbLmAklQ", "data": {"fldEs25NqAv9EFuErU2smr8E": "SN-HYT34L09", "fldIUowGldnpkBYpNefzdNP5": ["optEInCdMcvCN7EnyyGFMSbg"], "fldWchEQhWnfJSwwhVFjBCej": "SN-HYT34L09", "fldYsl5wsqQaAnbiJqOEaL1w": "2023-03-04T16:00:00.000Z", "fldj5j2j9ydcmoXmcNPfGale": ["optggJjXmDuEVXRkg4Xk7TAU"], "fldk3tqhIkgioQSRpwlbiHRd": ["recatNzZj5fAWd6Bhbd4ARyi"], "fldxz8lhQfgj6aZ92sj0lQh3": ["recL2gsDUHzeuh0lCSeN0Joe"]}, "values": {"fldEs25NqAv9EFuErU2smr8E": "Z300 Advanced - SN-HYT34L09", "fldIUowGldnpkBYpNefzdNP5": ["Z300 Advanced"], "fldWchEQhWnfJSwwhVFjBCej": "SN-HYT34L09", "fldYsl5wsqQaAnbiJqOEaL1w": "2023-03-04", "fldj5j2j9ydcmoXmcNPfGale": ["Returned"], "fldk3tqhIkgioQSRpwlbiHRd": ["SN-HYT34L09 - 20241218"], "fldxz8lhQfgj6aZ92sj0lQh3": ["Retail Innovations Ltd."]}}, {"templateId": "recJRGkWlZpayraJZLhpbbXR", "data": {"fldEs25NqAv9EFuErU2smr8E": "SN-ZX56Y78W", "fldIUowGldnpkBYpNefzdNP5": ["optE5OmPgGtiIaRUMnaTQ5mN"], "fldWchEQhWnfJSwwhVFjBCej": "SN-ZX56Y78W", "fldYsl5wsqQaAnbiJqOEaL1w": "2023-02-09T16:00:00.000Z", "fldj5j2j9ydcmoXmcNPfGale": ["optB5BuVuwv2RyZtPRChygll"], "fldk3tqhIkgioQSRpwlbiHRd": ["recHoKSVPBlcclYCd0yeq10V"], "fldxz8lhQfgj6aZ92sj0lQh3": ["recL2gsDUHzeuh0lCSeN0Joe"]}, "values": {"fldEs25NqAv9EFuErU2smr8E": "Y200 Standard - SN-ZX56Y78W", "fldIUowGldnpkBYpNefzdNP5": ["Y200 Standard"], "fldWchEQhWnfJSwwhVFjBCej": "SN-ZX56Y78W", "fldYsl5wsqQaAnbiJqOEaL1w": "2023-02-09", "fldj5j2j9ydcmoXmcNPfGale": ["In Service"], "fldk3tqhIkgioQSRpwlbiHRd": ["SN-ZX56Y78W - 20241218"], "fldxz8lhQfgj6aZ92sj0lQh3": ["Retail Innovations Ltd."]}}, {"templateId": "recqPVVd5iIncJW4A3e3qVji", "data": {"fldEs25NqAv9EFuErU2smr8E": "SN-A1B2C3D4", "fldIUowGldnpkBYpNefzdNP5": ["opt1nhjTfvhpTx9EOYPnDw1H"], "fldWchEQhWnfJSwwhVFjBCej": "SN-A1B2C3D4", "fldYsl5wsqQaAnbiJqOEaL1w": "2023-01-14T16:00:00.000Z", "fldgfadj3XEMqtZT4xZHZcdo": [], "fldj5j2j9ydcmoXmcNPfGale": ["optS6pyMYsKfp7Rz5SF5K2ZH"], "fldk3tqhIkgioQSRpwlbiHRd": ["recuG14Zn9FkzWdC8yDOmX80"], "fldxz8lhQfgj6aZ92sj0lQh3": ["recPjg7pSBbdEv8EsxWMCSiF"]}, "values": {"fldEs25NqAv9EFuErU2smr8E": "X100 Pro - SN-A1B2C3D4", "fldIUowGldnpkBYpNefzdNP5": ["X100 Pro"], "fldWchEQhWnfJSwwhVFjBCej": "SN-A1B2C3D4", "fldYsl5wsqQaAnbiJqOEaL1w": "2023-01-14", "fldgfadj3XEMqtZT4xZHZcdo": [], "fldj5j2j9ydcmoXmcNPfGale": ["Active"], "fldk3tqhIkgioQSRpwlbiHRd": ["SN-A1B2C3D4 - 20241218"], "fldxz8lhQfgj6aZ92sj0lQh3": ["Tech Solutions Inc."]}}]}, {"resourceType": "DATABASE", "templateId": "datxVclZPJDgdUWNdR2BWuJH", "name": "Client Information", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwaBdkqUVJPkeyhclvtFdQs", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fld4Jz3aOMf3TJ3Fomfjzp6V", "width": 210}, {"templateId": "fldUJj0bcioO6wKEatIQtV4Y"}, {"templateId": "fldpb0AAJECcKGxGV3TPxInZ"}, {"templateId": "fldukEydafjQK3pI1FOCwDFJ"}, {"templateId": "fld8keLhBkCVEGi88SUenqy8"}, {"templateId": "fldqVsqIbCK1NKAYmfjrS8iX"}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fld4Jz3aOMf3TJ3Fomfjzp6V", "privilege": "TYPE_EDIT", "name": "Client company name", "primary": true}, {"type": "LINK", "templateId": "fldUJj0bcioO6wKEatIQtV4Y", "privilege": "NAME_EDIT", "name": "<PERSON><PERSON>", "property": {"foreignDatabaseTemplateId": "dat7FgMJpjtnZdjOGSBVcOep", "brotherFieldTemplateId": "fldxz8lhQfgj6aZ92sj0lQh3"}, "primary": false}, {"type": "PHONE", "templateId": "fldpb0AAJECcKGxGV3TPxInZ", "privilege": "NAME_EDIT", "name": "Phone", "primary": false}, {"type": "EMAIL", "templateId": "fldukEydafjQK3pI1FOCwDFJ", "privilege": "NAME_EDIT", "name": "Email", "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fld8keLhBkCVEGi88SUenqy8", "privilege": "NAME_EDIT", "name": "Customer contact name", "primary": false}, {"type": "LINK", "templateId": "fldqVsqIbCK1NKAYmfjrS8iX", "privilege": "NAME_EDIT", "name": "Service requests", "property": {"foreignDatabaseTemplateId": "dat3v1uPkP44Cfw053M2pwVs", "brotherFieldTemplateId": "fldWsP2FYCEyQew7m2O3X54R"}, "primary": false}], "records": [{"templateId": "recL2gsDUHzeuh0lCSeN0Joe", "data": {"fld4Jz3aOMf3TJ3Fomfjzp6V": "Retail Innovations Ltd.", "fld8keLhBkCVEGi88SUenqy8": "<PERSON>", "fldUJj0bcioO6wKEatIQtV4Y": ["recJRGkWlZpayraJZLhpbbXR", "recLyqUxmUVwCax0WbLmAklQ"], "fldpb0AAJECcKGxGV3TPxInZ": "******-567-8901", "fldqVsqIbCK1NKAYmfjrS8iX": ["recatNzZj5fAWd6Bhbd4ARyi", "recHoKSVPBlcclYCd0yeq10V"], "fldukEydafjQK3pI1FOCwDFJ": "<EMAIL>"}, "values": {"fld4Jz3aOMf3TJ3Fomfjzp6V": "Retail Innovations Ltd.", "fld8keLhBkCVEGi88SUenqy8": "<PERSON>", "fldUJj0bcioO6wKEatIQtV4Y": ["SN-ZX56Y78W", "SN-HYT34L09"], "fldpb0AAJECcKGxGV3TPxInZ": "******-567-8901", "fldqVsqIbCK1NKAYmfjrS8iX": ["SN-HYT34L09 - 20241218", "SN-ZX56Y78W - 20241218"], "fldukEydafjQK3pI1FOCwDFJ": "<EMAIL>"}}, {"templateId": "recbVlFITIUSoIZ2akcQYUWX", "data": {"fld4Jz3aOMf3TJ3Fomfjzp6V": "Healthcare Systems Co.", "fld8keLhBkCVEGi88SUenqy8": "<PERSON>", "fldUJj0bcioO6wKEatIQtV4Y": ["recpBqRz2nHm33qQgJUWtx6g"], "fldpb0AAJECcKGxGV3TPxInZ": "+1-************", "fldqVsqIbCK1NKAYmfjrS8iX": ["recbZxyhFoPWz2ZfHZ2drUJl"], "fldukEydafjQK3pI1FOCwDFJ": "<EMAIL>"}, "values": {"fld4Jz3aOMf3TJ3Fomfjzp6V": "Healthcare Systems Co.", "fld8keLhBkCVEGi88SUenqy8": "<PERSON>", "fldUJj0bcioO6wKEatIQtV4Y": ["SN-9JK78MN5"], "fldpb0AAJECcKGxGV3TPxInZ": "+1-************", "fldqVsqIbCK1NKAYmfjrS8iX": ["SN-9JK78MN5 - 20241218"], "fldukEydafjQK3pI1FOCwDFJ": "<EMAIL>"}}, {"templateId": "recPjg7pSBbdEv8EsxWMCSiF", "data": {"fld4Jz3aOMf3TJ3Fomfjzp6V": "Tech Solutions Inc.", "fld8keLhBkCVEGi88SUenqy8": "<PERSON>", "fldUJj0bcioO6wKEatIQtV4Y": ["recqPVVd5iIncJW4A3e3qVji"], "fldpb0AAJECcKGxGV3TPxInZ": "+1-************", "fldqVsqIbCK1NKAYmfjrS8iX": ["recuG14Zn9FkzWdC8yDOmX80"], "fldukEydafjQK3pI1FOCwDFJ": "<EMAIL>"}, "values": {"fld4Jz3aOMf3TJ3Fomfjzp6V": "Tech Solutions Inc.", "fld8keLhBkCVEGi88SUenqy8": "<PERSON>", "fldUJj0bcioO6wKEatIQtV4Y": ["SN-A1B2C3D4"], "fldpb0AAJECcKGxGV3TPxInZ": "+1-************", "fldqVsqIbCK1NKAYmfjrS8iX": ["SN-A1B2C3D4 - 20241218"], "fldukEydafjQK3pI1FOCwDFJ": "<EMAIL>"}}]}, {"resourceType": "DATABASE", "templateId": "dat3v1uPkP44Cfw053M2pwVs", "name": "Repair Service Management ", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwZJBtwdl9gkIw3r62UXxUO", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldNpO9VnveGW5PTOUEqKa4m", "width": 182}, {"templateId": "fldqmD4XmiLObKumCWs3oNRC", "width": 225}, {"templateId": "fldWyNkn6vtVy2jR7AzrEC61"}, {"templateId": "fldoEFNaFGClrCXxsipU1Egb"}, {"templateId": "fldGJrNeQ745Og62mDlhnqjO"}, {"templateId": "fld8VrD5Rp0gZTHLMLL5p6CA"}, {"templateId": "fldGJlWFLbmv7pK7MXtcHISH"}, {"templateId": "fldt4o2SDvdrUDHfvniRHkms"}, {"templateId": "fldWsP2FYCEyQew7m2O3X54R"}, {"templateId": "fldrW3V8Miz0Wxnlc4T1B6HP"}]}, {"type": "TABLE", "templateId": "viwibJM0IzzsAsLOXt7XyUrC", "name": "Request form", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldNpO9VnveGW5PTOUEqKa4m", "hidden": false, "width": 218}, {"templateId": "fldWyNkn6vtVy2jR7AzrEC61", "hidden": false}, {"templateId": "fldoEFNaFGClrCXxsipU1Egb", "hidden": false}, {"templateId": "fld8VrD5Rp0gZTHLMLL5p6CA", "hidden": false}, {"templateId": "fldqmD4XmiLObKumCWs3oNRC", "hidden": false}, {"templateId": "fldGJrNeQ745Og62mDlhnqjO", "hidden": false}, {"templateId": "fldGJlWFLbmv7pK7MXtcHISH", "hidden": false}, {"templateId": "fldt4o2SDvdrUDHfvniRHkms", "hidden": false}, {"templateId": "fldWsP2FYCEyQew7m2O3X54R", "hidden": false}, {"templateId": "fldrW3V8Miz0Wxnlc4T1B6HP", "hidden": false}], "groups": []}], "fields": [{"type": "FORMULA", "templateId": "fldNpO9VnveGW5PTOUEqKa4m", "privilege": "TYPE_EDIT", "name": "Ticket ID（No need to fill）", "property": {"expressionTemplate": "{fldoEFNaFGClrCXxsipU1Egb}+“ - ”+DATETIME_FORMAT({fldqmD4XmiLObKumCWs3oNRC},\"YYYYMMDD\")"}, "primary": true}, {"type": "CREATED_TIME", "templateId": "fldqmD4XmiLObKumCWs3oNRC", "privilege": "NAME_EDIT", "name": "Submission Time", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": true}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldWyNkn6vtVy2jR7AzrEC61", "privilege": "NAME_EDIT", "name": "Issue Description", "primary": false}, {"type": "LINK", "templateId": "fldoEFNaFGClrCXxsipU1Egb", "privilege": "NAME_EDIT", "name": "Linked Device", "property": {"foreignDatabaseTemplateId": "dat7FgMJpjtnZdjOGSBVcOep", "brotherFieldTemplateId": "fldk3tqhIkgioQSRpwlbiHRd"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldGJrNeQ745Og62mDlhnqjO", "privilege": "NAME_EDIT", "name": "Ticket Status", "property": {"options": [{"id": "optUHfjwVRHnbEUyx2PcQCBF", "name": "In Progress", "color": "deepPurple"}, {"id": "optQHmChZ14TQ30ra0loEp6X", "name": "Pending Approval", "color": "indigo"}, {"id": "optPolZJ3HNk8Djoyv9DkKZc", "name": "Closed", "color": "blue"}, {"id": "optHFMLfngkoGZIUs5RxhLwS", "name": "Open", "color": "teal"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fld8VrD5Rp0gZTHLMLL5p6CA", "privilege": "NAME_EDIT", "name": "Priority", "property": {"options": [{"id": "opthwmjxTtF8A32oZkeh1cPT", "name": "High", "color": "red4"}, {"id": "optFfQJkohIic6IPTX1qcINZ", "name": "Low", "color": "teal3"}, {"id": "optqCRaiGyM7min5nX7S1i7b", "name": "Medium", "color": "blue"}], "defaultValue": ""}, "primary": false}, {"type": "LINK", "templateId": "fldGJlWFLbmv7pK7MXtcHISH", "privilege": "NAME_EDIT", "name": "Assigned Technician", "property": {"foreignDatabaseTemplateId": "datj5F3WR2oag2bPjqyXmkzN", "brotherFieldTemplateId": "fldmy1pCpumbLeRzAHZ7sKbe"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldt4o2SDvdrUDHfvniRHkms", "privilege": "NAME_EDIT", "name": "Solution", "primary": false}, {"type": "LINK", "templateId": "fldWsP2FYCEyQew7m2O3X54R", "privilege": "NAME_EDIT", "name": "Submitter", "property": {"foreignDatabaseTemplateId": "datxVclZPJDgdUWNdR2BWuJH", "brotherFieldTemplateId": "fldqVsqIbCK1NKAYmfjrS8iX"}, "primary": false}, {"type": "LOOKUP", "templateId": "fldrW3V8Miz0Wxnlc4T1B6HP", "privilege": "NAME_EDIT", "name": "Contact email", "property": {"databaseTemplateId": "datxVclZPJDgdUWNdR2BWuJH", "relatedLinkFieldTemplateId": "fldWsP2FYCEyQew7m2O3X54R", "lookupTargetFieldTemplateId": "fldukEydafjQK3pI1FOCwDFJ", "lookupTargetFieldType": "EMAIL", "dataType": "STRING", "lookUpLimit": "ALL", "rollUpType": "VALUES"}, "primary": false}], "records": [{"templateId": "recbZxyhFoPWz2ZfHZ2drUJl", "data": {"fld8VrD5Rp0gZTHLMLL5p6CA": ["opthwmjxTtF8A32oZkeh1cPT"], "fldGJrNeQ745Og62mDlhnqjO": "In Progress", "fldNpO9VnveGW5PTOUEqKa4m": null, "fldWsP2FYCEyQew7m2O3X54R": ["recbVlFITIUSoIZ2akcQYUWX"], "fldWyNkn6vtVy2jR7AzrEC61": "Overheating during operation", "fldoEFNaFGClrCXxsipU1Egb": ["recpBqRz2nHm33qQgJUWtx6g"], "fldqmD4XmiLObKumCWs3oNRC": "2024-12-18T16:00:00.000Z", "fldrW3V8Miz0Wxnlc4T1B6HP": null, "fldt4o2SDvdrUDHfvniRHkms": "Reinstalled software updates"}, "values": {"fld8VrD5Rp0gZTHLMLL5p6CA": ["High"], "fldGJrNeQ745Og62mDlhnqjO": "In Progress", "fldNpO9VnveGW5PTOUEqKa4m": "SN-9JK78MN5 - 20241218", "fldWsP2FYCEyQew7m2O3X54R": ["Healthcare Systems Co."], "fldWyNkn6vtVy2jR7AzrEC61": "Overheating during operation", "fldoEFNaFGClrCXxsipU1Egb": ["SN-9JK78MN5"], "fldqmD4XmiLObKumCWs3oNRC": "2024-12-18", "fldrW3V8Miz0Wxnlc4T1B6HP": ["<EMAIL>"], "fldt4o2SDvdrUDHfvniRHkms": "Reinstalled software updates"}}, {"templateId": "recHoKSVPBlcclYCd0yeq10V", "data": {"fld8VrD5Rp0gZTHLMLL5p6CA": ["optFfQJkohIic6IPTX1qcINZ"], "fldGJrNeQ745Og62mDlhnqjO": "Pending Approval", "fldNpO9VnveGW5PTOUEqKa4m": null, "fldWsP2FYCEyQew7m2O3X54R": ["recL2gsDUHzeuh0lCSeN0Joe"], "fldWyNkn6vtVy2jR7AzrEC61": "Software update failure", "fldoEFNaFGClrCXxsipU1Egb": ["recJRGkWlZpayraJZLhpbbXR"], "fldqmD4XmiLObKumCWs3oNRC": "2024-12-18T16:00:00.000Z", "fldrW3V8Miz0Wxnlc4T1B6HP": null, "fldt4o2SDvdrUDHfvniRHkms": "Screen recalibrated and tested"}, "values": {"fld8VrD5Rp0gZTHLMLL5p6CA": ["Low"], "fldGJrNeQ745Og62mDlhnqjO": "Pending Approval", "fldNpO9VnveGW5PTOUEqKa4m": "SN-ZX56Y78W - 20241218", "fldWsP2FYCEyQew7m2O3X54R": ["Retail Innovations Ltd."], "fldWyNkn6vtVy2jR7AzrEC61": "Software update failure", "fldoEFNaFGClrCXxsipU1Egb": ["SN-ZX56Y78W"], "fldqmD4XmiLObKumCWs3oNRC": "2024-12-18", "fldrW3V8Miz0Wxnlc4T1B6HP": ["<EMAIL>"], "fldt4o2SDvdrUDHfvniRHkms": "Screen recalibrated and tested"}}, {"templateId": "recatNzZj5fAWd6Bhbd4ARyi", "data": {"fld8VrD5Rp0gZTHLMLL5p6CA": ["optqCRaiGyM7min5nX7S1i7b"], "fldGJlWFLbmv7pK7MXtcHISH": ["recCqw4g3wrbHOeoyZoyey37"], "fldGJrNeQ745Og62mDlhnqjO": "Closed", "fldNpO9VnveGW5PTOUEqKa4m": null, "fldWsP2FYCEyQew7m2O3X54R": ["recL2gsDUHzeuh0lCSeN0Joe"], "fldWyNkn6vtVy2jR7AzrEC61": "Screen not displaying correctly", "fldoEFNaFGClrCXxsipU1Egb": ["recLyqUxmUVwCax0WbLmAklQ"], "fldqmD4XmiLObKumCWs3oNRC": "2024-12-18T16:00:00.000Z", "fldrW3V8Miz0Wxnlc4T1B6HP": null, "fldt4o2SDvdrUDHfvniRHkms": "Replaced power adapter"}, "values": {"fld8VrD5Rp0gZTHLMLL5p6CA": ["Medium"], "fldGJlWFLbmv7pK7MXtcHISH": [""], "fldGJrNeQ745Og62mDlhnqjO": "Closed", "fldNpO9VnveGW5PTOUEqKa4m": "SN-HYT34L09 - 20241218", "fldWsP2FYCEyQew7m2O3X54R": ["Retail Innovations Ltd."], "fldWyNkn6vtVy2jR7AzrEC61": "Screen not displaying correctly", "fldoEFNaFGClrCXxsipU1Egb": ["SN-HYT34L09"], "fldqmD4XmiLObKumCWs3oNRC": "2024-12-18", "fldrW3V8Miz0Wxnlc4T1B6HP": ["<EMAIL>"], "fldt4o2SDvdrUDHfvniRHkms": "Replaced power adapter"}}, {"templateId": "recuG14Zn9FkzWdC8yDOmX80", "data": {"fld8VrD5Rp0gZTHLMLL5p6CA": ["opthwmjxTtF8A32oZkeh1cPT"], "fldGJlWFLbmv7pK7MXtcHISH": ["rechUN0BmPHmtu6EKSY9hxHY"], "fldGJrNeQ745Og62mDlhnqjO": "Open", "fldNpO9VnveGW5PTOUEqKa4m": null, "fldWsP2FYCEyQew7m2O3X54R": ["recPjg7pSBbdEv8EsxWMCSiF"], "fldWyNkn6vtVy2jR7AzrEC61": "Power failure after use", "fldoEFNaFGClrCXxsipU1Egb": ["recqPVVd5iIncJW4A3e3qVji"], "fldqmD4XmiLObKumCWs3oNRC": "2024-12-18T16:00:00.000Z", "fldrW3V8Miz0Wxnlc4T1B6HP": null}, "values": {"fld8VrD5Rp0gZTHLMLL5p6CA": ["High"], "fldGJlWFLbmv7pK7MXtcHISH": ["<PERSON>"], "fldGJrNeQ745Og62mDlhnqjO": "Open", "fldNpO9VnveGW5PTOUEqKa4m": "SN-A1B2C3D4 - 20241218", "fldWsP2FYCEyQew7m2O3X54R": ["Tech Solutions Inc."], "fldWyNkn6vtVy2jR7AzrEC61": "Power failure after use", "fldoEFNaFGClrCXxsipU1Egb": ["SN-A1B2C3D4"], "fldqmD4XmiLObKumCWs3oNRC": "2024-12-18", "fldrW3V8Miz0Wxnlc4T1B6HP": ["<EMAIL>"]}}]}, {"resourceType": "DATABASE", "templateId": "datj5F3WR2oag2bPjqyXmkzN", "name": "Technician List", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwYnuqC4SnKSE802uVnHnDF", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fld2CrMTWDGnCNxHJM4QssIL", "width": 150}, {"templateId": "fldmy1pCpumbLeRzAHZ7sKbe", "width": 150}, {"templateId": "fldANQkIU30gVwWQaeV6U7Xq"}]}], "fields": [{"type": "MEMBER", "templateId": "fld2CrMTWDGnCNxHJM4QssIL", "privilege": "TYPE_EDIT", "name": "Name", "property": {}, "primary": true}, {"type": "LINK", "templateId": "fldmy1pCpumbLeRzAHZ7sKbe", "privilege": "NAME_EDIT", "name": "Service Requests", "property": {"foreignDatabaseTemplateId": "dat3v1uPkP44Cfw053M2pwVs", "brotherFieldTemplateId": "fldGJlWFLbmv7pK7MXtcHISH"}, "primary": false}, {"type": "EMAIL", "templateId": "fldANQkIU30gVwWQaeV6U7Xq", "privilege": "NAME_EDIT", "name": "Email", "primary": false}], "records": [{"templateId": "recKJQt6uDTJITSuRfA4bxRN", "data": {"fld2CrMTWDGnCNxHJM4QssIL": [], "fldANQkIU30gVwWQaeV6U7Xq": "micha<PERSON>.<EMAIL>"}, "values": {"fld2CrMTWDGnCNxHJM4QssIL": [], "fldANQkIU30gVwWQaeV6U7Xq": "micha<PERSON>.<EMAIL>"}}, {"templateId": "recCqw4g3wrbHOeoyZoyey37", "data": {"fld2CrMTWDGnCNxHJM4QssIL": [], "fldANQkIU30gVwWQaeV6U7Xq": "<EMAIL>", "fldmy1pCpumbLeRzAHZ7sKbe": ["recatNzZj5fAWd6Bhbd4ARyi"]}, "values": {"fld2CrMTWDGnCNxHJM4QssIL": [], "fldANQkIU30gVwWQaeV6U7Xq": "<EMAIL>", "fldmy1pCpumbLeRzAHZ7sKbe": ["SN-HYT34L09 - 20241218"]}}, {"templateId": "rechUN0BmPHmtu6EKSY9hxHY", "data": {"fld2CrMTWDGnCNxHJM4QssIL": ["mebLEjuvm9b3itUY2DFh8P18"], "fldANQkIU30gVwWQaeV6U7Xq": "<EMAIL>", "fldmy1pCpumbLeRzAHZ7sKbe": ["recuG14Zn9FkzWdC8yDOmX80"]}, "values": {"fld2CrMTWDGnCNxHJM4QssIL": ["<PERSON>"], "fldANQkIU30gVwWQaeV6U7Xq": "<EMAIL>", "fldmy1pCpumbLeRzAHZ7sKbe": ["SN-A1B2C3D4 - 20241218"]}}]}, {"resourceType": "FORM", "templateId": "fom70IZmxhawCvccPKbLqMm0", "name": "Repair Service Request Form", "formType": "DATABASE", "databaseTemplateId": "dat3v1uPkP44Cfw053M2pwVs", "metadata": {"type": "VIEW", "viewTemplateId": "viwibJM0IzzsAsLOXt7XyUrC"}}, {"resourceType": "AUTOMATION", "templateId": "auto_retrieve_email", "name": "New ticket reminder", "triggers": [{"triggerType": "RECORD_CREATED", "templateId": "received_email", "description": "New service ticket reminder", "input": {"type": "DATABASE", "databaseTemplateId": "dat3v1uPkP44Cfw053M2pwVs"}}], "actions": [{"templateId": "take_follower", "description": "Rotate follow-up technicians", "actionType": "ROUND_ROBIN", "input": {"type": "DATABASE", "databaseTemplateId": "datj5F3WR2oag2bPjqyXmkzN"}}, {"templateId": "actZM9b9hYrWV1zmtaMJSwGZ", "description": "Assign technician", "actionType": "UPDATE_RECORD", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _triggers.received_email.record.id %>", "fieldKeyType": "TEMPLATE_ID", "data": {"fldGJlWFLbmv7pK7MXtcHISH": "<%= JSON.stringify(_actions.take_follower.record.cells.fld2CrMTWDGnCNxHJM4QssIL.data) %>"}, "databaseTemplateId": "dat3v1uPkP44Cfw053M2pwVs"}}, {"templateId": "create_mission", "description": "Create a mission to follow up on a new ticket", "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "CREATE_TASK", "name": "Hi, you have a new Service request to follow up", "description": "You have a new customer device repair service request ticket to follow up  \n  \nMission Objectives: <%= _triggers.received_email.record.cells.fldNpO9VnveGW5PTOUEqKa4m.value %>  \nView ticket details to help customers troubleshoot and resolve issues, and change the status to Completed when completed.", "canReject": true, "canCompleteManually": true, "assignType": "DEDICATED", "to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= JSON.stringify(_actions.take_follower.record.cells.fld2CrMTWDGnCNxHJM4QssIL.data) %>"]}], "buttonText": {"en": "Follow up", "ja": "フォローアップ", "zh-CN": "跟进", "zh-TW": "跟進"}}}}]}, {"resourceType": "AUTOMATION", "templateId": "atohIOJfuvH6eJoJ1PpiDVtl", "name": "Notify the client after the device repair service request is completed", "triggers": [{"triggerType": "RECORD_MATCH", "templateId": "trglTjtRmBGQL4p78dakK70f", "description": "When the device repair service request is completed", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldGJrNeQ745Og62mDlhnqjO", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optPolZJ3HNk8Djoyv9DkKZc"}}]}, "databaseTemplateId": "dat3v1uPkP44Cfw053M2pwVs"}}], "actions": [{"templateId": "actUEtFY7eB2eq9wb5J33d6Y", "description": "Send an email to notify client", "actionType": "SEND_EMAIL", "input": {"subject": "Your device repair service has been completed", "body": {"markdown": "Your device repair service has been completed！\nService ticket: <%= _triggers.trglTjtRmBGQL4p78dakK70f.record.cells.fldNpO9VnveGW5PTOUEqKa4m.value %>\nYour device: <%= JSON.stringify(_triggers.trglTjtRmBGQL4p78dakK70f.record.cells.fldoEFNaFGClrCXxsipU1Egb.value) %>\nOur solution: <%= _triggers.trglTjtRmBGQL4p78dakK70f.record.cells.fldt4o2SDvdrUDHfvniRHkms.value %>  \n  \nIf you have any questions, please feel free to contact us.\nThank you for your support!", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "Your device repair service has been completed！", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "Service ticket: ", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trglTjtRmBGQL4p78dakK70f", "record", "cells", "fldNpO9VnveGW5PTOUEqKa4m", "value"], "tips": "", "names": ["触发器", "有记录满足条件时", "记录", "单元格", "Ticket ID", "value"]}}]}, {"type": "paragraph", "content": [{"text": "Your device:  ", "type": "text"}, {"type": "variable", "attrs": {"ids": "JSON.stringify(_triggers.trglTjtRmBGQL4p78dakK70f.record.cells.fldoEFNaFGClrCXxsipU1Egb.value)", "tips": "", "names": ["触发器", "有记录满足条件时", "记录", "单元格", "Linked Device", "value"]}}]}, {"type": "paragraph", "content": [{"text": "Our solution:  ", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trglTjtRmBGQL4p78dakK70f", "record", "cells", "fldt4o2SDvdrUDHfvniRHkms", "value"], "tips": "", "names": ["触发器", "有记录满足条件时", "记录", "单元格", "Resolution", "value"]}}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "If you have any questions, please feel free to contact us.", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "Thank you for your support! ", "type": "text"}]}]}}, "to": [{"type": "EMAIL_FIELD", "fieldTemplateId": "fldrW3V8Miz0Wxnlc4T1B6HP", "viewTemplateId": "viwZJBtwdl9gkIw3r62UXxUO", "databaseTemplateId": "dat3v1uPkP44Cfw053M2pwVs"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}], "initMissions": []}