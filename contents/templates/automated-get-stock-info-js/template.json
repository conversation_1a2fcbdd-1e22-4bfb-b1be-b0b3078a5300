{"templateId": "automated-get-stock-info-js", "name": {"en": "Automated Stock Data Retrieval (JavaScript)", "ja": "自動取得株価データ (JavaScript)", "zh-CN": "自动获取股票数据 (JavaScript)", "zh-TW": "自動獲取股票數據 (JavaScript)"}, "description": {"en": "Automatically fetches specific stock information on a daily basis and saves it to a table. This allows users to easily track and analyze stock trends, saving time and improving investment decisions.", "ja": "特定の株式情報を毎日自動的に取得し、テーブルに保存します。これにより、ユーザーは株価の動向を簡単に追跡し分析することができ、時間を節約し投資判断を改善することができます。", "zh-CN": "每天定时获取特定股票的信息保存到表格中，可以轻松追踪和分析股票趋势，节省时间并改善投资决策。", "zh-TW": "每天自動獲取特定股票的信息並保存到表格中，使用者可以輕鬆追蹤和分析股票趨勢，節省時間並改善投資決策。"}, "cover": "/assets/template/template-cover-automated-get-stock-info-js.png", "author": "<PERSON> <<EMAIL>>", "category": ["finance", "script"], "keywords": {"en": "Stock data automation, Daily stock retrieval, Investment analysis, Financial data management, Alpha Vantage integration", "ja": "株式データ自動化, 毎日の株価取得, 投資分析, 財務データ管理, Alpha Vantage統合", "zh-TW": "股票數據自動化, 每日股票檢索, 投資分析, 財務數據管理, Alpha Vantage整合", "zh-CN": "股票数据自动化, 每日股票检索, 投资分析, 财务数据管理, Alpha Vantage集成"}, "personas": {"en": "Financial Analyst, Data Scientist, Investment Manager, Software Developer, Quantitative Analyst, Portfolio Manager", "zh-CN": "金融分析师, 数据科学家, 投资经理, 软件开发人员, 量化分析师, 投资组合经理", "zh-TW": "金融分析師, 數據科學家, 投資經理, 軟體開發人員, 量化分析師, 投資組合經理", "ja": "金融アナリスト, データサイエンティスト, 投資マネージャー, ソフトウェア開発者, クオンツアナリスト, ポートフォリオマネージャー"}, "useCases": {"en": "Daily stock performance tracking, Investment portfolio analysis, Financial market research, Automated stock trend analysis, Real-time stock data monitoring, Historical stock data comparison, Data cleansing and preprocessing, Predictive modeling, Machine learning algorithm training, Data visualization, Trend analysis, Correlation analysis, Portfolio management, Risk assessment, Asset allocation, Performance benchmarking, Investment strategy development, Regulatory compliance, API integration, Automation script development, Data pipeline creation, Application development, Performance optimization, Error handling, Quantitative modeling, Statistical analysis, Algorithmic trading, Backtesting strategies, Market risk analysis, Signal generation, Portfolio rebalancing, Diversification strategies, Performance tracking, Client reporting, Investment policy formulation, Long-term investment planning", "zh-CN": "每日股票表现追踪, 投资组合分析, 金融市场研究, 自动化股票趋势分析, 实时股票数据监控, 历史股票数据比较, 数据清洗和预处理, 预测建模, 机器学习算法训练, 数据可视化, 趋势分析, 相关性分析, 投资组合管理, 风险评估, 资产配置, 业绩基准比较, 投资策略开发, 法规遵从, API 集成, 自动化脚本开发, 数据管道创建, 应用开发, 性能优化, 错误处理, 定量建模, 统计分析, 算法交易, 策略回测, 市场风险分析, 信号生成, 投资组合再平衡, 多元化策略, 业绩追踪, 客户报告, 投资政策制定, 长期投资规划", "zh-TW": "每日股票表現追踪, 投資組合分析, 金融市場研究, 自動化股票趨勢分析, 實時股票數據監控, 歷史股票數據比較, 數據清洗和預處理, 預測建模, 機器學習算法訓練, 數據可視化, 趨勢分析, 相關性分析, 投資組合管理, 風險評估, 資產配置, 業績基準比較, 投資策略開發, 法規遵從, API 集成, 自動化腳本開發, 數據管道創建, 應用開發, 性能優化, 錯誤處理, 定量建模, 統計分析, 算法交易, 策略回測, 市場風險分析, 信號生成, 投資組合再平衡, 多元化策略, 業績追蹤, 客戶報告, 投資政策制定, 長期投資規劃", "ja": "毎日の株価パフォーマンストラッキング, 投資ポートフォリオ分析, 金融市場調査, 自動化株価トレンド分析, リアルタイム株価データ監視, 歴史的株価データ比較, データクレンジングと前処理, 予測モデリング, 機械学習アルゴリズムの訓練, データ可視化, トレンド分析, 相関分析, ポートフォリオ管理, リスク評価, 資産配分, パフォーマンスベンチマーク, 投資戦略の開発, 規制遵守, API統合, 自動化スクリプト開発, データパイプライン作成, アプリケーション開発, パフォーマンス最適化, エラーハンドリング, 定量モデリング, 統計分析, アルゴリズム取引, 戦略バックテスト, マーケットリスク分析, シグナル生成, ポートフォリオリバランス, 分散投資戦略, パフォーマンストラッキング, クライアント報告, 投資方針策定, 長期投資計画"}, "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.1.1", "initMissions": [{"name": {"zh-CN": "💡自动获取股票数据 (JavaScript)模板使用须知", "zh-TW": "💡自動獲取股票數據 (JavaScript)模板使用须知", "ja": "💡自動取得株価情報 (JavaScript) テンプレートの使用方法", "en": "💡Automated get stock info (JavaScript) template usage instructions"}, "type": "READ_TEMPLATE_README", "templateId": "automated-get-stock-info-js", "time": 5, "beforeText": {"title": {"zh-CN": "恭喜您，模板已经安装完毕", "zh-TW": "恭喜您，模板已經安裝完畢", "ja": "おめでとうございます、テンプレートのインストールが完了しました", "en": "Congratulations, the template has been installed"}, "description": {"zh-CN": "下一步请您花几分钟阅读模板的的使用教程。", "zh-TW": "下一步請您花幾分鐘閱讀模板的使用教程。", "en": "Next, please take a few minutes to read the tutorial on how to use the template.", "ja": "次に、テンプレートの使用方法についてのチュートリアルを数分間お読みください。"}}, "assignType": "DEDICATED", "forcePopup": true, "wizardGuideId": "COMMON_MY_TODO_TUTORIAL", "redirect": {"type": "MY_MISSIONS"}, "to": [{"type": "CURRENT_OPERATOR"}]}, {"name": {"en": "💡Automated get stock info initialization task: modify the API Key and tickers", "zh-CN": "💡自动获取股票数据初始化任务：修改 API 密钥 和股票代码", "zh-TW": "💡自動獲取股票數據初始化任務：修改 API 金鑰 和股票代碼", "ja": "💡自動取得株価データの初期化タスク：API キーと株式コードを変更"}, "description": {"zh-CN": "## 1. 获取 API 密钥\n要使用 BIKA 获取股票信息，您需要获取 Alpha Vantage API 密钥。这是必要的步骤，您可以在[这里免费获取](https://www.alphavantage.co/support/#api-key)。\n\n## 2. 修改 API 密钥和股票代码\n当前示例中的 API 密钥需要将其替换为您自己的 API 密钥才能查询其他股票代码。模板中默认的股票代码为 IBM，您可以根据自己的需求将其更改为您想要查询的股票代码。", "zh-TW": "## 1. 獲取 API 金鑰\n要使用 BIKA 獲取股票信息，您需要獲取 Alpha Vantage API 金鑰。這是必要的步驟，您可以在[這裡免費獲取](https://www.alphavantage.co/support/#api-key)。\n\n## 2. 修改 API 金鑰和股票代碼\n當前範例中的 API 金鑰需要將其替換為您自己的 API 金鑰才能查詢其他股票代碼。範本中預設的股票代碼為 IBM，您可以根據自己的需求將其更改為您想要查詢的股票代碼。", "en": "## 1. Obtain API Key\nTo use BIKA to retrieve stock information, you need to obtain an Alpha Vantage API key. This is a necessary step, and you can get it for free [here](https://www.alphavantage.co/support/#api-key).\n\n## 2. Modify API Key and Stock Code\nThe API key in the current example needs to be replaced with your own API key to query other stock codes. The default stock code in the template is IBM, and you can change it to the stock code you want to query according to your needs.", "ja": "## 1. APIキーを取得する\nBIKAを使用して株式情報を取得するには、Alpha Vantage APIキーを取得する必要があります。これは必要なステップであり、[こちら](https://www.alphavantage.co/support/#api-key)から無料で取得できます。\n\n## 2. APIキーと株式コードの変更\n現在の例のAPIキーを、他の株式コードをクエリするために自分のAPIキーに置き換える必要があります。テンプレートのデフォルトの株式コードはIBMであり、ニーズに応じてクエリしたい株式コードに変更できます。"}, "type": "REDIRECT_SPACE_NODE", "nodeTemplateId": "stock_automation", "buttonText": {"en": "Go to modify", "zh-CN": "前往修改", "zh-TW": "前往修改", "ja": "前往"}, "assignType": "SHARE", "to": [{"type": "ADMIN"}], "canCompleteManually": true, "wizardGuideId": "COMMON_AUTOMATION_TUTORIAL"}], "resources": [{"resourceType": "DATABASE", "templateId": "datWmWd2yaOr3L0Hb6CFwETv", "name": {"en": "Stock List", "ja": "在庫リスト", "zh-CN": "股票列表", "zh-TW": "股票列表"}, "description": {"en": "Store the list of stocks that need to be queried", "ja": "クエリ対象の株式リストを保存する", "zh-CN": "存放需要查询的股票列表", "zh-TW": "存放需要查詢的股票列表"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwIq7SKfDMzQZABkn1F1Dua", "name": {"en": "<PERSON><PERSON><PERSON>", "zh-CN": "默认视图"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldjsoQ92R7bXgt3jF17Odt7", "hidden": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldjsoQ92R7bXgt3jF17Odt7", "privilege": "TYPE_EDIT", "name": {"en": "Code", "ja": "証券コード", "zh-CN": "股票代码", "zh-TW": "股票代码"}, "primary": true}], "records": [{"templateId": "recVqwIaTIliEUu5OkXJ7e3p", "data": {"fldjsoQ92R7bXgt3jF17Odt7": "TSLA"}, "values": {"fldjsoQ92R7bXgt3jF17Odt7": "TSLA"}}, {"templateId": "recJdQaoD8GbMT43Dkiq2qXm", "data": {"fldjsoQ92R7bXgt3jF17Odt7": "AAPL"}, "values": {"fldjsoQ92R7bXgt3jF17Odt7": "AAPL"}}, {"templateId": "recihssaF61v85cUbPc3awLV", "data": {"fldjsoQ92R7bXgt3jF17Odt7": "IBM"}, "values": {"fldjsoQ92R7bXgt3jF17Odt7": "IBM"}}]}, {"resourceType": "DATABASE", "templateId": "stock_database", "name": {"en": "Stock Data", "ja": "データを保存", "zh-CN": "股票数据", "zh-TW": "股票數據"}, "description": {"en": "Stores stock information acquired on a daily basis", "ja": "毎日取得した株価情報を保存します", "zh-CN": "存储每日抓取的股票信息", "zh-TW": "存儲每日獲取的股票信息"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwmOLzrHLBuuW0nfZEX5rpT", "name": {"en": "<PERSON><PERSON><PERSON>", "zh-CN": "默认视图"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "tickers", "hidden": false}, {"templateId": "daily_open", "hidden": false}, {"templateId": "daily_close", "hidden": false}, {"templateId": "daily_high", "hidden": false}, {"templateId": "daily_low", "hidden": false}, {"templateId": "daily_volume", "hidden": false}, {"templateId": "data_acquisition_time", "hidden": false}]}, {"type": "TABLE", "templateId": "viwAG2tePPN2S7Qm5Fk8Bw4B", "name": {"en": "Group by stock code", "ja": "銘柄コードごとにグループ化", "zh-CN": "按照股票代码分组", "zh-TW": "依照股票代碼分組"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [{"fieldTemplateId": "data_acquisition_time", "asc": false}], "fields": [{"templateId": "tickers", "hidden": false}, {"templateId": "daily_open", "hidden": false}, {"templateId": "daily_close", "hidden": false}, {"templateId": "daily_high", "hidden": false}, {"templateId": "daily_low", "hidden": false}, {"templateId": "daily_volume", "hidden": false}, {"templateId": "data_acquisition_time", "hidden": false}], "groups": [{"fieldTemplateId": "tickers", "asc": true}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "tickers", "privilege": "TYPE_EDIT", "name": {"en": "Tickers", "ja": "株式コード", "zh-CN": "股票代码", "zh-TW": "股票代碼"}, "required": true, "primary": true}, {"type": "SINGLE_TEXT", "templateId": "daily_open", "privilege": "NAME_EDIT", "name": {"en": "Daily open", "ja": "毎日の始値", "zh-CN": "每日开盘价", "zh-TW": "每日開盤價"}, "required": true, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "daily_close", "privilege": "NAME_EDIT", "name": {"en": "Daily close", "ja": "毎日の終値", "zh-CN": "每日收盘价", "zh-TW": "每日收盤價"}, "required": true, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "daily_high", "privilege": "NAME_EDIT", "name": {"en": "Daily high", "ja": "毎日の高値", "zh-CN": "每日最高价", "zh-TW": "每日最高價"}, "required": true, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "daily_low", "privilege": "NAME_EDIT", "name": {"en": "Daily low", "ja": "毎日の安値", "zh-CN": "每日最低价", "zh-TW": "每日最低價"}, "required": true, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "daily_volume", "privilege": "NAME_EDIT", "name": {"en": "Daily volume", "ja": "毎日の出来高", "zh-CN": "每日成交量", "zh-TW": "每日成交量"}, "required": true, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "data_acquisition_time", "privilege": "NAME_EDIT", "name": {"en": "Data acquisition time", "ja": "データ取得時間", "zh-CN": "数据收录时间", "zh-TW": "數據收錄時間"}, "primary": false}], "records": [{"templateId": "recIz5tW53VEfzQ5tm6fewN9", "data": {"tickers": "AAPL", "daily_low": "226.640", "daily_high": "231.460", "daily_open": "229.450", "daily_close": "228.880", "daily_volume": "57345884", "data_acquisition_time": "2024-07-17"}, "values": {"tickers": "AAPL", "daily_low": "226.640", "daily_high": "231.460", "daily_open": "229.450", "daily_close": "228.880", "daily_volume": "57345884", "data_acquisition_time": "2024-07-17"}}, {"templateId": "recIR8FoCAfWr8xXXmXzWSYG", "data": {"tickers": "GOOG", "daily_low": "181.620", "daily_high": "185.230", "daily_open": "184.680", "daily_close": "182.620", "daily_volume": "17004000", "data_acquisition_time": "2024-07-17"}, "values": {"tickers": "GOOG", "daily_low": "181.620", "daily_high": "185.230", "daily_open": "184.680", "daily_close": "182.620", "daily_volume": "17004000", "data_acquisition_time": "2024-07-17"}}, {"templateId": "reclvdZjg3aYKYgjYGfGk0Zv", "data": {"tickers": "NVDA", "daily_low": "116.72", "daily_high": "121.85", "daily_open": "121.35", "daily_close": "119.25", "daily_volume": "383353000", "data_acquisition_time": "2024-07-17"}, "values": {"tickers": "NVDA", "daily_low": "116.72", "daily_high": "121.85", "daily_open": "121.35", "daily_close": "119.25", "daily_volume": "383353000", "data_acquisition_time": "2024-07-17"}}, {"templateId": "recjUmTYBdw5Z6cJPRss9DxG", "data": {"tickers": "BABA", "daily_low": "132.3500", "daily_high": "136.2450", "daily_open": "132.9400", "daily_close": "132.7500", "daily_volume": "17943385", "data_acquisition_time": "2025-03-25"}, "values": {"tickers": "BABA", "daily_low": "132.3500", "daily_high": "136.2450", "daily_open": "132.9400", "daily_close": "132.7500", "daily_volume": "17943385", "data_acquisition_time": "2025-03-25"}}, {"templateId": "recDDvx7fmWKyVztv4LJyz8N", "data": {"tickers": "TSLA", "daily_low": "217.8000", "daily_high": "250.4400", "daily_open": "245.0000", "daily_close": "221.8600", "daily_volume": "171603472", "data_acquisition_time": "2025-04-08"}, "values": {"tickers": "TSLA", "daily_low": "217.8000", "daily_high": "250.4400", "daily_open": "245.0000", "daily_close": "221.8600", "daily_volume": "171603472", "data_acquisition_time": "2025-04-08"}}, {"templateId": "rec2XiiwvASLDi6dDBuEIoyw", "data": {"tickers": "AAPL", "daily_low": "169.2101", "daily_high": "190.3350", "daily_open": "186.7000", "daily_close": "172.4200", "daily_volume": "120859491", "data_acquisition_time": "2025-04-08"}, "values": {"tickers": "AAPL", "daily_low": "169.2101", "daily_high": "190.3350", "daily_open": "186.7000", "daily_close": "172.4200", "daily_volume": "120859491", "data_acquisition_time": "2025-04-08"}}, {"templateId": "recYD47CxEBuisAlyoe1OpKz", "data": {"tickers": "IBM", "daily_low": "217.2800", "daily_high": "233.0500", "daily_open": "232.5600", "daily_close": "221.0300", "daily_volume": "6849996", "data_acquisition_time": "2025-04-08"}, "values": {"tickers": "IBM", "daily_low": "217.2800", "daily_high": "233.0500", "daily_open": "232.5600", "daily_close": "221.0300", "daily_volume": "6849996", "data_acquisition_time": "2025-04-08"}}]}, {"resourceType": "AUTOMATION", "templateId": "stock_automation", "name": {"en": "Stock Information Automation", "ja": "株価自動化", "zh-CN": "股票信息自动化", "zh-TW": "股票信息自動化"}, "description": {"en": "Get specific stock information every day at 5 PM and write it to the database", "ja": "毎日午後 5 時に特定の株価情報を取得し、テーブルに書き込みます", "zh-CN": "每天下午 5 点获取指定股票信息，写入到表格中", "zh-TW": "每天下午 5 點獲取指定股票信息，寫入到表格中"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "scheduler", "description": {"en": "Triggered every day at 5 PM", "ja": "毎日午後 5 時にトリガー", "zh-CN": "每天下午 5 点触发", "zh-TW": "每天下午 5 點觸發"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "DAY", "interval": 1}}, "timezone": "AUTO", "datetime": "2025-03-03T09:00:00.000Z"}}}], "actions": [{"templateId": "actAQj7H7TfgW9B3K13wUkco", "description": {"en": "Find the stock code", "ja": "銘柄記号を探す", "zh-CN": "查找股票代码", "zh-TW": "尋找股票代碼"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": []}, "databaseTemplateId": "datWmWd2yaOr3L0Hb6CFwETv"}}, {"templateId": "actvO87f12EGpZAAwR0gy6jt", "description": {"en": "Traverse the stock codes and write data", "ja": "銘柄記号を反復処理してデータを書き込む", "zh-CN": "遍历股票代码并写入数据", "zh-TW": "遍歷股票代碼並寫入數據"}, "actionType": "LOOP", "input": {"type": "LOOP", "actionTemplateId": "actAQj7H7TfgW9B3K13wUkco", "path": "records", "ordered": true}, "actions": [{"templateId": "actXaXIMVl1kbuTOzEwj5YYf", "description": {"en": "Get stock information from Alpha Vantage", "ja": "Alpha Vantage から株価情報を取得", "zh-CN": "从 Alpha Vantage 获取股票信息", "zh-TW": "從 Alpha Vantage 獲取股票信息"}, "actionType": "RUN_SCRIPT", "input": {"type": "SCRIPT", "language": "javascript", "script": "  (async () => {\n\n  // Replace the API key with your own\n  const APIKEY = '__YOUR_API_KEY_HERE__'\n  \n  // Replace the code with the stock code you want to query\n  const CODE = '<%= _item.cells.fldjsoQ92R7bXgt3jF17Odt7.value %>';\n  \n  // Do not modify the following code\n  const url = 'https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=' + CODE + '&apikey=' + APIKEY;\n  \n  const response = await fetch(url, {\n    headers: {\n      'User-Agent': 'request'\n    }\n  });\n\n  if (!response.ok) {\n    throw new Error(response.status);\n  }\n\n  const data = await response.json();\n  const timeSeries = data['Time Series (Daily)'];\n  \n  if (!timeSeries) {\n    throw new Error('Time Series data not found.');\n  }\n\n  const latestDate = data['Meta Data']['3. Last Refreshed'];\n  const latestData = timeSeries[latestDate];\n\n  if (!latestData) {\n    throw new Error('Latest date data not found.');\n  }\n\n  const fetchTime = new Date().toISOString();\n\n  return {\n    \"open\": latestData[\"1. open\"],\n    \"high\": latestData[\"2. high\"],\n    \"low\": latestData[\"3. low\"],\n    \"close\": latestData[\"4. close\"],\n    \"volume\": latestData[\"5. volume\"],\n    \"symbol\": CODE,\n    \"fetch_time\": fetchTime,\n    \"date\": latestDate\n  };\n})();"}}, {"templateId": "actgJ4kdmuNjDV5ET3NFJWjV", "description": {"en": "Fill stock information to the database", "ja": "株価情報をテーブルに書き込む", "zh-CN": "将股票信息写入表格", "zh-TW": "將股票信息寫入表格"}, "actionType": "CREATE_RECORD", "input": {"type": "RECORD_BODY", "fieldKeyType": "ID", "data": {"tickers": "<%= _itemActions.actXaXIMVl1kbuTOzEwj5YYf.symbol %>", "daily_low": "<%= _itemActions.actXaXIMVl1kbuTOzEwj5YYf.low %>", "daily_high": "<%= _itemActions.actXaXIMVl1kbuTOzEwj5YYf.high %>", "daily_open": "<%= _itemActions.actXaXIMVl1kbuTOzEwj5YYf.open %>", "daily_close": "<%= _itemActions.actXaXIMVl1kbuTOzEwj5YYf.close %>", "daily_volume": "<%= _itemActions.actXaXIMVl1kbuTOzEwj5YYf.volume %>", "data_acquisition_time": "<%= _itemActions.actXaXIMVl1kbuTOzEwj5YYf.date %>"}, "databaseTemplateId": "stock_database"}}]}]}], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}