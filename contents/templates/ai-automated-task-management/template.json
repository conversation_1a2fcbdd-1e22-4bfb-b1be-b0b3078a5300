{"templateId": "ai-automated-task-management", "name": {"en": "AI Automated Task Management", "ja": "週間タスクのスマートリマインダーと自動AI週間レポート", "zh-CN": "周任务智能提醒与自动 AI 周报", "zh-TW": "周任務智能提醒與自動 AI 周報"}, "description": {"en": "Helps teams efficiently manage weekly tasks. Through a series of automation tools, including task summaries, progress reminders, and personal summary reports, team members can promptly obtain task information and progress, thereby improving collaboration efficiency and work transparency. By using these automation features, teams can maintain efficient operations and ensure that each member has a clear understanding and sense of responsibility for their tasks.", "ja": "チームが週間タスクを効率的に管理するのに役立ちます。タスクの要約、進捗リマインダー、個人のサマリーレポートなど、一連の自動化ツールを通じて、チームメンバーはタスク情報と進捗状況を迅速に取得できるため、コラボレーション効率と作業透明性が向上します。これらの自動化機能を使用することで、チームは効率的な運用を維持し、各メンバーが自分のタスクについて明確な理解と責任感を持つことができます。", "zh-CN": "帮助团队高效管理周任务并智能生成 AI 报告。通过一系列自动化工具，包括：任务汇总、进度提醒和个人总结报告，让团队成员能够及时获取任务信息和进展情况，提升协作效率和工作透明度，使团队保持高效运作，确保每位成员对其任务有清晰的认识和责任感。", "zh-TW": "幫助團隊高效管理周任務。通過一系列自動化工具，包括任務匯總、進度提醒和個人總結報告，團隊成員能夠及時獲取任務信息和進展情況，從而提升協作效率和工作透明度。使用這些自動化功能，可以幫助團隊保持高效運作，確保每位成員對其任務有清晰的認識和責任感。"}, "cover": "/assets/template/template-cover-ai-automated-task-management.jpg", "author": "<PERSON> <z<PERSON><PERSON><PERSON><PERSON>@vikadata.com>", "category": ["project", "automation", "ai", "official"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.2.4", "resources": [{"resourceType": "DATABASE", "templateId": "db_tasks", "name": {"en": "Weekly Team Tasks", "ja": "週間チームタスク", "zh-CN": "团队周任务", "zh-TW": "團隊周任務"}, "description": {"en": "Used to store the team’s weekly tasks, including task name, progress, owner, start time, end time, and other information.", "ja": "チームの週間タスクを保存するために使用されます。タスク名、進捗、所有者、開始時間、終了時間などの情報が含まれます。", "zh-CN": "用于存储团队的周任务，包括任务名称、进度、负责人、开始时间、结束时间等信息。", "zh-TW": "用於存儲團隊的周任務，包括任務名稱、進度、負責人、開始時間、結束時間等信息。"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "view_all_tasks", "name": {"en": "All Tasks", "ja": "すべてのタスク", "zh-CN": "所有任务", "zh-TW": "所有任務"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [{"fieldTemplateId": "field_createdtime", "asc": false}], "fields": [{"templateId": "field_task", "hidden": false}, {"templateId": "field_progress", "hidden": false}, {"templateId": "field_owner", "hidden": false}, {"templateId": "field_starttime", "hidden": false}, {"templateId": "field_endtime", "hidden": false}, {"templateId": "field_createdtime", "hidden": false}]}, {"type": "TABLE", "templateId": "view_my_tasks", "name": {"en": "My Tasks", "ja": "私のタスク", "zh-CN": "我的任务", "zh-TW": "我的任務"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "field_owner", "fieldType": "MEMBER", "clause": {"operator": "Contains", "value": ["Self"]}}]}, "sorts": [{"fieldTemplateId": "field_createdtime", "asc": false}], "fields": [{"templateId": "field_task", "hidden": false}, {"templateId": "field_progress", "hidden": false}, {"templateId": "field_owner", "hidden": false}, {"templateId": "field_starttime", "hidden": false}, {"templateId": "field_endtime", "hidden": false}, {"templateId": "field_createdtime", "hidden": false}]}, {"type": "TABLE", "templateId": "view_this_week", "name": {"en": "This Week", "ja": "今週", "zh-CN": "本周", "zh-TW": "本週"}, "filters": {"conjunction": "Or", "conditions": [], "conds": [{"fieldTemplateId": "field_createdtime", "fieldType": "CREATED_TIME", "clause": {"operator": "Is", "value": ["ThisWeek"]}}, {"fieldTemplateId": "field_starttime", "fieldType": "DATETIME", "clause": {"operator": "Is", "value": ["ThisWeek"]}}]}, "sorts": [{"fieldTemplateId": "field_createdtime", "asc": true}], "fields": [{"templateId": "field_task", "hidden": false}, {"templateId": "field_progress", "hidden": false}, {"templateId": "field_owner", "hidden": false}, {"templateId": "field_starttime", "hidden": false}, {"templateId": "field_endtime", "hidden": false}, {"templateId": "field_createdtime", "hidden": false}]}, {"type": "TABLE", "templateId": "view_previous_week", "name": {"en": "Previous Week", "ja": "先週", "zh-CN": "上周", "zh-TW": "上週"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "field_starttime", "fieldType": "DATETIME", "clause": {"operator": "Is", "value": ["PreviousWeek"]}}]}, "sorts": [{"fieldTemplateId": "field_createdtime", "asc": true}], "fields": [{"templateId": "field_task", "hidden": false}, {"templateId": "field_progress", "hidden": false}, {"templateId": "field_owner", "hidden": false}, {"templateId": "field_starttime", "hidden": false}, {"templateId": "field_endtime", "hidden": false}, {"templateId": "field_createdtime", "hidden": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "field_task", "privilege": "TYPE_EDIT", "name": {"en": "Task", "ja": "タスク", "zh-CN": "任务", "zh-TW": "任務"}, "primary": true}, {"type": "SINGLE_SELECT", "templateId": "field_progress", "privilege": "NAME_EDIT", "name": {"en": "Progress", "ja": "進捗", "zh-CN": "进度", "zh-TW": "進度"}, "property": {"options": [{"templateId": "opt_planning", "id": "opthC5XmcFP1fBBcQRDB6VpL", "name": "Planning 🙅‍♂️", "color": "red4"}, {"templateId": "opt_in_progress", "id": "optjUmdN6R62kP7dfVplfIKk", "name": "In progress🏄‍♂️", "color": "indigo5"}, {"templateId": "opt_dilivered", "id": "optXBo831wxDFEdFtoNMOQrk", "name": "Dilivered🎖", "color": "deepPurple"}, {"templateId": "opt_completed", "id": "optF4Jn0VbYhV1GTqfp2HYQB", "name": "Completed 💁‍♂️", "color": "teal5"}, {"templateId": "opt_on_hold", "id": "optvwiC5ljI80rHxEnDyHphx", "name": "On Hold ⚡️", "color": "pink5"}]}, "primary": false}, {"type": "MEMBER", "templateId": "field_owner", "privilege": "NAME_EDIT", "name": {"en": "Owner", "ja": "責任者", "zh-CN": "负责人", "zh-TW": "負責人"}, "property": {"many": false}, "primary": false}, {"type": "DATETIME", "templateId": "field_starttime", "privilege": "NAME_EDIT", "name": {"en": "Start Time", "ja": "開始時間", "zh-CN": "开始时间", "zh-TW": "開始時間"}, "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false, "autofill": true}, "primary": false}, {"type": "DATETIME", "templateId": "field_endtime", "privilege": "NAME_EDIT", "name": {"en": "End Time", "ja": "終了時間", "zh-CN": "结束时间", "zh-TW": "結束時間"}, "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "CREATED_TIME", "templateId": "field_createdtime", "privilege": "NAME_EDIT", "name": {"en": "Created Time", "ja": "作成時間", "zh-CN": "创建时间", "zh-TW": "創建時間"}, "property": {"dateFormat": "YYYY-MM-DD", "includeTime": true}, "primary": false}], "records": [{"templateId": "rechz6FUMVEcoxmpnaAf7s7x", "data": {"field_task": "Client Follow-ups", "field_owner": ["mebYTyD52sAMtTtcdxum1ixz"], "field_endtime": "2025-02-07T16:00:00.000Z", "field_progress": ["optjUmdN6R62kP7dfVplfIKk"], "field_starttime": "2025-02-11T02:27:32.912Z", "field_createdtime": "2025-02-06T02:27:33.590Z"}, "values": {"field_task": "Client Follow-ups", "field_owner": ["<PERSON><PERSON><PERSON>"], "field_endtime": "2025-02-07", "field_progress": ["In progress🏄‍♂️"], "field_starttime": "2025-02-11", "field_createdtime": "2025-02-06 02:27"}}, {"templateId": "recYNpKRQdcpTG3t4kiyWWrJ", "data": {"field_task": "Update Website Content", "field_owner": ["mebYTyD52sAMtTtcdxum1ixz"], "field_endtime": "2025-02-27T16:00:00.000Z", "field_progress": ["optjUmdN6R62kP7dfVplfIKk"], "field_starttime": "2025-02-10T01:38:01.766Z", "field_createdtime": "2025-02-08T01:38:02.042Z"}, "values": {"field_task": "Update Website Content", "field_owner": ["<PERSON><PERSON><PERSON>"], "field_endtime": "2025-02-27", "field_progress": ["In progress🏄‍♂️"], "field_starttime": "2025-02-10", "field_createdtime": "2025-02-08 01:38"}}, {"templateId": "rec3hep6d8Mylc7DQf1EgpTa", "data": {"field_task": "Conduct Team Meeting", "field_owner": ["mebYTyD52sAMtTtcdxum1ixz"], "field_endtime": "2025-02-18T16:00:00.000Z", "field_progress": ["optF4Jn0VbYhV1GTqfp2HYQB"], "field_starttime": "2025-02-10T01:38:43.910Z", "field_createdtime": "2025-02-08T01:38:57.912Z"}, "values": {"field_task": "Conduct Team Meeting", "field_owner": ["<PERSON><PERSON><PERSON>"], "field_endtime": "2025-02-18", "field_progress": ["Completed 💁‍♂️"], "field_starttime": "2025-02-10", "field_createdtime": "2025-02-08 01:38"}}, {"templateId": "recBM4GGZC2qcOvWmbDxiesE", "data": {"field_task": "Complete Project Report", "field_owner": ["meb8Eh1Z1WLHUoIhqGlmXFWL"], "field_endtime": "2025-02-20T16:00:00.000Z", "field_progress": ["optjUmdN6R62kP7dfVplfIKk"], "field_starttime": "2025-02-19T07:24:39.505Z", "field_createdtime": "2025-02-19T07:24:39.954Z"}, "values": {"field_task": "Complete Project Report", "field_owner": ["linxiaoxin"], "field_endtime": "2025-02-20", "field_progress": ["In progress🏄‍♂️"], "field_starttime": "2025-02-19", "field_createdtime": "2025-02-19 07:24"}}, {"templateId": "recKaDCs6PgIOq9Vgzgbnxae", "data": {"field_task": "Professional Development", "field_owner": ["mebLEjuvm9b3itUY2DFh8P18"], "field_endtime": "2025-04-16T16:00:00.000Z", "field_progress": ["optjUmdN6R62kP7dfVplfIKk"], "field_starttime": "2025-04-12T16:00:00.000Z", "field_createdtime": "2025-03-11T03:10:24.532Z"}, "values": {"field_task": "Professional Development", "field_owner": ["<PERSON>"], "field_endtime": "2025-04-16", "field_progress": ["In progress🏄‍♂️"], "field_starttime": "2025-04-12", "field_createdtime": "2025-03-11 03:10"}}]}, {"resourceType": "AUTOMATION", "templateId": "auto_monday_summary", "name": {"en": "Monday - Sync Schedule", "ja": "月曜日-同期スケジュール", "zh-CN": "周一-同步进度计划", "zh-TW": "週一-同步"}, "description": {"en": "Triggered at 18:00 on Monday, find the team’s task list for this week and send it to each member.", "ja": "月曜日の18:00にトリガーされ、今週のチームのタスクリストを検索し、各メンバーに送信します。", "zh-CN": "周一晚上18:00触发，查找团队的本周任务列表，并将团队任务清单发送给每一位成员。", "zh-TW": "周一晚上18:00觸發，查找團隊的本週任務列表，並將團隊任務清單發送給每一位成員。"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trigger_monday_18", "description": {"en": "Triggered at 18:00 on Monday", "ja": "月曜日の18:00にトリガー", "zh-CN": "周一晚上18:00触发", "zh-TW": "週一晚上18:00觸發"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["MON"]}}, "timezone": "AUTO", "datetime": "2024-11-06T10:00:01.221Z"}}}], "actions": [{"templateId": "action_find_this_week_tasks", "description": {"en": "Find the team’s task list for this week", "ja": "今週のチームのタスクリストを検索", "zh-CN": "查找团队的本周任务列表", "zh-TW": "查找團隊的本週任務列表"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "view_this_week", "databaseTemplateId": "db_tasks"}}, {"templateId": "action_monday_send_task_list", "description": {"en": "Send the team’s task list to each member", "ja": "チームのタスクリストを各メンバーに送信", "zh-CN": "给团队成员发送任务清单", "zh-TW": "給團隊成員發送任務清單"}, "actionType": "SEND_REPORT", "input": {"to": [{"type": "ALL_MEMBERS"}], "markdown": "## 📝 Team Weekly Task List\n\nThe table below contains all the tasks our team needs to handle this week. Please review it, and if there are any errors, make the necessary corrections promptly.\n\n<%= _renderRecordsAsGrid(_actions.action_find_this_week_tasks.records, ['field_task','field_progress','field_owner','field_starttime','field_endtime']) %>", "subject": "The team's task list for this week", "type": "MARKDOWN"}}]}, {"resourceType": "AUTOMATION", "templateId": "auto_wednesday_reminder", "name": {"en": "Wednesday - Progress Reminder", "ja": "水曜日-進捗リマインダー", "zh-CN": "周三-进度提醒", "zh-TW": "週三-進度提醒"}, "description": {"en": "Triggered at 18:00 on Wednesday, find the team’s member list, iterate through the list, find all the tasks for the member this week, and send a personal report to the member.", "ja": "水曜日の18:00にトリガーされ、チームのメンバーリストを検索し、リストを反復処理し、そのメンバーが今週行うすべてのタスクを検索し、そのメンバーに個人レポートを送信します。", "zh-CN": "周三傍晚18:00触发，查找团队的成员名单，遍历成员名单，查找该成员在本周的所有任务，并给成员发送个人专属报告。", "zh-TW": "週三傍晚18:00觸發，查找團隊的成員名單，遍歷成員名單，查找該成員在本週的所有任務，並給成員發送個人專屬報告。"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trigger_wednesday_18", "description": {"en": "Triggered at 18:00 on Wednesday", "ja": "水曜日の18:00にトリガー", "zh-CN": "周三傍晚18:00触发", "zh-TW": "週三傍晚18:00觸發"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["WED"]}}, "timezone": "AUTO", "datetime": "2024-11-06T10:00:06.795Z"}}}], "actions": [{"templateId": "action_wendesday_find_members", "description": {"en": "Find the team’s member list", "ja": "チームのメンバーリストを検索", "zh-CN": "查找团队的成员名单", "zh-TW": "查找團隊的成員名單"}, "actionType": "FIND_MEMBERS", "input": {"type": "MEMBER", "by": [{"type": "ALL_MEMBERS"}]}}, {"templateId": "action_wendesday_loop_members", "description": {"en": "Iterate through each member’s information", "ja": "各メンバーの情報を反復処理", "zh-CN": "循环处理每一位成员的信息", "zh-TW": "循環處理每一位成員的信息"}, "actionType": "LOOP", "input": {"type": "PREV_ACTION", "actionTemplateId": "action_wendesday_find_members", "path": "members"}, "actions": [{"templateId": "action_wendesday_find_member_task", "description": {"en": "Find all the tasks for the member this week", "ja": "そのメンバーが今週行うすべてのタスクを検索", "zh-CN": "找出该成员本周的所有任务", "zh-TW": "找出該成員本週的所有任務"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "field_starttime", "fieldType": "DATETIME", "clause": {"operator": "Is", "value": ["ThisWeek"]}}, {"fieldTemplateId": "field_owner", "fieldType": "MEMBER", "clause": {"operator": "Is", "value": ["<%= _item.id %>"]}}]}, "databaseTemplateId": "db_tasks"}}, {"templateId": "action_wendesday_send_report", "description": {"en": "Send a personal report to the member", "ja": "そのメンバーに個人レポートを送信", "zh-CN": "给成员发送个人定制报告", "zh-TW": "給成員發送個人定制報告"}, "actionType": "SEND_REPORT", "input": {"to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= _item.id %>"]}], "markdown": "## 💡Weekly Task Progress Report\n\nHi, <%= _item.name %>\n\nThe table below lists all the tasks you need to handle this week, along with their progress status. Please manage your time to avoid missing deadlines.\n\n<%= _renderRecordsAsGrid(_itemActions.action_wendesday_find_member_task.records, ['field_task','field_progress','field_starttime','field_endtime']) %>\n\n<br>", "subject": "Weekly Task Progress Report", "type": "MARKDOWN"}}]}]}, {"resourceType": "AUTOMATION", "templateId": "auto_friday_report", "name": {"en": "Friday - Weekly Summary Report", "ja": "金曜日-週間サマリーレポート", "zh-CN": "周五-周总结报告", "zh-TW": "週五-週總結報告"}, "description": {"en": "Triggered at 18:00 on Friday, find the team’s member list, iterate through the list, find all the tasks for the member this week, and send a personal report to the member.", "ja": "金曜日の18:00にトリガーされ、チームのメンバーリストを検索し、リストを反復処理し、そのメンバーが今週行うすべてのタスクを検索し、そのメンバーに個人レポートを送信します。", "zh-CN": "周五傍晚18:00触发，查找团队的成员名单，遍历成员名单，查找该成员在本周的所有任务，并给成员发送个人定制报告。", "zh-TW": "週五傍晚18:00觸發，查找團隊的成員名單，遍歷成員名單，查找該成員在本週的所有任務，並給成員發送個人定制報告。"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trigger_friday_18", "description": {"en": "Triggered at 18:00 on Friday", "ja": "金曜日の18:00にトリガー", "zh-CN": "周五傍晚18:00触发", "zh-TW": "週五傍晚18:00觸發"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["FRI"]}}, "timezone": "AUTO", "datetime": "2025-02-07T10:00:08.561Z"}}}], "actions": [{"templateId": "actUXrcRdOdHp7OWSOnYxpqB", "description": {"en": "Find a list of team members", "ja": "メンバーのリストを探します", "zh-CN": "查找团队的成员名单", "zh-TW": "查找團隊的成員名單"}, "actionType": "FIND_MEMBERS", "input": {"type": "MEMBER", "by": [{"type": "SPECIFY_UNITS", "unitIds": []}]}}, {"templateId": "action_friday_loop_members", "description": {"en": "Iterate through each member’s information", "ja": "各メンバーの情報を反復処理", "zh-CN": "循环处理每一位成员的信息", "zh-TW": "循環處理每一位成員的信息"}, "actionType": "LOOP", "input": {"type": "PREV_ACTION", "actionTemplateId": "actUXrcRdOdHp7OWSOnYxpqB", "path": "members"}, "actions": [{"templateId": "action_friday_find_member_task", "description": {"en": "Find all the tasks for the member this week", "ja": "そのメンバーが今週行うすべてのタスクを検索", "zh-CN": "找出该成员本周的所有任务", "zh-TW": "找出該成員本週的所有任務"}, "actionType": "FIND_RECORDS", "input": {"interruptIfNoRecord": false, "type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "field_owner", "fieldType": "MEMBER", "clause": {"operator": "Is", "value": ["<%= _item.id %>"]}}, {"fieldTemplateId": "field_starttime", "fieldType": "DATETIME", "clause": {"operator": "Is", "value": ["ThisWeek"]}}]}, "databaseTemplateId": "db_tasks"}}, {"templateId": "action-friday-ai-summary", "description": {"en": "Generate a summary report of the member’s tasks for this week", "ja": "そのメンバーの今週のタスクのサマリーレポートを生成", "zh-CN": "生成成员本周任务总结报告", "zh-TW": "生成成員本週任務總結報告"}, "actionType": "OPENAI_GENERATE_TEXT", "input": {"urlType": "URL", "type": "OPENAI_GENERATE_TEXT", "integrationId": "", "prompt": "Below is the weekly iteration task list for member <%= _item.name %>. Please generate a concise summary of approximately 200 words, highlighting the key progress of tasks completed this week. Additionally, provide a count of tasks marked as “Completed” and “Not Completed.” Use clear and direct language for better readability.            \n            \n<%= _renderRecordsAsGrid(_itemActions.action_friday_find_member_task.records, [\"field_task\",\"field_progress\",\"field_starttime\",\"field_endtime\"]) %>", "baseUrl": "https://api.moonshot.cn/v1", "apiKey": "", "model": "moonshot-v1-128k"}}, {"templateId": "action_friday_send_report", "description": {"en": "Send a personal report to the member", "ja": "そのメンバーに個人レポートを送信", "zh-CN": "给成员发送个人定制报告", "zh-TW": "給成員發送個人定制報告"}, "actionType": "SEND_REPORT", "input": {"to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= _item.id %>"]}], "markdown": "# ⭐Summary Report of Tasks⭐                                          \n                                          \nHi <%= _item.name %>. Here’s a quick summary of your tasks and accomplishments from this past week. It highlights your progress, completed items, and any outstanding tasks that may need your attention. Please review the details below to help plan for the upcoming week.                                          \n                                          \n## 🎯AI Summary                                 \n<%= _itemActions['action-friday-ai-summary'].body.choices[0].message.content %>      \n      \n## 📑Task List      \n<%= _renderRecordsAsGrid(_itemActions.action_friday_find_member_task.records, ['field_task','field_progress','field_starttime','field_endtime']) %>", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "# ⭐Summary Report of Tasks⭐                                        ", "type": "text"}, {"type": "hardBreak"}, {"text": "                                        ", "type": "text"}, {"type": "hardBreak"}, {"text": "Hi ", "type": "text"}, {"type": "variable", "attrs": {"ids": "_item,name", "tips": "", "names": "当前项,name"}}, {"text": ". Here’s a quick summary of your tasks and accomplishments from this past week. It highlights your progress, completed items, and any outstanding tasks that may need your attention. Please review the details below to help plan for the upcoming week.                                        ", "type": "text"}, {"type": "hardBreak"}, {"text": "                                        ", "type": "text"}, {"type": "hardBreak"}, {"text": "## 🎯AI Summary                               ", "type": "text"}, {"type": "hardBreak"}, {"type": "variable", "attrs": {"ids": "_itemActions,action-friday-ai-summary,body,choices,[0],message,content", "tips": "", "names": "当前执行器,OpenAI - 生成文本,body,choices,#1,message,content"}}, {"text": "    ", "type": "text"}, {"type": "hardBreak"}, {"text": "    ", "type": "text"}, {"type": "hardBreak"}, {"text": "## 📑Task List    ", "type": "text"}, {"type": "hardBreak"}, {"type": "variable", "attrs": {"ids": "_renderRecordsAsGrid(_itemActions.action_friday_find_member_task.records, ['field_task','field_progress','field_starttime','field_endtime'])", "tips": "选中的字段: 任务, 进度, 开始时间, 结束时间", "names": ["当前执行器", "查找记录", "网格列表"]}}]}]}, "subject": "Hi, This Week's Task Summary is Here", "type": "MARKDOWN"}}]}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}