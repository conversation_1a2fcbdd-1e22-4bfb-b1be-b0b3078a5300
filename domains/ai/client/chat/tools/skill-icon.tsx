import type { ToolInvocation } from '@ai-sdk/ui-utils';
import React from 'react';
import { useTRPCQuery } from '@bika/api-caller/context';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import { AvatarImg, AvatarSize } from '@bika/ui/components/avatar/index';
import { AISkillsetClientRegistry } from '../../../../ai-skillset/client-registry';

interface Props {
  skillsets: SkillsetSelectDTO[];
  toolInvocation: ToolInvocation;
}

/**
 *  通过 Skillsets，来真正获得技能图标
 */
function SkillIconBase(props: Props): React.ReactNode {
  const trpcQuery = useTRPCQuery();
  const { data: skillsetsVOs, isLoading: isLoadingSkillsetsVOs } = trpcQuery.ai.getSkillsets.useQuery(props.skillsets);

  // 从 skillsetsVOs 中获取对应的 skillsetVO, key 和 kind 相同匹配
  const skillsetVO = React.useMemo(() => {
    if (!skillsetsVOs || isLoadingSkillsetsVOs) return undefined;
    const skillset = skillsetsVOs.find((_skillset) => _skillset.key === props.toolInvocation.toolName);
    if (skillset) {
      return skillset;
    }
    return skillsetsVOs.find((_skillset) => _skillset.skills?.some((sk) => sk.key === props.toolInvocation.toolName));
  }, [skillsetsVOs, isLoadingSkillsetsVOs, props.toolInvocation.toolName]);

  // 特殊 UI 配置
  const skillsetUIMap = React.useMemo(
    () => AISkillsetClientRegistry.getManySkillsetUI(props.skillsets),
    [props.skillsets],
  );
  let nodeIcon = undefined;
  const skillUICfg = skillsetUIMap ? skillsetUIMap[props.toolInvocation.toolName] : undefined;

  if (skillUICfg.log) {
    // 从服务端 VO 拿到图标，转换为 INodeIconValue
    nodeIcon = {
      kind: 'avatar' as const,
      avatar: skillsetVO.logo,
      name: skillsetVO.name,
    };
  }
}
// 客户端自定义置了 skill icon (不是 skillsets 哦)，覆盖这个图标
if (skillUICfg?.customIcon) {
  // return {
  //   kind: 'avatar' as const,
  //   avatar: skillUICfg.customIcon,
  // };

  nodeIcon = {
    // kind: 'avatar' as const,
    // avatar: skillsetVO.logo,
    // name: skillsetVO.name,
    kind: 'avatar' as const,
    avatar: skillUICfg.customIcon,
  };
}

  // if (skillsetVO?.logo) {
  //   // 从服务端 VO 拿到图标，直接返回
  //   return <AvatarImg avatar={skillsetVO.logo} name={skillsetVO.name} customSize={AvatarSize.Size32} />;
  // }
  // // 客户端自定义置了 skill icon (不是 skillsets 哦)，覆盖这个图标
  // if (skillUICfg?.customIcon) {
  //   return <AvatarImg avatar={skillUICfg.customIcon} customSize={AvatarSize.Size32} />;
  // }
  // return null;
  return (
    <NodeIcon value={nodeIcon} size={AvatarSize.Size32} />
  )
}

export const SkillIcon = React.memo(SkillIconBase);
