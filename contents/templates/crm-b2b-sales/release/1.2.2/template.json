{"templateId": "crm-b2b-sales", "name": {"en": "B2B AI CRM", "ja": "B2B顧客管理と販売追跡", "zh-CN": "B2B AI 客户管理", "zh-TW": "B2B客戶管理與銷售追蹤"}, "description": {"en": "AI automation to manage your clients, prompt you or your sales team to write visit records every week, suitable for B2B sales teams targeting enterprise organizations.", "ja": "AI は顧客を自動的に管理し、あなたまたは営業チームに毎週訪問記録を書き留めるよう促します。これは、企業組織と対峙する B2B 営業チームに適しています。", "zh-CN": "AI自动化管理客户，提示您或您的销售团队每周填写拜访记录，适合企业组织的B2B销售团队。", "zh-TW": "AI自動化管理客戶，提示您或您的銷售團隊每周填寫拜訪記錄，適合企業組織的B2B銷售團隊"}, "keywords": {"en": "B2B CRM, customer management, sales tracking, enterprise sales, client management, business-to-business", "zh-CN": "B2B客户管理, 销售追踪, 企业销售, 客户管理, 工商业务", "zh-TW": "B2B客戶管理, 銷售追蹤, 企業銷售, 客戶管理, 工商業務", "ja": "B2B顧客管理, 販売追跡, 企業営業, クライアント管理, ビジネス対ビジネス"}, "personas": {"en": "Manufacturing Sales Manager, Finance Marketing Director, Healthcare Business Development Manager, IT Sales Manager, Retail Regional Sales Manager, Real Estate Sales Director, Education Marketing Operations Specialist, Transportation & Logistics Sales Manager, Government Procurement Officer, Energy Business Development Specialist, Hospitality Marketing Manager, Construction Marketing Manager, Agriculture Sales Manager, Food & Beverage Sales Specialist, Chemical Industry Sales Director, Media & Entertainment Sales Manager, Automotive Market Development Specialist, Telecommunications Marketing Director, Medical Devices Sales Manager, Advertising & Marketing Sales Manager, SaaS Sales, Telesales, SDR, Sales Manager, Enterprise Executive, SMB Owner, Startup Founder, Sales Team Manager, Marketing Manager, Business Development Manager, Customer Relationship Management Specialist, Marketing Analyst", "zh-CN": "制造业销售经理,金融业营销总监,医疗行业业务拓展经理,IT行业销售主管,零售业区域销售经理,房地产销售总监,教育行业市场运营专员,运输物流销售主管,政府采购人员,能源行业业务拓展专员,酒店行业市场部经理,建筑业营销主管,农业销售经理,餐饮行业销售专员,化工行业销售总监,文化传媒销售经理,汽车行业市场开发专员,通信行业营销总监,医疗器械销售主管,广告营销销售经理,SaaS软件销售,电话销售,SDR,销售经理,大型企业高管,中小企业老板,初创公司创始人,销售团队主管,营销主管,业务拓展经理,客户关系管理专员,营销分析师", "zh-TW": "製造業銷售經理,金融業行銷總監,醫療行業業務拓展經理,IT行業銷售主管,零售業區域銷售經理,房地產銷售總監,教育行業市場運營專員,運輸物流銷售主管,政府採購人員,能源行業業務拓展專員,酒店行業市場部經理,建築業行銷主管,農業銷售經理,餐飲行業銷售專員,化工行業銷售總監,文化傳媒銷售經理,汽車行業市場開發專員,通信行業行銷總監,醫療器械銷售主管,廣告行銷銷售經理,SaaS軟體銷售,電話銷售,SDR,銷售經理,大型企業高管,中小企業老闆,初創公司創始人,銷售團隊主管,行銷主管,業務拓展經理,客戶關係管理專員,行銷分析師", "ja": "製造業セールスマネージャー,金融業マーケティングディレクター,ヘルスケア事業開発マネージャー,ITセールスマネージャー,小売地域セールスマネージャー,不動産販売ディレクター,教育マーケティング オペレーションスペシャリスト,輸送物流セールスマネージャー,政府調達オフィサー,エネルギー事業開発スペシャリスト,ホテルマーケティングマネージャー,建設マーケティングマネージャー,農業セールスマネージャー,飲食セールススペシャリスト,化学工業セールスディレクター,メディア&エンターテイメントセールスマネージャー,自動車市場開発スペシャリスト,通信マーケティングディレクター,医療機器セールスマネージャー,広告&マーケティングセールスマネージャー,SaaSセールス,テレセールス,SDR,セールスマネージャー,企業エグゼクティブ,中小企業オーナー,スタートアップ創業者,セールスチームマネージャー,マーケティングマネージャー,事業開発マネージャー,顧客関係管理スペシャリスト,マーケティング分析師"}, "useCases": {"zh-CN": "制造业销售经理-管理销售流程, 预测销售, 跟踪客户互动, 优化销售策略,金融业营销总监-构建客户关系网络, 规划营销活动, 分析客户画像, 个性化营销,医疗行业业务拓展经理-开发新客户, 跟踪销售线索, 管理合作伙伴关系, 提升销售转化率,IT行业销售主管-管理销售团队, 制定销售目标, 监控销售指标, 优化销售流程,零售业区域销售经理-协调门店销售, 管理客户关系, 分析客户消费习惯, 制定促销策略,房地产销售总监-管理销售团队, 预测销售业绩, 开发营销渠道, 提升客户满意度,教育行业市场运营专员-开展市场调研, 规划营销活动, 管理学生/家长信息, 优化招生渠道,运输物流销售主管-开发新客户, 维护客户关系, 跟踪销售机会, 提升服务质量,政府采购人员-管理供应商信息, 跟踪采购需求, 优化采购流程, 提高采购效率,能源行业业务拓展专员-开发潜在客户, 管理销售线索, 跟踪销售进度, 提高成交率,酒店行业市场部经理-制定营销策略, 管理客户数据, 优化预订渠道, 提升客户忠诚度,建筑业营销主管-管理投标信息, 跟踪项目进度, 维护供应商关系, 提高中标率,农业销售经理-管理经销商/代理商关系, 分析市场需求, 优化产品组合, 提高销量,餐饮行业销售专员-开拓新客户, 管理订单信息, 分析客户消费习惯, 提升客户满意度,化工行业销售总监-制定销售目标, 管理销售团队, 优化销售渠道, 提高销售业绩", "en": "Manufacturing Sales Manager - manage the sales process, forecast sales, track customer interactions, optimize sales strategies, Financial Marketing Director - build customer relationship network, plan marketing activities, analyze customer profiles, implement personalized marketing, Medical Industry Business Development Manager - develop new customers, track sales leads, manage partner relationships, improve sales conversion rate, IT Sales Supervisor - manage sales team, set sales targets, monitor sales metrics, optimize sales processes, Retail Regional Sales Manager - coordinate store sales, manage customer relationships, analyze customer buying habits, develop promotional strategies, Real Estate Sales Director - manage sales team, forecast sales performance, develop marketing channels, improve customer satisfaction, Education Market Operations Specialist - conduct market research, plan marketing activities, manage student/parent information, optimize enrollment channels, Transportation & Logistics Sales Supervisor - develop new customers, maintain customer relationships, track sales opportunities, improve service quality, Government Procurement Specialist - manage supplier information, track procurement needs, optimize procurement processes, improve procurement efficiency, Energy Industry Business Development Specialist - develop potential customers, manage sales leads, track sales progress, increase conversion rate, Hotel Marketing Manager - develop marketing strategies, manage customer data, optimize booking channels, improve customer loyalty, Construction Marketing Supervisor - manage bidding information, track project progress, maintain supplier relationships, improve win rate, Agricultural Sales Manager - manage distributor/agent relationships, analyze market demand, optimize product portfolio, increase sales volume, Food & Beverage Sales Specialist - develop new customers, manage order information, analyze customer buying habits, improve customer satisfaction, Chemical Industry Sales Director - set sales targets, manage sales team, optimize sales channels, improve sales performance.", "zh-TW": "製造業銷售經理 - 管理銷售流程, 預測銷售, 追蹤客戶互動, 優化銷售策略, 財務行銷總監 - 建立客戶關係網路, 規劃行銷活動, 分析客戶概況, 實施個人化行銷, 醫療行業業務發展經理 - 開發新客戶, 追蹤銷售線索, 管理合作夥伴關係, 提高銷售轉化率, IT銷售主管 - 管理銷售團隊, 設定銷售目標, 監控銷售指標, 優化銷售流程, 零售區域銷售經理 - 協調門店銷售, 管理客戶關係, 分析客戶購買習慣, 制定促銷策略, 房地產銷售總監 - 管理銷售團隊, 預測銷售表現, 開發行銷渠道, 提高客戶滿意度, 教育市場營運專員 - 進行市場調研, 規劃行銷活動, 管理學生/家長資訊, 優化招生管道, 運輸物流銷售主管 - 開發新客戶, 維護客戶關係, 跟蹤銷售機會, 提高服務品質, 政府採購專員 - 管理供應商資訊, 跟蹤採購需求, 優化採購流程, 提高採購效率, 能源行業業務發展專員 - 開發潛在客戶, 管理銷售線索, 跟蹤銷售進度, 提高轉化率, 酒店行銷經理 - 制定行銷策略, 管理客戶數據, 優化訂房管道, 提高客戶忠誠度, 建築行銷主管 - 管理投標資訊, 跟蹤項目進度, 維護供應商關係, 提高中標率, 農業銷售經理 - 管理經銷商/代理商關係, 分析市場需求, 優化產品組合, 提高銷量, 食品飲料銷售專員 - 開發新客戶, 管理訂單資訊, 分析客戶購買習慣, 提高客戶滿意度, 化工行業銷售總監 - 設定銷售目標, 管理銷售團隊, 優化銷售渠道, 提高銷售績效", "ja": "製造業販売マネージャー - 販売プロセスを管理, 売上予測, 顧客対応を追跡, 販売戦略を最適化, 金融マーケティングディレクター - 顧客関係ネットワークを構築, マーケティング活動を計画, 顧客プロファイルを分析, パーソナライズドマーケティングを実施, 医療業界ビジネス開発マネージャー - 新規顧客を開発, 販売リードを追跡, パートナー関係を管理, 販売コンバージョン率を改善, ITセールスリーダー - 販売チームを管理, 販売目標を設定, 販売メトリクスを監視, 販売プロセスを最適化, 小売地域販売マネージャー - 店舗販売を調整, 顧客関係を管理, 顧客の購買習慣を分析, プロモーション戦略を開発, 不動産販売ディレクター - 販売チームを管理, 販売実績を予測, マーケティングチャネルを開発, 顧客満足度を向上, 教育市場オペレーションスペシャリスト - 市場調査を実施, マーケティング活動を計画, 学生/保護者情報を管理, エンロールチャネルを最適化, 運輸物流販売リーダー - 新規顧客を開発, 顧客関係を維持, 販売機会を追跡, サービス品質を改善, 政府調達スペシャリスト - サプライヤー情報を管理, 調達ニーズを追跡, 調達プロセスを最適化, 調達効率を向上, エネルギー業界ビジネス開発スペシャリスト - 潜在顧客を開発, 販売リードを管理, 販売進捗を追跡, コンバージョン率を向上, ホテルマーケティングマネージャー - マーケティング戦略を開発, 顧客データを管理, 予約チャネルを最適化, 顧客ロイヤルティを向上, 建設マーケティングスーパーバイザー - 入札情報を管理, プロジェクトの進捗を追跡, サプライヤー関係を維持, 受注率を向上, 農業販売マネージャー - 販売代理店/代理店の関係を管理, 市場需要を分析, 製品ポートフォリオを最適化, 売上高を増加, 飲料食品販売スペシャリスト - 新規顧客を開発, 注文情報を管理, 顧客の購買習慣を分析, 顧客満足度を向上, 化学業界販売ディレクター - 販売目標を設定, 販売チームを管理, 販売チャネルを最適化, 販売成績を向上"}, "cover": "/assets/template/template-cover-crm-b2b-sales.png", "author": "<PERSON> <z<PERSON><PERSON><PERSON><PERSON>@vikadata.com>", "category": ["official", "sales"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.2.2", "initMissions": [{"type": "INVITE_MEMBER", "name": {"zh-CN": "任务 - 邀请销售团队成员加入空间站", "en": "Mission - Invite sales team members to join the workspace", "zh-TW": "任務 - 邀請銷售團隊成員加入空間站", "ja": "ミッション - 販売チームメンバーをワークスペースに招待"}, "description": {"zh-CN": "邀请您的销售团队成员加入空间站，开始使用B2B AI CRM模板。", "en": "Invite your sales team members to join the workspace and start using the B2B AI CRM template.", "zh-TW": "邀請您的銷售團隊成員加入空間站，開始使用B2B AI CRM模板。", "ja": "販売チームメンバーをワークスペースに招待し、B2B AI CRMテンプレートの使用を開始します。"}, "to": [{"type": "ADMIN"}]}, {"name": {"en": "🚀 Template Guide", "zh-CN": "🚀 模板指南", "zh-TW": "🚀 模板指南", "ja": "🚀 テンプレートガイド"}, "type": "READ_TEMPLATE_README", "templateId": "crm-b2b-sales", "time": 10, "beforeText": {"title": {"zh-CN": "恭喜您，模板已经安装完毕", "zh-TW": "恭喜您，模板已經安裝完畢", "ja": "おめでとうございます, テンプレートのインストールが完了しました", "en": "Congratulations, the template has been installed"}, "description": {"zh-CN": "下一步请您花几分钟阅读模板的使用指南。", "zh-TW": "下一步請您花幾分鐘閱讀模板的使用指南。", "en": "Next, please take a few minutes to read the guide on how to use the template.", "ja": "次に, テンプレートの使用方法についてのチュートリアルを数分間お読みください。"}}, "assignType": "DEDICATED", "forcePopup": true, "wizardGuideId": "COMMON_AUTOMATION_TUTORIAL", "redirect": {"type": "MY_MISSIONS"}, "to": [{"type": "CURRENT_OPERATOR"}]}], "resources": [{"resourceType": "DATABASE", "templateId": "database_people", "name": {"en": "Contacts", "ja": "コンタクト", "zh-CN": "联系人", "zh-TW": "聯絡人"}, "description": {"en": "The communication person in the customer enterprise. This database records the contact information, position, department, etc. of the contact person.", "ja": "連絡先は、顧客企業のコミュニケーション担当者です。このデータベースには、連絡先の連絡先情報、役職、部署などが記録されています。", "zh-CN": "联系人是客户企业里的对接沟通人，本库收录联系人的联系方式、职位、部门等信息。", "zh-TW": "聯絡人是客戶企業裡的對接溝通人，本庫收錄聯絡人的聯絡方式、職位、部門等信息。"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwRckYcKvLOhucMR1xIAN2f", "name": {"en": "ALL", "ja": "すべて", "zh-CN": "全部", "zh-TW": "全部"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [{"fieldTemplateId": "field_organization", "asc": false}], "fields": [{"templateId": "field_name", "hidden": false}, {"templateId": "field_organization", "hidden": false}, {"templateId": "field_email", "hidden": false}, {"templateId": "field_phone", "hidden": false}, {"templateId": "field_department", "hidden": false, "width": 195}, {"templateId": "field_position", "hidden": false}, {"templateId": "field_created_time", "hidden": false}, {"templateId": "fldwz9wwbHh9Cje4KgzWvDlT", "hidden": false, "width": 163}, {"templateId": "fldsgj6PzZeFAtwomvmLXz9Q", "hidden": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "field_name", "privilege": "TYPE_EDIT", "name": {"en": "Name", "ja": "名前", "zh-CN": "姓名", "zh-TW": "姓名"}, "primary": true}, {"type": "EMAIL", "templateId": "field_email", "privilege": "NAME_EDIT", "name": {"en": "Email", "ja": "メールアドレス", "zh-CN": "邮箱", "zh-TW": "郵箱"}, "primary": false}, {"type": "PHONE", "templateId": "field_phone", "privilege": "NAME_EDIT", "name": {"en": "Phone", "ja": "電話番号", "zh-CN": "电话号码", "zh-TW": "電話號碼"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "field_department", "privilege": "NAME_EDIT", "name": {"en": "Department", "ja": "部署", "zh-CN": "部门", "zh-TW": "部門"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "field_position", "privilege": "NAME_EDIT", "name": {"en": "Position", "ja": "ポジション", "zh-CN": "职位", "zh-TW": "職位"}, "primary": false}, {"type": "LINK", "templateId": "field_organization", "privilege": "NAME_EDIT", "name": {"en": "Companies", "ja": "会社", "zh-CN": "公司", "zh-TW": "公司"}, "description": {"en": "The companies to which the contact belongs.", "ja": "連絡先が所属する会社。", "zh-CN": "联系人所属的公司。", "zh-TW": "聯絡人所屬的公司。"}, "property": {"foreignDatabaseTemplateId": "database_organization", "brotherFieldTemplateId": "fldZz44TfQajMtSyZnA9veaQ"}, "primary": false}, {"type": "CREATED_TIME", "templateId": "field_created_time", "privilege": "NAME_EDIT", "name": {"en": "Created Time", "ja": "作成日時", "zh-CN": "创建时间", "zh-TW": "創建時間"}, "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}, "primary": false}, {"type": "LINK", "templateId": "fldwz9wwbHh9Cje4KgzWvDlT", "privilege": "FULL_EDIT", "name": {"en": "Visit Records", "ja": "訪問記録です", "zh-CN": "拜访记录", "zh-TW": "拜訪記錄"}, "property": {"foreignDatabaseTemplateId": "database_visit", "brotherFieldTemplateId": "database_visit:field_people"}, "primary": false}, {"type": "LOOKUP", "templateId": "fldsgj6PzZeFAtwomvmLXz9Q", "privilege": "FULL_EDIT", "name": {"en": "Sales manager", "ja": "営業担当者です", "zh-CN": "销售负责人", "zh-TW": "銷售負責人"}, "required": false, "property": {"relatedLinkFieldTemplateId": "database_people:field_organization", "lookupTargetFieldTemplateId": "fldX932G9962cAmJdaESkZNs", "lookupTargetFieldType": "MEMBER", "dataType": "STRING", "lookUpLimit": "ALL", "rollUpType": "VALUES"}, "primary": false}], "records": [{"templateId": "recLG5iaGGJYsD2Xs1hfQGW4", "data": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "12345678", "field_position": "Sales manager", "field_department": "Sales DIVISION", "field_created_time": "2025-02-17T08:57:53.149Z", "field_organization": ["rec1f0iBJwe7t8m4yL5QWHlz"], "fldwz9wwbHh9Cje4KgzWvDlT": ["recZvkUirTHSOgvZICqf1BxL"]}, "values": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "12345678", "field_position": "Sales manager", "field_department": "Sales DIVISION", "field_created_time": "2025-01-14T06:15:44.437Z", "field_organization": ["Bika.ai "], "fldsgj6PzZeFAtwomvmLXz9Q": ["<PERSON><PERSON><PERSON>"], "fldwz9wwbHh9Cje4KgzWvDlT": ["2025-02-18 <PERSON><PERSON><PERSON>a<PERSON> -<PERSON>"]}}, {"templateId": "reca04196pcxjKsKw2WRoXYj", "data": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "12345678", "field_position": "Personnel manager", "field_department": "Ministry of Personnel", "field_created_time": "2025-02-17T08:57:53.150Z", "field_organization": ["recKSma4htDBKZYiRxU8hq8S"], "fldwz9wwbHh9Cje4KgzWvDlT": ["recFTj2OcaBdgjFotQ4Xb5vS"]}, "values": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "12345678", "field_position": "Personnel manager", "field_department": "Ministry of Personnel", "field_created_time": "2025-01-14T06:15:44.437Z", "field_organization": ["Nana Tech"], "fldsgj6PzZeFAtwomvmLXz9Q": ["<PERSON><PERSON><PERSON>"], "fldwz9wwbHh9Cje4KgzWvDlT": ["2025-02-18 <PERSON>-<PERSON>"]}}, {"templateId": "recHrsGoTAjcKNRr0DxmQh0H", "data": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "12345678", "field_position": "CEO", "field_department": "Board of directors", "field_created_time": "2025-02-17T08:57:53.150Z", "field_organization": ["recsE1NnIkc1awJC2hHxJ0WH"], "fldwz9wwbHh9Cje4KgzWvDlT": ["rechb4ESLCvTsxIC5BbtAyOo"]}, "values": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "12345678", "field_position": "CEO", "field_department": "Board of directors", "field_created_time": "2025-01-14T06:15:44.437Z", "field_organization": ["Alaxi Travel"], "fldsgj6PzZeFAtwomvmLXz9Q": ["<PERSON><PERSON><PERSON>"], "fldwz9wwbHh9Cje4KgzWvDlT": ["2025-02-18 Alaxi Travel-Kobe Bryant"]}}, {"templateId": "recWoEvMpuVESNClhjheBsQU", "data": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "12345678", "field_position": "Chief financial officer", "field_department": "Financial department", "field_created_time": "2025-02-17T08:57:53.150Z", "field_organization": ["rec6cRzajLAMMl11FG9231RW"], "fldwz9wwbHh9Cje4KgzWvDlT": ["recvvQK57RYN7nig0krb8Bx1"]}, "values": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "12345678", "field_position": "Chief financial officer", "field_department": "Financial department", "field_created_time": "2025-01-14", "field_organization": ["Flying Club LTD"], "fldsgj6PzZeFAtwomvmLXz9Q": ["<PERSON><PERSON><PERSON>"], "fldwz9wwbHh9Cje4KgzWvDlT": ["2025-02-18 Flying Club LTD-Leon"]}}, {"templateId": "recExe43TEd9DehI8akwq6Kp", "data": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "123123123123", "field_position": "HRP", "field_department": "HR", "field_created_time": "2025-02-18T07:45:41.464Z", "field_organization": ["receAGs8vyTdZvwOT82q3Ttn"], "fldwz9wwbHh9Cje4KgzWvDlT": ["recAkDpElwcflf6iv7adqPQv"]}, "values": {"field_name": "<PERSON>", "field_email": "<EMAIL>", "field_phone": "123123123123", "field_position": "HRP", "field_department": "HR", "field_created_time": "2025-02-18", "field_organization": ["Visionary Ventures"], "fldsgj6PzZeFAtwomvmLXz9Q": ["<PERSON><PERSON><PERSON>"], "fldwz9wwbHh9Cje4KgzWvDlT": ["2025-02-18 Visionary Ventures-<PERSON>"]}}]}, {"resourceType": "DATABASE", "templateId": "database_organization", "name": {"en": "Companies", "ja": "会社", "zh-CN": "客户公司", "zh-TW": "客戶公司"}, "description": {"en": "Records the basic information of the customer enterprise, such as the company name, scale, address, etc.", "ja": "顧客会社の基本情報を記録します。会社名、規模、住所など。", "zh-CN": "记录客户公司的基本信息，如公司名、规模、地址等。", "zh-TW": "記錄客戶公司的基本信息，如公司名、規模、地址等。"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "view_all", "name": {"en": "All", "ja": "すべて", "zh-CN": "全部", "zh-TW": "全部"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "field_name", "hidden": false}, {"templateId": "field_leads", "hidden": false}, {"templateId": "field_description", "hidden": false, "width": 260}, {"templateId": "field_scale", "hidden": false, "width": 128}, {"templateId": "field_address", "hidden": false, "width": 217}, {"templateId": "field_created_time", "hidden": false}, {"templateId": "fldZz44TfQajMtSyZnA9veaQ", "hidden": false, "width": 173}, {"templateId": "flduGEzjMNYHhu5VAF6kmHho", "hidden": false}, {"templateId": "fldX932G9962cAmJdaESkZNs", "hidden": false}]}, {"type": "TABLE", "templateId": "view_my", "name": {"en": "My Clients", "ja": "私の顧客", "zh-CN": "我的客户", "zh-TW": "我的客戶"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldX932G9962cAmJdaESkZNs", "fieldType": "MEMBER", "clause": {"operator": "Is", "value": ["Self"]}}]}, "sorts": [], "fields": [{"templateId": "field_name", "hidden": false}, {"templateId": "field_leads", "hidden": false}, {"templateId": "field_description", "hidden": false}, {"templateId": "field_scale", "hidden": false}, {"templateId": "field_address", "hidden": false}, {"templateId": "field_created_time", "hidden": false}, {"templateId": "fldZz44TfQajMtSyZnA9veaQ", "hidden": false}, {"templateId": "flduGEzjMNYHhu5VAF6kmHho", "hidden": false}, {"templateId": "fldX932G9962cAmJdaESkZNs", "hidden": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "field_name", "privilege": "TYPE_EDIT", "name": {"en": "Company Name", "ja": "会社名", "zh-CN": "公司名称", "zh-TW": "公司名稱"}, "description": {"en": "", "ja": "", "zh-CN": "", "zh-TW": ""}, "primary": true}, {"type": "SINGLE_SELECT", "templateId": "field_leads", "privilege": "NAME_EDIT", "name": {"en": "Lead Source", "ja": "リードソース", "zh-CN": "线索来源", "zh-TW": "線索來源"}, "property": {"options": [{"templateId": "1", "id": "optMwLHZTYmZaHz5YUQChxSi", "name": "Website Registration", "color": "deepPurple"}, {"templateId": "2", "id": "opt9QECOfLDbEaWcNsQmOzg2", "name": "Social Media", "color": "deepPurple"}, {"templateId": "3", "id": "optOfO6Pgr9NC0FKNXaj12RE", "name": "Organic Search (SEO)", "color": "deepPurple"}, {"templateId": "4", "id": "optUAzkdlSpNmghZPkkbU7s3", "name": "Trade Shows/Events", "color": "deepPurple"}, {"templateId": "5", "id": "opt7JXdtIDtlIdkOTrw8T43z", "name": "Email Marketing", "color": "deepPurple"}]}, "primary": false}, {"type": "LONG_TEXT", "templateId": "field_description", "privilege": "NAME_EDIT", "name": {"en": "Description", "ja": "説明", "zh-CN": "描述", "zh-TW": "描述"}, "primary": false}, {"type": "NUMBER", "templateId": "field_scale", "privilege": "NAME_EDIT", "name": {"en": "Scale", "ja": "会社規模", "zh-CN": "公司规模", "zh-TW": "公司規模"}, "property": {"precision": 0, "symbol": ""}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "field_address", "privilege": "NAME_EDIT", "name": {"en": "Address", "ja": "会社所在地", "zh-CN": "公司所在地址", "zh-TW": "公司所在地址"}, "primary": false}, {"type": "CREATED_TIME", "templateId": "field_created_time", "privilege": "NAME_EDIT", "name": {"en": "Created Time", "ja": "作成日時", "zh-CN": "创建时间", "zh-TW": "創建時間"}, "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}, "primary": false}, {"type": "LINK", "templateId": "fldZz44TfQajMtSyZnA9veaQ", "privilege": "FULL_EDIT", "name": {"en": "Contacts", "ja": "連絡先です", "zh-CN": "联系人", "zh-TW": "聯繫人"}, "property": {"foreignDatabaseTemplateId": "database_people", "brotherFieldTemplateId": "database_people:field_organization"}, "primary": false}, {"type": "LINK", "templateId": "flduGEzjMNYHhu5VAF6kmHho", "privilege": "FULL_EDIT", "name": {"en": "Visit Records", "ja": "訪問記録です", "zh-CN": "拜访记录", "zh-TW": "拜訪記錄"}, "property": {"foreignDatabaseTemplateId": "database_visit", "brotherFieldTemplateId": "database_visit:field_organization"}, "primary": false}, {"type": "MEMBER", "templateId": "fldX932G9962cAmJdaESkZNs", "privilege": "FULL_EDIT", "name": {"en": "Sales manager", "ja": "営業担当者です", "zh-CN": "销售负责人", "zh-TW": "銷售負責人"}, "property": {}, "primary": false}], "records": [{"templateId": "rec1f0iBJwe7t8m4yL5QWHlz", "data": {"field_name": "Bika.ai ", "field_leads": ["optMwLHZTYmZaHz5YUQChxSi"], "field_scale": 70, "field_address": "Canada", "field_description": "Bika.ai is an innovative business intelligence and knowledge automation platform that combines the capabilities of database systems with AI-driven automation capabilities.", "field_created_time": "2025-02-17T08:57:53.150Z", "fldX932G9962cAmJdaESkZNs": ["mebYTyD52sAMtTtcdxum1ixz"], "fldZz44TfQajMtSyZnA9veaQ": ["recLG5iaGGJYsD2Xs1hfQGW4"], "flduGEzjMNYHhu5VAF6kmHho": ["recZvkUirTHSOgvZICqf1BxL"]}, "values": {"field_name": "Bika.ai ", "field_leads": ["Website Registration"], "field_scale": "70", "field_address": "Canada", "field_description": "Bika.ai is an innovative business intelligence and knowledge automation platform that combines the capabilities of database systems with AI-driven automation capabilities.", "field_created_time": "2025-01-14T06:15:44.439Z", "fldX932G9962cAmJdaESkZNs": ["<PERSON><PERSON><PERSON>"], "fldZz44TfQajMtSyZnA9veaQ": ["<PERSON>"], "flduGEzjMNYHhu5VAF6kmHho": ["2025-02-18 <PERSON><PERSON><PERSON>a<PERSON> -<PERSON>"]}}, {"templateId": "recKSma4htDBKZYiRxU8hq8S", "data": {"field_name": "Nana Tech", "field_leads": ["opt7JXdtIDtlIdkOTrw8T43z"], "field_scale": 50, "field_address": "Amsterdam", "field_description": "Experience gold products support users with zero risk, zero investment, zero threshold trial", "field_created_time": "2025-02-17T08:57:53.150Z", "fldX932G9962cAmJdaESkZNs": ["mebYTyD52sAMtTtcdxum1ixz"], "fldZz44TfQajMtSyZnA9veaQ": ["reca04196pcxjKsKw2WRoXYj"], "flduGEzjMNYHhu5VAF6kmHho": ["recFTj2OcaBdgjFotQ4Xb5vS"]}, "values": {"field_name": "Nana Tech", "field_leads": ["Email Marketing"], "field_scale": "50", "field_address": "Amsterdam", "field_description": "Experience gold products support users with zero risk, zero investment, zero threshold trial", "field_created_time": "2025-01-14T06:15:44.439Z", "fldX932G9962cAmJdaESkZNs": ["<PERSON><PERSON><PERSON>"], "fldZz44TfQajMtSyZnA9veaQ": ["<PERSON>"], "flduGEzjMNYHhu5VAF6kmHho": ["2025-02-18 <PERSON>-<PERSON>"]}}, {"templateId": "recsE1NnIkc1awJC2hHxJ0WH", "data": {"field_name": "Alaxi Travel", "field_leads": ["optUAzkdlSpNmghZPkkbU7s3"], "field_scale": 150, "field_address": "Canada", "field_description": "Established in 2019 and located in Canada.", "field_created_time": "2025-02-17T08:57:53.150Z", "fldX932G9962cAmJdaESkZNs": ["mebYTyD52sAMtTtcdxum1ixz"], "fldZz44TfQajMtSyZnA9veaQ": ["recHrsGoTAjcKNRr0DxmQh0H"], "flduGEzjMNYHhu5VAF6kmHho": ["rechb4ESLCvTsxIC5BbtAyOo"]}, "values": {"field_name": "Alaxi Travel", "field_leads": ["Trade Shows/Events"], "field_scale": "150", "field_address": "Canada", "field_description": "Established in 2019 and located in Canada.", "field_created_time": "2025-01-14T06:15:44.439Z", "fldX932G9962cAmJdaESkZNs": ["<PERSON><PERSON><PERSON>"], "fldZz44TfQajMtSyZnA9veaQ": ["<PERSON>"], "flduGEzjMNYHhu5VAF6kmHho": ["2025-02-18 Alaxi Travel-Kobe Bryant"]}}, {"templateId": "rec6cRzajLAMMl11FG9231RW", "data": {"field_name": "Flying Club LTD", "field_leads": ["opt9QECOfLDbEaWcNsQmOzg2"], "field_scale": 100, "field_address": " TSIM SHA TSUI HONG KONG", "field_description": "Established in 2017, it is located in the Hong Kong Special Administrative Region.", "field_created_time": "2025-02-17T08:57:53.150Z", "fldX932G9962cAmJdaESkZNs": ["mebYTyD52sAMtTtcdxum1ixz"], "fldZz44TfQajMtSyZnA9veaQ": ["recWoEvMpuVESNClhjheBsQU"], "flduGEzjMNYHhu5VAF6kmHho": ["recvvQK57RYN7nig0krb8Bx1"]}, "values": {"field_name": "Flying Club LTD", "field_leads": ["Social Media"], "field_scale": "100", "field_address": " TSIM SHA TSUI HONG KONG", "field_description": "Established in 2017, it is located in the Hong Kong Special Administrative Region.", "field_created_time": "2025-01-14", "fldX932G9962cAmJdaESkZNs": ["<PERSON><PERSON><PERSON>"], "fldZz44TfQajMtSyZnA9veaQ": ["<PERSON>"], "flduGEzjMNYHhu5VAF6kmHho": ["2025-02-18 Flying Club LTD-Leon"]}}, {"templateId": "receAGs8vyTdZvwOT82q3Ttn", "data": {"field_name": "Visionary Ventures", "field_leads": ["optUAzkdlSpNmghZPkkbU7s3"], "field_scale": 2000, "field_address": "JAVA RD NORTH POINT HONG KONG", "field_description": "Established in 2009, it is located in the Hong Kong Special Administrative Region.", "field_created_time": "2025-02-17T08:57:53.150Z", "fldX932G9962cAmJdaESkZNs": ["mebYTyD52sAMtTtcdxum1ixz"], "fldZz44TfQajMtSyZnA9veaQ": ["recExe43TEd9DehI8akwq6Kp"], "flduGEzjMNYHhu5VAF6kmHho": ["recAkDpElwcflf6iv7adqPQv"]}, "values": {"field_name": "Visionary Ventures", "field_leads": ["Trade Shows/Events"], "field_scale": "2000", "field_address": "JAVA RD NORTH POINT HONG KONG", "field_description": "Established in 2009, it is located in the Hong Kong Special Administrative Region.", "field_created_time": "2025-01-14", "fldX932G9962cAmJdaESkZNs": ["<PERSON><PERSON><PERSON>"], "fldZz44TfQajMtSyZnA9veaQ": ["<PERSON>"], "flduGEzjMNYHhu5VAF6kmHho": ["2025-02-18 Visionary Ventures-<PERSON>"]}}]}, {"resourceType": "DATABASE", "templateId": "database_visit", "name": {"en": "Visit Records", "ja": "訪問記録", "zh-CN": "拜访记录", "zh-TW": "拜訪記錄"}, "description": {"en": "Record the visit records of the salesperson to the customer enterprise.", "ja": "営業担当者が顧客企業を訪問した記録", "zh-CN": "记录销售人员对客户企业的拜访记录。", "zh-TW": "記錄銷售人員對客戶企業的拜訪記錄。"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "view_all", "name": {"en": "All", "ja": "すべて", "zh-CN": "全部", "zh-TW": "全部"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "field_name", "hidden": false}, {"templateId": "field_organization", "hidden": false, "width": 154}, {"templateId": "field_people", "hidden": false, "width": 171}, {"templateId": "field_content", "hidden": false, "width": 244}, {"templateId": "field_follower", "hidden": false}, {"templateId": "field_visit_time", "hidden": false}, {"templateId": "field_creator", "hidden": false}, {"templateId": "field_created_time", "hidden": false}]}, {"type": "TABLE", "templateId": "view_my", "name": {"en": "My visits", "ja": "私の訪問記録", "zh-CN": "我的拜访", "zh-TW": "我的拜訪"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "field_creator", "fieldType": "CREATED_BY", "clause": {"operator": "Is", "value": "Self"}}]}, "sorts": [], "fields": [{"templateId": "field_name", "hidden": false}, {"templateId": "field_organization", "hidden": false}, {"templateId": "field_people", "hidden": false}, {"templateId": "field_content", "hidden": false}, {"templateId": "field_follower", "hidden": false}, {"templateId": "field_visit_time", "hidden": false}, {"templateId": "field_creator", "hidden": false}, {"templateId": "field_created_time", "hidden": false}]}, {"type": "TABLE", "templateId": "view_this_week", "name": {"en": "This Week", "ja": "今週の訪問", "zh-CN": "本周拜访", "zh-TW": "本週拜訪"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "field_created_time", "fieldType": "DATETIME", "clause": {"operator": "Is", "value": ["ThisWeek"]}}]}, "sorts": [], "fields": [{"templateId": "field_name", "hidden": false}, {"templateId": "field_organization", "hidden": false}, {"templateId": "field_people", "hidden": false}, {"templateId": "field_content", "hidden": false}, {"templateId": "field_follower", "hidden": false}, {"templateId": "field_visit_time", "hidden": false}, {"templateId": "field_creator", "hidden": false}, {"templateId": "field_created_time", "hidden": false}]}], "fields": [{"type": "FORMULA", "templateId": "field_name", "privilege": "TYPE_EDIT", "name": {"en": "Visit Record", "ja": "訪問記録", "zh-CN": "拜访记录", "zh-TW": "拜訪記錄"}, "property": {"expressionTemplate": "{field_created_time} & \" \" & {field_organization} & \"-\" & {field_people}"}, "primary": true}, {"type": "LINK", "templateId": "field_organization", "privilege": "NAME_EDIT", "name": {"en": "Company", "ja": "会社", "zh-CN": "客户公司", "zh-TW": "客戶公司"}, "property": {"foreignDatabaseTemplateId": "database_organization", "brotherFieldTemplateId": "flduGEzjMNYHhu5VAF6kmHho"}, "primary": false}, {"type": "LOOKUP", "templateId": "field_people", "privilege": "NAME_EDIT", "name": {"en": "Person", "ja": "連絡先", "zh-CN": "客户名称", "zh-TW": "聯絡人"}, "required": false, "property": {"relatedLinkFieldTemplateId": "database_visit:field_organization", "lookupTargetFieldTemplateId": "fldZz44TfQajMtSyZnA9veaQ", "lookupTargetFieldType": "LINK", "dataType": "STRING", "lookUpLimit": "ALL", "rollUpType": "VALUES"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "field_content", "privilege": "NAME_EDIT", "name": {"en": "Content", "ja": "内容の要約", "zh-CN": "内容摘要", "zh-TW": "內容摘要"}, "primary": false}, {"type": "LOOKUP", "templateId": "field_follower", "privilege": "NAME_EDIT", "name": {"en": "Sales Lead", "ja": "フォロワー", "zh-CN": "跟进人", "zh-TW": "跟進人"}, "required": false, "property": {"relatedLinkFieldTemplateId": "database_visit:field_organization", "lookupTargetFieldTemplateId": "fldX932G9962cAmJdaESkZNs", "lookupTargetFieldType": "MEMBER", "dataType": "STRING", "lookUpLimit": "ALL", "rollUpType": "VALUES"}, "primary": false}, {"type": "DATETIME", "templateId": "field_visit_time", "privilege": "NAME_EDIT", "name": {"en": "Visit Time", "ja": "訪問時間", "zh-CN": "拜访时间", "zh-TW": "拜訪時間"}, "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": true, "autofill": true}, "primary": false}, {"type": "CREATED_BY", "templateId": "field_creator", "privilege": "NAME_EDIT", "name": {"en": "Creator", "ja": "作成者", "zh-CN": "创建人", "zh-TW": "創建人"}, "primary": false}, {"type": "CREATED_TIME", "templateId": "field_created_time", "privilege": "NAME_EDIT", "name": {"en": "Created Time", "ja": "作成日時", "zh-CN": "创建时间", "zh-TW": "創建時間"}, "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}, "primary": false}], "records": [{"templateId": "recZvkUirTHSOgvZICqf1BxL", "data": {"field_name": "2025-02-18 <PERSON><PERSON><PERSON>a<PERSON> -<PERSON>", "field_people": ["recLG5iaGGJYsD2Xs1hfQGW4"], "field_content": "I visited the client's company to understand their entire business process. The next step is to build a DEMO.", "field_creator": "usri0GBsSQCKJfmmyUi14XI3", "field_visit_time": "2025-02-17T07:41:00.000Z", "field_created_time": "2025-02-18T07:40:11.160Z", "field_organization": ["rec1f0iBJwe7t8m4yL5QWHlz"]}, "values": {"field_name": "2025-02-18 <PERSON><PERSON><PERSON>a<PERSON> -<PERSON>", "field_people": ["<PERSON>"], "field_content": "I visited the client's company to understand their entire business process. The next step is to build a DEMO.", "field_creator": "<PERSON><PERSON><PERSON>", "field_follower": ["<PERSON><PERSON><PERSON>"], "field_visit_time": "2025-02-17 07:41", "field_created_time": "2025-02-18", "field_organization": ["Bika.ai "]}}, {"templateId": "recFTj2OcaBdgjFotQ4Xb5vS", "data": {"field_name": "2025-02-18 <PERSON>-<PERSON>", "field_people": ["reca04196pcxjKsKw2WRoXYj"], "field_content": "I visited the client's company to sign the contract, which has been completed. The next step is to proceed with the payment collection.", "field_creator": "usri0GBsSQCKJfmmyUi14XI3", "field_visit_time": "2025-02-17T07:41:00.000Z", "field_created_time": "2025-02-18T07:40:16.651Z", "field_organization": ["recKSma4htDBKZYiRxU8hq8S"]}, "values": {"field_name": "2025-02-18 <PERSON>-<PERSON>", "field_people": ["<PERSON>"], "field_content": "I visited the client's company to sign the contract, which has been completed. The next step is to proceed with the payment collection.", "field_creator": "<PERSON><PERSON><PERSON>", "field_follower": ["<PERSON><PERSON><PERSON>"], "field_visit_time": "2025-02-17 07:41", "field_created_time": "2025-02-18", "field_organization": ["Nana Tech"]}}, {"templateId": "rechb4ESLCvTsxIC5BbtAyOo", "data": {"field_name": "2025-02-18 Alaxi Travel-Kobe Bryant", "field_people": ["recHrsGoTAjcKNRr0DxmQh0H"], "field_content": "\nI had an online meeting with the client, focusing mainly on the budget and targets for this quarter.", "field_creator": "usri0GBsSQCKJfmmyUi14XI3", "field_visit_time": "2025-02-17T07:41:00.000Z", "field_created_time": "2025-02-18T07:40:23.418Z", "field_organization": ["recsE1NnIkc1awJC2hHxJ0WH"]}, "values": {"field_name": "2025-02-18 Alaxi Travel-Kobe Bryant", "field_people": ["<PERSON>"], "field_content": "\nI had an online meeting with the client, focusing mainly on the budget and targets for this quarter.", "field_creator": "<PERSON><PERSON><PERSON>", "field_follower": ["<PERSON><PERSON><PERSON>"], "field_visit_time": "2025-02-17 07:41", "field_created_time": "2025-02-18", "field_organization": ["Alaxi Travel"]}}, {"templateId": "recvvQK57RYN7nig0krb8Bx1", "data": {"field_name": "2025-02-18 Flying Club LTD-Leon", "field_people": ["recWoEvMpuVESNClhjheBsQU"], "field_content": "I visited the client's company to discuss the arrangements for the event on November 11th and prepared three plans along with their respective quotations.", "field_creator": "usri0GBsSQCKJfmmyUi14XI3", "field_visit_time": "2025-02-17T07:41:00.000Z", "field_created_time": "2025-02-18T07:40:27.686Z", "field_organization": ["rec6cRzajLAMMl11FG9231RW"]}, "values": {"field_name": "2025-02-18 Flying Club LTD-Leon", "field_people": ["<PERSON>"], "field_content": "I visited the client's company to discuss the arrangements for the event on November 11th and prepared three plans along with their respective quotations.", "field_creator": "<PERSON><PERSON><PERSON>", "field_follower": ["<PERSON><PERSON><PERSON>"], "field_visit_time": "2025-02-17 07:41", "field_created_time": "2025-02-18", "field_organization": ["Flying Club LTD"]}}, {"templateId": "recAkDpElwcflf6iv7adqPQv", "data": {"field_name": "2025-02-18 Visionary Ventures-<PERSON>", "field_people": ["recExe43TEd9DehI8akwq6Kp"], "field_content": "I visited the client's company to set up the event venue and got to know the person in charge of the event at the client's company.", "field_creator": "usri0GBsSQCKJfmmyUi14XI3", "field_visit_time": "2025-02-17T07:41:17.662Z", "field_created_time": "2025-02-18T07:40:31.317Z", "field_organization": ["receAGs8vyTdZvwOT82q3Ttn"]}, "values": {"field_name": "2025-02-18 Visionary Ventures-<PERSON>", "field_people": ["<PERSON>"], "field_content": "I visited the client's company to set up the event venue and got to know the person in charge of the event at the client's company.", "field_creator": "<PERSON><PERSON><PERSON>", "field_follower": ["<PERSON><PERSON><PERSON>"], "field_visit_time": "2025-02-17 07:41", "field_created_time": "2025-02-18", "field_organization": ["Visionary Ventures"]}}]}, {"resourceType": "MIRROR", "templateId": "my_clients_mirror", "name": {"en": "My Clients", "ja": "私の顧客", "zh-CN": "我的客户", "zh-TW": "我的客戶"}, "mirrorType": "DATABASE_VIEW", "databaseTemplateId": "database_organization", "viewTemplateId": "view_my"}, {"resourceType": "MIRROR", "templateId": "my_visits_mirror", "name": {"en": "My Visits", "ja": "私の訪問記録", "zh-CN": "我的拜访记录", "zh-TW": "我的拜訪記錄"}, "mirrorType": "DATABASE_VIEW", "databaseTemplateId": "database_visit", "viewTemplateId": "view_my"}, {"resourceType": "AUTOMATION", "templateId": "automation_sales_weekly_summary", "name": {"en": "Auto send weekly report of sales team visit records", "ja": "営業チームの訪問記録を自動的に集計し、営業マネージャーに送信します", "zh-CN": "自动发送销售团队拜访周报", "zh-TW": "自動發送銷售團隊拜訪周報"}, "description": {"en": "Weekly summary of sales team visit records, sent to sales manager", "ja": "毎週、営業チームの訪問記録を自動的に集計し、営業マネージャーに送信します", "zh-CN": "每周自动汇总销售团队的拜访记录，发送给销售经理", "zh-TW": "每周自動匯總銷售團隊的拜訪記錄，發送給銷售經理"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trigger_scheduler_summary", "description": {"en": "Every Friday 5pm", "ja": "毎週金曜日午後5時", "zh-CN": "每周五下午5点", "zh-TW": "每周五下午5點"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["FRI"]}}, "timezone": "AUTO", "datetime": "2025-01-14T09:00:00.000Z"}}}], "actions": [{"templateId": "action_get_list_sales_team", "description": {"en": "Get the list of sales team members", "ja": "営業チームのメンバーのリストを取得", "zh-CN": "获取销售团队的成员名单", "zh-TW": "獲取銷售團隊的成員名單"}, "actionType": "FIND_MEMBERS", "input": {"type": "MEMBER", "by": [{"type": "UNIT_ROLE", "roleTemplateId": ""}, {"type": "SPECIFY_UNITS", "unitIds": []}]}}, {"templateId": "action_count_visit_records", "description": {"en": "Find all members' visit records in a week", "ja": "一週間のすべてのメンバーの訪問記録をカウント", "zh-CN": "统计一周内所有成员的拜访记录", "zh-TW": "統計一周內所有成員的拜訪記錄"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "view_this_week", "databaseTemplateId": "database_visit"}}, {"templateId": "actSMuFkKNTkM4QB9omQJ6sN", "description": {"en": "AI Statistical Analysis", "ja": "AI統計分析", "zh-CN": "AI 统计分析", "zh-TW": "AI 統計分析"}, "actionType": "OPENAI_GENERATE_TEXT", "input": {"urlType": "URL", "type": "OPENAI_GENERATE_TEXT", "prompt": "This is the list of the sales team<%= JSON.stringify(_actions.action_get_list_sales_team.members) %>, and this is the visit record for this week.<%= _renderRecordsAsGrid(_actions.action_count_visit_records.records, [\"fldL9jUZAyQzg9MILyB67Ue6\",\"fldqDZ9AjKMWxSRi6q2CBCM3\",\"fldZW9H1qyhl0kG8ssZKYtVW\",\"fldK2qAazd15K5IN7VgsXwfq\",\"fldIpuqksyk6eBGJqt1lLJji\",\"fldwgzJ7QTAemiidWacqZOnb\"]) %>Please compile and fill in the statistics according to the format below based on the sales team list and this week's visit records (note that the follow-up personnel in the visit records are the sales members, so do not treat customer names as sales personnel; the number of sales team members should match the list). The evaluation standards are:           \n👏outstanding,😟Waiting to be promoted.                    \n            \n                                 \n --------------                                                     \n## 📊 Overview                                                      \nTime is in a hurry, a week has passed, let's take a look at the sales team's performance this week!🎉                                                      \nThe sales team currently has ？ members                                                      \nTotal completed this week: ？ visit records                                                      \n                \n## 🎯 KPI progress                                                      \nThe team's current goal is to visit at least five clients per week, and here's how far each member is going:                                                      \n|Member | Number of completions | Performance this week |", "baseUrl": "https://api.moonshot.cn/v1", "apiKey": "", "model": "moonshot-v1-128k", "timeout": 90}}, {"templateId": "report_generate_summary", "description": {"en": "Generate and push sales weekly report", "ja": "営業週報を生成してプッシュ", "zh-CN": "生成并推送销售周报", "zh-TW": "生成並推送銷售周報"}, "actionType": "SEND_REPORT", "input": {"to": [{"type": "UNIT_ROLE", "roleTemplateId": ""}, {"type": "CURRENT_OPERATOR"}], "markdown": "<%= _actions.actSMuFkKNTkM4QB9omQJ6sN.body.choices[0].message.content %>\n\n### 👏 Visit records this week\n<%= _renderRecordsAsGrid(_actions.action_count_visit_records.records, ['fldL9jUZAyQzg9MILyB67Ue6','fldqDZ9AjKMWxSRi6q2CBCM3','fldZW9H1qyhl0kG8ssZKYtVW','fldK2qAazd15K5IN7VgsXwfq','fldIpuqksyk6eBGJqt1lLJji','fldwgzJ7QTAemiidWacqZOnb']) %>", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "variable", "attrs": {"ids": "_actions,actSMuFkKNTkM4QB9omQJ6sN,body,choices,[0],message,content", "tips": "", "names": "Actions,OpenAI - Generate Text,body,choices,#1,message,content"}}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "### 👏 Visit records this week", "type": "text"}]}, {"type": "paragraph", "content": [{"type": "variable", "attrs": {"ids": "_renderRecordsAsGrid(_actions.action_count_visit_records.records, ['fldL9jUZAyQzg9MILyB67Ue6','fldqDZ9AjKMWxSRi6q2CBCM3','fldZW9H1qyhl0kG8ssZKYtVW','fldK2qAazd15K5IN7VgsXwfq','fldIpuqksyk6eBGJqt1lLJji','fldwgzJ7QTAemiidWacqZOnb'])", "tips": "Selected Fields: Visit Record, Company, Person, Content, Follower, Visit Time", "names": ["Actions", "Find Records", "Grid List"]}}]}]}, "subject": "Weekly sales report", "type": "MARKDOWN"}}]}, {"resourceType": "AUTOMATION", "templateId": "automation-dispatch-visit-mission", "name": {"en": "Daily Sales Mission Dispatch", "ja": "毎日セールスミッションディスパッチ", "zh-CN": "每日销售任务派发", "zh-TW": "每日銷售任務派發"}, "description": {"en": "Automatically assign missions to the sales team to visit customers every day", "ja": "毎日、営業チームに顧客を訪問するためのタスクを自動的に割り当てます", "zh-CN": "每日自动派发拜访客户的任务给销售团队", "zh-TW": "每日自動派發拜訪客戶的任務給銷售團隊"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trigger_scheduler", "description": {"en": "Triggered every morning at 9 a.m.", "ja": "毎朝午前9時にトリガー", "zh-CN": "每天早上9点触发", "zh-TW": "每天早上9點觸發"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["MON", "TUE", "WED", "THU", "FRI"]}}, "timezone": "AUTO", "datetime": "2025-01-14T01:00:00.000Z"}}}], "actions": [{"templateId": "action_create_visit_mission", "description": {"en": "Create a mission to visit customers", "ja": "顧客を訪問するミッションを作成", "zh-CN": "创建拜访客户任务", "zh-TW": "創建拜訪客戶任務"}, "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "CREATE_RECORD", "name": {"en": "Visit Customers", "ja": "顧客を訪問", "zh-CN": "拜访客户", "zh-TW": "拜訪客戶"}, "description": "Customer relationship management is an important part of sales work. It is essential to visit at least one customer per day and document the content of the visit. Good visit records can help the team better understand customer needs and improve sales efficiency.", "canReject": false, "canCompleteManually": false, "canTransfer": false, "assignType": "DEDICATED", "to": [{"type": "UNIT_ROLE", "roleTemplateId": ""}], "buttonText": {"en": "Fill in the visit record", "ja": "訪問記録を記入", "zh-CN": "填写拜访记录", "zh-TW": "填寫拜訪記錄"}, "viewTemplateId": "view_my", "databaseTemplateId": "database_visit"}}}]}]}