---
title: 如何使用 ToolSDK.ai，搭建一个支持Github OAuth 创建 Issue 的表单Action？
slug: toolsdk.ai/help/zh-CN/tutorials/build-github-create-issue-oauth-formapp-action
sidebar_position: 3
---


# 如何使用 ToolSDK.ai，搭建一个支持Github OAuth 创建 Issue 的表单Action？

借助 [FormApp.ai](http://FormApp.ai) 这一强大工具，可轻松实现与各类第三方工具的无缝连接。

作为 AI 自动化调度体系的关键构成部分，它能够将多种功能与技术整合，通过高效的算法和智能的流程编排，精心打包出一个功能完备、适应性强的商业 AI 智能体。

这一智能体不仅具备强大的API和AI执行力，还能依据不同的业务场景和需求，灵活地执行任务，为企业在复杂多变的商业环境中提供智能化、个性化的解决方案 ，助力企业提升运营效率、降低成本，增强市场竞争力。

## 功能简介

先来看一下效果.... Github Issue 创建了！

首先，了解一下什么是OAuth？流程图...

解决方案，怎么做的呢？

## 操作流程

### 一、新建Github集成

<b>进入FormApp.ai</b>

点击导航栏中的 <b>Submit</b>，切换到 <b>New Integration</b>页面。

先填写基础信息

<b>Key：</b> 此集成在FormApp中的唯一标识

<b>名称和描述：</b> 填写集成名称和简要说明，并可以切换多语言进行填写

<b>认证类型：</b> 选择 <b>OAuth2</b>，兼容Github的认证模式

最后点击底部 Submit 提交

<img src="https://book.bika.ai/bika-content/assets/G5ZAbL9Q4o2K32xc2cwlCusGget.png" src-width="3356" src-height="1854"/>

### 集成配置

#### 复制 Redirect URL

创建完成后会自动进入配置页，你可以跟随步骤引导完成集成的配置

集成 GitHub 可以直接从第二步开始，展开 Copy your Auth Redirect URL，复制链接备用。

Redirect URL是OAuth流程中重要的返回地址，用于确保认证信息的平滑传递

<img src="https://book.bika.ai/bika-content/assets/ZNvebbuCHo3dOoxwxr1lBOhpgJh.png" src-width="1440" src-height="778"/>

#### 创建 GitHub app

新标签页进入 GitHub，在个人设置菜单底部找到 <u>Github 开发者设置</u>，并新建 OAuth App

填写你的 App 名称和其他基本信息，并将前一步复制的Redirect URL填入 <b>Authorization callback URL，</b>然后点击底部的注册按钮

<img src="https://book.bika.ai/bika-content/assets/HzjCb5tIqoXb08xSzqQlwAQPgUb.png" src-width="1440" src-height="778"/>

创建成功后，点击生成 Client Secrets

<img src="https://book.bika.ai/bika-content/assets/Q63GbG1FuozJ6vxlGUblTeUCgAc.png" src-width="1440" src-height="778"/>

<img src="https://book.bika.ai/bika-content/assets/MaH8b580Yo9KTMx82j4lkYhegHe.png" src-width="1440" src-height="778"/>

#### 填写 Client ID 和 Client secret

回到 Formapp，在第三步中填入Github App中生成的Client ID和Client Secret

<img src="https://book.bika.ai/bika-content/assets/B0DnbYrFhoDPfExUQz3lqVNygvf.png" src-width="1440" src-height="778"/>

#### 配置认证 API 

首先需要填写 GitHub 的 Authorization URL，通常不需要额外的设置。

下方步骤所需 API 皆可查阅 GItHub 官方 API 文档<u>GitHub REST API</u>

```text
GET https://github.com/login/oauth/authorize
```

<img src="https://book.bika.ai/bika-content/assets/PxHcbPuPyouXrwxYOplljjoqgzh.png" src-width="1440" src-height="778"/>

然后需要填写 scope 范围，范围允许您准确指定你需要的访问类型。范围限制OAuth令牌的访问，具体 scope 范围可查看文档 <u>Scopes for OAuth apps - GitHub Docs</u>

在当前场景中我们需要填写的有以下范围，多个scope 用空格分隔

```text
repo user write:issues read:org
```

<img src="https://book.bika.ai/bika-content/assets/Wq7Cbdnceo0PiYxzqvxl0LNNgEe.png" src-width="1440" src-height="778"/>

接着需要填写 Access Token Request。Formapp 包含默认字段，不需要额外的设置

```text
POST https://github.com/login/oauth/access_token
```

<img src="https://book.bika.ai/bika-content/assets/N6IxbbPgHoFKssx9htWlMBY2gnd.png" src-width="1440" src-height="778"/>

最后需要添加一个测试 API 调用，用于测试身份验证是否生效

下方案例中的 API 可以获取用户信息，也可以选择GItHub 提供的其他 API 进行测试

```text
GET https://api.github.com/user
```

<img src="https://book.bika.ai/bika-content/assets/HAOKbInQ0o7kzzxoI2XlSTytgqC.png" src-width="1440" src-height="778"/>

最后点击底部的 Update 按钮即可保存集成配置

#### 测试集成

在右侧的预览区域即可点击 Go Auth test 按钮进行测试。在浏览器的弹出窗口中确认，连接成功后可以看到多了一条 Credential，代表集成配置成功

<img src="https://book.bika.ai/bika-content/assets/XfdtbFNbFoMnN6xIMszlClzcgSh.png" src-width="1440" src-height="778"/>

## 搭建 Create github issue Action

### 填写 Action 基本信息

点击导航栏的 Submit，切换到 New Action，与创建集成相同，填写 Action 的 Key、名称及描述等基础信息

<img src="https://book.bika.ai/bika-content/assets/JfRybXbgLowvSzxfpfjlujJEgle.png" src-width="1440" src-height="778"/>

### 选择 GItHub 集成

在 Integrations 中，选择上一步创建的 GItHub 集成。右侧预览区可实时看到表单区增加的集成选择项。将集成与操作绑定，确保操作可用且数据通过指定账户传输

<img src="https://book.bika.ai/bika-content/assets/M0ivbistrod2vUxajvtljhr6g3b.png" src-width="1440" src-height="778"/>

### 配置表单

通过 GItHub <u>官方 API 文档</u> 提供的信息，创建一个 GItHub Issue 需要填写的参数有：owner、repo、title，可选的参数有 body、assignee 等

以 创建一个包含标题和正文的 Issue 为例，我们需要创建以下几个字段：

- owner（必填）
- repo（必填）
- title（必填）
- body（选填）

<img src="https://book.bika.ai/bika-content/assets/IkgvbdwPpokmJQxhlNZldhMMgsg.png" src-width="1440" src-height="778"/>

### 配置 API 

首先将创建 Issue 的 API 填入 输入框，以下是官方提供的 API 

通过 {{bundle.inputData.xxx}} 的方式，将链接中的 <b>OWNER</b> 和 <b>REPO</b> 与最终表单输入的字段绑定。输入 {{ 将会自动提示可选项

- {{bundle.inputData.owner}} 对应 OWNER；
- {{bundle.inputData.repo}} 对应 REPO

```text
// 官方 API 示例 POST https://api.github.com/repos/{OWNER}/{REPO}/issues

POST https://api.github.com/repos/{{bundle.inputData.owner}}/{{bundle.inputData.repo}}/issues
```

<img src="https://book.bika.ai/bika-content/assets/B9YDbKxJWoF00dxERLcliXTdgag.png" src-width="1440" src-height="778"/>

### 设置请求头和请求体

请求头包含了关于请求的附加信息，告诉服务器如何处理请求或描述客户端的某些属性。常见的请求头字段有：

- <b>Content-Type</b>：指示请求体的数据类型（如 application/json、application/x-www-form-urlencoded 等）。
- <b>Authorization</b>：用于携带身份验证信息（如 token）。

请求体通常用于 POST、PUT 或 PATCH 请求中，用来传递发送给服务器的数据。请求体携带的是要提交给服务器的内容，比如 JSON 数据、表单数据等。通常用于创建或更新资源

URL 参数用于传递查询条件或筛选信息，通常用于 GET 请求

根据官方文档要求，在请求头中添加 <b>Authorization: Bearer &lt;YOUR-TOKEN&gt;</b>，<b>&lt;YOUR-TOKEN&gt;</b> 部分使用 <b>{{bundle.authData.{IntegrationKey}.access_token}}</b> 进行绑定

<img src="https://book.bika.ai/bika-content/assets/MiPWbYcgcoe1UvxQvYclTGfQgCc.png" src-width="1440" src-height="778"/>

<img src="https://book.bika.ai/bika-content/assets/W0IybMsLioVRNIxklcPl1Ypbg9u.png" src-width="2880" src-height="1800"/>

接着配置请求体，根据GItHub的数据结构，直接按格式输入以下 JSON 代码，同样将 title 和 body 与用户在表单输入的值绑定

```text
{
  "title": "{{bundle.inputData.title}}",
  "body": "{{bundle.inputData.body}}"
}
```

<img src="https://book.bika.ai/bika-content/assets/SuJUbOYyroPP8ixaWBplW8tegSc.png" src-width="1440" src-height="778"/>

最后点击底部的 Submit 按钮保存提交即可

## 测试 Action 

GItHub Create an Issue Action 就完成了，接下来可以在右侧预览区域之间运行测试。

首先选择集成，如果你已经连接过 GItHub，可以直接在下拉菜单中选择，或者点击 Go Auth test 新建连接

<img src="https://book.bika.ai/bika-content/assets/YrkJbWbRXoGFVCxmqFvl0TVYgeh.png" src-width="1440" src-height="778"/>

随后依次填写 Owner、repo、Issue title、Issue body，最后点击底部 Test 按钮

进入 GItHub 项目的主页，在浏览器链接中可以找到 Owner 和 repo 信息

<u>https://github.com/{OWNER}/{REPO</u><u>}</u>

<img src="https://book.bika.ai/bika-content/assets/NNB5bwqeDoYnACxhqQXliLRdgeh.png" src-width="1440" src-height="778"/>

随后将会在当前窗口弹出返回结果，代表测试运行成功。同时也可以在 GItHub Issue 中找到新建的 Issue

<img src="https://book.bika.ai/bika-content/assets/DkP5bF6mnoXXYwx6UqZll9nSgsc.png" src-width="1440" src-height="778"/>

<img src="https://book.bika.ai/bika-content/assets/Tab7bj4S0opxD5x8zEAlFtXhgSb.png" src-width="1440" src-height="778"/>

