import { NodeResourceType } from '@bika/types/node/bo';
import { NodeDetailVO } from '@bika/types/node/vo';
import { MaterialIcons, AntDesign, Feather } from '@expo/vector-icons';
import { useWindowDimensions } from 'react-native';
import Markdown from 'react-native-markdown-display';
import RenderHtml, { MixedStyleDeclaration } from 'react-native-render-html';
import {
  DashboardOutlined,
  FolderNormalOutlined,
  MirrorOutlined,
} from '@/assets/svg';
import AutomationOutlined from '@/assets/svg/automation_outlined';
import DatabaseOutlined from '@/assets/svg/database_outlined';
import { VText } from '@/components/UI/Text';
import { useColor } from '@/hooks/useColor';

export const formatHTMLSource = (content?: string) => {
  return {
    html: content
      ? `${content}`
      : `<p style="text-align:center;">NOT REPORT CONTENT FOUND</p>`,
  };
};

export const MarkdownRender = ({ content }: { content?: string }) => {
  const colors = useColor();

  return (
    <Markdown
      style={{
        heading1: {
          color: colors.textCommonPrimary,
          fontSize: 20,
          fontWeight: 'bold',
          marginVertical: 6,
        },
        heading2: {
          color: colors.textCommonPrimary,
          fontSize: 18,
          fontWeight: '600',
          marginVertical: 6,
        },
        heading3: {
          color: colors.textCommonPrimary,
          fontSize: 16,
          fontWeight: '600',
          marginVertical: 6,
        },
        body: {
          color: colors.textCommonPrimary,
          fontSize: 14,
          fontWeight: 'normal',
        },
      }}
    >
      {content}
    </Markdown>
  );
};

export const HTMLRender = ({ content }: { content?: string }) => {
  const colors = useColor();

  const { width } = useWindowDimensions();

  const baseStyle: MixedStyleDeclaration = {
    color: colors.textCommonPrimary,
  };

  return (
    <RenderHtml
      contentWidth={width}
      source={formatHTMLSource(content)}
      baseStyle={baseStyle}
    />
  );
};

export const RenderNodeResource = ({
  resource,
}: {
  resource: NodeDetailVO | null;
}) => {
  const colors = useColor();

  if (!resource) {
    return null;
  }

  switch (resource?.type) {
    case 'AUTOMATION':
      return (
        <VText variant="B1" color={colors.textCommonPrimary}>
          AUTOMATION
        </VText>
      );
    case 'DATABASE':
      return (
        <VText variant="B1" color={colors.textCommonPrimary}>
          DATABASE
        </VText>
      );
    case 'FOLDER':
      return (
        <VText variant="B1" color={colors.textCommonPrimary}>
          FOLDER
        </VText>
      );
    case 'TEMPLATE':
      return (
        <VText variant="B1" color={colors.textCommonPrimary}>
          TEMPLATE
        </VText>
      );
    case 'DASHBOARD':
      return (
        <VText variant="B1" color={colors.textCommonPrimary}>
          DASHBOARD
        </VText>
      );
    default:
      return (
        <VText variant="B1" color={colors.textCommonPrimary}>
          NONE
        </VText>
      );
  }
};

export const RenderNodeResourceIcon = ({
  type,
  size = 24,
}: {
  type: NodeResourceType;
  size?: number;
}) => {
  const colors = useColor();

  switch (type) {
    case 'AUTOMATION':
      return (
        <AutomationOutlined
          width={size}
          height={size}
          color={colors.textCommonPrimary}
        />
      );
    case 'DATABASE':
      return (
        <DatabaseOutlined
          width={size}
          height={size}
          color={colors.textCommonPrimary}
        />
      );
    case 'FOLDER':
      return (
        <FolderNormalOutlined
          width={size}
          height={size}
          color={colors.textCommonPrimary}
        />
      );
    case 'MIRROR':
      return (
        <MirrorOutlined
          width={size}
          height={size}
          color={colors.textCommonPrimary}
        />
      );
    case 'TEMPLATE':
      return (
        <Feather name="command" size={size} color={colors.textCommonPrimary} />
      );
    case 'FORM':
      return (
        <AntDesign name="form" size={size} color={colors.textCommonPrimary} />
      );
    case 'DASHBOARD':
      return (
        <DashboardOutlined
          width={size}
          height={size}
          color={colors.textCommonPrimary}
        />
      );
    case 'DATAPAGE':
      return (
        <AntDesign name="table" size={size} color={colors.textCommonPrimary} />
      );
    case 'AI':
      return (
        <MaterialIcons
          name="science"
          size={size}
          color={colors.textCommonPrimary}
        />
      );
    case 'CANVAS':
      return (
        <MaterialIcons
          name="crop-original"
          size={size}
          color={colors.textCommonPrimary}
        />
      );
    case 'DOCUMENT':
      return (
        <MaterialIcons
          name="description"
          size={size}
          color={colors.textCommonPrimary}
        />
      );
    // case 'VIEW':
    //   return (
    //     <MirrorOutlined
    //       width={size}
    //       height={size}
    //       color={colors.textCommonPrimary}
    //     />
    //   );
    case 'ROOT':
      return (
        <MaterialIcons
          name="home"
          size={size}
          color={colors.textCommonPrimary}
        />
      );
    // case 'WEB_PAGE':
    //   return (
    //     <MaterialIcons
    //       name="web"
    //       size={size}
    //       color={colors.textCommonPrimary}
    //     />
    //   );
    case 'REPORT_TEMPLATE':
      return (
        <MaterialIcons
          name="insert-chart"
          size={size}
          color={colors.textCommonPrimary}
        />
      );
    case 'ALIAS':
      return (
        <MaterialIcons
          name="link"
          size={size}
          color={colors.textCommonPrimary}
        />
      );
    // case 'CAMPAIGN':
    //   return (
    //     <MaterialIcons
    //       name="campaign"
    //       size={size}
    //       color={colors.textCommonPrimary}
    //     />
    //   );
    default:
      return null;
  }
};
