---
title: How to use OpenAI SDK to connect ToolSDK.ai
sidebar_label: How to use OpenAI SDK to connect ToolSDK.ai
slug: formapp/help/en/tutorials/how-to-use-openai-sdk-to-call-toolsdk
sidebar_position: 2
---

# How to use OpenAI SDK to connect ToolSDK.ai

> This document aims to explain how to use the OpenAI SDK or AI SDK to integrate with ToolSDK, helping developers quickly understand the integration process and usage.

## What is ToolSDK

ToolSDK is a platform that provides access to a large number of MCP serve Agent tools. You can register your tools here and invoke them using the OpenAI SDK, AI SDK, or your custom SDK.

## Quick Starting

```ts
// 1. First, import toolSDK
import { ToolSDK } from '@tool.ai/sdk';
// 2. Then, create a toolSDK instance
const toolSDK = new ToolSDK({
  apiKey: process.env.TOOLS_SDK_TOKEN || '',
  // baseURL: 'https://api.toolsdk.ai/v1',
});
// 3. Finally, add toolSD<PERSON>'s tools
const tools = ToolSDK.tools();
```

### AI SDK

We will demonstrate how to integrate toolSDK by following the[Quick Starting - AI SDK](https://v5.ai-sdk.dev/docs/getting-started/nodejs)

```ts
import { openai } from '@ai-sdk/openai';
import { ModelMessage, streamText, tool } from 'ai';
import 'dotenv/config';
import { z } from 'zod';
import * as readline from 'node:readline/promises';

// 1. First, import toolSDK
import { ToolSDK } from '@tool.ai/sdk';

// 2. Then, create a toolSDK instance
const toolSDK = new ToolSDK({
  token: process.env.TOOLS_SDK_TOKEN || '',
  apiEndpoint: 'https://api.toolsdk.ai/v1',
});

const terminal = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const messages: ModelMessage[] = [];

async function main() {
  while (true) {
    const userInput = await terminal.question('You: ');

    messages.push({ role: 'user', content: userInput });

    const result = streamText({
      model: openai('gpt-4o'),
      messages,
      // 3. Finally, add toolSDK's tools
      tools: ToolSDK.tools(),
      // tools: {
      //   weather: tool({
      //     description: 'Get the weather in a location (fahrenheit)',
      //     inputSchema: z.object({
      //       location: z
      //         .string()
      //         .describe('The location to get the weather for'),
      //     }),
      //     execute: async ({ location }) => {
      //       const temperature = Math.round(Math.random() * (90 - 32) + 32);
      //       return {
      //         location,
      //         temperature,
      //       };
      //     },
      //   }),
      // },
    });

    let fullResponse = '';
    process.stdout.write('\nAssistant: ');
    for await (const delta of result.textStream) {
      fullResponse += delta;
      process.stdout.write(delta);
    }
    process.stdout.write('\n\n');

    messages.push({ role: 'assistant', content: fullResponse });
  }
}

main().catch(console.error);
```

### OpenAI SDK

If you are using the [Chat Create - OpenAI SDK](https://platform.openai.com/docs/api-reference/chat/create), please refer to the following integration method:

```ts
import OpenAI from 'openai';
import { ToolSDK } from '@tool.ai/sdk';

// Initialize ToolSDK
const toolSDK = new ToolSDK({
  token: process.env.TOOLS_SDK_TOKEN || '',
  apiEndpoint: 'https://api.toolsdk.ai/v1',
});

const client = new OpenAI();

// Get all tools (automatically adapted to OpenAI format)
// const openaiTools = toolSDK.getOpenAITools();

const completion = await client.chat.completions.create({
  model: 'gpt-4o',
  messages: [
    {
      role: 'developer',
      content: 'You are a helpful assistant.',
    },
    {
      role: 'user',
      content: 'Hello!',
    },
  ],
  tools: stoolSDK.getOpenAITools(['weather']); // Use on-demand loading tools
  // tools = [
  //   {
  //     type: 'function',
  //     name: 'get_weather',
  //     description: 'Get current temperature for a given location.',
  //     parameters: {
  //       type: 'object',
  //       properties: {
  //         location: {
  //           type: 'string',
  //           description: 'The city and state, e.g. San Francisco, CA',
  //         },
  //       },
  //       required: ['location'],
  //       additionalProperties: False,
  //     },
  //   },
  // ],
});

console.log(completion.choices[0].message.content);
```

### Summary

✅ Scenario 1: Integrate specific tools (e.g., weather and currency)

```ts
const result = streamText({
  model: openai('gpt-4o'),
  messages,
  tools: toolSDK.getTools(['weather', 'currency']),
});
```

✅ Scenario 2: Filter tools by tag (e.g., "finance")

```ts
const result = streamText({
  model: openai('gpt-4o'),
  messages,
  tools: toolSDK.getToolsByTag('finance'),
});
```
