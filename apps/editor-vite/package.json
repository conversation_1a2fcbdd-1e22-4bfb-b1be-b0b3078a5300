{"name": "@bika/editor-vite", "private": true, "version": "1.9.0-alpha.15", "type": "module", "scripts": {"dev": "dotenv -e ../web/.env.local -- vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@bika/contents": "workspace:*", "@bika/domains": "workspace:*", "@bika/server": "workspace:*", "@bika/types": "workspace:*", "@bika/ui": "workspace:*", "@hono/node-server": "^1.13.2", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "hono": "^4.6.12", "mime-types": "^2.1.35", "path-browserify": "^1.0.1", "postcss": "^8.4.40", "react": "18.3.1", "react-dom": "18.3.1", "tailwindcss": "^3.4.7"}, "devDependencies": {"@hono/vite-dev-server": "^0.16.0", "@types/node": "^22.0.0", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.14.0", "@typescript-eslint/parser": "^8.14.0", "@vitejs/plugin-react": "^4.3.1", "eslint": "^8", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.7", "typescript": "^5.7.2", "vite": "^5.3.4"}}