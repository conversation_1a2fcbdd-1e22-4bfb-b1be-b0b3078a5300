import { ArticleContent } from '@bika/domains/website/client/article';
import { TreeView } from '@bika/domains/website/client/help/tree';
import type { PageProps } from '@bika/types/content/bo';
import type { Locale } from '@bika/types/i18n/bo';
import { getHelpDetail, getHelps, getStaticParams } from '../loader';
import style from './index.module.css';
import { notFound } from 'next/navigation';

interface Props {
  params: Promise<{
    lang: Locale;
    slugs: string[];
  }>;
}
export default async function FormAppHelpPage(props: Props) {
  const { slugs, lang } = await props.params;
  const pages: PageProps[] = await getHelps(lang);
  // const { tree, modifiedHtml } = generateTocAndModifyHtml(content);

  const post = await getHelpDetail(lang, slugs);

  if (!post) {
    notFound();
  }

  return (
    <TreeView data={pages}>
      <div className={style.content}>
        <ArticleContent
          relatedLinks={undefined}
          templateInfos={undefined}
          locale={'en'}
          html={post.content}
          toc={undefined}
        />
      </div>
    </TreeView>
  );
}
export function generateStaticParams() {
  return getStaticParams();
}

export const dynamicParams = false;
