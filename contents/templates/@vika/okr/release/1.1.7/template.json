{"templateId": "@vika/okr", "name": {"en": "<PERSON><PERSON>", "ja": "<PERSON><PERSON>", "zh-TW": "維格 OKR", "zh-CN": "维格 OKR "}, "description": {"en": "This is a template for managing team quarterly OKRs in Vika, helping teams better set and track goals.", "ja": "これはVikaでチームの四半期のOKRを管理するためのテンプレートで、チームが目標をよりよく設定し、追跡するのを支援します。", "zh-TW": "這是一個用於維格管理團隊季度 OKR 的模板，幫助團隊更好地制定和追踪目標。", "zh-CN": "这是一个用于维格管理团队季度 OKR 的模板，帮助团队更好地制定和追踪目标。"}, "cover": "/assets/template/template-cover-no-image-color.png", "author": "Na<PERSON>a<PERSON>on <<EMAIL>>", "category": "project", "keywords": {"zh-CN": "轮值, 值班, 提醒, 企微, 目标管理, 团队管理, 自动化", "zh-TW": "輪值, 值班, 提醒, 企微, 目標管理, 團隊管理, 自動化", "en": "rotation, duty, reminder, wecom, goal management, team management, automation", "ja": "ローテーション, 当番, リマインダー, 企業微信, ゴール管理, チーム管理, 自動化"}, "personas": {"en": "admin, manager, project manager, HR", "ja": "管理者, マネージャー, プロジェクトマネージャー, 人事", "zh-TW": "管理員, 管理者, 项目经理, 人力资源", "zh-CN": "管理员, 经理, 项目经理, 人力资源"}, "useCases": {"zh-CN": "企业微信自动化提醒, 团队目标管理, OKR制定和评审, 提高团队一致性, 时间节省工具, 团队成员参与度提升, 流程自动化, 团队绩效管理, 目标追踪工具, 高效团队管理, 企业目标管理, 项目目标管理, 团队沟通与协作, 自动提醒和报告, 目标进度跟踪, 管理者监督工具, 任务分配和跟进, 团队绩效分析, 目标达成度评估, 周期性目标设定, 个人和团队目标对齐, 绩效考核和奖励, 企业战略规划, 团队协同效能提升", "en": "WeCom automation reminder, team goal management, OKR formulation and review, improve team consistency, time-saving tools, increase team member participation, process automation, team performance management, goal tracking tools, efficient team management, enterprise goal management, project goal management, team communication and collaboration, automatic reminders and reports, goal progress tracking, manager supervision tools, task assignment and follow-up, team performance analysis, goal achievement evaluation, periodic goal setting, personal and team goal alignment, performance appraisal and rewards, enterprise strategic planning, team collaborative efficiency improvement", "ja": "企業微信の自動化リマインダー, チームの目標管理, OKRの策定とレビュー, チームの一貫性向上, 時間節約ツール, チームメンバーの参加度向上, プロセスの自動化, チームのパフォーマンス管理, 目標追跡ツール, 効率的なチーム管理, 企業の目標管理, プロジェクトの目標管理, チームのコミュニケーションと協力, 自動リマインダーとレポート, 目標の進捗状況の追跡, マネージャーの監督ツール, タスクの割り当てとフォローアップ, チームのパフォーマンス分析, 目標の達成度評価, 周期的な目標設定, 個人とチームの目標の整合, パフォーマンス評価と報酬, 企業の戦略的計画, チームの協力効率向上", "zh-TW": "企業微信自動提醒, 團隊目標管理, OKR制定和審核, 提高團隊一致性, 節省時間工具, 提高團隊成員參與度, 流程自動化, 團隊績效管理, 目標追蹤工具, 高效團隊管理, 企業目標管理, 項目目標管理, 團隊溝通與協作, 自動提醒和報告, 目標進度追蹤, 管理者監督工具, 任務分配和跟進, 團隊績效分析, 目標達成度評估, 週期性目標設定, 個人和團隊目標對齊, 績效考核和獎勵, 企業戰略規劃, 團隊協同效能提升"}, "visibility": "PRIVATE", "schemaVersion": "v1", "version": "1.1.7", "resources": [{"resourceType": "DATABASE", "templateId": "okr_database", "name": {"en": "OKR Database", "zh-CN": "OKR 数据库"}, "description": {"en": "Team members' OKRs", "zh-CN": "存储团队成员的季度 OKR。"}, "permissions": [], "type": "DATUM", "views": [{"templateId": "all_okr_view", "name": "所有 OKR", "type": "TABLE"}, {"templateId": "my_okr_view", "name": "我的 OKR", "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "okr_creator", "operator": "Is", "fieldType": "CREATED_BY", "value": ["Self"]}]}, "type": "TABLE"}, {"templateId": "okr_review_view", "name": "我负责评审的 OKR", "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "okr_align_superior", "operator": "Is", "fieldType": "MEMBER", "value": ["Self"]}]}, "type": "TABLE"}, {"description": {"zh-CN": "用来控制填写OKR任务的结构", "en": "Used to control the structure of the task of filling in OKR", "zh-TW": "用來控制填寫OKR任務的結構", "ja": "OKRの記入タスクの構造を制御するために使用されます"}, "templateId": "submit_okr_view", "name": "提交OKR视图", "type": "TABLE", "fields": [{"templateId": "okr_quarter"}, {"templateId": "okr_creator"}, {"templateId": "okr_review"}, {"templateId": "okr_details"}, {"templateId": "okr_align_superior"}, {"templateId": "okr_self_score"}, {"templateId": "okr_superior_score", "hidden": true}, {"templateId": "okr_attachment"}, {"templateId": "okr_submit_time"}]}], "fields": [{"templateId": "okr_quarter", "name": "季度", "description": "OKR 所属的季度", "required": true, "type": "SINGLE_SELECT", "property": {"options": [{"templateId": "2024q1", "name": "2024 Q1", "color": "blue"}, {"templateId": "2024q2", "name": "2024 Q2", "color": "green"}, {"templateId": "2024q3", "name": "2024 Q3", "color": "deepPurple"}, {"templateId": "2024q4", "name": "2024 Q4", "color": "red"}]}}, {"templateId": "okr_creator", "name": "提交人", "type": "CREATED_BY"}, {"templateId": "okr_review", "name": "What is your previous quarter progress? Review it.", "description": "\n请列出本季度的新OKR和Milestone，示例：\n\n研发工程师R&D Engineer的OKR示例：\n- O: sprint: 确保AI服务AIServer等开发和每周迭代有Github Contribution\n  - KR: 完成数据分析师、QA机器人、GPT-4 Chat机器人、Form机器人、考试机器人相关Issue > 3\n  - KR: 每周Github contribution > 5 & develop分支PR > 1 & commit > 2 & 每周Github Issues close > 2\n  - KR: 日常关注和解答开源社区和客户用户提出的问题 相关 Issue >= 3\n- O: feat: AI Prompting和功能实现\n  - KR: AIServer的AIForm、Chatbot、QABot、AnalystBot的功能实现\n  - KR: 基于DataBus对URL、Attachment、Refiner进行数据训练\n  - KR: 实现PDF、TableBundle、TextBundle多模态数据的Embedding和产品需求实现\n\nProduct Manager、Marketing、Sales的OKR示例：\n\n- O1: 提升产品的市场份额和用户满意度\n    - KR1: 增加产品的市场份额至行业前三名\n    - KR2: 提升用户满意度调查得分至90分以上\n- O1: 实现销售目标并增加客户满意度\n    - KR1: 达到季度销售额目标，增长率不低于20%\n    - KR2: 提高客户满意度调查得分至85分以上\n- O2: 拓展新客户和市场份额\n    - KR1: 获得10个新客户并完成首次交易\n    - KR2: 将产品销售到2个新市场，并建立长期合作关系\n- O1: 提升品牌知名度和市场影响力\n    - KR1: 增加社交媒体关注者数至10万人\n    - KR2: 提高品牌在行业内的知名度，获得5次媒体报道\n- O2: 改善市场营销效果和转化率\n    - KR1: 提高营销活动的转化率至5%\n    - KR2: 优化网站流量和用户体验，将跳出率降低至50%以下\n\nMilestone:\n* 10月30日: xxxx\n* 11月30日: yyyy\n\n实际OKR根据组织的具体情况和目标进行定制。\n          ", "required": true, "type": "LONG_TEXT"}, {"templateId": "okr_details", "name": "What is your this new quarter OKRs and Milestones?", "description": "\n请列出本季度的新OKR和Milestone，示例：\n\n研发工程师R&D Engineer的OKR示例：\n- O: sprint: 确保AI服务AIServer等开发和每周迭代有Github Contribution\n  - KR: 完成数据分析师、QA机器人、GPT-4 Chat机器人、Form机器人、考试机器人相关Issue > 3\n  - KR: 每周Github contribution > 5 & develop分支PR > 1 & commit > 2 & 每周Github Issues close > 2\n  - KR: 日常关注和解答开源社区和客户用户提出的问题 相关 Issue >= 3\n- O: feat: AI Prompting和功能实现\n  - KR: AIServer的AIForm、Chatbot、QABot、AnalystBot的功能实现\n  - KR: 基于DataBus对URL、Attachment、Refiner进行数据训练\n  - KR: 实现PDF、TableBundle、TextBundle多模态数据的Embedding和产品需求实现\n\nProduct Manager、Marketing、Sales的OKR示例：\n\n- O1: 提升产品的市场份额和用户满意度\n    - KR1: 增加产品的市场份额至行业前三名\n    - KR2: 提升用户满意度调查得分至90分以上\n- O1: 实现销售目标并增加客户满意度\n    - KR1: 达到季度销售额目标，增长率不低于20%\n    - KR2: 提高客户满意度调查得分至85分以上\n- O2: 拓展新客户和市场份额\n    - KR1: 获得10个新客户并完成首次交易\n    - KR2: 将产品销售到2个新市场，并建立长期合作关系\n- O1: 提升品牌知名度和市场影响力\n    - KR1: 增加社交媒体关注者数至10万人\n    - KR2: 提高品牌在行业内的知名度，获得5次媒体报道\n- O2: 改善市场营销效果和转化率\n    - KR1: 提高营销活动的转化率至5%\n    - KR2: 优化网站流量和用户体验，将跳出率降低至50%以下\n\nMilestone:\n* 10月30日: xxxx\n* 11月30日: yyyy\n\n实际OKR根据组织的具体情况和目标进行定制。\n          ", "required": true, "validators": [{"timing": "ON_SUBMIT", "level": "WARNING", "type": "AI_PROMPT", "prompt": "Your task is to evaluate the OKR text in the text. All that is required is that the objectives and key results are outlined. Key results should be related to the objective.\nIMPORTANT: Specificity and measurability are not require.\nHere are four examples of OKR evaluations:\n\nExample 1:\nObjective: 提高公司产品在市场中的竞争力\nKey Result 1: 在下个季度末，产品的市场份额增加 5%\nKey Result 2: 客户满意度评分从 80% 提高到 90%\n\nResponse:\n{\n  \"success\": true\n}\n\nExample 2:\nObjective: 扩大品牌影响力\nKey Result 1: 增加广告投放\nKey Result 2: 提升社交媒体互动次数\n\nResponse:\n{\n  \"success\": false,\n    \"message\": \"目标虽然方向正确，但不够具体。关键结果 '增加广告投放' 缺乏可量化的指标，例如没有指定增加的比例或具体数值。'提升社交媒体互动次数' 需要明确提升的目标百分比或具体数值才能量化。此外，两个关键结果都需要更明确地说明如何支持品牌影响力的扩大。\"\n}\n\nExample 3:\nObjective: Increase the efficiency of our customer service department\nKey Result 1: Reduce average call resolution time by 2 minutes\nKey Result 2: Achieve a customer satisfaction score of 95%\n\nResponse:\n{\n  \"success\": true\n}\n\nExample 4:\nObjective: Enhance online presence\nKey Result 1: Launch a new marketing campaign\nKey Result 2: Increase followers on social media platforms\n\nResponse:\n{\n  \"success\": false,\n  \"message\": \"The objective 'Enhance online presence' is directionally sound but lacks specificity. The key result 'Launch a new marketing campaign' is not quantifiable without metrics to define success. 'Increase followers on social media platforms' needs a specific target for the increase to be measurable. Additionally, both key results require clearer linkage to how they will enhance the online presence.\"\n}"}], "type": "LONG_TEXT"}, {"templateId": "okr_align_superior", "name": "上级领导", "description": "该成员的上级领导，用于提醒上级 Review OKR", "required": true, "type": "MEMBER", "property": {"many": false}}, {"templateId": "okr_self_score", "name": "自评分", "description": "对自己本季度 OKR 的评分", "required": true, "type": "SINGLE_SELECT", "property": {"options": [{"templateId": "0", "name": "0分：未达成目标，态度有问题", "color": "red"}, {"templateId": "0.1", "name": "0.1分：未达成目标", "color": "teal"}, {"templateId": "0.2", "name": "0.2分：目标达成效果较差，未及时调整", "color": "green"}, {"templateId": "0.3", "name": "0.3分：目标达成效果较差", "color": "yellow"}, {"templateId": "0.4", "name": "0.4分：目标达成效果一般，还需努力", "color": "orange"}, {"templateId": "0.5", "name": "0.5分：目标达成效果一般", "color": "blue"}, {"templateId": "0.6", "name": "0.6分：目标基本达成", "color": "indigo"}, {"templateId": "0.7", "name": "0.7分：目标达成效果不错", "color": "pink"}, {"templateId": "0.8", "name": "0.8分：100%达成目标", "color": "tangerine"}, {"templateId": "0.9", "name": "0.9分：110%达成目标", "color": "deepPurple"}, {"templateId": "1", "name": "1分：超出预期", "color": "deepPurple"}]}}, {"templateId": "okr_superior_score", "name": "上级评分", "description": "上级对你本季度 OKR 的评分", "type": "SINGLE_SELECT", "property": {"options": [{"templateId": "0", "name": "0分：未达成目标，态度有问题", "color": "red"}, {"templateId": "0.1", "name": "0.1分：未达成目标", "color": "teal"}, {"templateId": "0.2", "name": "0.2分：目标达成效果较差，未及时调整", "color": "green"}, {"templateId": "0.3", "name": "0.3分：目标达成效果较差", "color": "yellow"}, {"templateId": "0.4", "name": "0.4分：目标达成效果一般，还需努力", "color": "orange"}, {"templateId": "0.5", "name": "0.5分：目标达成效果一般", "color": "blue"}, {"templateId": "0.6", "name": "0.6分：目标基本达成", "color": "indigo"}, {"templateId": "0.7", "name": "0.7分：目标达成效果不错", "color": "pink"}, {"templateId": "0.8", "name": "0.8分：100%达成目标", "color": "tangerine"}, {"templateId": "0.9", "name": "0.9分：110%达成目标", "color": "deepPurple"}, {"templateId": "1", "name": "1分：超出预期", "color": "deepPurple"}]}}, {"templateId": "okr_attachment", "name": "附件", "type": "ATTACHMENT"}, {"templateId": "okr_submit_time", "name": "提交时间", "type": "CREATED_TIME", "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}}]}, {"resourceType": "AUTOMATION", "templateId": "okr_submit_automation", "name": "OKR Review", "description": "当成员提交 OKR 时提醒上级 Review OKR", "permissions": [], "status": "ACTIVE", "triggers": [{"triggerType": "RECORD_CREATED", "templateId": "okr_created", "description": "当成员提交 OKR 时触发", "input": {"type": "DATABASE", "databaseTemplateId": "okr_database"}}], "actions": [{"templateId": "review_okr_mission", "description": "提醒上级 Review OKR，并打上评分", "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "UPDATE_RECORD", "name": "<%= _triggers[0].record.cells.okr_creator.value[0] %>的 OKR 已经提交了，需要你审阅", "description": "请点击「审核 OKR」 按钮，并在「上级评分」字段中进行打分，如果过程中有问题可以在记录详情评论时 @ 相关成员", "canReject": false, "canCompleteManually": false, "canTransfer": false, "afterActions": [{"templateId": "okr_review_success_notify", "description": "通知提交人查看上级评分", "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "REVIEW_RECORD", "name": "OKR 评分结果通知", "description": "<%= record.cells.okr_creator.value[0] %>，你的 OKR 已经被审阅，点击查看评分结果", "to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= record.cells.okr_creator.data[0] %>"], "withVariables": true}], "buttonText": "确认评分结果", "databaseTemplateId": "okr_database", "recordId": "<%= record.id %>"}}}], "assignType": "DEDICATED", "to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= _triggers[0].record.cells.okr_align_superior.data[0] %>"], "withVariables": true}], "reminders": [{"name": "Review OKR", "description": "请检查 <%= _triggers[0].record.cells.okr_creator.value[0] %> 的 OKR，并完成评分", "scheduler": {"repeat": {"every": {"type": "DAY", "interval": 1}}, "timezone": "AUTO", "datetime": {"hour": 10, "minute": 30, "type": "TODAY"}}, "to": []}], "buttonText": "审核 OKR", "databaseTemplateId": "okr_database", "recordId": "<%= _triggers[0].record.id %>"}}}]}, {"resourceType": "AUTOMATION", "templateId": "okr_review_automation", "name": "每周 Review OKR", "description": "提醒团队成员 Review OKR", "permissions": [], "status": "ACTIVE", "triggers": [{"triggerType": "SCHEDULER", "templateId": "okr_review_scheduler", "description": "每周一 00:00 触发", "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["MON"]}}, "timezone": "AUTO", "datetime": {"hour": 0, "minute": 0, "type": "TODAY"}}}}], "actions": [{"templateId": "find_quarter_okr_for_weekly_review", "description": "向所有本季度创建过 OKR 的成员发送 OKR Review 任务", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "databaseTemplateId": "okr_database", "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "okr_submit_time", "operator": "Is", "fieldType": "CREATED_TIME", "value": ["ThisQuarter"]}]}}, "actions": [{"templateId": "review_okr_mission", "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "COMMENT_RECORD", "name": "每周 OKR Review", "description": "请点击 「评论 OKR」跳转到你填写的 OKR 后评论你上周的 OKR 进度", "assignType": "DEDICATED", "to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= _record.cells.okr_creator.data[0] %>"], "withVariables": true}], "buttonText": "评论 OKR", "databaseTemplateId": "okr_database", "recordId": "<%= _record.id %>"}}}]}]}, {"resourceType": "AUTOMATION", "templateId": "okr_write_automation", "name": "季度末提醒填写下个季度 OKR", "description": "在每个季度的最后一个月25号提醒团队成员填写下个季度的 OKR", "permissions": [], "status": "ACTIVE", "triggers": [{"triggerType": "SCHEDULER", "templateId": "okr_write_scheduler", "description": "季度末月 25 日 10:00 开始触发", "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "MONTH", "interval": 3, "monthDays": [25]}}, "timezone": "AUTO", "datetime": {"hour": 10, "type": "QUARTER", "quarter": 0, "monthOfQuarter": 3, "dayOfMonth": 25}}}}], "actions": [{"templateId": "update_next_quarter_okr_mission", "description": "创建填写下个季度 OKR 的任务", "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "CREATE_RECORD", "name": "<%= _to.name %>, 请填写下个季度的 OKR", "description": "请尽快点击「填写 OKR」 按钮进行 OKR 的录入和提交，如有问题可以联系 HR 或相关管理员", "canReject": false, "canCompleteManually": false, "canTransfer": false, "assignType": "DEDICATED", "to": [{"type": "UNIT_ROLE", "roleTemplateId": "role_okr_member"}, {"type": "CURRENT_OPERATOR"}], "buttonText": "填写 OKR", "databaseTemplateId": "okr_database", "viewTemplateId": "submit_okr_view"}}}]}, {"resourceType": "AUTOMATION", "templateId": "okr_report_automation", "name": "季度末生成 OKR 报告", "description": "在每个季度结束时生成 OKR 报告，以便团队回顾和展望", "permissions": [], "status": "ACTIVE", "triggers": [{"triggerType": "SCHEDULER", "templateId": "okr_report_scheduler", "description": "季度末最后一天 10:00触发", "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "MONTH", "interval": 3, "monthDays": ["LAST_DAY"]}}, "timezone": "AUTO", "datetime": {"hour": 10, "type": "QUARTER", "quarter": 0, "monthOfQuarter": 3, "dayOfMonth": "LAST_DAY"}}}}], "actions": [{"templateId": "find_quarter_okr_review", "description": "查找所有在本季度创建的 OKR", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "databaseTemplateId": "okr_database", "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "okr_submit_time", "operator": "Is", "fieldType": "CREATED_TIME", "value": ["ThisQuarter"]}]}}}, {"templateId": "send_quarter_okr_report", "description": "整理并生成 OKR 报告", "actionType": "SEND_REPORT", "input": {"to": [{"type": "UNIT_ROLE", "roleTemplateId": "role_okr_member"}, {"type": "CURRENT_OPERATOR"}], "type": "MARKDOWN", "subject": "⭐️ <%\n                const date = new Date();\n                const month = date.getMonth();\n                let quarter = '';\n                if (month >= 0 && month <= 2) {\n                  quarter = 'Q1';\n                } else if (month >= 3 && month <= 5) {\n                  quarter = 'Q2';\n                } else if (month >= 6 && month <= 8) {\n                  quarter = 'Q3';\n                } else if (month >= 9 && month <= 11) {\n                  quarter = 'Q4';\n                }\n                print(date.getFullYear() + quarter);\n              %> OKR Review", "markdown": "\n<%\n  const date = new Date();\n  const month = date.getMonth();\n  let quarter = '';\n  if (month >= 0 && month <= 2) {\n    quarter = 'Q1';\n  } else if (month >= 3 && month <= 5) {\n    quarter = 'Q2';\n  } else if (month >= 6 && month <= 8) {\n    quarter = 'Q3';\n  } else if (month >= 9 && month <= 11) {\n    quarter = 'Q4';\n  }\n  print(date.getFullYear() + quarter);\n%> 季度大家的 OKR 完成情况如下：\n\n<% _actions[0].records.forEach(function(okr_item) { %>\n  <h1><%= okr_item.cells.okr_creator.value[0] %></h1>\n  <h2>上季度回顾</h2>\n  <%= okr_item.cells.okr_review.value %>\n  <h2>本季度 OKR</h2>\n  <%= okr_item.cells.okr_details.value %>\n  <br/>\n<% }); %>          \n\n希望大家在下个季度继续努力，也欢迎大家对 OKR 进行反馈和建议。\n"}}]}], "initMissions": [{"type": "READ_TEMPLATE_README", "templateId": "@vika/okr", "name": "💡OKR 模板使用须知", "forcePopup": true, "assignType": "DEDICATED", "to": [{"type": "CURRENT_OPERATOR"}], "time": 10, "beforeText": {"title": {"zh-CN": "恭喜您，模板已经安装完毕", "zh-TW": "恭喜您，模板已經安裝完畢", "ja": "おめでとうございます、テンプレートのインストールが完了しました", "en": "Congratulations, the template has been installed"}, "description": {"zh-CN": "下一步请您花几分钟阅读模板的的使用教程。", "zh-TW": "下一步請您花幾分鐘閱讀模板的使用教程。", "en": "Next, please take a few minutes to read the tutorial on how to use the template.", "ja": "次に、テンプレートの使用方法についてのチュートリアルを数分間お読みください。"}}, "redirect": {"type": "MY_MISSIONS"}, "wizardGuideId": "COMMON_MY_TODO_TUTORIAL"}, {"type": "INVITE_MEMBER", "name": "💡OKR 系统初始化任务 1：请邀请需要填写 OKR 的成员加入空间站", "description": "你需要邀请需要填写 OKR 的成员加入空间站，可以设置角色为「OKR 参与者」然后创建邀请链接", "canCompleteManually": true, "assignType": "SHARE", "to": [{"type": "ADMIN"}], "roleTemplateId": "role_okr_member"}, {"type": "REDIRECT_SPACE_NODE", "name": "💡OKR 系统初始化任务 2：试试手动发起 OKR 收集", "description": "每季度最后一个月的 25 号上午10点，BIKA 会自动创建一个收集 OKR 的任务。你也可以尝试一下手动发起 OKR 收集任务~提示：在自动化界面底部点击“手动触发”可以预览自动化的效果，点击“启动/停用”可以启用或停用自动化。", "canCompleteManually": true, "assignType": "SHARE", "to": [{"type": "ADMIN"}], "nodeTemplateId": "okr_write_automation", "wizardGuideId": "OKR_INIT_MISSION_MANUAL_TRIGGER"}], "newMemberJoinMissions": [{"type": "REDIRECT_SPACE_NODE", "name": "新手任务：欢迎加入空间站！请查看 OKR 系统的介绍，了解如何使用 OKR 系统，这将对后续的使用很有帮助", "description": "## OKR 系统介绍\n\nVika OKR 管理模板旨在协助团队高效运作，支持 OKR（目标与关键结果）工作法的全面实施，包括季度目标设定、进度跟踪和回顾。通过这个模板，您可以轻松组织和记录 OKR 流程，提升团队协作效率。\n\n## 采用 Vika OKR 工作法能够带来哪些价值？\n\n使用 Vika OKR 管理模板可以实现\n\n- **自动化收集 OKR**：定时通知团队成员填写 OKR 报告，简化 OKR 数据收集过程。\n- **自动化 OKR 审核流程**：提交 OKR 后自动提醒上级审核，确保 OKR 的质量和对齐。\n- **定期检查 OKR 进展**：每周和每月自动提醒成员检查和更新 OKR 进展，保持目标的动态管理。\n- **季度报告自动生成**：自动生成季度末 OKR 完成情况报告，助力团队回顾和规划。\n\n## 如何使用 Vika OKR 管理模板？\n\n- 如果你是新加入的同学，我们建议你可以先在「OKR Database」中查看其他同学的 OKR，了解 OKR 的设定和填写方式。\n\n- 每个季度我们都会自动提醒成员填写 OKR，你可以在 OKR Database 下「我的 OKR」视图中查看和更新您的 OKR。同时，你也可以在「我负责评审的 OKR」视图中查看需要审核的 OKR。\n\n- 如果你是团队的领导，你可以在「所有 OKR」视图中查看所有成员的 OKR，以便及时了解团队的目标和进展。", "canCompleteManually": true, "assignType": "DEDICATED", "to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= member.id %>"], "withVariables": true, "requireRoleTemplateId": "role_okr_member"}], "nodeTemplateId": "okr_database"}], "presetUnits": [{"type": "ROLE", "name": "OKR 参与者", "templateId": "role_okr_member"}]}