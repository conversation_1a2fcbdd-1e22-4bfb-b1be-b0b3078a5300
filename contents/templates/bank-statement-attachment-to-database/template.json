{"templateId": "bank-statement-attachment-to-database", "visibility": "WAITING_LIST", "name": {"zh-CN": "银行对账单附件转数据库", "zh-TW": "銀行對賬單附件轉數據庫", "ja": "銀行の明細書添付ファイルをデータベースに変換", "en": "Bank Statement Attachment to Database"}, "description": {"zh-CN": "将手工上传的银行对账单PDF附件，通过图像识别技术，提取总支出和股票数据，并生成数据记录到Bika数据表中", "zh-TW": "將手工上傳的銀行對賬單PDF附件，透過圖像識別技術，提取總支出和股票數據，並生成數據記錄到Bika數據表中", "ja": "手動でアップロードされた銀行の明細書PDF添付ファイルを、画像認識技術を用いて、総支出と株式データを抽出し、Bikaデータベースにデータレコードを生成する", "en": "Manually upload a PDF attachment of the bank statement, use image recognition technology to extract the total expenditure and stock data, and generate data records into the Bika database"}, "cover": "/assets/template/template-cover-bank-statement-attachment-to-database.png", "author": "", "category": ["operation", "automation"], "keywords": {"zh-CN": "银行对账单, 图像识别, 数据记录, Bika 数据表", "zh-TW": "銀行對賬單, 圖像識別, 資料記錄, Bika 資料表", "ja": "銀行の明細書, 画像認識, データレコード, Bikaデータベース", "en": "Bank Statement, Image Recognition, Data Record, Bika Database"}, "personas": {"zh-CN": "财务人员, 数据管理员", "zh-TW": "財務人員, 資料管理員", "ja": "財務担当者, データ管理者", "en": "Financial Staff, Data Administrator"}, "useCases": {"zh-CN": "将银行对账单附件转化为数据记录，便于财务分析和管理", "zh-TW": "將銀行對賬單附件轉化為資料記錄，便於財務分析和管理", "ja": "銀行の明細書添付ファイルをデータレコードに変換し、財務分析と管理を容易にする", "en": "Convert bank statement attachments into data records for easy financial analysis and management"}, "schemaVersion": "v1", "ignoreChanged": [], "version": "1.0.5", "resources": [{"resourceType": "AUTOMATION", "templateId": "bank-statement-processing", "name": {"zh-CN": "银行对账单处理自动化", "zh-TW": "銀行對賬單處理自動化", "ja": "銀行の明細書処理自動化", "en": "Bank Statement Processing Automation"}, "description": {"zh-CN": "处理手工上传的银行对账单PDF附件，进行图像识别并将数据记录到数据库", "zh-TW": "處理手動上傳的銀行對賬單PDF附件，進行圖像識別並將資料記錄到資料庫", "ja": "手動でアップロードされた銀行の明細書PDF添付ファイルを処理し、画像認識を行い、データをデータベースに記録する", "en": "Process the manually uploaded PDF attachment of the bank statement, perform image recognition and record the data into the database"}, "status": "INACTIVE", "triggers": [{"triggerType": "FORM_SUBMITTED", "templateId": "bank-statement-upload", "description": {"zh-CN": "当银行对账单附件上传表单提交时触发", "zh-TW": "當銀行對賬單附件上傳表單提交時觸發", "ja": "銀行の明細書添付ファイルのアップロードフォームが提出されたときにトリガー", "en": "Triggered when the bank statement attachment upload form is submitted"}, "input": {"type": "FORM", "formTemplateId": "bank-statement-upload-form"}}], "actions": [{"templateId": "image-recognition", "description": {"zh-CN": "对上传的银行对账单PDF进行图像识别", "zh-TW": "對上傳的銀行對賬單PDF進行圖像識別", "ja": "アップロードされた銀行の明細書PDFに対して画像認識を行う", "en": "Perform image recognition on the uploaded bank statement PDF"}, "actionType": "AI_SUMMARY", "input": {"type": "AI_SUMMARY", "prompt": "从上传的银行对账单PDF中识别出总支出和股票数据"}}, {"templateId": "data-record-creation", "description": {"zh-CN": "将图像识别结果生成数据记录并保存到Bika数据表", "zh-TW": "將圖像識別結果生成資料記錄並保存到Bika資料表", "ja": "画像認識結果をデータレコードに生成し、Bikaデータベースに保存する", "en": "Generate data records from the image recognition results and save them to the Bika database"}, "actionType": "CREATE_RECORD", "input": {"type": "RECORD_BODY", "fieldKeyType": "ID", "databaseTemplateId": "bank-statement-database", "data": {"total_expenditure": "<%= _actions.image-recognition.summary.totalExpenditure %>", "stock_data": "<%= _actions.image-recognition.summary.stockData %>"}}}]}, {"resourceType": "DATABASE", "templateId": "bank-statement-database", "name": {"zh-CN": "银行对账单数据库", "zh-TW": "銀行對賬單資料庫", "ja": "銀行の明細書データベース", "en": "Bank Statement Database"}, "description": {"zh-CN": "存储银行对账单数据", "zh-TW": "存儲銀行對賬單資料", "ja": "銀行の明細書データを保存する", "en": "Store bank statement data"}, "databaseType": "DATUM", "views": [{"templateId": "all-bank-statements", "name": {"zh-CN": "所有银行对账单", "zh-TW": "所有銀行對賬單", "ja": "すべての銀行の明細書", "en": "All Bank Statements"}, "type": "TABLE"}], "fields": [{"templateId": "total_expenditure", "name": {"zh-CN": "总支出", "zh-TW": "總支出", "ja": "総支出", "en": "Total Expenditure"}, "property": {}, "required": true, "type": "NUMBER"}, {"templateId": "stock_data", "name": {"zh-CN": "股票数据", "zh-TW": "股票資料", "ja": "株式データ", "en": "Stock Data"}, "required": true, "type": "LONG_TEXT"}], "records": []}], "initMissions": [], "newMemberJoinMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}