{"templateId": "auto-send-pay-slips", "name": {"en": "Auto Send Pay Slips", "zh-CN": "自动发送工资单"}, "description": {"en": "By utilizing this automatic pay stub distribution template, companies can achieve automation, efficiency, and standardization in payroll distribution tasks, automatically generate payroll reports via AI every month, enhancing the overall level of human resource management and providing strong support for the development of the business.\n", "zh-CN": "通过使用这个自动发送工资单模板，企业可以实现工资发放工作的自动化、高效化和规范化，每月自动通过 AI 生成发薪报告，提升整体的人力资源管理水平，为企业的发展提供有力支持。"}, "cover": "/assets/template/auto-send-pay-slips/auto-send-pay-slips.png", "author": "<PERSON> <<EMAIL>>", "category": ["operation", "automation"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.2", "resources": [{"resourceType": "FORM", "templateId": "fom52EyJR0YxSLXe3uFTvyPe", "name": {"en": "Onboarding Form", "zh-CN": "入职信息填写"}, "brandLogo": {"type": "UNSPLASH", "url": "https://images.unsplash.com/photo-1734077457229-8017ee15c7c7?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb"}, "formType": "DATABASE", "databaseTemplateId": "datiaFKRnTZYjr1P8CffmGET", "metadata": {"type": "VIEW", "viewTemplateId": "viwmbxiWB1NsoBvRIDYkLf8E"}}, {"resourceType": "DATABASE", "templateId": "datiaFKRnTZYjr1P8CffmGET", "name": {"en": "Employee Roster", "zh-CN": "员工名册"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwavdJjbow8pntL8OAVAI10", "name": {"en": "Employee handbook", "zh-CN": "职工大全"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldKKCQWN620gBfKrFVtksTs", "hidden": false}, {"templateId": "fldHFnN5gAR7COobF99m8mfG", "hidden": false}, {"templateId": "fldGLPp19IrYKeD5sjsoyr3E", "hidden": false}, {"templateId": "fldakpR2jTsb8OdrqBnYfaN9", "hidden": false, "width": 150}, {"templateId": "fldIyFFDNSQZ2KkWr9sJwmOY", "hidden": false}, {"templateId": "fld7NmjgEZBTtim15tdr79Om", "hidden": false}, {"templateId": "fldaczzyFkytwo9cp9IFEEKn", "hidden": false}, {"templateId": "fldYYeyGUutjiCZA8eTMCMBg", "hidden": false, "width": 227}, {"templateId": "fldfMghvuVBpi9SgdjRZ7YBv", "hidden": false}, {"templateId": "fldXUZUUZwJBVY91eNVeogFC", "hidden": false}]}, {"type": "TABLE", "templateId": "viwmbxiWB1NsoBvRIDYkLf8E", "name": {"en": "Entry form entry", "zh-CN": "入职表单录入"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldKKCQWN620gBfKrFVtksTs", "hidden": false}, {"templateId": "fldHFnN5gAR7COobF99m8mfG", "hidden": true}, {"templateId": "fldGLPp19IrYKeD5sjsoyr3E", "hidden": false}, {"templateId": "fldakpR2jTsb8OdrqBnYfaN9", "hidden": false, "width": 150}, {"templateId": "fldIyFFDNSQZ2KkWr9sJwmOY", "hidden": false}, {"templateId": "fld7NmjgEZBTtim15tdr79Om", "hidden": true}, {"templateId": "fldfMghvuVBpi9SgdjRZ7YBv", "hidden": false}, {"templateId": "fldYYeyGUutjiCZA8eTMCMBg", "hidden": true}, {"templateId": "fldXUZUUZwJBVY91eNVeogFC", "hidden": false}, {"templateId": "fldaczzyFkytwo9cp9IFEEKn", "hidden": true}], "groups": []}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldKKCQWN620gBfKrFVtksTs", "privilege": "TYPE_EDIT", "name": {"en": "Name", "zh-CN": "姓名"}, "primary": true}, {"type": "SINGLE_TEXT", "templateId": "fldHFnN5gAR7COobF99m8mfG", "privilege": "NAME_EDIT", "name": {"en": "Job number", "zh-CN": "工号"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldGLPp19IrYKeD5sjsoyr3E", "privilege": "NAME_EDIT", "name": {"en": "Position", "zh-CN": "职位"}, "property": {"options": [{"id": "optqQQkHhXiaBcKuz2uOTyMM", "name": "Product manager", "color": "deepPurple"}, {"id": "opt5LLHI3E94lB6Q6287Jdzw", "name": "Senior test engineer", "color": "indigo"}, {"id": "optiM96SQwHitgoIsb4wCIbJ", "name": "Business architect", "color": "blue"}, {"id": "optuELcsAJ140AqvC8quXAjM", "name": "Content operation", "color": "teal"}, {"id": "optaPhi6p1hu2rSIObfrIqrR", "name": "Hiring manager", "color": "green"}, {"id": "optrRuvlwseQJPGyZfWUX1Qt", "name": "Operation and maintenance", "color": "yellow"}, {"id": "opt1jSZL4RahS9DFtNsQmfC7", "name": "UX", "color": "orange"}, {"id": "optNkH6SD9D2gOOai32o1Z9l", "name": "CFO", "color": "tangerine"}, {"id": "optHvsy63Lg3qyMnQi3aybgT", "name": "Business manager", "color": "pink"}], "defaultValue": ""}, "primary": false}, {"type": "PHONE", "templateId": "fldakpR2jTsb8OdrqBnYfaN9", "privilege": "NAME_EDIT", "name": {"en": "Contact number", "zh-CN": "联系电话"}, "primary": false}, {"type": "EMAIL", "templateId": "fldIyFFDNSQZ2KkWr9sJwmOY", "privilege": "NAME_EDIT", "name": {"en": "Mailbox", "zh-CN": "邮箱"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fld7NmjgEZBTtim15tdr79Om", "privilege": "NAME_EDIT", "name": {"en": "Status", "zh-CN": "状态"}, "description": {"en": "", "zh-CN": ""}, "property": {"options": [{"id": "optwKtbPKGORhdmo8qxQaCWV", "name": "Full-time", "color": "deepPurple"}, {"id": "optNUoTTaZ78OmbkRlbwCq73", "name": "Part-time", "color": "indigo"}, {"id": "opt7PffIUvU38vYawg1tkvhl", "name": "Intern", "color": "blue"}], "defaultValue": ""}, "primary": false}, {"type": "LINK", "templateId": "fldaczzyFkytwo9cp9IFEEKn", "privilege": "NAME_EDIT", "name": {"en": "Payroll", "zh-CN": "工资表"}, "property": {"foreignDatabaseTemplateId": "datfkeWD29MrlhVcpuGHK0ke", "brotherFieldTemplateId": "fldpouyK8HRtvLCqhHdLh7Dt"}, "primary": false}, {"type": "CURRENCY", "templateId": "fldYYeyGUutjiCZA8eTMCMBg", "privilege": "NAME_EDIT", "name": {"en": "Monthly Salary", "zh-CN": "月薪"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "¥", "symbolAlign": "left"}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldfMghvuVBpi9SgdjRZ7YBv", "privilege": "NAME_EDIT", "name": {"en": "Bank Card Number", "zh-CN": "银行卡卡号"}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldXUZUUZwJBVY91eNVeogFC", "privilege": "NAME_EDIT", "name": {"en": "Bank of Deposit", "zh-CN": "开户行"}, "primary": false}], "records": [{"templateId": "recz1lhRiW3DhviEGEjaaUfF", "data": {"fld7NmjgEZBTtim15tdr79Om": ["optwKtbPKGORhdmo8qxQaCWV"], "fldGLPp19IrYKeD5sjsoyr3E": ["optaPhi6p1hu2rSIObfrIqrR"], "fldHFnN5gAR7COobF99m8mfG": "1005", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "jing xiao ling", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": 6600, "fldaczzyFkytwo9cp9IFEEKn": ["recWiqQQOk7wU7mLvjFTRiNX", "recqKuBsWhRucqK7n8TEq26n"], "fldakpR2jTsb8OdrqBnYfaN9": "***********", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}, "values": {"fld7NmjgEZBTtim15tdr79Om": ["Full-time"], "fldGLPp19IrYKeD5sjsoyr3E": ["Hiring manager"], "fldHFnN5gAR7COobF99m8mfG": "1005", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "jing xiao ling", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": "¥6600", "fldaczzyFkytwo9cp9IFEEKn": ["1 - jing xiao ling", "2 - jing xiao ling"], "fldakpR2jTsb8OdrqBnYfaN9": "***********", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}}, {"templateId": "rec4gRKyyomnFzgucdCeQXfH", "data": {"fld7NmjgEZBTtim15tdr79Om": ["optwKtbPKGORhdmo8qxQaCWV"], "fldGLPp19IrYKeD5sjsoyr3E": ["optrRuvlwseQJPGyZfWUX1Qt"], "fldHFnN5gAR7COobF99m8mfG": "1004", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "<PERSON><PERSON> min", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": 5500, "fldaczzyFkytwo9cp9IFEEKn": ["rec4LS7wrQCZoMSiBmEe37U4", "recfDO5KgRaGuCw3sKkf3cyb"], "fldakpR2jTsb8OdrqBnYfaN9": "18820190719", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}, "values": {"fld7NmjgEZBTtim15tdr79Om": ["Full-time"], "fldGLPp19IrYKeD5sjsoyr3E": ["Operation and maintenance"], "fldHFnN5gAR7COobF99m8mfG": "1004", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "<PERSON><PERSON> min", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": "¥5500", "fldaczzyFkytwo9cp9IFEEKn": ["2 - <PERSON><PERSON> min", "1 - <PERSON><PERSON> min"], "fldakpR2jTsb8OdrqBnYfaN9": "18820190719", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}}, {"templateId": "recXOH22Z3t21d8SL8xZFlF4", "data": {"fld7NmjgEZBTtim15tdr79Om": ["optNUoTTaZ78OmbkRlbwCq73"], "fldGLPp19IrYKeD5sjsoyr3E": ["opt1jSZL4RahS9DFtNsQmfC7"], "fldHFnN5gAR7COobF99m8mfG": "1003", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "zhou can can", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": 3000, "fldaczzyFkytwo9cp9IFEEKn": [], "fldakpR2jTsb8OdrqBnYfaN9": "18820190721", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}, "values": {"fld7NmjgEZBTtim15tdr79Om": ["Part-time"], "fldGLPp19IrYKeD5sjsoyr3E": ["UX"], "fldHFnN5gAR7COobF99m8mfG": "1003", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "zhou can can", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": "¥3000", "fldaczzyFkytwo9cp9IFEEKn": [], "fldakpR2jTsb8OdrqBnYfaN9": "18820190721", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}}, {"templateId": "reczGDRrq1wkOnzZJXQYfsM7", "data": {"fld7NmjgEZBTtim15tdr79Om": ["optwKtbPKGORhdmo8qxQaCWV"], "fldGLPp19IrYKeD5sjsoyr3E": ["optNkH6SD9D2gOOai32o1Z9l"], "fldHFnN5gAR7COobF99m8mfG": "1002", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "liang yuan shu", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": 20000, "fldaczzyFkytwo9cp9IFEEKn": [], "fldakpR2jTsb8OdrqBnYfaN9": "18820190721", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}, "values": {"fld7NmjgEZBTtim15tdr79Om": ["Full-time"], "fldGLPp19IrYKeD5sjsoyr3E": ["CFO"], "fldHFnN5gAR7COobF99m8mfG": "1002", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "liang yuan shu", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": "¥20000", "fldaczzyFkytwo9cp9IFEEKn": [], "fldakpR2jTsb8OdrqBnYfaN9": "18820190721", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}}, {"templateId": "recoNCwtOO2QdyDCT6pd2Ajz", "data": {"fld7NmjgEZBTtim15tdr79Om": ["optwKtbPKGORhdmo8qxQaCWV"], "fldGLPp19IrYKeD5sjsoyr3E": ["optHvsy63Lg3qyMnQi3aybgT"], "fldHFnN5gAR7COobF99m8mfG": "1001", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "liu hai qi", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": 8000, "fldaczzyFkytwo9cp9IFEEKn": ["recPjTH5L9WWJ3qdu6wYRiWC", "rec7XINufh8e51mo6ucZmrfS"], "fldakpR2jTsb8OdrqBnYfaN9": "***********", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}, "values": {"fld7NmjgEZBTtim15tdr79Om": ["Full-time"], "fldGLPp19IrYKeD5sjsoyr3E": ["Business manager"], "fldHFnN5gAR7COobF99m8mfG": "1001", "fldIyFFDNSQZ2KkWr9sJwmOY": "<EMAIL>", "fldKKCQWN620gBfKrFVtksTs": "liu hai qi", "fldXUZUUZwJBVY91eNVeogFC": "xxxxxxx", "fldYYeyGUutjiCZA8eTMCMBg": "¥8000", "fldaczzyFkytwo9cp9IFEEKn": ["2 - liu hai qi", "1 - liu hai qi"], "fldakpR2jTsb8OdrqBnYfaN9": "***********", "fldfMghvuVBpi9SgdjRZ7YBv": "xxxxxx"}}]}, {"resourceType": "DATABASE", "templateId": "datfkeWD29MrlhVcpuGHK0ke", "name": {"en": "Payroll Sheet", "zh-CN": "工资单"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwrDh0NTXgTCPujwgia1BOi", "name": {"en": "Pay sheet", "zh-CN": "工资条"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldOEZnIn1mtGlQOnGYmTMIw", "hidden": false}, {"templateId": "fldpouyK8HRtvLCqhHdLh7Dt", "hidden": false}, {"templateId": "fldiFxMPQK8nPxQ3BCBXF4FR", "hidden": false}, {"templateId": "fldX4MObErJ5AzFfhDlAtpG6", "hidden": false}, {"templateId": "fldQ71VHRVoU6j7qBcZ2vA0F", "hidden": false}, {"templateId": "fldryPRefD3z2GT3A9cO3zbs", "hidden": false}, {"templateId": "fldnjmJRqaMhNB2I9F0iiPHU", "hidden": false, "width": 150}, {"templateId": "fldPL42hSmglbq4r7T2bM8TV", "hidden": false}, {"templateId": "fld9aMVi6QfRw44K4E3wN8vF", "hidden": false}, {"templateId": "fldgLpeFpTVXGFCoHr3BtzjR", "hidden": false}, {"templateId": "fldvqOJQksoFfLpT9YwFlTTK", "hidden": false}, {"templateId": "fldLq4nDDVhgVtFJYyjwdC7A", "hidden": true}, {"templateId": "fld4uUbM2yeFvjB7ghm2Nepo", "hidden": true}, {"templateId": "fldc0FbWQbeLsWmbqTTa52dz", "hidden": false}, {"templateId": "fldAf3S2oNzjIaOOOOGuYNXc", "hidden": false}, {"templateId": "fldaF8DSxLlyBD2m3yuI9Mwv", "hidden": false}, {"templateId": "fldQUFqRJgJNVyta8wObUS3r", "hidden": false}, {"templateId": "fldeSWw9J2SDWpxCTzSq8nwI", "hidden": false}]}, {"type": "TABLE", "templateId": "viwllL2gSlWqhESYB5h9zSHj", "name": {"en": "1-Turn out for work", "zh-CN": "1-出勤"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldOEZnIn1mtGlQOnGYmTMIw", "hidden": false}, {"templateId": "fldpouyK8HRtvLCqhHdLh7Dt", "hidden": false}, {"templateId": "fldiFxMPQK8nPxQ3BCBXF4FR", "hidden": false}, {"templateId": "fldQ71VHRVoU6j7qBcZ2vA0F", "hidden": false}, {"templateId": "fldryPRefD3z2GT3A9cO3zbs", "hidden": false}, {"templateId": "fldnjmJRqaMhNB2I9F0iiPHU", "hidden": false}, {"templateId": "fldPL42hSmglbq4r7T2bM8TV", "hidden": false}, {"templateId": "fldaF8DSxLlyBD2m3yuI9Mwv", "hidden": true}, {"templateId": "fldQUFqRJgJNVyta8wObUS3r", "hidden": true}, {"templateId": "fldLq4nDDVhgVtFJYyjwdC7A", "hidden": true}, {"templateId": "fld4uUbM2yeFvjB7ghm2Nepo", "hidden": true}, {"templateId": "fld9aMVi6QfRw44K4E3wN8vF", "hidden": true}, {"templateId": "fldgLpeFpTVXGFCoHr3BtzjR", "hidden": true}, {"templateId": "fldvqOJQksoFfLpT9YwFlTTK", "hidden": true}, {"templateId": "fldc0FbWQbeLsWmbqTTa52dz", "hidden": true}, {"templateId": "fldAf3S2oNzjIaOOOOGuYNXc", "hidden": true}, {"templateId": "fldX4MObErJ5AzFfhDlAtpG6", "hidden": false}, {"templateId": "fldeSWw9J2SDWpxCTzSq8nwI", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viw892kIICYoPOFvsxQhLVwE", "name": {"en": "2-Insurance", "zh-CN": "2-五险一金"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldOEZnIn1mtGlQOnGYmTMIw", "hidden": false}, {"templateId": "fldpouyK8HRtvLCqhHdLh7Dt", "hidden": false}, {"templateId": "fldiFxMPQK8nPxQ3BCBXF4FR", "hidden": false}, {"templateId": "fldQ71VHRVoU6j7qBcZ2vA0F", "hidden": true}, {"templateId": "fldryPRefD3z2GT3A9cO3zbs", "hidden": true}, {"templateId": "fldnjmJRqaMhNB2I9F0iiPHU", "hidden": true}, {"templateId": "fldPL42hSmglbq4r7T2bM8TV", "hidden": true}, {"templateId": "fldLq4nDDVhgVtFJYyjwdC7A", "hidden": false}, {"templateId": "fldaF8DSxLlyBD2m3yuI9Mwv", "hidden": true}, {"templateId": "fldQUFqRJgJNVyta8wObUS3r", "hidden": true}, {"templateId": "fld4uUbM2yeFvjB7ghm2Nepo", "hidden": false}, {"templateId": "fld9aMVi6QfRw44K4E3wN8vF", "hidden": false}, {"templateId": "fldgLpeFpTVXGFCoHr3BtzjR", "hidden": false}, {"templateId": "fldvqOJQksoFfLpT9YwFlTTK", "hidden": true}, {"templateId": "fldc0FbWQbeLsWmbqTTa52dz", "hidden": true}, {"templateId": "fldAf3S2oNzjIaOOOOGuYNXc", "hidden": true}, {"templateId": "fldX4MObErJ5AzFfhDlAtpG6", "hidden": false}, {"templateId": "fldeSWw9J2SDWpxCTzSq8nwI", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwBC3LXee2ZXDMMpzfHwnYc", "name": {"en": "3-Tax", "zh-CN": "3-个税"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldOEZnIn1mtGlQOnGYmTMIw", "hidden": false}, {"templateId": "fldpouyK8HRtvLCqhHdLh7Dt", "hidden": false}, {"templateId": "fldiFxMPQK8nPxQ3BCBXF4FR", "hidden": false}, {"templateId": "fldQ71VHRVoU6j7qBcZ2vA0F", "hidden": true}, {"templateId": "fldryPRefD3z2GT3A9cO3zbs", "hidden": true}, {"templateId": "fldnjmJRqaMhNB2I9F0iiPHU", "hidden": true}, {"templateId": "fldPL42hSmglbq4r7T2bM8TV", "hidden": true}, {"templateId": "fldvqOJQksoFfLpT9YwFlTTK", "hidden": false}, {"templateId": "fldaF8DSxLlyBD2m3yuI9Mwv", "hidden": true}, {"templateId": "fldQUFqRJgJNVyta8wObUS3r", "hidden": true}, {"templateId": "fldLq4nDDVhgVtFJYyjwdC7A", "hidden": true}, {"templateId": "fld4uUbM2yeFvjB7ghm2Nepo", "hidden": true}, {"templateId": "fld9aMVi6QfRw44K4E3wN8vF", "hidden": true}, {"templateId": "fldgLpeFpTVXGFCoHr3BtzjR", "hidden": true}, {"templateId": "fldc0FbWQbeLsWmbqTTa52dz", "hidden": true}, {"templateId": "fldAf3S2oNzjIaOOOOGuYNXc", "hidden": true}, {"templateId": "fldX4MObErJ5AzFfhDlAtpG6", "hidden": false}, {"templateId": "fldeSWw9J2SDWpxCTzSq8nwI", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwW50fz1ncHyihnEIBelq4X", "name": {"en": "All", "zh-CN": "全视图"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldOEZnIn1mtGlQOnGYmTMIw", "hidden": false}, {"templateId": "fldpouyK8HRtvLCqhHdLh7Dt", "hidden": false}, {"templateId": "fldiFxMPQK8nPxQ3BCBXF4FR", "hidden": false}, {"templateId": "fldQ71VHRVoU6j7qBcZ2vA0F", "hidden": false}, {"templateId": "fldryPRefD3z2GT3A9cO3zbs", "hidden": false}, {"templateId": "fldnjmJRqaMhNB2I9F0iiPHU", "hidden": false}, {"templateId": "fldPL42hSmglbq4r7T2bM8TV", "hidden": false}, {"templateId": "fldaF8DSxLlyBD2m3yuI9Mwv", "hidden": false}, {"templateId": "fldQUFqRJgJNVyta8wObUS3r", "hidden": false}, {"templateId": "fldLq4nDDVhgVtFJYyjwdC7A", "hidden": false}, {"templateId": "fld4uUbM2yeFvjB7ghm2Nepo", "hidden": false}, {"templateId": "fld9aMVi6QfRw44K4E3wN8vF", "hidden": false}, {"templateId": "fldgLpeFpTVXGFCoHr3BtzjR", "hidden": false}, {"templateId": "fldvqOJQksoFfLpT9YwFlTTK", "hidden": false}, {"templateId": "fldc0FbWQbeLsWmbqTTa52dz", "hidden": false}, {"templateId": "fldAf3S2oNzjIaOOOOGuYNXc", "hidden": false}, {"templateId": "fldX4MObErJ5AzFfhDlAtpG6", "hidden": false}, {"templateId": "fldeSWw9J2SDWpxCTzSq8nwI", "hidden": false}], "groups": []}], "fields": [{"type": "FORMULA", "templateId": "fldOEZnIn1mtGlQOnGYmTMIw", "privilege": "TYPE_EDIT", "name": {"en": "Title", "zh-CN": "标题"}, "required": false, "property": {"expressionTemplate": "MONTH({fldiFxMPQK8nPxQ3BCBXF4FR})&“ - ”&{fldpouyK8HRtvLCqhHdLh7Dt}"}, "primary": true}, {"type": "LINK", "templateId": "fldpouyK8HRtvLCqhHdLh7Dt", "privilege": "NAME_EDIT", "name": {"en": "Name", "zh-CN": "姓名"}, "property": {"foreignDatabaseTemplateId": "datiaFKRnTZYjr1P8CffmGET", "brotherFieldTemplateId": "fldaczzyFkytwo9cp9IFEEKn"}, "primary": false}, {"type": "DATETIME", "templateId": "fldiFxMPQK8nPxQ3BCBXF4FR", "privilege": "NAME_EDIT", "name": {"en": "Pay Month", "zh-CN": "发薪月份"}, "property": {"dateFormat": "YYYY-MM", "includeTime": false}, "primary": false}, {"type": "NUMBER", "templateId": "fldQ71VHRVoU6j7qBcZ2vA0F", "privilege": "NAME_EDIT", "name": {"en": "Number of Days Required for Attendance", "zh-CN": "应出勤天数"}, "property": {"precision": 0, "commaStyle": "thousand", "symbol": "", "symbolAlign": "right"}, "primary": false}, {"type": "NUMBER", "templateId": "fldryPRefD3z2GT3A9cO3zbs", "privilege": "NAME_EDIT", "name": {"en": "Leave Days", "zh-CN": "请假天数"}, "property": {"precision": 0, "commaStyle": "thousand", "symbol": "", "symbolAlign": "right"}, "primary": false}, {"type": "FORMULA", "templateId": "fldnjmJRqaMhNB2I9F0iiPHU", "privilege": "NAME_EDIT", "name": {"en": "Actual Attendance Days", "zh-CN": "实际出勤天数"}, "required": false, "property": {"expressionTemplate": "{fldQ71VHRVoU6j7qBcZ2vA0F}-{fldryPRefD3z2GT3A9cO3zbs}"}, "primary": false}, {"type": "FORMULA", "templateId": "fldPL42hSmglbq4r7T2bM8TV", "privilege": "NAME_EDIT", "name": {"en": "Deduction for Absences", "zh-CN": "缺勤扣款"}, "required": false, "property": {"expressionTemplate": "ROUND((({fldX4MObErJ5AzFfhDlAtpG6}/{fldQ71VHRVoU6j7qBcZ2vA0F})*{fldryPRefD3z2GT3A9cO3zbs}),2)"}, "primary": false}, {"type": "LOOKUP", "templateId": "fldaF8DSxLlyBD2m3yuI9Mwv", "privilege": "NAME_EDIT", "name": {"en": "Email", "zh-CN": "邮箱"}, "required": false, "property": {"relatedLinkFieldTemplateId": "datfkeWD29MrlhVcpuGHK0ke:fldpouyK8HRtvLCqhHdLh7Dt", "lookupTargetFieldTemplateId": "fldIyFFDNSQZ2KkWr9sJwmOY", "lookupTargetFieldType": "EMAIL", "dataType": "STRING", "lookUpLimit": "ALL", "rollUpType": "VALUES"}, "primary": false}, {"type": "CHECKBOX", "templateId": "fldQUFqRJgJNVyta8wObUS3r", "privilege": "NAME_EDIT", "name": {"en": "Pay Slip Sending", "zh-CN": "工资单发送"}, "primary": false}, {"type": "CURRENCY", "templateId": "fldLq4nDDVhgVtFJYyjwdC7A", "privilege": "NAME_EDIT", "name": {"en": "Company Social Security", "zh-CN": "公司社保"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "¥", "symbolAlign": "left"}, "primary": false}, {"type": "CURRENCY", "templateId": "fld4uUbM2yeFvjB7ghm2Nepo", "privilege": "NAME_EDIT", "name": {"en": "Corporate Reserve", "zh-CN": "公司公积金"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "¥", "symbolAlign": "left"}, "primary": false}, {"type": "CURRENCY", "templateId": "fld9aMVi6QfRw44K4E3wN8vF", "privilege": "NAME_EDIT", "name": {"en": "Personal Social Security", "zh-CN": "个人社保"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "¥", "symbolAlign": "left"}, "primary": false}, {"type": "CURRENCY", "templateId": "fldgLpeFpTVXGFCoHr3BtzjR", "privilege": "NAME_EDIT", "name": {"en": "Individual Reserve Fund", "zh-CN": "个人公积金"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "¥", "symbolAlign": "left"}, "primary": false}, {"type": "CURRENCY", "templateId": "fldvqOJQksoFfLpT9YwFlTTK", "privilege": "NAME_EDIT", "name": {"en": "Current Period Tax Due", "zh-CN": "本期应补(退)税额"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "¥", "symbolAlign": "left"}, "primary": false}, {"type": "LOOKUP", "templateId": "fldc0FbWQbeLsWmbqTTa52dz", "privilege": "NAME_EDIT", "name": {"en": "Bank Card Number", "zh-CN": "银行卡号"}, "required": false, "property": {"relatedLinkFieldTemplateId": "datfkeWD29MrlhVcpuGHK0ke:fldpouyK8HRtvLCqhHdLh7Dt", "lookupTargetFieldTemplateId": "fldfMghvuVBpi9SgdjRZ7YBv", "lookupTargetFieldType": "SINGLE_TEXT", "dataType": "STRING", "lookUpLimit": "ALL", "rollUpType": "VALUES"}, "primary": false}, {"type": "LOOKUP", "templateId": "fldAf3S2oNzjIaOOOOGuYNXc", "privilege": "NAME_EDIT", "name": {"en": "Bank of Deposit", "zh-CN": "开户行"}, "required": false, "property": {"relatedLinkFieldTemplateId": "datfkeWD29MrlhVcpuGHK0ke:fldpouyK8HRtvLCqhHdLh7Dt", "lookupTargetFieldTemplateId": "fldXUZUUZwJBVY91eNVeogFC", "lookupTargetFieldType": "SINGLE_TEXT", "dataType": "STRING", "lookUpLimit": "ALL", "rollUpType": "VALUES"}, "primary": false}, {"type": "LOOKUP", "templateId": "fldX4MObErJ5AzFfhDlAtpG6", "privilege": "NAME_EDIT", "name": {"en": "Net Salary", "zh-CN": "应发工资"}, "required": false, "property": {"relatedLinkFieldTemplateId": "datfkeWD29MrlhVcpuGHK0ke:fldpouyK8HRtvLCqhHdLh7Dt", "lookupTargetFieldTemplateId": "fldYYeyGUutjiCZA8eTMCMBg", "lookupTargetFieldType": "CURRENCY", "dataType": "NUMBER", "lookUpLimit": "ALL", "rollUpType": "VALUES", "formatting": {"type": "CURRENCY", "property": {}}}, "primary": false}, {"type": "FORMULA", "templateId": "fldeSWw9J2SDWpxCTzSq8nwI", "privilege": "NAME_EDIT", "name": {"en": "Gross Salary", "zh-CN": "实发工资"}, "required": false, "property": {"expressionTemplate": "ROUND({fldX4MObErJ5AzFfhDlAtpG6} - {fld9aMVi6QfRw44K4E3wN8vF} - {fldvqOJQksoFfLpT9YwFlTTK} - {fldPL42hSmglbq4r7T2bM8TV}, 2)"}, "primary": false}], "records": [{"templateId": "rec4LS7wrQCZoMSiBmEe37U4", "data": {"fld4uUbM2yeFvjB7ghm2Nepo": 118, "fld9aMVi6QfRw44K4E3wN8vF": 906.76, "fldAf3S2oNzjIaOOOOGuYNXc": "xxxxxxx", "fldLq4nDDVhgVtFJYyjwdC7A": 1036, "fldOEZnIn1mtGlQOnGYmTMIw": "2 - <PERSON><PERSON> min", "fldPL42hSmglbq4r7T2bM8TV": 0, "fldQ71VHRVoU6j7qBcZ2vA0F": 21, "fldQUFqRJgJNVyta8wObUS3r": true, "fldX4MObErJ5AzFfhDlAtpG6": 5500, "fldaF8DSxLlyBD2m3yuI9Mwv": "<EMAIL>", "fldeSWw9J2SDWpxCTzSq8nwI": 4507.24, "fldgLpeFpTVXGFCoHr3BtzjR": 118, "fldc0FbWQbeLsWmbqTTa52dz": "xxxxxx", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-02-18T16:00:00.000Z", "fldnjmJRqaMhNB2I9F0iiPHU": 21, "fldpouyK8HRtvLCqhHdLh7Dt": ["rec4gRKyyomnFzgucdCeQXfH"], "fldvqOJQksoFfLpT9YwFlTTK": 86}, "values": {"fld4uUbM2yeFvjB7ghm2Nepo": "¥118", "fld9aMVi6QfRw44K4E3wN8vF": "¥906.76", "fldAf3S2oNzjIaOOOOGuYNXc": ["xxxxxxx"], "fldLq4nDDVhgVtFJYyjwdC7A": "¥1036", "fldOEZnIn1mtGlQOnGYmTMIw": "2 - <PERSON><PERSON> min", "fldPL42hSmglbq4r7T2bM8TV": "0", "fldQ71VHRVoU6j7qBcZ2vA0F": "21", "fldQUFqRJgJNVyta8wObUS3r": "1", "fldX4MObErJ5AzFfhDlAtpG6": ["5500"], "fldaF8DSxLlyBD2m3yuI9Mwv": ["<EMAIL>"], "fldc0FbWQbeLsWmbqTTa52dz": ["xxxxxx"], "fldeSWw9J2SDWpxCTzSq8nwI": "4507.24", "fldgLpeFpTVXGFCoHr3BtzjR": "¥118", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-02", "fldnjmJRqaMhNB2I9F0iiPHU": "21", "fldpouyK8HRtvLCqhHdLh7Dt": ["<PERSON><PERSON> min"], "fldvqOJQksoFfLpT9YwFlTTK": "¥86"}}, {"templateId": "recfDO5KgRaGuCw3sKkf3cyb", "data": {"fld4uUbM2yeFvjB7ghm2Nepo": 118, "fld9aMVi6QfRw44K4E3wN8vF": 906.76, "fldAf3S2oNzjIaOOOOGuYNXc": "xxxxxxx", "fldLq4nDDVhgVtFJYyjwdC7A": 1036, "fldOEZnIn1mtGlQOnGYmTMIw": "1 - <PERSON><PERSON> min", "fldPL42hSmglbq4r7T2bM8TV": 0, "fldQ71VHRVoU6j7qBcZ2vA0F": 21, "fldQUFqRJgJNVyta8wObUS3r": true, "fldX4MObErJ5AzFfhDlAtpG6": 5500, "fldaF8DSxLlyBD2m3yuI9Mwv": "<EMAIL>", "fldeSWw9J2SDWpxCTzSq8nwI": 4516.24, "fldgLpeFpTVXGFCoHr3BtzjR": 118, "fldc0FbWQbeLsWmbqTTa52dz": "xxxxxx", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-01-13T16:00:00.000Z", "fldnjmJRqaMhNB2I9F0iiPHU": 21, "fldpouyK8HRtvLCqhHdLh7Dt": ["rec4gRKyyomnFzgucdCeQXfH"], "fldryPRefD3z2GT3A9cO3zbs": null, "fldvqOJQksoFfLpT9YwFlTTK": 77}, "values": {"fld4uUbM2yeFvjB7ghm2Nepo": "¥118", "fld9aMVi6QfRw44K4E3wN8vF": "¥906.76", "fldAf3S2oNzjIaOOOOGuYNXc": ["xxxxxxx"], "fldLq4nDDVhgVtFJYyjwdC7A": "¥1036", "fldOEZnIn1mtGlQOnGYmTMIw": "1 - <PERSON><PERSON> min", "fldPL42hSmglbq4r7T2bM8TV": "0", "fldQ71VHRVoU6j7qBcZ2vA0F": "21", "fldQUFqRJgJNVyta8wObUS3r": "1", "fldX4MObErJ5AzFfhDlAtpG6": ["5500"], "fldaF8DSxLlyBD2m3yuI9Mwv": ["<EMAIL>"], "fldc0FbWQbeLsWmbqTTa52dz": ["xxxxxx"], "fldeSWw9J2SDWpxCTzSq8nwI": "4516.24", "fldgLpeFpTVXGFCoHr3BtzjR": "¥118", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-01", "fldnjmJRqaMhNB2I9F0iiPHU": "21", "fldpouyK8HRtvLCqhHdLh7Dt": ["<PERSON><PERSON> min"], "fldvqOJQksoFfLpT9YwFlTTK": "¥77"}}, {"templateId": "recWiqQQOk7wU7mLvjFTRiNX", "data": {"fld4uUbM2yeFvjB7ghm2Nepo": 118, "fld9aMVi6QfRw44K4E3wN8vF": 906.76, "fldAf3S2oNzjIaOOOOGuYNXc": "xxxxxxx", "fldLq4nDDVhgVtFJYyjwdC7A": 1036, "fldOEZnIn1mtGlQOnGYmTMIw": "1 - jing xiao ling", "fldPL42hSmglbq4r7T2bM8TV": 0, "fldQ71VHRVoU6j7qBcZ2vA0F": 21, "fldQUFqRJgJNVyta8wObUS3r": true, "fldX4MObErJ5AzFfhDlAtpG6": 6600, "fldaF8DSxLlyBD2m3yuI9Mwv": "<EMAIL>", "fldeSWw9J2SDWpxCTzSq8nwI": 5607.24, "fldgLpeFpTVXGFCoHr3BtzjR": 118, "fldc0FbWQbeLsWmbqTTa52dz": "xxxxxx", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-01-14T16:00:00.000Z", "fldnjmJRqaMhNB2I9F0iiPHU": 21, "fldpouyK8HRtvLCqhHdLh7Dt": ["recz1lhRiW3DhviEGEjaaUfF"], "fldryPRefD3z2GT3A9cO3zbs": null, "fldvqOJQksoFfLpT9YwFlTTK": 86}, "values": {"fld4uUbM2yeFvjB7ghm2Nepo": "¥118", "fld9aMVi6QfRw44K4E3wN8vF": "¥906.76", "fldAf3S2oNzjIaOOOOGuYNXc": ["xxxxxxx"], "fldLq4nDDVhgVtFJYyjwdC7A": "¥1036", "fldOEZnIn1mtGlQOnGYmTMIw": "1 - jing xiao ling", "fldPL42hSmglbq4r7T2bM8TV": "0", "fldQ71VHRVoU6j7qBcZ2vA0F": "21", "fldQUFqRJgJNVyta8wObUS3r": "1", "fldX4MObErJ5AzFfhDlAtpG6": ["6600"], "fldaF8DSxLlyBD2m3yuI9Mwv": ["<EMAIL>"], "fldc0FbWQbeLsWmbqTTa52dz": ["xxxxxx"], "fldeSWw9J2SDWpxCTzSq8nwI": "5607.24", "fldgLpeFpTVXGFCoHr3BtzjR": "¥118", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-01", "fldnjmJRqaMhNB2I9F0iiPHU": "21", "fldpouyK8HRtvLCqhHdLh7Dt": ["jing xiao ling"], "fldvqOJQksoFfLpT9YwFlTTK": "¥86"}}, {"templateId": "recPjTH5L9WWJ3qdu6wYRiWC", "data": {"fld4uUbM2yeFvjB7ghm2Nepo": 118, "fld9aMVi6QfRw44K4E3wN8vF": 906.76, "fldAf3S2oNzjIaOOOOGuYNXc": "xxxxxxx", "fldLq4nDDVhgVtFJYyjwdC7A": 1036, "fldOEZnIn1mtGlQOnGYmTMIw": "2 - liu hai qi", "fldPL42hSmglbq4r7T2bM8TV": 380.95, "fldQ71VHRVoU6j7qBcZ2vA0F": 21, "fldQUFqRJgJNVyta8wObUS3r": true, "fldX4MObErJ5AzFfhDlAtpG6": 8000, "fldaF8DSxLlyBD2m3yuI9Mwv": "<EMAIL>", "fldeSWw9J2SDWpxCTzSq8nwI": 6632.29, "fldgLpeFpTVXGFCoHr3BtzjR": 118, "fldc0FbWQbeLsWmbqTTa52dz": "xxxxxx", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-02-17T16:00:00.000Z", "fldnjmJRqaMhNB2I9F0iiPHU": 20, "fldpouyK8HRtvLCqhHdLh7Dt": ["recoNCwtOO2QdyDCT6pd2Ajz"], "fldryPRefD3z2GT3A9cO3zbs": 1, "fldvqOJQksoFfLpT9YwFlTTK": 80}, "values": {"fld4uUbM2yeFvjB7ghm2Nepo": "¥118", "fld9aMVi6QfRw44K4E3wN8vF": "¥906.76", "fldAf3S2oNzjIaOOOOGuYNXc": ["xxxxxxx"], "fldLq4nDDVhgVtFJYyjwdC7A": "¥1036", "fldOEZnIn1mtGlQOnGYmTMIw": "2 - liu hai qi", "fldPL42hSmglbq4r7T2bM8TV": "380.95", "fldQ71VHRVoU6j7qBcZ2vA0F": "21", "fldQUFqRJgJNVyta8wObUS3r": "1", "fldX4MObErJ5AzFfhDlAtpG6": ["8000"], "fldaF8DSxLlyBD2m3yuI9Mwv": ["<EMAIL>"], "fldc0FbWQbeLsWmbqTTa52dz": ["xxxxxx"], "fldeSWw9J2SDWpxCTzSq8nwI": "6632.29", "fldgLpeFpTVXGFCoHr3BtzjR": "¥118", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-02", "fldnjmJRqaMhNB2I9F0iiPHU": "20", "fldpouyK8HRtvLCqhHdLh7Dt": ["liu hai qi"], "fldryPRefD3z2GT3A9cO3zbs": "1", "fldvqOJQksoFfLpT9YwFlTTK": "¥80"}}, {"templateId": "recqKuBsWhRucqK7n8TEq26n", "data": {"fld4uUbM2yeFvjB7ghm2Nepo": 118, "fld9aMVi6QfRw44K4E3wN8vF": 906.76, "fldAf3S2oNzjIaOOOOGuYNXc": "xxxxxxx", "fldLq4nDDVhgVtFJYyjwdC7A": 1036, "fldOEZnIn1mtGlQOnGYmTMIw": "2 - jing xiao ling", "fldPL42hSmglbq4r7T2bM8TV": 0, "fldQ71VHRVoU6j7qBcZ2vA0F": 21, "fldQUFqRJgJNVyta8wObUS3r": true, "fldX4MObErJ5AzFfhDlAtpG6": 6600, "fldaF8DSxLlyBD2m3yuI9Mwv": "<EMAIL>", "fldeSWw9J2SDWpxCTzSq8nwI": 5575.24, "fldgLpeFpTVXGFCoHr3BtzjR": 118, "fldc0FbWQbeLsWmbqTTa52dz": "xxxxxx", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-02-17T16:00:00.000Z", "fldnjmJRqaMhNB2I9F0iiPHU": 21, "fldpouyK8HRtvLCqhHdLh7Dt": ["recz1lhRiW3DhviEGEjaaUfF"], "fldryPRefD3z2GT3A9cO3zbs": 0, "fldvqOJQksoFfLpT9YwFlTTK": 118}, "values": {"fld4uUbM2yeFvjB7ghm2Nepo": "¥118", "fld9aMVi6QfRw44K4E3wN8vF": "¥906.76", "fldAf3S2oNzjIaOOOOGuYNXc": ["xxxxxxx"], "fldLq4nDDVhgVtFJYyjwdC7A": "¥1036", "fldOEZnIn1mtGlQOnGYmTMIw": "2 - jing xiao ling", "fldPL42hSmglbq4r7T2bM8TV": "0", "fldQ71VHRVoU6j7qBcZ2vA0F": "21", "fldQUFqRJgJNVyta8wObUS3r": "1", "fldX4MObErJ5AzFfhDlAtpG6": ["6600"], "fldaF8DSxLlyBD2m3yuI9Mwv": ["<EMAIL>"], "fldc0FbWQbeLsWmbqTTa52dz": ["xxxxxx"], "fldeSWw9J2SDWpxCTzSq8nwI": "5575.24", "fldgLpeFpTVXGFCoHr3BtzjR": "¥118", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-02", "fldnjmJRqaMhNB2I9F0iiPHU": "21", "fldpouyK8HRtvLCqhHdLh7Dt": ["jing xiao ling"], "fldryPRefD3z2GT3A9cO3zbs": "0", "fldvqOJQksoFfLpT9YwFlTTK": "¥118"}}, {"templateId": "rec7XINufh8e51mo6ucZmrfS", "data": {"fld4uUbM2yeFvjB7ghm2Nepo": 118, "fld9aMVi6QfRw44K4E3wN8vF": 906.76, "fldAf3S2oNzjIaOOOOGuYNXc": "xxxxxxx", "fldLq4nDDVhgVtFJYyjwdC7A": 1036, "fldOEZnIn1mtGlQOnGYmTMIw": "1 - liu hai qi", "fldPL42hSmglbq4r7T2bM8TV": 0, "fldQ71VHRVoU6j7qBcZ2vA0F": 21, "fldQUFqRJgJNVyta8wObUS3r": true, "fldX4MObErJ5AzFfhDlAtpG6": 8000, "fldaF8DSxLlyBD2m3yuI9Mwv": "<EMAIL>", "fldeSWw9J2SDWpxCTzSq8nwI": 6963.24, "fldgLpeFpTVXGFCoHr3BtzjR": 118, "fldc0FbWQbeLsWmbqTTa52dz": "xxxxxx", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-01-14T16:00:00.000Z", "fldnjmJRqaMhNB2I9F0iiPHU": 21, "fldpouyK8HRtvLCqhHdLh7Dt": ["recoNCwtOO2QdyDCT6pd2Ajz"], "fldryPRefD3z2GT3A9cO3zbs": null, "fldvqOJQksoFfLpT9YwFlTTK": 130}, "values": {"fld4uUbM2yeFvjB7ghm2Nepo": "¥118", "fld9aMVi6QfRw44K4E3wN8vF": "¥906.76", "fldAf3S2oNzjIaOOOOGuYNXc": ["xxxxxxx"], "fldLq4nDDVhgVtFJYyjwdC7A": "¥1036", "fldOEZnIn1mtGlQOnGYmTMIw": "1 - liu hai qi", "fldPL42hSmglbq4r7T2bM8TV": "0", "fldQ71VHRVoU6j7qBcZ2vA0F": "21", "fldQUFqRJgJNVyta8wObUS3r": "1", "fldX4MObErJ5AzFfhDlAtpG6": ["8000"], "fldaF8DSxLlyBD2m3yuI9Mwv": ["<EMAIL>"], "fldc0FbWQbeLsWmbqTTa52dz": ["xxxxxx"], "fldeSWw9J2SDWpxCTzSq8nwI": "6963.24", "fldgLpeFpTVXGFCoHr3BtzjR": "¥118", "fldiFxMPQK8nPxQ3BCBXF4FR": "2025-01", "fldnjmJRqaMhNB2I9F0iiPHU": "21", "fldpouyK8HRtvLCqhHdLh7Dt": ["liu hai qi"], "fldvqOJQksoFfLpT9YwFlTTK": "¥130"}}]}, {"resourceType": "AUTOMATION", "templateId": "manual_trigger_send_emails_in_bulk", "name": {"en": "Auto Send Pay Slips", "zh-CN": "自动化发送工资单"}, "description": {"en": "Auto send pay slips", "zh-CN": "手工触发批量发送邮件"}, "triggers": [{"triggerType": "RECORD_MATCH", "templateId": "trigger_send_emails_in_bulk", "description": {"en": "When the sending conditions are met", "ja": "送信条件が満たされたとき", "zh-CN": "本月发薪员工", "zh-TW": "當滿足發送條件時"}, "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldQUFqRJgJNVyta8wObUS3r", "fieldType": "CHECKBOX", "clause": {"operator": "Is", "value": true}}, {"fieldTemplateId": "fldiFxMPQK8nPxQ3BCBXF4FR", "fieldType": "DATETIME", "clause": {"operator": "Is", "value": ["PreviousMonth"]}}]}, "databaseTemplateId": "datfkeWD29MrlhVcpuGHK0ke"}}], "actions": [{"templateId": "acttYAbIfBuRYlXWycQ299fx", "description": {"en": "Find employees paid this month", "zh-CN": "查找本月发薪员工"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldiFxMPQK8nPxQ3BCBXF4FR", "fieldType": "DATETIME", "clause": {"operator": "Is", "value": ["PreviousMonth"]}}]}, "databaseTemplateId": "datfkeWD29MrlhVcpuGHK0ke"}}, {"templateId": "act62j9iHqFoMB48I8zYLO2g", "actionType": "LOOP", "input": {"type": "PREV_ACTION", "actionTemplateId": "acttYAbIfBuRYlXWycQ299fx", "path": "records"}, "actions": [{"description": {"en": "Send a pay slip email confirmation", "zh-CN": "发送工资条邮件确认"}, "actionType": "SEND_EMAIL", "templateId": "actfhj3EtAbhYaR98vCfgGpr", "input": {"subject": "<%= JSON.stringify(_item.cells.fldpouyK8HRtvLCqhHdLh7Dt.value) %>Pay slips, Please confirm", "body": {"markdown": "<%= JSON.stringify(_item.cells.fldpouyK8HRtvLCqhHdLh7Dt.value) %>Hello，Here is your salary details for <%= _item.cells.fldiFxMPQK8nPxQ3BCBXF4FR.value %>，Please check and confirm.                         \n                   \nDetails are as follows:                                  \n- Net Pay：<%= _item.cells.fldX4MObErJ5AzFfhDlAtpG6.value %>                                    \n- Deduction for Absences：<%= _item.cells.fldPL42hSmglbq4r7T2bM8TV.value %>                                  \n- Personal social security:<%= _item.cells.fld9aMVi6QfRw44K4E3wN8vF.value %>                                  \n- Individual reserve fund:<%= _item.cells.fldgLpeFpTVXGFCoHr3BtzjR.value %>                                  \n- Current Period Tax Due:<%= _item.cells.fldvqOJQksoFfLpT9YwFlTTK.value %>                                    \n- Net Pay：<%= _item.cells.fldeSWw9J2SDWpxCTzSq8nwI.value %>                                    \n                                    \nIf you have any questions, please contact HR TEAM as soon as possible", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "variable", "attrs": {"ids": "JSON.stringify(_item.cells.fldpouyK8HRtvLCqhHdLh7Dt.value)", "tips": ": ", "names": "Current item, cells, 姓名, Field value"}}, {"text": "Hello，Here is your salary details for ", "type": "text"}, {"type": "variable", "attrs": {"ids": "_item,cells,fldiFxMPQK8nPxQ3BCBXF4FR,value", "tips": "", "names": "Current item,cells,发薪月份,Field value"}}, {"text": "，Please check and confirm.                       ", "type": "text"}, {"type": "hardBreak"}, {"text": "                 ", "type": "text"}, {"type": "hardBreak"}, {"text": "Details are as follows:                                ", "type": "text"}, {"type": "hardBreak"}, {"text": "- Net Pay：<%= _item.cells.fldX4MObErJ5AzFfhDlAtpG6.value %>                                  ", "type": "text"}, {"type": "hardBreak"}, {"text": "- Deduction for Absences：<%= _item.cells.fldPL42hSmglbq4r7T2bM8TV.value %>                                ", "type": "text"}, {"type": "hardBreak"}, {"text": "- Personal social security:<%= _item.cells.fld9aMVi6QfRw44K4E3wN8vF.value %>                                ", "type": "text"}, {"type": "hardBreak"}, {"text": "- Individual reserve fund:<%= _item.cells.fldgLpeFpTVXGFCoHr3BtzjR.value %>                                ", "type": "text"}, {"type": "hardBreak"}, {"text": "- Current Period Tax Due:<%= _item.cells.fldvqOJQksoFfLpT9YwFlTTK.value %>                                  ", "type": "text"}, {"type": "hardBreak"}, {"text": "- Net Pay：<%= _item.cells.fldeSWw9J2SDWpxCTzSq8nwI.value %>                                  ", "type": "text"}, {"type": "hardBreak"}, {"text": "                                  ", "type": "text"}, {"type": "hardBreak"}, {"text": "If you have any questions, please contact HR TEAM as soon as possible", "type": "text"}]}]}}, "to": [{"type": "EMAIL_STRING", "email": "<%= _item.cells.fldaF8DSxLlyBD2m3yuI9Mwv.value %>"}, {"type": "EMAIL_STRING", "email": ""}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}]}, {"resourceType": "AUTOMATION", "templateId": "atolvCz3VWuYPzF7LQujDJRt", "name": {"en": "Labor cost report this month", "zh-CN": "月度人工成本报告"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trgn4m18FpUrGlvPVi610IzN", "description": {"en": "Every month after pay", "zh-CN": "每月发工资后"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "MONTH", "interval": 1, "monthDays": [15]}}, "datetime": "2025-02-19T14:43:13.285Z"}}}], "actions": [{"templateId": "actdVBHOnOpA7dYNxglARPdN", "description": {"en": "Find payroll records", "zh-CN": "查找发薪记录"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldiFxMPQK8nPxQ3BCBXF4FR", "fieldType": "DATETIME", "clause": {"operator": "Is", "value": ["PreviousMonth"]}}, {"fieldTemplateId": "fldQUFqRJgJNVyta8wObUS3r", "fieldType": "CHECKBOX", "clause": {"operator": "Is", "value": true}}]}, "databaseTemplateId": "datfkeWD29MrlhVcpuGHK0ke"}}, {"templateId": "act02Dm8v8usDW8dZOhW181r", "actionType": "LOOP", "input": {"type": "PREV_ACTION", "actionTemplateId": "actdVBHOnOpA7dYNxglARPdN", "path": "records"}, "actions": [{"description": {"en": "AI Report", "zh-CN": "AI生成报告"}, "actionType": "OPENAI_GENERATE_TEXT", "templateId": "actWYxpXjzPcpWJxJAbpMy54", "input": {"urlType": "URL", "type": "OPENAI_GENERATE_TEXT", "integrationId": "", "prompt": "This is the salary of this month, please help me briefly analyze the labor cost of this month.  Report in English  \n<%= JSON.stringify(_actions.actdVBHOnOpA7dYNxglARPdN.records) %>", "baseUrl": "https://api.siliconflow.cn/v1", "apiKey": "", "model": "Pro/deepseek-ai/DeepSeek-R1", "timeout": 120}}, {"description": {"en": "Send monthly reports", "zh-CN": "生成月度报告"}, "actionType": "SEND_REPORT", "templateId": "actI1ntz2rz1Z21BW73z1AQu", "input": {"to": [{"type": "CURRENT_OPERATOR"}], "markdown": "<%= _itemActions.actWYxpXjzPcpWJxJAbpMy54.body.choices[0].message.content %>", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "hardBreak"}, {"type": "variable", "attrs": {"ids": "_itemActions,actWYxpXjzPcpWJxJAbpMy54,body,choices,[0],message,content", "tips": "", "names": "当前执行器,OpenAI - 生成文本,body,choices,#1,message,content"}}]}]}, "subject": "Labor cost report this month", "type": "MARKDOWN"}}]}]}], "initMissions": []}