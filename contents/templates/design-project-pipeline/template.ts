import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'design-project-pipeline',
  name: 'Design project pipeline',
  description:
    'This folder contains a template to manage design project progress, helping teams stay updated on project status. You can track task status, priority, due dates, and team members.\n' +
    '\n' +
    'Please review the README file for instructions before starting.',
  keywords: {
    en: 'collaboration, follow-up, project management, team coordination, task tracking, performance evaluation',
  },
  cover: '/assets/template/design-project-pipeline/design-project-pipeline.png',
  author: '<PERSON> <<EMAIL>>',
  category: ['project'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '0.1.1',
  personas: {
    // 4 roles at least
    en: 'Project Manager, Design Lead, UX Researcher, Product Designer',
  },
  useCases: {
    en: 'Project kickoff meeting, Task assignment and tracking, Design review session, User feedback analysis, Prototype testing, Weekly project update, Budget review, Product feature discussion, Stakeholder feedback, Resource allocation discussion, Cross-functional collaboration, Team performance evaluation, Marketing campaign planning, User journey mapping, Design brainstorming workshop, Event planning coordination, Risk management review, Customer segmentation analysis, Sprint planning, Quality assurance meeting, Strategy alignment meeting, Performance metrics review, Client progress update, Design critique session',
  },
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'datZHHtrdAsej6IHb19L19fd',
      name: 'Design team',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viw3vFagsJnVEM4hbeu8yx3H',
          name: 'All team members',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fld8c854cT6f6rdwM3akxs56',
              hidden: false,
            },
            {
              templateId: 'fldbuFGz1O18lBYmJGbT0ILZ',
              hidden: false,
            },
            {
              templateId: 'fldesYG8B3iqxaPvC4AkhExB',
              hidden: false,
            },
            {
              templateId: 'fld6NZHT46t1w0vW1N7h1JxS',
              width: 150,
            },
            {
              templateId: 'fldt5uwGA3aQwvMhHAeDHAZG',
              width: 408,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fld8c854cT6f6rdwM3akxs56',
          privilege: 'TYPE_EDIT',
          name: 'Name',
          primary: true,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldbuFGz1O18lBYmJGbT0ILZ',
          privilege: 'NAME_EDIT',
          name: 'Headshot',
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldesYG8B3iqxaPvC4AkhExB',
          privilege: 'NAME_EDIT',
          name: 'Role',
          primary: false,
        },
        {
          type: 'EMAIL',
          templateId: 'fld6NZHT46t1w0vW1N7h1JxS',
          privilege: 'NAME_EDIT',
          name: 'Email address',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldt5uwGA3aQwvMhHAeDHAZG',
          privilege: 'NAME_EDIT',
          name: 'Assigned tasks',
          property: {
            foreignDatabaseTemplateId: 'datmvXxDPpxt8XDHhyCh63RK',
            brotherFieldTemplateId: 'fldN883lmUIVU0FMKalIqcZd',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recohiIMjhGDeYb3GyYdIjBG',
          data: {
            fld6NZHT46t1w0vW1N7h1JxS: '<EMAIL>',
            fld8c854cT6f6rdwM3akxs56: 'Carol Chen',
            fldbuFGz1O18lBYmJGbT0ILZ: [
              {
                name: '下载.png',
                id: 'tplattrUQVDY8DlVp3EcHWzatGI',
                path: 'template/tplattrUQVDY8DlVp3EcHWzatGI.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 533896,
              },
            ],
            fldesYG8B3iqxaPvC4AkhExB: 'Content Designer',
            fldt5uwGA3aQwvMhHAeDHAZG: ['recbCYml21uU1qSqyQMA6d6I'],
          },
          values: {
            fld6NZHT46t1w0vW1N7h1JxS: '<EMAIL>',
            fld8c854cT6f6rdwM3akxs56: 'Carol Chen',
            fldbuFGz1O18lBYmJGbT0ILZ: ['下载.png'],
            fldesYG8B3iqxaPvC4AkhExB: 'Content Designer',
            fldt5uwGA3aQwvMhHAeDHAZG: ['Social Media Graphics'],
          },
        },
        {
          templateId: 'recXTHtPIxgho5nevIBpyG7a',
          data: {
            fld6NZHT46t1w0vW1N7h1JxS: '<EMAIL>',
            fld8c854cT6f6rdwM3akxs56: 'Bob Lee',
            fldbuFGz1O18lBYmJGbT0ILZ: [
              {
                name: '111111111111.png',
                id: 'tplattA6uzjMscmhdtkTVj4a6X0',
                path: 'template/tplattA6uzjMscmhdtkTVj4a6X0.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 482748,
              },
            ],
            fldesYG8B3iqxaPvC4AkhExB: 'UX Designer',
            fldt5uwGA3aQwvMhHAeDHAZG: ['recNXbJeuo3ZApkTCHq266Tx'],
          },
          values: {
            fld6NZHT46t1w0vW1N7h1JxS: '<EMAIL>',
            fld8c854cT6f6rdwM3akxs56: 'Bob Lee',
            fldbuFGz1O18lBYmJGbT0ILZ: ['111111111111.png'],
            fldesYG8B3iqxaPvC4AkhExB: 'UX Designer',
            fldt5uwGA3aQwvMhHAeDHAZG: ['Email Campaign'],
          },
        },
        {
          templateId: 'reckMDJnJgdjk1R6iSGw0n5C',
          data: {
            fld6NZHT46t1w0vW1N7h1JxS: '<EMAIL>',
            fld8c854cT6f6rdwM3akxs56: 'Alice Johnson',
            fldbuFGz1O18lBYmJGbT0ILZ: [
              {
                name: '11111.png',
                id: 'tplattuzvrMLgNdiHN5RBrxxQYO',
                path: 'template/tplattuzvrMLgNdiHN5RBrxxQYO.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 456158,
              },
            ],
            fldesYG8B3iqxaPvC4AkhExB: 'Lead Designer',
            fldt5uwGA3aQwvMhHAeDHAZG: ['recsHRaDaUA923TMwguHviif'],
          },
          values: {
            fld6NZHT46t1w0vW1N7h1JxS: '<EMAIL>',
            fld8c854cT6f6rdwM3akxs56: 'Alice Johnson',
            fldbuFGz1O18lBYmJGbT0ILZ: ['11111.png'],
            fldesYG8B3iqxaPvC4AkhExB: 'Lead Designer',
            fldt5uwGA3aQwvMhHAeDHAZG: ['Website Redesign'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datmvXxDPpxt8XDHhyCh63RK',
      name: 'Design job log',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwUm5lrYeDwOFXH9y0VWQef',
          name: 'All projects',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fld9KhH7P2NI2LpCRZ42q9vR',
            },
            {
              templateId: 'fldbfqJfPphTpQpYoVAUWZRP',
            },
            {
              templateId: 'fldH9BGsVRb6bCLJNlZhNOsz',
            },
            {
              templateId: 'fldN883lmUIVU0FMKalIqcZd',
              width: 150,
            },
            {
              templateId: 'fldAVJpfNghBH5iPKs7cJoRU',
              hidden: true,
            },
            {
              templateId: 'fldzQidzxlM2IKdX4iXbKVed',
              hidden: true,
            },
            {
              templateId: 'fldzFEUIa7pI5nY567p6gUpb',
              hidden: true,
            },
            {
              templateId: 'fldTqjkFQWkY4SmzsCc7gPDH',
              hidden: true,
            },
            {
              templateId: 'fldZ838BshbXOLwletehSXkY',
              hidden: true,
            },
            {
              templateId: 'fld2g9njegxctpx7iit9QQ46',
              hidden: true,
            },
            {
              templateId: 'fldOgRWLtq5Lnth3i8nUWqZG',
              hidden: true,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fld9KhH7P2NI2LpCRZ42q9vR',
          privilege: 'TYPE_EDIT',
          name: 'Job request',
          primary: true,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldbfqJfPphTpQpYoVAUWZRP',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Multi Select',
            ja: '複数選択',
            'zh-CN': 'Status',
            'zh-TW': '多選',
          },
          property: {
            options: [
              {
                id: 'optCFGiirhA8XWXnTBaQwTF0',
                name: 'Blocked',
                color: 'tangerine5',
              },
              {
                id: 'optjWq2NsUkEWiNtPsTCLclr',
                name: 'Need to start',
                color: 'pink4',
              },
              {
                id: 'optnKe1X5ddzMwPE0gyH9IT9',
                name: 'In progress',
                color: 'blue4',
              },
              {
                id: 'opt53SHYAfZ261Jtpd1ZCrrW',
                name: 'Completed',
                color: 'teal5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldH9BGsVRb6bCLJNlZhNOsz',
          privilege: 'NAME_EDIT',
          name: 'Type of asset',
          property: {
            options: [
              {
                id: 'optVKwMokVuTQuQLSUOrNxIy',
                name: 'Billboard',
                color: 'deepPurple5',
              },
              {
                id: 'optUC5mZnjXiG4pRItsrQUVA',
                name: 'Email',
                color: 'green',
              },
              {
                id: 'optfVcPqgzR0abUDEJXlwESx',
                name: 'Mobile',
                color: 'orange',
              },
              {
                id: 'opt9iuHylqgjQsW6t6iWUQbr',
                name: 'Print',
                color: 'red',
              },
              {
                id: 'opt5kjM8fQrJpss3aC0mFB0O',
                name: 'Social media',
                color: 'tangerine',
              },
              {
                id: 'opts6PUBwJCmCIdzORfnQQYt',
                name: 'Web',
                color: 'pink4',
              },
              {
                id: 'optxPnNOMgVIcXXbUbb0VsyH',
                name: 'Other',
                color: 'yellow',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldN883lmUIVU0FMKalIqcZd',
          privilege: 'NAME_EDIT',
          name: 'Assigned to',
          property: {
            foreignDatabaseTemplateId: 'datZHHtrdAsej6IHb19L19fd',
            brotherFieldTemplateId: 'fldt5uwGA3aQwvMhHAeDHAZG',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldAVJpfNghBH5iPKs7cJoRU',
          privilege: 'NAME_EDIT',
          name: 'Priority',
          property: {
            options: [
              {
                id: 'optIZyDy7dAFPcvWVGcA78DF',
                name: 'Critical',
                color: 'deepPurple5',
              },
              {
                id: 'optUuyRScQbmtVkZE7Th9NB1',
                name: 'Important',
                color: 'indigo',
              },
              {
                id: 'opt9rI93h1vRHWqHxiexlKws',
                name: 'Desirable',
                color: 'yellow',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldzQidzxlM2IKdX4iXbKVed',
          privilege: 'NAME_EDIT',
          name: 'Due date',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldzFEUIa7pI5nY567p6gUpb',
          privilege: 'NAME_EDIT',
          name: 'Est. completion',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'CHECKBOX',
          templateId: 'fldTqjkFQWkY4SmzsCc7gPDH',
          privilege: 'NAME_EDIT',
          name: 'Assets approved',
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldZ838BshbXOLwletehSXkY',
          privilege: 'NAME_EDIT',
          name: 'Target audience',
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fld2g9njegxctpx7iit9QQ46',
          privilege: 'NAME_EDIT',
          name: 'Submitted',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldOgRWLtq5Lnth3i8nUWqZG',
          privilege: 'NAME_EDIT',
          name: 'Submitter',
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recbCYml21uU1qSqyQMA6d6I',
          data: {
            fld2g9njegxctpx7iit9QQ46: '2024-12-07T16:00:00.000Z',
            fld9KhH7P2NI2LpCRZ42q9vR: 'Social Media Graphics',
            fldAVJpfNghBH5iPKs7cJoRU: ['opt9rI93h1vRHWqHxiexlKws'],
            fldH9BGsVRb6bCLJNlZhNOsz: ['opt5kjM8fQrJpss3aC0mFB0O'],
            fldN883lmUIVU0FMKalIqcZd: ['recohiIMjhGDeYb3GyYdIjBG'],
            fldOgRWLtq5Lnth3i8nUWqZG: 'Lisa Wong',
            fldZ838BshbXOLwletehSXkY:
              'General Public, Social Media Followers - Users on Instagram, Facebook, and Twitter who are potential customers or followers.',
            fldbfqJfPphTpQpYoVAUWZRP: ['optjWq2NsUkEWiNtPsTCLclr'],
            fldzFEUIa7pI5nY567p6gUpb: '2024-12-17T16:00:00.000Z',
            fldzQidzxlM2IKdX4iXbKVed: '2024-12-10T16:00:00.000Z',
          },
          values: {
            fld2g9njegxctpx7iit9QQ46: '2024-12-07',
            fld9KhH7P2NI2LpCRZ42q9vR: 'Social Media Graphics',
            fldAVJpfNghBH5iPKs7cJoRU: ['Desirable'],
            fldH9BGsVRb6bCLJNlZhNOsz: ['Social media'],
            fldN883lmUIVU0FMKalIqcZd: ['Carol Chen'],
            fldOgRWLtq5Lnth3i8nUWqZG: 'Lisa Wong',
            fldZ838BshbXOLwletehSXkY:
              'General Public, Social Media Followers - Users on Instagram, Facebook, and Twitter who are potential customers or followers.',
            fldbfqJfPphTpQpYoVAUWZRP: ['Need to start'],
            fldzFEUIa7pI5nY567p6gUpb: '2024-12-17',
            fldzQidzxlM2IKdX4iXbKVed: '2024-12-10',
          },
        },
        {
          templateId: 'recNXbJeuo3ZApkTCHq266Tx',
          data: {
            fld2g9njegxctpx7iit9QQ46: '2024-11-11T16:00:00.000Z',
            fld9KhH7P2NI2LpCRZ42q9vR: 'Email Campaign',
            fldAVJpfNghBH5iPKs7cJoRU: ['optUuyRScQbmtVkZE7Th9NB1'],
            fldH9BGsVRb6bCLJNlZhNOsz: ['optUC5mZnjXiG4pRItsrQUVA'],
            fldN883lmUIVU0FMKalIqcZd: ['recXTHtPIxgho5nevIBpyG7a'],
            fldOgRWLtq5Lnth3i8nUWqZG: 'John Kim',
            fldTqjkFQWkY4SmzsCc7gPDH: true,
            fldZ838BshbXOLwletehSXkY:
              '\tNew Subscribers - Recently subscribed users interested in company updates and promotions.',
            fldbfqJfPphTpQpYoVAUWZRP: ['opt53SHYAfZ261Jtpd1ZCrrW'],
            fldzFEUIa7pI5nY567p6gUpb: '2024-10-22T16:00:00.000Z',
            fldzQidzxlM2IKdX4iXbKVed: '2024-10-14T16:00:00.000Z',
          },
          values: {
            fld2g9njegxctpx7iit9QQ46: '2024-11-11',
            fld9KhH7P2NI2LpCRZ42q9vR: 'Email Campaign',
            fldAVJpfNghBH5iPKs7cJoRU: ['Important'],
            fldH9BGsVRb6bCLJNlZhNOsz: ['Email'],
            fldN883lmUIVU0FMKalIqcZd: ['Bob Lee'],
            fldOgRWLtq5Lnth3i8nUWqZG: 'John Kim',
            fldTqjkFQWkY4SmzsCc7gPDH: '1',
            fldZ838BshbXOLwletehSXkY:
              '\tNew Subscribers - Recently subscribed users interested in company updates and promotions.',
            fldbfqJfPphTpQpYoVAUWZRP: ['Completed'],
            fldzFEUIa7pI5nY567p6gUpb: '2024-10-22',
            fldzQidzxlM2IKdX4iXbKVed: '2024-10-14',
          },
        },
        {
          templateId: 'recsHRaDaUA923TMwguHviif',
          data: {
            fld2g9njegxctpx7iit9QQ46: '2024-10-31T16:00:00.000Z',
            fld9KhH7P2NI2LpCRZ42q9vR: 'Website Redesign',
            fldAVJpfNghBH5iPKs7cJoRU: ['optIZyDy7dAFPcvWVGcA78DF'],
            fldH9BGsVRb6bCLJNlZhNOsz: ['opts6PUBwJCmCIdzORfnQQYt'],
            fldN883lmUIVU0FMKalIqcZd: ['reckMDJnJgdjk1R6iSGw0n5C'],
            fldOgRWLtq5Lnth3i8nUWqZG: 'Sarah Parker',
            fldTqjkFQWkY4SmzsCc7gPDH: true,
            fldZ838BshbXOLwletehSXkY:
              'Marketing, Product Teams, Key Stakeholders - Teams responsible for product launch and updates.',
            fldbfqJfPphTpQpYoVAUWZRP: ['optnKe1X5ddzMwPE0gyH9IT9'],
            fldzFEUIa7pI5nY567p6gUpb: '2024-11-06T16:00:00.000Z',
            fldzQidzxlM2IKdX4iXbKVed: '2024-11-03T16:00:00.000Z',
          },
          values: {
            fld2g9njegxctpx7iit9QQ46: '2024-10-31',
            fld9KhH7P2NI2LpCRZ42q9vR: 'Website Redesign',
            fldAVJpfNghBH5iPKs7cJoRU: ['Critical'],
            fldH9BGsVRb6bCLJNlZhNOsz: ['Web'],
            fldN883lmUIVU0FMKalIqcZd: ['Alice Johnson'],
            fldOgRWLtq5Lnth3i8nUWqZG: 'Sarah Parker',
            fldTqjkFQWkY4SmzsCc7gPDH: '1',
            fldZ838BshbXOLwletehSXkY:
              'Marketing, Product Teams, Key Stakeholders - Teams responsible for product launch and updates.',
            fldbfqJfPphTpQpYoVAUWZRP: ['In progress'],
            fldzFEUIa7pI5nY567p6gUpb: '2024-11-06',
            fldzQidzxlM2IKdX4iXbKVed: '2024-11-03',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
