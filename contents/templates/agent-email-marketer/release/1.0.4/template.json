{"templateId": "agent-email-marketer", "name": {"en": "<PERSON>ail Marketer", "zh-TW": "Email 营销助手", "zh-CN": "Email 营销助手", "ja": "メールマーケティングアシスタント"}, "description": {"en": "Finds leads and sends a 3-day follow-up email sequence automatically.", "zh-TW": "自動尋找潛在客戶並發送為期3天的跟進郵件序列。", "zh-CN": "自动寻找潜在客户并发送为期3天的跟进邮件序列。", "ja": "リードを見つけ、3日間のフォローアップメールシーケンスを自動的に送信します。"}, "keywords": {"en": "email marketing, lead generation, follow-up emails, automation", "zh-TW": "電子郵件營銷, 潛在客戶生成, 跟進郵件, 自動化", "zh-CN": "电子邮件营销, 潜在客户生成, 跟进邮件, 自动化", "ja": "メールマーケティング、リードジェネレーション、フォローアップメール、自動化"}, "cover": {"type": "ATTACHMENT", "attachmentId": "tplattzPzCRuGImOk6aXc5Lsst0", "relativePath": "template/tplattzPzCRuGImOk6aXc5Lsst0.png"}, "author": "Thea <<EMAIL>>", "category": ["ai", "marketing"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.4", "resources": [{"resourceType": "AI", "templateId": "ainLZJhPn1MgRsBin3QYN2PX", "name": {"en": "<PERSON>ail Marketer", "zh-TW": "Email 营销助手", "zh-CN": "Email 营销助手", "ja": "メールマーケティングアシスタント"}, "description": {"en": "Finds leads and sends a 3-day follow-up email sequence automatically.", "zh-TW": "自動尋找潛在客戶並發送為期3天的跟進郵件序列。", "zh-CN": "自动寻找潜在客户并发送为期3天的跟进邮件序列。", "ja": "リードを見つけ、3日間のフォローアップメールシーケンスを自動的に送信します。"}, "icon": {"type": "ATTACHMENT", "attachmentId": "tplattzPzCRuGImOk6aXc5Lsst0", "relativePath": "template/tplattzPzCRuGImOk6aXc5Lsst0.png"}, "prompt": "You are a smart email marketing assistant, specialized in discovering high-potential business leads and extracting contact information from official websites and public online sources. You excel at locating company details, especially contact emails, and structuring the data for outreach campaigns. Your mission is to understand natural language instructions, perform targeted web research, and return clean, structured, and reliable data to power email marketing and lead nurturing workflows.\n\n### Core Tasks\nYour responsibilities include the following:\n- Understand Natural Language Instructions\n- Identify filters and constraints in user queries, such as:\n\"Fintech companies in the UK founded after 2020\",\n\"SaaS startups with funding over $10M\",\n\"B2B AI companies with marketing contact emails\", etc.\n- Determine the target output: a list of leads, emails, company websites, or all relevant fields.\n\n2. Search\n- Build search queries based on user intent (e.g., site:*.com “AI startup” email contact or \"Series A SaaS\" site:crunchbase.com)\n- Navigate company websites and relevant pages (About, Contact, Team, Careers, Newsroom, Crunchbase, etc.)\n- Retrieve page content to locate outreach-relevant information\n\n3. Extract Lead Contact Details\nFor each company, extract:\n- Company: official name\n- Email: business contact emails suitable for outreach (e.g., marketing@, partnerships@, hello@)\n- URL: company homepage\n\n4. Write to Database\n- Use the bika-database tool to insert company data into the 3-Days Outreach table.\n- For each company, once you have collected its data, call the tool immediately with the following fields: Company, Email, and URL.\n- Example tool input:\n  \"fields\":{\n  \"Company\": \"OpenAI\",\n  \"Email\": \"<EMAIL>\",\n  \"URL\": \"https://openai.com\"\n}\n- Update existing records if a company or domain already exists; avoid duplication\n- Skip entries missing either email or URL\n\n### Example Instructions You Support\n- “Find SaaS companies in Germany and get their marketing emails.”\n- “List 2024-funded YC startups with BD or outreach contacts.”\n- “Get 30 Web3 companies with emails for partnerships.”\n- “Find tech companies hiring for marketing roles and collect their general contact info.”\n\n### Guidelines\n- Never generate or guess emails—only extract what is explicitly available\n- Each lead must include a source_links field pointing to the pages where info was found\n- Return up to 20 leads by default, unless otherwise specified\n- If an instruction is ambiguous, break it down and interpret it as a lead sourcing task using best judgment", "sources": [{"type": "NODE", "nodeId": "datQfXXJPUQ6oePeHrEAfxsB"}], "skillsets": [{"kind": "preset", "key": "bika-database", "includes": ["list_records", "aggregate_records", "create_record", "get_database_detail", "get_fields_schema", "get_select_field_options"]}, {"kind": "preset", "key": "bika-search", "includes": ["bika_search_pages", "bika_search_images"]}], "asMember": true}, {"resourceType": "AUTOMATION", "templateId": "atoH8jQ8BKn5BWVRHh4iNVzt", "name": {"en": "Event Mailer", "zh-TW": "发送活动邮件", "zh-CN": "发送活动邮件", "ja": "イベントメール送信"}, "description": {"en": "A 3-day email sequence to introduce Bika.ai’s features and encourage user engagement.", "zh-TW": "一個為期3天的電子郵件序列，用於介紹Bika.ai的功能並鼓勵用戶參與。", "zh-CN": "一个为期3天的电子邮件序列，用于介绍Bika.ai的功能并鼓励用户参与。", "ja": "Bika.aiの機能を紹介し、ユーザーのエンゲージメントを促す3日間のメールシーケンスです。"}, "triggers": [{"triggerType": "MANUALLY", "templateId": "trgNLxK35kwrZarBOg2UTIy5", "description": {"en": "Manually trigger the email sequence", "zh-TW": "手動觸發電子郵件序列", "zh-CN": "手动触发电子邮件序列", "ja": "メールシーケンスを手動でトリガーします"}, "input": {"fields": []}}], "actions": [{"templateId": "actnQrxzfluP08GAT8lokBAZ", "description": {"en": "Send first email", "zh-TW": "發送第一封郵件", "zh-CN": "发送第一封邮件", "ja": "最初のメールを送信します"}, "actionType": "SEND_EMAIL", "input": {"type": "SERVICE", "subject": "Discover the Use Cases of Bika.ai!(1/3)", "body": {"html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <style>\n        body {\n            background-color: white;\n        }\n       .hidden-content {\n            color: white;\n        }\n    </style>\n</head>\n<body>\n    <p>Dear User,</p>\n    <p>Welcome back! In our previous email, we introduced the basics of Bika.ai. Now, we would like to guide you through the core features of our platform, helping you make the most of our services.</p>\n    <p>Bika.ai is a collaborative AI automation platform that proactively reminds and initiates tasks for people. You don’t need to converse with the AI; instead, the AI automatically assists you or your team at scheduled times to complete tasks. Bika.ai offers the following features:</p>\n    <h2>Smart Tasks</h2>\n    <p>AI proactively creates tasks, assigns them, and determines completion automatically without human intervention, such as daily accounting, daily journaling, team tasks, morning meeting reports, and quarterly OKR reviews.</p>\n    <h2>AI Reports</h2>\n    <p>AI generates reports based on your activities or tasks, such as weekly journal summaries, income and expense analysis, sales weekly reports, attendance statistics, etc.</p>\n    <h2>Data Visualization</h2>\n    <p>AI automates task creation, assigns people to collect data, and generates multidimensional tables supporting billions of rows, with charts generated from your data, such as sales figures, customer growth, team performance, and population surveys.</p>\n    <h2>Smart Reminders</h2>\n    <p>Thoughtful reminders for every anniversary and important event, such as friends' birthdays, client gifts, sales visits, and performance tracking.</p>\n    <h2>Voice Input</h2>\n    <p>Talk to Bika to convert your voice into structured data, such as quick inspiration notes, client visit entries, personal journals, and team CRM.</p>\n    <p>To help you quickly master these features, we recommend the following resources:</p>\n    <p>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Feature Demo Videos</a><br>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Detailed User Manual</a>\n    </p>\n    <p>If you have any questions or need further assistance, please feel free to contact us. We look forward to your success with Bika.ai!</p>\n    <p>Best regards,</p>\n    <p>The Bika.ai Team</p>\n    <p class=\"hidden-content\">[<%= _automation.runHistoryId %>]</p>\n</body>\n</html>"}, "to": [{"type": "EMAIL_FIELD", "fieldTemplateId": "fldnwC9UMhe6Cp3TQGbCw00H", "viewTemplateId": "viwLVZr9bXcM5nOS1TMfWHHx", "databaseTemplateId": "datQfXXJPUQ6oePeHrEAfxsB"}], "senderName": "", "cc": [], "bcc": [], "replyTo": []}}, {"templateId": "actONUvoOjL6310x2glE69O1", "description": {"en": "Delay 1 day", "zh-TW": "延遲1天", "zh-CN": "延迟1天", "ja": "1日間遅延します"}, "actionType": "DELAY", "input": {"type": "DELAY", "unit": "DAY", "value": 1}}, {"templateId": "actAIRzSdcmNTsxbeGLOPg4Q", "description": {"en": "Send second email", "zh-TW": "發送第二封郵件", "zh-CN": "发送第二封邮件", "ja": "2通目のメールを送信します"}, "actionType": "SEND_EMAIL", "input": {"type": "SERVICE", "subject": "Discover the Use Cases of Bika.ai!(2/3)", "body": {"html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <style>\n        body {\n            background-color: white;\n        }\n       .hidden-content {\n            color: white;\n        }\n    </style>\n</head>\n<body>\n    <p>Dear User,</p>\n    <p>Welcome back! In our previous email, we introduced the basics of Bika.ai. Now, we would like to guide you through the core features of our platform, helping you make the most of our services.</p>\n    <p>Bika.ai is a collaborative AI automation platform that proactively reminds and initiates tasks for people. You don’t need to converse with the AI; instead, the AI automatically assists you or your team at scheduled times to complete tasks. Bika.ai offers the following features:</p>\n    <h2>Smart Tasks</h2>\n    <p>AI proactively creates tasks, assigns them, and determines completion automatically without human intervention, such as daily accounting, daily journaling, team tasks, morning meeting reports, and quarterly OKR reviews.</p>\n    <h2>AI Reports</h2>\n    <p>AI generates reports based on your activities or tasks, such as weekly journal summaries, income and expense analysis, sales weekly reports, attendance statistics, etc.</p>\n    <h2>Data Visualization</h2>\n    <p>AI automates task creation, assigns people to collect data, and generates multidimensional tables supporting billions of rows, with charts generated from your data, such as sales figures, customer growth, team performance, and population surveys.</p>\n    <h2>Smart Reminders</h2>\n    <p>Thoughtful reminders for every anniversary and important event, such as friends' birthdays, client gifts, sales visits, and performance tracking.</p>\n    <h2>Voice Input</h2>\n    <p>Talk to Bika to convert your voice into structured data, such as quick inspiration notes, client visit entries, personal journals, and team CRM.</p>\n    <p>To help you quickly master these features, we recommend the following resources:</p>\n    <p>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Feature Demo Videos</a><br>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Detailed User Manual</a>\n    </p>\n    <p>If you have any questions or need further assistance, please feel free to contact us. We look forward to your success with Bika.ai!</p>\n    <p>Best regards,</p>\n    <p>The Bika.ai Team</p>\n    <p class=\"hidden-content\">[<%= _automation.runHistoryId %>]</p>\n</body>\n</html>\n    "}, "to": [{"type": "EMAIL_FIELD", "fieldTemplateId": "fldnwC9UMhe6Cp3TQGbCw00H", "viewTemplateId": "viwLVZr9bXcM5nOS1TMfWHHx", "databaseTemplateId": "datQfXXJPUQ6oePeHrEAfxsB"}], "senderName": "", "cc": [], "bcc": [], "replyTo": []}}, {"templateId": "actjx2wlJNEqOJ10XfiHUkcx", "description": {"en": "Delay 1 day", "zh-TW": "延遲1天", "zh-CN": "延迟1天", "ja": "1日間遅延します"}, "actionType": "DELAY", "input": {"type": "DELAY", "unit": "DAY", "value": 1}}, {"templateId": "actVTnAxkPpvWUHdilhPXwLm", "description": {"en": "Send third email", "zh-TW": "發送第三封郵件", "zh-CN": "发送第三封邮件", "ja": "3通目のメールを送信します"}, "actionType": "SEND_EMAIL", "input": {"type": "SERVICE", "subject": "Who Can Benefit from Bika.ai!(3/3)", "body": {"html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <style>\n        body {\n            background-color: white;\n        }\n       .hidden-content {\n            color: white;\n        }\n    </style>\n</head>\n<body>\n    <p>Dear User,</p>\n    <p>Welcome back! In our previous email, we introduced the basics of Bika.ai. Now, we would like to guide you through the core features of our platform, helping you make the most of our services.</p>\n    <p>Bika.ai is a collaborative AI automation platform that proactively reminds and initiates tasks for people. You don’t need to converse with the AI; instead, the AI automatically assists you or your team at scheduled times to complete tasks. Bika.ai offers the following features:</p>\n    <h2>Smart Tasks</h2>\n    <p>AI proactively creates tasks, assigns them, and determines completion automatically without human intervention, such as daily accounting, daily journaling, team tasks, morning meeting reports, and quarterly OKR reviews.</p>\n    <h2>AI Reports</h2>\n    <p>AI generates reports based on your activities or tasks, such as weekly journal summaries, income and expense analysis, sales weekly reports, attendance statistics, etc.</p>\n    <h2>Data Visualization</h2>\n    <p>AI automates task creation, assigns people to collect data, and generates multidimensional tables supporting billions of rows, with charts generated from your data, such as sales figures, customer growth, team performance, and population surveys.</p>\n    <h2>Smart Reminders</h2>\n    <p>Thoughtful reminders for every anniversary and important event, such as friends' birthdays, client gifts, sales visits, and performance tracking.</p>\n    <h2>Voice Input</h2>\n    <p>Talk to Bika to convert your voice into structured data, such as quick inspiration notes, client visit entries, personal journals, and team CRM.</p>\n    <p>To help you quickly master these features, we recommend the following resources:</p>\n    <p>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Feature Demo Videos</a><br>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Detailed User Manual</a>\n    </p>\n    <p>If you have any questions or need further assistance, please feel free to contact us. We look forward to your success with Bika.ai!</p>\n    <p>Best regards,</p>\n    <p>The Bika.ai Team</p>\n    <p class=\"hidden-content\">[<%= _automation.runHistoryId %>]</p>\n</body>\n</html>\n    "}, "to": [{"type": "EMAIL_FIELD", "fieldTemplateId": "fldnwC9UMhe6Cp3TQGbCw00H", "viewTemplateId": "viwLVZr9bXcM5nOS1TMfWHHx", "databaseTemplateId": "datQfXXJPUQ6oePeHrEAfxsB"}], "senderName": "", "cc": [], "bcc": [], "replyTo": []}}]}, {"resourceType": "DATABASE", "templateId": "datQfXXJPUQ6oePeHrEAfxsB", "name": {"en": "3-Days Outreach", "zh-TW": "3天跟進郵件", "zh-CN": "3天跟进邮件", "ja": "3日間のアウトリーチ"}, "description": {"en": "A database to store leads and their contact information for a 3-day email outreach campaign.", "zh-TW": "用於存儲潛在客戶及其聯繫信息的數據庫，用於為期3天的電子郵件跟進活動。", "zh-CN": "用于存储潜在客户及其联系信息的数据库，用于为期3天的电子邮件跟进活动。", "ja": "3日間のメールアウトリーチキャンペーンのためのリードとその連絡先情報を保存するデータベースです。"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwLVZr9bXcM5nOS1TMfWHHx", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": [], "conds": []}, "sorts": [], "fields": [{"templateId": "fldx1Jv9gCwW8tt9UA1AzaqC", "hidden": false}, {"templateId": "fldnwC9UMhe6Cp3TQGbCw00H", "hidden": false}, {"templateId": "fldX30k3SmYQypIVY081aayw", "hidden": false}, {"templateId": "fldVwxZXvNQgXfodSCerFe3w", "hidden": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldx1Jv9gCwW8tt9UA1AzaqC", "privilege": "TYPE_EDIT", "name": "Company", "primary": true}, {"type": "EMAIL", "templateId": "fldnwC9UMhe6Cp3TQGbCw00H", "privilege": "NAME_EDIT", "name": "Email", "primary": false}, {"type": "URL", "templateId": "fldX30k3SmYQypIVY081aayw", "privilege": "NAME_EDIT", "name": "URL", "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldVwxZXvNQgXfodSCerFe3w", "privilege": "NAME_EDIT", "name": "Status", "property": {"options": [{"id": "opt7x08UIN89l", "name": "Not Sent", "color": "orange5"}, {"id": "optMeAHqfv1tN", "name": "Sending", "color": "indigo"}, {"id": "optpL71TxLIP3", "name": "<PERSON><PERSON>", "color": "deepPurple5"}], "defaultValue": "opt7x08UIN89l"}, "primary": false}], "records": [{"templateId": "recfl3Pv5SVgkuAIxjo41nGR", "data": {"fldVwxZXvNQgXfodSCerFe3w": ["opt7x08UIN89l"], "fldX30k3SmYQypIVY081aayw": "https://relevanceai.com", "fldx1Jv9gCwW8tt9UA1AzaqC": "Relevance AI"}, "values": {"fldVwxZXvNQgXfodSCerFe3w": ["Not Sent"], "fldX30k3SmYQypIVY081aayw": "https://relevanceai.com", "fldx1Jv9gCwW8tt9UA1AzaqC": "Relevance AI"}}, {"templateId": "rec3BmQRw9QU0QW2FvTD15IQ", "data": {"fldVwxZXvNQgXfodSCerFe3w": ["opt7x08UIN89l"], "fldX30k3SmYQypIVY081aayw": "https://decagon.ai", "fldx1Jv9gCwW8tt9UA1AzaqC": "Decagon"}, "values": {"fldVwxZXvNQgXfodSCerFe3w": ["Not Sent"], "fldX30k3SmYQypIVY081aayw": "https://decagon.ai", "fldx1Jv9gCwW8tt9UA1AzaqC": "Decagon"}}, {"templateId": "reccsUcbMFQoDGE8AjLNtSWT", "data": {"fldVwxZXvNQgXfodSCerFe3w": ["opt7x08UIN89l"], "fldX30k3SmYQypIVY081aayw": "https://mobidev.biz", "fldx1Jv9gCwW8tt9UA1AzaqC": "<PERSON><PERSON><PERSON><PERSON>"}, "values": {"fldVwxZXvNQgXfodSCerFe3w": ["Not Sent"], "fldX30k3SmYQypIVY081aayw": "https://mobidev.biz", "fldx1Jv9gCwW8tt9UA1AzaqC": "<PERSON><PERSON><PERSON><PERSON>"}}, {"templateId": "rec5cLVCjDnaJKpuMsN7wKAw", "data": {"fldVwxZXvNQgXfodSCerFe3w": ["opt7x08UIN89l"], "fldX30k3SmYQypIVY081aayw": "https://idealink.tech", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Idea Link"}, "values": {"fldVwxZXvNQgXfodSCerFe3w": ["Not Sent"], "fldX30k3SmYQypIVY081aayw": "https://idealink.tech", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Idea Link"}}, {"templateId": "recpHEMX7JmVbhCM2hX2gNvj", "data": {"fldVwxZXvNQgXfodSCerFe3w": ["opt7x08UIN89l"], "fldX30k3SmYQypIVY081aayw": "https://oyelabs.com", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Oyelabs"}, "values": {"fldVwxZXvNQgXfodSCerFe3w": ["Not Sent"], "fldX30k3SmYQypIVY081aayw": "https://oyelabs.com", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Oyelabs"}}, {"templateId": "recbNHAZMNrcjMZk36XNyXPp", "data": {"fldVwxZXvNQgXfodSCerFe3w": ["opt7x08UIN89l"], "fldX30k3SmYQypIVY081aayw": "https://www.intuz.com", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Intuz"}, "values": {"fldVwxZXvNQgXfodSCerFe3w": ["Not Sent"], "fldX30k3SmYQypIVY081aayw": "https://www.intuz.com", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Intuz"}}, {"templateId": "recbCbk4vuRGfJ5D2OmP1Ib8", "data": {"fldVwxZXvNQgXfodSCerFe3w": ["opt7x08UIN89l"], "fldX30k3SmYQypIVY081aayw": "https://idealink.tech", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Idea Link"}, "values": {"fldVwxZXvNQgXfodSCerFe3w": ["Not Sent"], "fldX30k3SmYQypIVY081aayw": "https://idealink.tech", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Idea Link"}}, {"templateId": "recZjRoiYlr0GjBExzRwXLxV", "data": {"fldVwxZXvNQgXfodSCerFe3w": ["opt7x08UIN89l"], "fldX30k3SmYQypIVY081aayw": "https://relevanceai.com", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Relevance AI"}, "values": {"fldVwxZXvNQgXfodSCerFe3w": ["Not Sent"], "fldX30k3SmYQypIVY081aayw": "https://relevanceai.com", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Relevance AI"}}, {"templateId": "recM20ihHkBbnRqjN5edmNqq", "data": {"fldVwxZXvNQgXfodSCerFe3w": ["opt7x08UIN89l"], "fldX30k3SmYQypIVY081aayw": "https://www.lindy.ai", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Lindy AI"}, "values": {"fldVwxZXvNQgXfodSCerFe3w": ["Not Sent"], "fldX30k3SmYQypIVY081aayw": "https://www.lindy.ai", "fldnwC9UMhe6Cp3TQGbCw00H": "<EMAIL>", "fldx1Jv9gCwW8tt9UA1AzaqC": "Lindy AI"}}]}], "initMissions": []}