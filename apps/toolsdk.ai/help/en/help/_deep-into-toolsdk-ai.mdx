---
title: 深入 ToolSDK.ai
slug: toolsdk.ai/help/en/help/deep-into-toolsdk-ai
sidebar_position: 1
---


# 深入 ToolSDK.ai

# Architecture

（图）

# Concepts

可以用来无限扩展AI自动化的能力，接入到你的系统里面，或者扩展你的那些其他的软件系统的能力。

那为了那个方便后面的那个理解，因为这个相当来，相当相对来说就有一些那一个技术的术语，所以我就先做一些名词的那个解释释义。

以下是名词释义：

- Package: MCP Server、Zapier App和 ToolApp（完整兼容 Zapier App 扩展，加入 ToolApp 的能力）
- Configuration：集成，应用，通常是环境变量和验证信息（Authentication）
- Component：组件，包括 Tools、Configuration
- Developer：toolsdk.ai的登录用户、开发者，制作Action和Integration的主体
- Account: the end user id in your apps, ToolSDK.ai uses this to store integration credentials for that end user.
- Consumer: the binding entity in your apps, typically the ID of your business system instance. For example, if you're working on an Automation Action, this would be the action's ID. Formapp.ai uses this ID to store form input values.

# Package 对比

ToolSDK.ai 支持多种类型的包，兼容 MCP Server、Zapier App 等。其插件可以通过上传统一接口，为你的 AI 和 SaaS UI 软件提供服务，并连接第三方应用。

<table>
  <colgroup>
    <col width="179"/>
    <col width="147"/>
    <col width="141"/>
    <col width="238"/>
  </colgroup>
  <tbody>
    <tr>
      <td><p><b>Package</b></p></td>
      <td><p><b>ToolApp</b></p></td>
      <td><p><b>Zapier App</b></p></td>
      <td><p><b>MCP Server</b></p></td>
    </tr>
    <tr>
      <td rowspan="5"><p><b>Component</b></p></td>
      <td><p>Configuration</p></td>
      <td><p>Authentication</p></td>
      <td><p>Configuration</p></td>
    </tr>
    <tr>
      <td><p>Tool</p></td>
      <td><p>Action</p></td>
      <td><p>Tool</p></td>
    </tr>
    <tr>
      <td><p>Trigger</p></td>
      <td><p>Trigger</p></td>
      <td><ul><li>N/A</li></ul></td>
    </tr>
    <tr>
      <td><p>Resource</p></td>
      <td><p>Resource</p></td>
      <td><p>Resource</p></td>
    </tr>
    <tr>
      <td><p>Prompt</p></td>
      <td><ul><li>N/A</li></ul></td>
      <td><p>Prompt</p></td>
    </tr>
  </tbody>
</table>
