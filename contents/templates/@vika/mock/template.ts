import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: '<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>',
  templateId: '@vika/mock',
  // 无对应的模板封面图，使用默认缺图
  cover: '/assets/template/template-cover-no-image-color.png',
  name: '维格 Mock集成测试模板',
  description: '一次性安装常用模板，突显各种功能，方便进行测试',
  visibility: 'PRIVATE',
  version: '0.1.1',
  category: 'project',
  keywords: {
    'zh-CN': '测试, mock, okr, scrum, standup, 内部, 开发',
    en: 'test, mock, okr, scrum, standup, internal, development',
    'zh-TW': '測試, mock, okr, scrum, standup, 內部, 開發',
    ja: 'テスト, mock, okr, scrum, standup, 内部, 開発',
  },
  personas: {
    'zh-CN': '产品经理, 开发, 测试, 设计',
    en: 'Product Manager, Developer, Tester, Designer',
    'zh-TW': '產品經理, 開發, 測試, 設計',
    ja: 'プロダクトマネージャー, 開発者, テスター, デザイナー',
  },
  useCases: {
    'zh-CN':
      '团队OKR管理, 项目计划与任务追踪, 销售机会管理, 工作流程优化, 绩效评估和考核, 团队晨会记录, 客户关系管理, 日程安排与提醒, 目标追踪与达成, 任务分配与协作, 工作时间管理, 数据分析与报告, 团队协同与沟通, 项目进度跟踪, 销售业绩分析, 客户沟通记录, 个人时间管理, 任务优先级管理, 团队绩效分析, 项目资源调配, 销售漏斗管理, 客户数据可视化, 团队目标协作, 工作事项提醒',
    en: 'Team OKR Management, Project Planning and Task Tracking, Sales Opportunity Management, Workflow Optimization, Performance Evaluation and Assessment, Team Standup Record, Customer Relationship Management, Schedule and Reminder, Goal Tracking and Achievement, Task Assignment and Collaboration, Work Time Management, Data Analysis and Reporting, Team Collaboration and Communication, Project Progress Tracking, Sales Performance Analysis, Customer Communication Record, Personal Time Management, Task Priority Management, Team Performance Analysis, Project Resource Allocation, Sales Funnel Management, Customer Data Visualization, Team Goal Collaboration, Work Item Reminder',
    'zh-TW':
      '團隊OKR管理, 項目計劃與任務追踪, 銷售機會管理, 工作流程優化, 績效評估和考核, 團隊晨會記錄, 客戶關係管理, 日程安排與提醒, 目標追踪與達成, 任務分配與協作, 工作時間管理, 數據分析與報告, 團隊協同與溝通, 項目進度跟踪, 銷售業績分析, 客戶溝通記錄, 個人時間管理, 任務優先級管理, 團隊績效分析, 項目資源調配, 銷售漏斗管理, 客戶數據可視化, 團隊目標協作, 工作事項提醒',
    ja: 'チームOKR管理, プロジェクト計画とタスク追跡, 営業機会管理, ワークフローの最適化, パフォーマンス評価と評価, チームスタンドアップ記録, 顧客関係管理, スケジュールとリマインダー, 目標の追跡と達成, タスクの割り当てと協力, 労働時間の管理, データ分析と報告, チームの協力とコミュニケーション, プロジェクトの進捗状況の追跡, 営業パフォーマンス分析, 顧客コミュニケーション記録, 個人の時間管理, タスクの優先順位管理, チームパフォーマンス分析, プロジェクトリソースの割り当て, 営業ファネル管理, 顧客データの視覚化, チーム目標の協力, 作業項目のリマインダー',
  },
  initMissions: [
    {
      type: 'QUEST',
      name: 'MOCK:新手系列任务',
      canReject: true,
      missions: [
        {
          type: 'SET_SPACE_NAME',
          name: '去修改空间站名称',
          description: '你可以自定义空间站名称，快去设置吧！',
          to: [{ type: 'ALL_MEMBERS' }], // 通常这里，只有创建人1个人，即自己
          canReject: true,
        },
      ],
      to: [{ type: 'ALL_MEMBERS' }],
    },
    {
      type: 'SEQUENCE',
      name: 'MOCK:新手序列任务',
      canReject: true,
      missions: [
        {
          type: 'SET_SPACE_NAME',
          name: '去修改空间站名称',
          description: '你可以自定义空间站名称，快去设置吧！',
          to: [{ type: 'ALL_MEMBERS' }],
          canReject: true,
        },
      ],
      to: [{ type: 'ALL_MEMBERS' }],
    },
  ],
  dependencies: {
    'base-team': '*',
    '@vika/okr': '*',
    '@vika/scrum-standup': '*',
  },
  resources: [
    {
      resourceType: 'FOLDER',
      name: 'mock文件夹-1',
      templateId: 'folder_mock_1',
      children: [
        {
          resourceType: 'FOLDER',
          name: 'mock子文件夹-1-1',
          templateId: 'folder_mock_1_1',
          children: [
            {
              resourceType: 'DATABASE',
              name: 'mock空数据库-1-1-1',
              templateId: 'database_empty_1_1_1',
              databaseType: 'DATUM',
            },
            {
              resourceType: 'DATABASE',
              name: 'mock空数据库-1-1-2',
              templateId: 'database_empty_1_1_2',
              databaseType: 'DATUM',
            },
          ],
        },
      ],
    },
    {
      resourceType: 'FOLDER',
      name: 'mock文件夹-2',
      templateId: 'folder_mock_2',
      children: [
        {
          resourceType: 'FOLDER',
          name: 'mock子文件夹-2-1',
          templateId: 'folder_mock_2_1',
          children: [
            {
              resourceType: 'DATABASE',
              name: 'mock空数据库-2-1-1',
              templateId: 'database_empty_2_1_1',
              databaseType: 'DATUM',
            },
            {
              resourceType: 'DATABASE',
              name: 'mock空数据库-2-1-2',
              templateId: 'database_empty_2_1_2',
              databaseType: 'DATUM',
            },
          ],
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      name: 'mock空数据库-3',
      templateId: 'database_empty_3',
      databaseType: 'DATUM',
    },
    {
      resourceType: 'DATABASE',
      templateId: 'test_link_base',
      databaseType: 'DATUM',
      name: 'test_link_base',
      fields: [
        {
          templateId: 'name',
          name: 'name',
          type: 'LONG_TEXT',
        },
        {
          templateId: 'two_way_link',
          name: 'two_way_link',
          type: 'LINK',
          property: {
            foreignDatabaseTemplateId: '@vika/mock:test_link',
            brotherFieldTemplateId: '@vika/mock:test_link:two_way_link',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'test_link',
      databaseType: 'DATUM',
      name: 'test_link',
      fields: [
        {
          templateId: 'link_name',
          name: 'name',
          type: 'LONG_TEXT',
        },
        {
          templateId: 'one_way_link',
          name: 'one_way_link',
          type: 'ONE_WAY_LINK',
          property: {
            foreignDatabaseTemplateId: '@vika/mock:test_link_base',
          },
        },
        {
          templateId: 'lookup',
          name: 'lookup_name',
          type: 'LOOKUP',
          property: {
            relatedLinkFieldTemplateId: '@vika/mock:test_link:one_way_link',
            lookupTargetFieldTemplateId: '@vika/mock:test_link_base:name',
          },
        },
        {
          templateId: 'two_way_link',
          name: 'two_way_link',
          type: 'LINK',
          property: {
            foreignDatabaseTemplateId: '@vika/mock:test_link_base',
            brotherFieldTemplateId: '@vika/mock:test_link_base:two_way_link',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'database_test_date_range',
      databaseType: 'DATUM',
      name: 'test_date_range',
      fields: [
        {
          templateId: 'field_name',
          name: 'name',
          type: 'LONG_TEXT',
        },
        {
          templateId: 'field_date_range',
          name: 'date_range',
          type: 'DATERANGE',
          property: {
            dateFormat: 'DD/MM/YYYY',
            includeTime: true,
            timeZone: 'Asia/Tokyo',
            timeFormat: 'hh:mm',
          },
        },
      ],
    },
  ],
};
export default template;
