import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: 'pengjin <<EMAIL>>',
  templateId: 'automated-get-currency-info-js',
  name: {
    en: 'Automated Currency Data Retrieval (JavaScript)',
    ja: '自動取得通貨情報 (JavaScript)',
    'zh-TW': '自動獲取匯率數據 (JavaScript)',
    'zh-CN': '自动获取汇率数据 (JavaScript)',
  },
  cover: '/assets/template/template-cover-automated-get-currency-info-js.png',
  description: {
    en: 'Automatically get specific currency rate information every day and save it to a table. Users can easily track and analyze currency trends, save time, and improve investment decisions.',
    ja: '毎日特定の通貨レート情報を取得し、それを表に保存します。ユーザーは通貨のトレンドを簡単に追跡・分析でき、時間を節約し、投資判断を改善できます。',
    'zh-TW': '每天自動獲取特定匯率的信息並保存到表格中，使用者可以輕鬆追蹤和分析匯率趨勢，節省時間並改善投資決策。',
    'zh-CN': '每天定时获取特定汇率的信息保存到表格中，可以轻松追踪和分析汇率趋势，节省时间并改善投资决策。',
  },
  keywords: {
    en: 'Automated exchange rate data, Daily exchange rate updates, Investment analysis, Financial management',
    ja: '顧客行動分析, リスク予測モデル, ポートフォリオ最適化, 自動為替レート更新',
    'zh-TW': '客戶行為分析, 風險預測模型, 投資組合優化, 自動匯率刷新',
    'zh-CN': '客户行为分析, 风险预测模型, 投资组合优化, 自动汇率刷新',
  },
  personas: {
    en: 'Data Analyst,Risk Management Consultant, Portfolio Manager, Financial Data Analyst',
    'zh-CN': '数据分析师, 风险管理顾问, 投资组合经理, 财务数据分析师',
    'zh-TW': '數據分析師, 風險管理顧問, 投資組合經理, 財務數據分析師',
    ja: 'データアナリスト, リスク管理コンサルタント, ポートフォリオマネージャー, 財務データアナリスト',
  },
  useCases: {
    en: 'Automatic Exchange Rate Refresh, Real-Time Financial Data Storage, Financial Performance Evaluation, Portfolio Analysis, Risk Data Analysis, Financial Report Automation, Financial Data Integration, Customer Behavior Analysis, Market Trend Analysis, Customer Segmentation, Data Visualization, User Feedback Analysis, Sales Data Analysis, Risk Prediction Model Development, Risk Assessment Report, Risk Mitigation Strategy Formulation, Internal Control Evaluation, Risk Training and Drills, Emergency Response Planning, Real-Time Risk Monitoring, Portfolio Optimization, Investment Return Analysis, Risk Assessment, Market Opportunity Identification, Asset Allocation Recommendation, Portfolio Report Generation',
    'zh-CN':
      '自动汇率刷新, 财务数据实时存储, 财务绩效评估, 投资组合分析, 风险数据分析, 财务报告自动化, 财务数据整合, 客户行为分析, 市场趋势分析, 客户细分, 数据可视化, 用户反馈分析, 销售数据分析, 风险预测模型开发, 风险评估报告, 风险缓解策略制定, 内部控制评估, 风险培训和演练, 应急预案制定, 实时风险监控, 投资组合优化, 投资回报分析, 风险评估, 市场机会识别, 资产配置建议, 投资组合报告生成',
    'zh-TW':
      '自動匯率刷新, 財務數據實時存儲, 財務績效評估, 投資組合分析, 風險數據分析, 財務報告自動化, 財務數據整合, 客戶行為分析, 市場趨勢分析, 客戶細分, 數據可視化, 用戶反饋分析, 銷售數據分析, 風險預測模型開發, 風險評估報告, 風險緩解策略制定, 內部控制評估, 風險培訓和演練, 應急預案制定, 實時風險監控, 投資組合優化, 投資回報分析, 風險評估, 市場機會識別, 資產配置建議, 投資組合報告生成',
    ja: '自動為替レート更新, 財務データのリアルタイム保存, 財務パフォーマンス評価, ポートフォリオ分析, リスクデータ分析, 財務報告の自動化, 財務データ統合, 顧客行動分析, 市場トレンド分析, 顧客セグメンテーション, データの可視化, ユーザーフィードバック分析, 販売データ分析, リスク予測モデルの開発, リスク評価報告書, リスク軽減戦略の策定, 内部統制の評価, リスクトレーニングと訓練, 緊急対応計画, リアルタイムリスク監視, ポートフォリオ最適化, 投資収益分析, リスク評価, 市場機会の識別, 資産配分の提案, ポートフォリオ報告書の作成',
  },
  version: '1.0.3',
  visibility: 'PUBLIC',
  category: ['finance', 'script'],
  initMissions: [
    {
      name: {
        'zh-CN': '💡自动获取汇率数据 (JavaScript)模板使用须知',
        'zh-TW': '💡自動獲取匯率數據 (JavaScript) 模板使用須知',
        ja: '💡為替レートデータを自動取得する (JavaScript) テンプレート使用の注意',
        en: '💡Instructions for Using the Template to Automatically Retrieve Exchange Rate Data (JavaScript)',
      },
      type: 'READ_TEMPLATE_README',
      templateId: 'automated-get-currency-info-js',
      time: 5,
      beforeText: {
        title: {
          'zh-CN': '恭喜您，模板已经安装完毕',
          'zh-TW': '恭喜您，模板已經安裝完畢',
          ja: 'おめでとうございます、テンプレートのインストールが完了しました',
          en: 'Congratulations, the template has been installed',
        },
        description: {
          'zh-CN': '下一步请您花几分钟阅读模板的的使用教程。',
          'zh-TW': '下一步請您花幾分鐘閱讀模板的使用教程。',
          en: 'Next, please take a few minutes to read the tutorial on how to use the template.',
          ja: '次に、テンプレートの使用方法についてのチュートリアルを数分間お読みください。',
        },
      },
      assignType: 'DEDICATED',
      forcePopup: true,
      wizardGuideId: 'COMMON_MY_TODO_TUTORIAL',
      redirect: {
        type: 'MY_MISSIONS',
      },
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
    {
      name: {
        en: '💡Automated get stock info initialization task: Check the effect of writing stock data',
        'zh-CN': '💡自动获取汇率数据初始化任务：查看汇率数据写入效果',
        'zh-TW': '💡自動獲取匯率數據初始化任務：檢查匯率數據寫入效果',
        ja: '💡自動取得通貨情報初期化タスク: 通貨情報の書き込み効果を確認',
      },
      description: {
        en: 'Head over to see how to write stock information into a table, and then specific stock information will be automatically acquired and written into the table every day at 10:00 AM',
        'zh-CN': '前往查看如何将汇率信息写入表格中，之后每天上午 10:00 将会自动获取特定汇率信息并写入表格中',
        'zh-TW': '前往查看如何將匯率信息寫入表格中，之後每天上午 10:00 將會自動獲取特定匯率信息並寫入表格中',
        ja: 'テーブルに株価情報を書き込む方法を確認してください。その後、特定の株価情報が毎日午前 10 時に自動的に取得され、テーブルに書き込まれます',
      },
      type: 'REDIRECT_SPACE_NODE',
      nodeTemplateId: 'currency_automation',
      buttonText: {
        en: 'Go to',
        'zh-CN': '前往',
        'zh-TW': '前往',
        ja: '前往',
      },
      assignType: 'SHARE',
      to: [
        {
          type: 'ADMIN',
        },
      ],
      canCompleteManually: true,
      wizardGuideId: 'COMMON_AUTOMATION_TUTORIAL',
    },
  ],
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'currency_automation',
      name: {
        en: 'Currency Automation',
        ja: '通貨情報自動化',
        'zh-CN': '汇率信息自动化',
        'zh-TW': '匯率信息自動化',
      },
      description: {
        en: 'Get specific stock information every day at 10 AM and write it to a table',
        ja: '毎日午前 10 時に特定の通貨情報を取得し、テーブルに書き込みます',
        'zh-CN': '每天上午 10 点获取当天的指定汇率信息，写入到表格中',
        'zh-TW': '每天上午 10 點獲取當天的指定匯率信息，寫入到表格中',
      },
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'scheduler',
          description: {
            en: 'Triggered every day at 10 AM',
            ja: '毎日午前 10 時にトリガー',
            'zh-CN': '每天上午 10 点触发',
            'zh-TW': '每天上午 10 點觸發',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              repeat: {
                every: {
                  type: 'DAY',
                  interval: 1,
                },
              },
              timezone: 'AUTO',
              datetime: '2025-01-21T02:00:00.000Z',
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'get_currency_rate',
          description: {
            en: 'Get currency rate information',
            ja: '通貨レート情報を取得',
            'zh-CN': '获取汇率信息',
            'zh-TW': '獲取匯率信息',
          },
          actionType: 'RUN_SCRIPT',
          input: {
            type: 'SCRIPT',
            language: 'javascript',
            script:
              '\n' +
              '(async () => {\n' +
              "const url = 'https://api.exchangerate-api.com/v4/latest/USD';\n" +
              'const response = await fetch(url);\n' +
              'if (!response.ok) {\n' +
              '  throw new Error(response.status);\n' +
              '}\n' +
              'return await response.json();\n' +
              '})()\n',
          },
        },
        {
          templateId: 'write_currency_rate',
          description: {
            en: 'Write currency rate information to the table',
            ja: '通貨レート情報をテーブルに書き込む',
            'zh-CN': '将汇率信息写入表格',
            'zh-TW': '將匯率信息寫入表格',
          },
          actionType: 'CREATE_RECORD',
          input: {
            type: 'RECORD_BODY',
            fieldKeyType: 'TEMPLATE_ID',
            data: {
              jpy: '¥<%= _actions.get_currency_rate.rates.JPY %>',
              cny: '¥<%= _actions.get_currency_rate.rates.CNY %>',
              gbp: '£<%= _actions.get_currency_rate.rates.GBP %>',
              aud: 'A<%= _actions.get_currency_rate.rates.AUD %>',
              hkd: 'HK<%= _actions.get_currency_rate.rates.HKD %>',
              eur: '€<%= _actions.get_currency_rate.rates.EUR %>',
              usd: '$<%= _actions.get_currency_rate.rates.USD %>',
              sgd: 'S<%= _actions.get_currency_rate.rates.SGD %>',
              krw: '₩<%= _actions.get_currency_rate.rates.KRW %>',
              cad: 'C<%= _actions.get_currency_rate.rates.CAD %>',
            },
            databaseTemplateId: 'currency_database',
          },
        },
        {
          templateId: 'actXTX1JVrUpRvJO5QxuyJaR',
          description: {
            en: "Query 7 day's exchange rate information",
            ja: '過去7日間の為替レート情報を照会する',
            'zh-CN': '查询过去7天的汇率信息',
            'zh-TW': '查詢过去7天的匯率資訊',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_VIEW',
            viewTemplateId: 'viwSdx792qF6ugXmIraxWCIm',
            databaseTemplateId: 'currency_database',
          },
        },
        {
          templateId: 'actYEQc4IGjlyWgbbFYFMBPc',
          description: {
            en: 'Send exchange rate information report',
            ja: '為替レート情報レポートを送信する',
            'zh-CN': '发送汇率信息报告',
            'zh-TW': '發送匯率信息報告',
          },
          actionType: 'SEND_REPORT',
          input: {
            to: [
              {
                type: 'CURRENT_OPERATOR',
              },
            ],
            markdown:
              '### Exchange rate information for the past 7 days is as follows:       \n' +
              "<%= _renderRecordsAsGrid(_actions.actXTX1JVrUpRvJO5QxuyJaR.records, ['usd','eur','jpy','gbp','cny','hkd','aud','cad','sgd','krw','created_time']) %>",
            json: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      text: '### Exchange rate information for the past 7 days is as follows:     ',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      type: 'variable',
                      attrs: {
                        ids: "_renderRecordsAsGrid(_actions.actXTX1JVrUpRvJO5QxuyJaR.records, ['usd','eur','jpy','gbp','cny','hkd','aud','cad','sgd','krw','created_time'])",
                        tips: 'Selected Fields: USD, EUR, JPY, GBP, CNY, HKD, AUD, CAD, SGD, KRW, Created At',
                        names: ['Actions', 'Find Records', 'Grid List'],
                      },
                    },
                  ],
                },
              ],
            },
            subject: 'Exchange Rate Information Report',
            type: 'MARKDOWN',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'currency_database',
      name: {
        en: 'Exchange Rate Data',
        ja: '通貨データ',
        'zh-CN': '汇率数据',
        'zh-TW': '匯率數據',
      },
      description: {
        en: 'Store the currency information fetched daily',
        ja: '毎日取得した通貨情報を保存します',
        'zh-CN': '存储每日抓取的汇率信息',
        'zh-TW': '存儲每日抓取的匯率信息',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwjlwSOiXPS8dw53feKba5o',
          name: {
            en: 'Overall View',
            ja: '総合ビュー (そうごうビュー)',
            'zh-CN': '总视图',
            'zh-TW': '總視圖',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'usd',
              hidden: false,
            },
            {
              templateId: 'eur',
              hidden: false,
            },
            {
              templateId: 'jpy',
              hidden: false,
            },
            {
              templateId: 'gbp',
              hidden: false,
            },
            {
              templateId: 'cny',
              hidden: false,
              width: 150,
            },
            {
              templateId: 'hkd',
              hidden: false,
            },
            {
              templateId: 'aud',
              hidden: false,
            },
            {
              templateId: 'cad',
              hidden: false,
            },
            {
              templateId: 'sgd',
              hidden: false,
            },
            {
              templateId: 'krw',
              hidden: false,
            },
            {
              templateId: 'created_time',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwSdx792qF6ugXmIraxWCIm',
          name: {
            en: '7 Days FX Rate Recap',
            ja: '過去7日間の為替レート情報',
            'zh-CN': '过去7天汇率信息',
            'zh-TW': '過去7天匯率資訊',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'created_time',
                fieldType: 'CREATED_TIME',
                clause: {
                  operator: 'IsGreaterEqual',
                  value: ['SomeDayBefore', 7],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'usd',
              hidden: false,
            },
            {
              templateId: 'eur',
              hidden: false,
            },
            {
              templateId: 'jpy',
              hidden: false,
            },
            {
              templateId: 'gbp',
              hidden: false,
            },
            {
              templateId: 'cny',
              hidden: false,
            },
            {
              templateId: 'hkd',
              hidden: false,
            },
            {
              templateId: 'aud',
              hidden: false,
            },
            {
              templateId: 'cad',
              hidden: false,
            },
            {
              templateId: 'sgd',
              hidden: false,
            },
            {
              templateId: 'krw',
              hidden: false,
            },
            {
              templateId: 'created_time',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'usd',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'USD',
            ja: '米ドル',
            'zh-CN': '美元',
            'zh-TW': '美元',
          },
          required: true,
          primary: true,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'eur',
          privilege: 'NAME_EDIT',
          name: {
            en: 'EUR',
            ja: 'ユーロ',
            'zh-CN': '欧元',
            'zh-TW': '歐元',
          },
          required: true,
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'jpy',
          privilege: 'NAME_EDIT',
          name: {
            en: 'JPY',
            ja: '円',
            'zh-CN': '日元',
            'zh-TW': '日元',
          },
          required: true,
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'gbp',
          privilege: 'NAME_EDIT',
          name: {
            en: 'GBP',
            ja: 'ポンド',
            'zh-CN': '英镑',
            'zh-TW': '英鎊',
          },
          required: true,
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'cny',
          privilege: 'NAME_EDIT',
          name: {
            en: 'CNY',
            ja: '人民元',
            'zh-CN': '人民币',
            'zh-TW': '人民幣',
          },
          required: true,
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'hkd',
          privilege: 'NAME_EDIT',
          name: {
            en: 'HKD',
            ja: '香港ドル',
            'zh-CN': '港币',
            'zh-TW': '港幣',
          },
          required: true,
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'aud',
          privilege: 'NAME_EDIT',
          name: {
            en: 'AUD',
            ja: '豪ドル',
            'zh-CN': '澳元',
            'zh-TW': '澳幣',
          },
          required: true,
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'cad',
          privilege: 'NAME_EDIT',
          name: {
            en: 'CAD',
            ja: 'カナダドル',
            'zh-CN': '加元',
            'zh-TW': '加幣',
          },
          required: true,
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'sgd',
          privilege: 'NAME_EDIT',
          name: {
            en: 'SGD',
            ja: 'シンガポールドル',
            'zh-CN': '新加坡元',
            'zh-TW': '新加坡元',
          },
          required: true,
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'krw',
          privilege: 'NAME_EDIT',
          name: {
            en: 'KRW',
            ja: 'ウォン',
            'zh-CN': '韩元',
            'zh-TW': '韓元',
          },
          required: true,
          primary: false,
        },
        {
          type: 'CREATED_TIME',
          templateId: 'created_time',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Created At',
            ja: '作成日時',
            'zh-CN': '创建时间',
            'zh-TW': '創建時間',
          },
          property: {
            timeZone: 'Asia/Shanghai',
            dateFormat: 'YYYY/MM/DD',
            includeTime: false,
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recbK641dGAy4PssU3rljZke',
          data: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
          values: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
        },
        {
          templateId: 'recpn7HpQr3AgIhCln2GsWLq',
          data: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
          values: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
        },
        {
          templateId: 'recwZiTlulOMULE0q8Yezrrw',
          data: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
          values: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
        },
        {
          templateId: 'recwDcWaTGZHoV4pMnHiaLBO',
          data: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
          values: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
        },
        {
          templateId: 'recMJtWnXApHpq5XxMwNwANq',
          data: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
          values: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
        },
        {
          templateId: 'rechXhbzSH03VrbcknONoOhB',
          data: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
          values: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.28',
            eur: '€0.961',
            gbp: '£0.8',
            hkd: 'HK7.79',
            jpy: '¥152.62',
            krw: '₩1445.16',
            sgd: 'S1.35',
            usd: '$1',
          },
        },
        {
          templateId: 'reckpAQDDJBJrmr4kGmDZjU2',
          data: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.29',
            eur: '€0.963',
            gbp: '£0.805',
            hkd: 'HK7.79',
            jpy: '¥151.75',
            krw: '₩1447.04',
            sgd: 'S1.35',
            usd: '$1',
          },
          values: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.29',
            eur: '€0.963',
            gbp: '£0.805',
            hkd: 'HK7.79',
            jpy: '¥151.75',
            krw: '₩1447.04',
            sgd: 'S1.35',
            usd: '$1',
          },
        },
        {
          templateId: 'rect8HDX8WZaSDuqmJKAJunk',
          data: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.29',
            eur: '€0.967',
            gbp: '£0.805',
            hkd: 'HK7.79',
            jpy: '¥151.59',
            krw: '₩1450.03',
            sgd: 'S1.35',
            usd: '$1',
          },
          values: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.29',
            eur: '€0.967',
            gbp: '£0.805',
            hkd: 'HK7.79',
            jpy: '¥151.59',
            krw: '₩1450.03',
            sgd: 'S1.35',
            usd: '$1',
          },
        },
        {
          templateId: 'recMwc8ZFxvkjz5GnEuTlJDw',
          data: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.29',
            eur: '€0.967',
            gbp: '£0.806',
            hkd: 'HK7.79',
            jpy: '¥151.58',
            krw: '₩1448.47',
            sgd: 'S1.35',
            usd: '$1',
          },
          values: {
            aud: 'A1.59',
            cad: 'C1.43',
            cny: '¥7.29',
            eur: '€0.967',
            gbp: '£0.806',
            hkd: 'HK7.79',
            jpy: '¥151.58',
            krw: '₩1448.47',
            sgd: 'S1.35',
            usd: '$1',
          },
        },
        {
          templateId: 'recFjOBnfERbHOV3INwxbMSJ',
          data: {
            aud: 'A1.6',
            cad: 'C1.43',
            cny: '¥7.31',
            eur: '€0.969',
            gbp: '£0.807',
            hkd: 'HK7.79',
            jpy: '¥151.82',
            krw: '₩1452.77',
            sgd: 'S1.36',
            usd: '$1',
          },
          values: {
            aud: 'A1.6',
            cad: 'C1.43',
            cny: '¥7.31',
            eur: '€0.969',
            gbp: '£0.807',
            hkd: 'HK7.79',
            jpy: '¥151.82',
            krw: '₩1452.77',
            sgd: 'S1.36',
            usd: '$1',
          },
        },
      ],
    },
  ],
};

export default template;
