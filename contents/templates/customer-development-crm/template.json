{"templateId": "customer-development-crm", "name": "Customer development CRM", "description": "A customer development CRM template to manage customer personas, interviews, problem statements, and feature enhancements for product improvement", "cover": "/assets/template/customer-development-crm/cover.png", "author": "<PERSON><PERSON> <<EMAIL>>", "category": ["sales", "marketing"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.2", "resources": [{"resourceType": "DATABASE", "templateId": "database_features", "name": "Features/Enhancements", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all_feature", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "feat_name"}, {"templateId": "link_problem", "width": 150}, {"templateId": "cost"}, {"templateId": "impact"}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "feat_name", "privilege": "TYPE_EDIT", "name": "Name", "primary": true}, {"type": "LINK", "templateId": "link_problem", "privilege": "NAME_EDIT", "name": "Problem Statements", "property": {"foreignDatabaseTemplateId": "database_problem", "brotherFieldTemplateId": "database_problem:link_feat"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "cost", "privilege": "NAME_EDIT", "name": "Cost", "property": {"options": [{"id": "opt1", "name": "Low", "color": "pink2"}, {"id": "opt2", "name": "Medium", "color": "indigo2"}, {"id": "opt3", "name": "High", "color": "deepPurple2"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "impact", "privilege": "NAME_EDIT", "name": "Impact", "property": {"options": [{"id": "opta", "name": "Low", "color": "blue2"}, {"id": "optb", "name": "Medium", "color": "indigo2"}, {"id": "optc", "name": "High", "color": "deepPurple2"}], "defaultValue": ""}, "primary": false}], "records": [{"templateId": "record1", "data": {"feat_name": "Improved customer support chat", "cost": ["opt3"], "link_problem": ["re_2"], "impact": ["opta"]}, "values": {"feat_name": "Improved customer support chat", "cost": ["High"], "link_problem": ["High customer churn rate"], "impact": ["Low"]}}, {"templateId": "record2", "data": {"feat_name": "Automated reporting feature", "cost": ["opt2"], "link_problem": ["re_3"], "impact": ["optb"]}, "values": {"feat_name": "Automated reporting feature", "cost": ["Medium"], "link_problem": ["Difficulty integrating with current systems"], "impact": ["Medium"]}}, {"templateId": "record3", "data": {"feat_name": "User-friendly interface redesign", "cost": ["opt1"], "link_problem": ["re_2"], "impact": ["optc"]}, "values": {"feat_name": "User-friendly interface redesign", "cost": ["Low"], "link_problem": ["High customer churn rate"], "impact": ["High"]}}, {"templateId": "record4", "data": {"feat_name": "Enhanced data analytics", "cost": ["opt2"], "link_problem": ["re_1"], "impact": ["opta"]}, "values": {"feat_name": "Enhanced data analytics", "cost": ["Medium"], "link_problem": ["Limited scalability"], "impact": ["Low"]}}, {"templateId": "record5", "data": {"feat_name": "Seamless API integration", "cost": ["opt1"], "link_problem": ["re_3"], "impact": ["optb"]}, "values": {"feat_name": "Seamless API integration", "cost": ["Low"], "link_problem": ["Difficulty integrating with current systems"], "impact": ["Medium"]}}]}, {"resourceType": "DATABASE", "templateId": "database_problem", "name": "Problem Statements", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all_problem", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "problem_name"}, {"templateId": "link_interviews"}, {"templateId": "link_feat", "width": 150}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "problem_name", "privilege": "TYPE_EDIT", "name": "Name", "primary": true}, {"type": "LINK", "templateId": "link_interviews", "privilege": "NAME_EDIT", "name": "Interviews", "property": {"foreignDatabaseTemplateId": "database_interview", "brotherFieldTemplateId": "database_interview:link_to_problem"}, "primary": false}, {"type": "LINK", "templateId": "link_feat", "privilege": "NAME_EDIT", "name": "Related features/enhancements", "property": {"foreignDatabaseTemplateId": "database_features", "brotherFieldTemplateId": "database_features:link_problem"}, "primary": false}], "records": [{"templateId": "re_1", "data": {"problem_name": "Limited scalability", "link_feat": ["record4"], "link_interviews": ["record_2"]}, "values": {"problem_name": "Limited scalability", "link_feat": ["Enhanced data analytics"], "link_interviews": ["<PERSON>"]}}, {"templateId": "re_2", "data": {"problem_name": "High customer churn rate", "link_feat": ["record3", "record1"], "link_interviews": ["record_1"]}, "values": {"problem_name": "High customer churn rate", "link_feat": ["User-friendly interface redesign", "Improved customer support chat"], "link_interviews": ["<PERSON>"]}}, {"templateId": "re_3", "data": {"problem_name": "Difficulty integrating with current systems", "link_feat": ["record5", "record2"], "link_interviews": ["record_3"]}, "values": {"problem_name": "Difficulty integrating with current systems", "link_feat": ["Seamless API integration", "Automated reporting feature"], "link_interviews": ["<PERSON>"]}}]}, {"resourceType": "DATABASE", "templateId": "database_interview", "name": "Interviews", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all_interview", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "interview_name", "width": 302}, {"templateId": "link_to_customer", "width": 228}, {"templateId": "date"}, {"templateId": "notes"}, {"templateId": "link_to_problem"}]}], "fields": [{"type": "FORMULA", "templateId": "interview_name", "privilege": "TYPE_EDIT", "name": "Name", "property": {"expressionTemplate": "CONCATENATE( {link_to_customer},\" (\",DATETIME_FORMAT({date},'M/D/YY'),\")\")"}, "primary": true}, {"type": "LINK", "templateId": "link_to_customer", "privilege": "NAME_EDIT", "name": "Customers", "property": {"foreignDatabaseTemplateId": "database_customer", "brotherFieldTemplateId": "database_customer:link_to_interview_session"}, "primary": false}, {"type": "DATETIME", "templateId": "date", "privilege": "NAME_EDIT", "name": "Date", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "LONG_TEXT", "templateId": "notes", "privilege": "NAME_EDIT", "name": "Notes", "primary": false}, {"type": "LINK", "templateId": "link_to_problem", "privilege": "NAME_EDIT", "name": "Problem Statements", "property": {"foreignDatabaseTemplateId": "database_problem", "brotherFieldTemplateId": "database_problem:link_interviews"}, "primary": false}], "records": [{"templateId": "record_1", "data": {"interview_name": "<PERSON>", "link_to_problem": ["re_2"], "link_to_customer": ["recy"], "date": "2024-10-15T16:00:00.000Z", "notes": "Positive feedback on user interface"}, "values": {"interview_name": "<PERSON> (10/15/24)", "link_to_problem": ["High customer churn rate"], "link_to_customer": ["<PERSON>"], "date": "2024-10-15", "notes": "Positive feedback on user interface"}}, {"templateId": "record_2", "data": {"interview_name": "<PERSON>", "link_to_problem": ["re_1"], "link_to_customer": ["recz", "recw"], "date": "2024-10-13T16:00:00.000Z", "notes": "Needs more information on pricing"}, "values": {"interview_name": "<PERSON> (10/13/24)", "link_to_problem": ["Limited scalability"], "link_to_customer": ["<PERSON>", "<PERSON>"], "date": "2024-10-13", "notes": "Needs more information on pricing"}}, {"templateId": "record_3", "data": {"interview_name": "<PERSON>", "link_to_problem": ["re_3"], "link_to_customer": ["recx"], "date": "2024-10-11T16:00:00.000Z", "notes": "Customer interested in product expansion"}, "values": {"interview_name": "<PERSON> (10/11/24)", "link_to_problem": ["Difficulty integrating with current systems"], "link_to_customer": ["<PERSON>"], "date": "2024-10-11", "notes": "Customer interested in product expansion"}}]}, {"resourceType": "DATABASE", "templateId": "database_customer", "name": "Customers", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all_customer", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "customer_name"}, {"templateId": "email", "width": 317}, {"templateId": "persona"}, {"templateId": "usage_type"}, {"templateId": "link_to_interview_session", "width": 150}]}, {"type": "TABLE", "templateId": "founder_only", "name": "Founders & Managers only", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "persona", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Contains", "value": ["op1", "op2"]}}]}, "sorts": [], "fields": [{"templateId": "customer_name", "hidden": false}, {"templateId": "email", "hidden": false}, {"templateId": "persona", "hidden": false}, {"templateId": "usage_type", "hidden": false}, {"templateId": "link_to_interview_session", "hidden": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "customer_name", "privilege": "TYPE_EDIT", "name": "Name", "primary": true}, {"type": "EMAIL", "templateId": "email", "privilege": "NAME_EDIT", "name": "Email", "property": {"defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "persona", "privilege": "NAME_EDIT", "name": "<PERSON>a", "property": {"options": [{"id": "op1", "name": "Founder", "color": "deepPurple2"}, {"id": "op2", "name": "Manager", "color": "indigo2"}, {"id": "op3", "name": "Business Development", "color": "teal2"}, {"id": "op4", "name": "Engineer", "color": "orange2"}, {"id": "op5", "name": "Customer Success", "color": "pink2"}, {"id": "op6", "name": "Sales", "color": "red2"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "usage_type", "privilege": "NAME_EDIT", "name": "Usage type", "property": {"options": [{"id": "opt_a", "name": "Champion", "color": "teal2"}, {"id": "opt_b", "name": "Churn risk", "color": "red2"}], "defaultValue": ""}, "primary": false}, {"type": "LINK", "templateId": "link_to_interview_session", "privilege": "NAME_EDIT", "name": "Interview Session(s)", "property": {"foreignDatabaseTemplateId": "database_interview", "brotherFieldTemplateId": "database_interview:link_to_customer"}, "primary": false}], "records": [{"templateId": "recz", "data": {"persona": ["op4"], "customer_name": "<PERSON>", "email": "<EMAIL>", "usage_type": ["opt_a"], "link_to_interview_session": ["record_2"]}, "values": {"persona": ["Engineer"], "customer_name": "<PERSON>", "email": "<EMAIL>", "usage_type": ["Champion"], "link_to_interview_session": ["<PERSON>"]}}, {"templateId": "recy", "data": {"persona": ["op3"], "customer_name": "<PERSON>", "email": "<EMAIL>", "usage_type": ["opt_b"], "link_to_interview_session": ["record_1"]}, "values": {"persona": ["Business Development"], "customer_name": "<PERSON>", "email": "<EMAIL>", "usage_type": ["Churn risk"], "link_to_interview_session": ["<PERSON>"]}}, {"templateId": "recx", "data": {"persona": ["op2"], "customer_name": "<PERSON>", "email": "<EMAIL>", "usage_type": ["opt_a"], "link_to_interview_session": ["record_3"]}, "values": {"persona": ["Manager"], "customer_name": "<PERSON>", "email": "<EMAIL>", "usage_type": ["Champion"], "link_to_interview_session": ["<PERSON>"]}}, {"templateId": "recw", "data": {"persona": ["op1"], "customer_name": "<PERSON>", "email": "<EMAIL>", "usage_type": ["opt_b"], "link_to_interview_session": ["record_2"]}, "values": {"persona": ["Founder"], "customer_name": "<PERSON>", "email": "<EMAIL>", "usage_type": ["Churn risk"], "link_to_interview_session": ["<PERSON>"]}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}