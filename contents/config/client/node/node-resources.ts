import type { ILocaleContext } from '@bika/contents/i18n';
import type { NodeResourceType } from '@bika/types/node/bo';
import type { IFeatureAbilityConfig } from '@bika/types/website/bo';

export function getResourcesTypesConfig(locale: ILocaleContext): Record<NodeResourceType, IFeatureAbilityConfig> {
  return {
    FOLDER: {
      display: 'SHOW',
      label: locale.t.resource.type.folder,
      description: locale.t.resource.type.folder_description,
      iconPath: '/assets/icons/resource/folder.svg',
    },
    TEMPLATE: {
      display: 'HIDDEN',
      label: 'template',
      description: '模板资源类型',
      iconPath: '',
    },
    DATABASE: {
      display: 'SHOW',
      label: locale.t.resource.type.database,
      description: locale.t.resource.type.database_description,
      iconPath: '/assets/icons/resource/database.svg',
    },
    DATAPAGE: {
      display: 'HIDDEN',
      label: 'data page',
      description: '数据页面资源类型',
      iconPath: '',
    },
    CANVAS: {
      display: 'HIDDEN',
      label: locale.t.resource.type.canvas,
      description: locale.t.resource.type.canvas_description,
      iconPath: '/assets/icons/resource/canvas.svg',
    },
    ALIAS: {
      display: 'HIDDEN',
      label: 'alias',
      description: '别名资源类型',
      iconPath: '',
    },
    DOCUMENT: {
      display: 'SHOW',
      label: locale.t.resource.type.doc,
      description: locale.t.resource.type.doc_description,
      iconPath: '/assets/icons/resource/doc.svg',
    },
    FILE: {
      display: 'SHOW',
      label: locale.t.resource.type.file,
      description: locale.t.resource.type.file_description,
      iconPath: '/assets/icons/database/attachment.svg',
    },
    // VIEW: {
    //   display: 'HIDDEN',
    //   label: locale.t.resource.type.view,
    //   description: locale.t.resource.type.view_description,
    //   iconPath: '/assets/icons/resource/view.svg',
    // },
    AUTOMATION: {
      display: 'SHOW',
      label: locale.t.resource.type.automation,
      description: locale.t.resource.type.automation_description,
      iconPath: '/assets/icons/resource/automation.svg',
    },
    DASHBOARD: {
      display: 'SHOW',
      label: locale.t.resource.type.dashboard,
      description: locale.t.resource.type.dashboard_description,
      iconPath: '/assets/icons/resource/dashboard.svg',
    },
    REPORT_TEMPLATE: {
      display: 'HIDDEN',
      label: locale.t.resource.type.report_template,
      description: locale.t.resource.type.report_template_description,
      iconPath: '/assets/icons/resource/report.svg',
    },
    EMBED: {
      display: 'HIDDEN',
      label: locale.t.resource.type.web_page,
      description: locale.t.resource.type.web_page_description,
      screenshots: [{ url: '/assets/screenshot/stories/node-resource-web-page.png' }],
      iconPath: '/assets/icons/resource/web-page.svg',
    },
    PAGE: {
      display: 'SHOW',
      label: locale.t.resource.type.code_page,
      description: locale.t.resource.type.code_page_description,
      iconPath: '/assets/icons/resource/code.svg',
    },
    // APP_PAGE: {
    //   display: 'HIDDEN',
    //   label: locale.t.resource.type.app_page,
    //   description: locale.t.resource.type.app_page_description,
    //   iconPath: '/assets/icons/resource/code.svg',
    // },
    ROOT: {
      display: 'HIDDEN',
      label: 'root',
      description: '根资源类型',
      iconPath: '',
    },
    FORM: {
      display: 'SHOW',
      label: locale.t.resource.type.form,
      description: locale.t.resource.type.form_description,
      iconPath: '/assets/icons/resource/form.svg',
    },
    AI: {
      display: 'SHOW',
      label: locale.t.resource.type.ai,
      description: locale.t.resource.type.ai_description,
      iconPath: '/assets/icons/resource/ai.svg',
    },
    MIRROR: {
      display: 'SHOW',
      label: locale.t.resource.type.mirror,
      description: locale.t.resource.type.mirror_description,
      iconPath: '/assets/icons/resource/mirror.svg',
    },
  };
}
