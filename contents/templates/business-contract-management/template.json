{"templateId": "business-contract-management", "name": "Business Contract Management", "description": {"en": "The challenges in traditional contract management, such as dispersed information, difficulty in tracking, repetitive data entry, and lack of transparency in status, have been addressed. This effectively helps teams efficiently manage the contract lifecycle, reduces manual operations, and improves data accuracy and team collaboration efficiency", "ja": "The challenges in traditional contract management, such as dispersed information, difficulty in tracking, repetitive data entry, and lack of transparency in status, have been addressed. This effectively helps teams efficiently manage the contract lifecycle, reduces manual operations, and improves data accuracy and team collaboration efficiency", "zh-CN": "The challenges in traditional contract management, such as dispersed information, difficulty in tracking, repetitive data entry, and lack of transparency in status, have been addressed. This effectively helps teams efficiently manage the contract lifecycle, reduces manual operations, and improves data accuracy and team collaboration efficiency", "zh-TW": "The challenges in traditional contract management, such as dispersed information, difficulty in tracking, repetitive data entry, and lack of transparency in status, have been addressed. This effectively helps teams efficiently manage the contract lifecycle, reduces manual operations, and improves data accuracy and team collaboration efficiency"}, "cover": "/assets/template/business-contract-management/business-contract-management.png", "author": "pengjin <<EMAIL>>", "category": ["project"], "keywords": "Contract Lifecycle Management, Automated Reminders, Contract Expiration Notifications, Data Accuracy, Team Collaboration", "useCases": "Improved Contract Lifecycle Management, Automated Contract Expiration Notifications, Enhanced Data Accuracy, Reducing Manual Operations, Effective Contract Tracking, Increased Status Transparency, Optimized Team Collaboration, Streamlined Contract Renewal Process", "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.11", "resources": [{"resourceType": "AUTOMATION", "templateId": "atoC9Pz2bY4uP7roNl5h3zmZ", "name": "Expiration Reminder Automation", "triggers": [{"triggerType": "DATETIME_FIELD_REACHED", "templateId": "trgQPgJmIicCPQO49rOfOYEG", "description": "\"Requested Delivery Date\" as an automation trigger condition", "input": {"type": "DATETIME_FIELD_REACHED", "datetime": {"type": "AHEAD", "unit": "DAY", "value": 3}, "fieldTemplateId": "fldhsCmdpj9uZJlOfrDHxEsL", "databaseTemplateId": "dat0AXYXGmJ4Ku5lIDBcW66J"}}], "actions": [{"templateId": "actjm0Yj4H5CttPLtp3c5RMe", "description": "Send an email reminder to the corresponding member by retrieving the \"Requested By\" email content", "actionType": "SEND_EMAIL", "input": {"subject": "Transaction Time Reminder", "body": {"html": "<p>Dear <span names=\"触发器,日期字段到期触发,record,cells,Requested By,value\" tips=\"\" ids=\"JSON.stringify(_triggers.trgQPgJmIicCPQO49rOfOYEG.record.cells.fldMWsFWytezQXt7invcelpQ.value)\" data-variable=\"\" class=\"variable-tag\" title=\"\"><%= JSON.stringify(_triggers.trgQPgJmIicCPQO49rOfOYEG.record.cells.fldMWsFWytezQXt7invcelpQ.value) %></span><br><br>This is a reminder regarding the transaction you have scheduled. According to our records, your transaction is set to take place at the following time:<br><br>Transaction Time: <span names=\"触发器,日期字段到期触发,record,cells,Requested Delivery Date,value\" tips=\"\" ids=\"_triggers,trgQPgJmIicCPQO49rOfOYEG,record,cells,fldhsCmdpj9uZJlOfrDHxEsL,value\" data-variable=\"\" class=\"variable-tag\"><%= _triggers.trgQPgJmIicCPQO49rOfOYEG.record.cells.fldhsCmdpj9uZJlOfrDHxEsL.value %></span><br><br>Please ensure that all related matters are prepared before the transaction to avoid any unnecessary delays or issues. If you have any questions or need further assistance, feel free to contact us at any time. </p>", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "Dear ", "type": "text"}, {"type": "variable", "attrs": {"ids": "JSON.stringify(_triggers.trgQPgJmIicCPQO49rOfOYEG.record.cells.fldMWsFWytezQXt7invcelpQ.value)", "tips": "", "names": ["触发器", "日期字段到期触发", "record", "cells", "Requested By", "value"]}}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "This is a reminder regarding the transaction you have scheduled. According to our records, your transaction is set to take place at the following time:", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "Transaction Time: ", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trgQPgJmIicCPQO49rOfOYEG", "record", "cells", "fldhsCmdpj9uZJlOfrDHxEsL", "value"], "tips": "", "names": ["触发器", "日期字段到期触发", "record", "cells", "Requested Delivery Date", "value"]}}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "Please ensure that all related matters are prepared before the transaction to avoid any unnecessary delays or issues. If you have any questions or need further assistance, feel free to contact us at any time. ", "type": "text"}]}]}}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trgQPgJmIicCPQO49rOfOYEG.record.cells.fldbJtxcSGoXCqQdXZcPu6Ib.value %>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}, {"resourceType": "FORM", "templateId": "fomdEei2DJ19ND6F4E6ZtKPs", "name": "Contract Submission Form", "formType": "DATABASE", "databaseTemplateId": "datafQuqRnR8LMq7aeGfbueQ", "metadata": {"type": "VIEW", "viewTemplateId": "viwJDk2hdwqdPUiCNIAWYCtn"}}, {"resourceType": "DATABASE", "templateId": "dat0AXYXGmJ4Ku5lIDBcW66J", "name": "Contract Activity", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwq6alQP3HiuxHPw1R7AJD2", "name": "Contract Activity", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldPnRfxDxAXzO7McX1umXBD", "hidden": false}, {"templateId": "fldMWsFWytezQXt7invcelpQ", "hidden": false, "width": 278}, {"templateId": "fldqfGIGhPAza7pZSGxHqSta", "hidden": false, "width": 205}, {"templateId": "fldhsCmdpj9uZJlOfrDHxEsL", "hidden": false, "width": 188}, {"templateId": "fldR2IowsecdffqjVMDShalm", "hidden": false}, {"templateId": "fldCi0wzcXmraQf2hqgmWeg6", "hidden": false}, {"templateId": "fldSaxZEWgZd8s6x9GOhzsPs", "hidden": false}, {"templateId": "fldbJtxcSGoXCqQdXZcPu6Ib", "hidden": false, "width": 150}], "groups": []}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldSaxZEWgZd8s6x9GOhzsPs", "privilege": "TYPE_EDIT", "name": "Description of Request", "primary": true}, {"type": "SINGLE_SELECT", "templateId": "fldPnRfxDxAXzO7McX1umXBD", "privilege": "FULL_EDIT", "name": "Service Requested", "property": {"options": [{"id": "opt1XMrd3ucp78KpIfJZAoBJ", "name": "option1"}, {"id": "optniMeAoeYZGfxRmK7WY50J", "name": "Approve", "color": "red"}, {"id": "opt0ZtycIPIxn9ZSnMfaNFtI", "name": "Generate", "color": "red"}, {"id": "optdcXinu6q08at3fHy0Cb2a", "name": "Negotiate", "color": "red"}, {"id": "optc7Fs3sVjgysahPbDwWYVR", "name": "Revise", "color": "red"}], "defaultValue": ""}, "primary": false}, {"type": "MEMBER", "templateId": "fldMWsFWytezQXt7invcelpQ", "privilege": "FULL_EDIT", "name": "Requested By", "property": {}, "primary": false}, {"type": "MEMBER", "templateId": "fldqfGIGhPAza7pZSGxHqSta", "privilege": "FULL_EDIT", "name": "Assigned to", "property": {}, "primary": false}, {"type": "DATETIME", "templateId": "fldhsCmdpj9uZJlOfrDHxEsL", "privilege": "FULL_EDIT", "name": "Requested Delivery Date", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "LINK", "templateId": "fldR2IowsecdffqjVMDShalm", "privilege": "FULL_EDIT", "name": "Related Contract", "property": {"foreignDatabaseTemplateId": "datafQuqRnR8LMq7aeGfbueQ", "brotherFieldTemplateId": "fldQFXh3y16o9c3oqvHmV7JO"}, "primary": false}, {"type": "LOOKUP", "templateId": "fldCi0wzcXmraQf2hqgmWeg6", "privilege": "FULL_EDIT", "name": "Existing Contract", "property": {"databaseTemplateId": "datafQuqRnR8LMq7aeGfbueQ", "relatedLinkFieldTemplateId": "fldR2IowsecdffqjVMDShalm", "lookupTargetFieldTemplateId": "fldMjxUqII4yMKVoMDUC4zpz"}, "primary": false}, {"type": "EMAIL", "templateId": "fldbJtxcSGoXCqQdXZcPu6Ib", "privilege": "FULL_EDIT", "name": "Requested By email", "primary": false}], "records": [{"templateId": "rec9ZG4GDRph0aTXZrlB6dJH", "data": {"fldMWsFWytezQXt7invcelpQ": ["mebOao2Jhx7coXmOEunhpC4n"], "fldPnRfxDxAXzO7McX1umXBD": ["optdcXinu6q08at3fHy0Cb2a"], "fldR2IowsecdffqjVMDShalm": ["recr693TLErDwZsUEGOL62a7"], "fldSaxZEWgZd8s6x9GOhzsPs": "Discuss the American Agreement", "fldbJtxcSGoXCqQdXZcPu6Ib": "<EMAIL>", "fldhsCmdpj9uZJlOfrDHxEsL": "2025-01-05T16:00:00.000Z", "fldqfGIGhPAza7pZSGxHqSta": ["mebOao2Jhx7coXmOEunhpC4n"]}, "values": {"fldCi0wzcXmraQf2hqgmWeg6": [null], "fldMWsFWytezQXt7invcelpQ": ["彭进"], "fldPnRfxDxAXzO7McX1umXBD": ["Negotiate"], "fldR2IowsecdffqjVMDShalm": ["<PERSON>'s termination"], "fldSaxZEWgZd8s6x9GOhzsPs": "Discuss the American Agreement", "fldbJtxcSGoXCqQdXZcPu6Ib": "<EMAIL>", "fldhsCmdpj9uZJlOfrDHxEsL": "2025-01-05", "fldqfGIGhPAza7pZSGxHqSta": ["彭进"]}}, {"templateId": "recc6Ksk95ntLaTh4AOrOYot", "data": {"fldMWsFWytezQXt7invcelpQ": ["mebOao2Jhx7coXmOEunhpC4n"], "fldPnRfxDxAXzO7McX1umXBD": ["optc7Fs3sVjgysahPbDwWYVR"], "fldR2IowsecdffqjVMDShalm": ["rec59ldjjHpsfw4cN0BLNAzJ"], "fldSaxZEWgZd8s6x9GOhzsPs": "Update <PERSON>'s Offer: Adjust Salary to67", "fldbJtxcSGoXCqQdXZcPu6Ib": "<EMAIL>", "fldhsCmdpj9uZJlOfrDHxEsL": "2025-01-07T16:00:00.000Z", "fldqfGIGhPAza7pZSGxHqSta": ["mebOao2Jhx7coXmOEunhpC4n"]}, "values": {"fldCi0wzcXmraQf2hqgmWeg6": ["headshot-cyan-1.png"], "fldMWsFWytezQXt7invcelpQ": ["彭进"], "fldPnRfxDxAXzO7McX1umXBD": ["Revise"], "fldR2IowsecdffqjVMDShalm": ["G Jefferson non compete"], "fldSaxZEWgZd8s6x9GOhzsPs": "Update <PERSON>'s Offer: Adjust Salary to67", "fldbJtxcSGoXCqQdXZcPu6Ib": "<EMAIL>", "fldhsCmdpj9uZJlOfrDHxEsL": "2025-01-07", "fldqfGIGhPAza7pZSGxHqSta": ["彭进"]}}, {"templateId": "recCSMByEAtW0liouLQD9u5g", "data": {"fldMWsFWytezQXt7invcelpQ": ["mebOao2Jhx7coXmOEunhpC4n"], "fldPnRfxDxAXzO7McX1umXBD": ["optc7Fs3sVjgysahPbDwWYVR"], "fldR2IowsecdffqjVMDShalm": ["rec3v36qSqKEkydXzlHDlptU"], "fldSaxZEWgZd8s6x9GOhzsPs": "Amend Bill of Sale for American Buyer", "fldbJtxcSGoXCqQdXZcPu6Ib": "<EMAIL>", "fldhsCmdpj9uZJlOfrDHxEsL": "2025-01-05T16:00:00.000Z", "fldqfGIGhPAza7pZSGxHqSta": ["mebOao2Jhx7coXmOEunhpC4n"]}, "values": {"fldCi0wzcXmraQf2hqgmWeg6": ["headshot-pink-1.png"], "fldMWsFWytezQXt7invcelpQ": ["彭进"], "fldPnRfxDxAXzO7McX1umXBD": ["Revise"], "fldR2IowsecdffqjVMDShalm": ["Renter's Insurance - 490 McDowell Street"], "fldSaxZEWgZd8s6x9GOhzsPs": "Amend Bill of Sale for American Buyer", "fldbJtxcSGoXCqQdXZcPu6Ib": "<EMAIL>", "fldhsCmdpj9uZJlOfrDHxEsL": "2025-01-05", "fldqfGIGhPAza7pZSGxHqSta": ["彭进"]}}]}, {"resourceType": "DATABASE", "templateId": "datafQuqRnR8LMq7aeGfbueQ", "name": "Contracts", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwOG8orIbsZWjVVWGxD4kN5", "name": "Contracts by Category", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldRQaQ5s5w8z5pjvzDPIziW", "hidden": false, "width": 353}, {"templateId": "fldMjxUqII4yMKVoMDUC4zpz", "hidden": false}, {"templateId": "fldECiogGwC45TVKtLkOOZ7F", "hidden": false}, {"templateId": "fldLE6mP55HwK7S5GBm99p2I", "hidden": false, "width": 262}, {"templateId": "fldXY9FIwaLf2lqpkg2aEXKz", "hidden": false, "width": 327}, {"templateId": "fldxibAWoSgnoqiUJUyu41fq", "hidden": false, "width": 323}, {"templateId": "fldjLauEgiW6OxsGpJhfUoVj", "hidden": false, "width": 262}, {"templateId": "fldW0Cti1yTX9ysQS9COz0ka", "hidden": false, "width": 318}, {"templateId": "fldGqcl0Pp1KPF1zKTaVKLWE", "hidden": false, "width": 349}, {"templateId": "fldq81K0aY4sMB1IYqyRjlcR", "hidden": false}, {"templateId": "fldQFXh3y16o9c3oqvHmV7JO", "hidden": false, "width": 248}], "groups": [{"fieldTemplateId": "fldECiogGwC45TVKtLkOOZ7F", "asc": true}]}, {"type": "TABLE", "templateId": "viwJDk2hdwqdPUiCNIAWYCtn", "name": "Contract Submission Form view", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldRQaQ5s5w8z5pjvzDPIziW", "hidden": false}, {"templateId": "fldMjxUqII4yMKVoMDUC4zpz", "hidden": false}, {"templateId": "fldECiogGwC45TVKtLkOOZ7F", "hidden": false}, {"templateId": "fldLE6mP55HwK7S5GBm99p2I", "hidden": false}, {"templateId": "fldXY9FIwaLf2lqpkg2aEXKz", "hidden": false}, {"templateId": "fldxibAWoSgnoqiUJUyu41fq", "hidden": true}, {"templateId": "fldjLauEgiW6OxsGpJhfUoVj", "hidden": true}, {"templateId": "fldW0Cti1yTX9ysQS9COz0ka", "hidden": false}, {"templateId": "fldGqcl0Pp1KPF1zKTaVKLWE", "hidden": false}, {"templateId": "fldq81K0aY4sMB1IYqyRjlcR", "hidden": false}, {"templateId": "fldQFXh3y16o9c3oqvHmV7JO", "hidden": true}], "groups": []}, {"type": "TABLE", "templateId": "viwIrThqSakXR9QmHXsKkTqB", "name": "Upcoming Expirations", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldGqcl0Pp1KPF1zKTaVKLWE", "fieldType": "DATETIME", "clause": {"operator": "IsGreaterEqual", "value": ["SomeDayBefore", 90]}}]}, "sorts": [], "fields": [{"templateId": "fldRQaQ5s5w8z5pjvzDPIziW", "hidden": false}, {"templateId": "fldMjxUqII4yMKVoMDUC4zpz", "hidden": false}, {"templateId": "fldECiogGwC45TVKtLkOOZ7F", "hidden": false}, {"templateId": "fldLE6mP55HwK7S5GBm99p2I", "hidden": false}, {"templateId": "fldXY9FIwaLf2lqpkg2aEXKz", "hidden": false}, {"templateId": "fldxibAWoSgnoqiUJUyu41fq", "hidden": false}, {"templateId": "fldjLauEgiW6OxsGpJhfUoVj", "hidden": false}, {"templateId": "fldW0Cti1yTX9ysQS9COz0ka", "hidden": false}, {"templateId": "fldGqcl0Pp1KPF1zKTaVKLWE", "hidden": false}, {"templateId": "fldq81K0aY4sMB1IYqyRjlcR", "hidden": false}, {"templateId": "fldQFXh3y16o9c3oqvHmV7JO", "hidden": false}], "groups": []}, {"type": "KANBAN", "templateId": "viwsIcQeuTA8dVq2TkJUtPVW", "name": "Contract Status", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldRQaQ5s5w8z5pjvzDPIziW", "hidden": false}, {"templateId": "fldMjxUqII4yMKVoMDUC4zpz", "hidden": false}, {"templateId": "fldECiogGwC45TVKtLkOOZ7F", "hidden": false}, {"templateId": "fldLE6mP55HwK7S5GBm99p2I", "hidden": false}, {"templateId": "fldXY9FIwaLf2lqpkg2aEXKz", "hidden": false}, {"templateId": "fldxibAWoSgnoqiUJUyu41fq", "hidden": false}, {"templateId": "fldjLauEgiW6OxsGpJhfUoVj", "hidden": false}, {"templateId": "fldW0Cti1yTX9ysQS9COz0ka", "hidden": false}, {"templateId": "fldGqcl0Pp1KPF1zKTaVKLWE", "hidden": false}, {"templateId": "fldq81K0aY4sMB1IYqyRjlcR", "hidden": false}, {"templateId": "fldQFXh3y16o9c3oqvHmV7JO", "hidden": false}], "groups": [], "extra": {"kanbanGroupingFieldId": "fldECiogGwC45TVKtLkOOZ7F"}}, {"type": "GALLERY", "templateId": "viw0Cle4wlXOnCioN6ck242A", "name": "Documents on File", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldRQaQ5s5w8z5pjvzDPIziW", "hidden": false}, {"templateId": "fldMjxUqII4yMKVoMDUC4zpz", "hidden": false}, {"templateId": "fldECiogGwC45TVKtLkOOZ7F", "hidden": false}, {"templateId": "fldLE6mP55HwK7S5GBm99p2I", "hidden": false}, {"templateId": "fldXY9FIwaLf2lqpkg2aEXKz", "hidden": false}, {"templateId": "fldxibAWoSgnoqiUJUyu41fq", "hidden": false}, {"templateId": "fldjLauEgiW6OxsGpJhfUoVj", "hidden": false}, {"templateId": "fldW0Cti1yTX9ysQS9COz0ka", "hidden": false}, {"templateId": "fldGqcl0Pp1KPF1zKTaVKLWE", "hidden": false}, {"templateId": "fldq81K0aY4sMB1IYqyRjlcR", "hidden": false}, {"templateId": "fldQFXh3y16o9c3oqvHmV7JO", "hidden": false}], "groups": [], "extra": {"coverFieldId": "fldMjxUqII4yMKVoMDUC4zpz", "coverStretch": "FILL", "displayFieldName": true}}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldRQaQ5s5w8z5pjvzDPIziW", "privilege": "TYPE_EDIT", "name": "Contract Description", "primary": true}, {"type": "ATTACHMENT", "templateId": "fldMjxUqII4yMKVoMDUC4zpz", "privilege": "FULL_EDIT", "name": "Contract", "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldECiogGwC45TVKtLkOOZ7F", "privilege": "FULL_EDIT", "name": "Category", "property": {"options": [{"id": "optQpE4mBVTwDKXS901ic68Z", "name": "Sales", "color": "red"}, {"id": "optgnaiL0rJ9eILfZ2HpRfGP", "name": "Business", "color": "indigo1"}, {"id": "optsdwDk3d7GYZ6KjhN74HQb", "name": "Employment/HR", "color": "green1"}, {"id": "optdwiz2v5WxiETcvHZoccoD", "name": "Insurance", "color": "pink1"}, {"id": "opt5Tva5nrXiIWXmXqOtjzPB", "name": "Lease", "color": "yellow"}, {"id": "opt499JXFYUvBlsJUsmx9vE1", "name": "Other", "color": "orange"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldLE6mP55HwK7S5GBm99p2I", "privilege": "FULL_EDIT", "name": "Contract Type", "property": {"options": [{"id": "optismdyyDCaaOmp0JWSB7jl", "name": "Bill of Sale", "color": "deepPurple1"}, {"id": "optsEgOzJKAowEEksiDuzCyh", "name": "Purchase Order", "color": "indigo1"}, {"id": "optB0ceN49At9XhfWcs9jF1M", "name": "Confidentiality Agreement (NDA)", "color": "blue1"}, {"id": "optvSRPSZbwFCzzBsEgG0po5", "name": "Licensing Agreement", "color": "teal1"}, {"id": "opt8vgFlA4ZbUhPlQKyb9bQM", "name": "Employment Agreement", "color": "yellow1"}, {"id": "opt4GZPk9WVGKkCrgbpwKz8c", "name": "Employment Separation Agreement", "color": "tangerine1"}, {"id": "optUFtZYA1lL2cPtQkeHxOo0", "name": "Non-Compete Agreement", "color": "pink1"}, {"id": "optpXC4n5VopZcAdZKSpWyOc", "name": "Insurance Policy", "color": "indigo2"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldXY9FIwaLf2lqpkg2aEXKz", "privilege": "FULL_EDIT", "name": "Other Parties Involved", "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldxibAWoSgnoqiUJUyu41fq", "privilege": "FULL_EDIT", "name": "Status", "property": {"options": [{"id": "optYF7O1MtqQmEcg7WGIJZlj", "name": "Negotiation", "color": "red"}, {"id": "optQ4gHcX3NZlA7NfY4PmAsl", "name": "Approved/accepted", "color": "indigo1"}, {"id": "optf0MzvL7rrQAuuHMQRqtJK", "name": "In development", "color": "blue1"}, {"id": "optewczIROqGvaKUJwkImCnr", "name": "Internal review", "color": "green1"}, {"id": "optw8KJa9Wut7QmZl1ygw644", "name": "External review", "color": "orange1"}, {"id": "optGTWhJf6T2xnyXwM2jy4Lb", "name": "Terminated/expired", "color": "blue"}, {"id": "optmXeJpGARHkPKTzhsx6PLP", "name": "Other", "color": "green"}], "defaultValue": ""}, "primary": false}, {"type": "DATETIME", "templateId": "fldjLauEgiW6OxsGpJhfUoVj", "privilege": "FULL_EDIT", "name": "Signing Date", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "DATETIME", "templateId": "fldW0Cti1yTX9ysQS9COz0ka", "privilege": "FULL_EDIT", "name": "Effective Date", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "DATETIME", "templateId": "fldGqcl0Pp1KPF1zKTaVKLWE", "privilege": "FULL_EDIT", "name": "Expiration Date", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldq81K0aY4sMB1IYqyRjlcR", "privilege": "FULL_EDIT", "name": "Notes", "primary": false}, {"type": "LINK", "templateId": "fldQFXh3y16o9c3oqvHmV7JO", "privilege": "FULL_EDIT", "name": "Related Contract Activity", "property": {"foreignDatabaseTemplateId": "dat0AXYXGmJ4Ku5lIDBcW66J", "brotherFieldTemplateId": "fldR2IowsecdffqjVMDShalm"}, "primary": false}], "records": [{"templateId": "recr693TLErDwZsUEGOL62a7", "data": {"fldECiogGwC45TVKtLkOOZ7F": ["optQpE4mBVTwDKXS901ic68Z"], "fldGqcl0Pp1KPF1zKTaVKLWE": "2024-12-26T16:00:00.000Z", "fldLE6mP55HwK7S5GBm99p2I": ["opt4GZPk9WVGKkCrgbpwKz8c"], "fldMjxUqII4yMKVoMDUC4zpz": [{"name": "headshot-cyan-1.png", "id": "tplattMpm0z3rFkoaX23RlWAFtM", "path": "template/tplattMpm0z3rFkoaX23RlWAFtM.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 499769}], "fldQFXh3y16o9c3oqvHmV7JO": ["rec9ZG4GDRph0aTXZrlB6dJH"], "fldRQaQ5s5w8z5pjvzDPIziW": "Patent-protected algorithm", "fldW0Cti1yTX9ysQS9COz0ka": "2024-07-31T00:00:00.000Z", "fldXY9FIwaLf2lqpkg2aEXKz": "<PERSON>", "fldq81K0aY4sMB1IYqyRjlcR": "", "fldxibAWoSgnoqiUJUyu41fq": ["optw8KJa9Wut7QmZl1ygw644"]}, "values": {"fldECiogGwC45TVKtLkOOZ7F": ["Sales"], "fldGqcl0Pp1KPF1zKTaVKLWE": "2024-12-26", "fldLE6mP55HwK7S5GBm99p2I": ["Employment Separation Agreement"], "fldMjxUqII4yMKVoMDUC4zpz": ["headshot-cyan-1.png"], "fldQFXh3y16o9c3oqvHmV7JO": ["Negotiate the Amerivan deal"], "fldRQaQ5s5w8z5pjvzDPIziW": "Patent-protected algorithm", "fldW0Cti1yTX9ysQS9COz0ka": "2024-07-31", "fldXY9FIwaLf2lqpkg2aEXKz": "<PERSON>", "fldq81K0aY4sMB1IYqyRjlcR": "", "fldxibAWoSgnoqiUJUyu41fq": ["External review"]}}, {"templateId": "rec59ldjjHpsfw4cN0BLNAzJ", "data": {"fldECiogGwC45TVKtLkOOZ7F": ["optsdwDk3d7GYZ6KjhN74HQb"], "fldGqcl0Pp1KPF1zKTaVKLWE": "2024-12-24T16:00:00.000Z", "fldLE6mP55HwK7S5GBm99p2I": ["optUFtZYA1lL2cPtQkeHxOo0"], "fldMjxUqII4yMKVoMDUC4zpz": [{"name": "headshot-cyan-1.png", "id": "tplattMpm0z3rFkoaX23RlWAFtM", "path": "template/tplattMpm0z3rFkoaX23RlWAFtM.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 499769}], "fldQFXh3y16o9c3oqvHmV7JO": ["recc6Ksk95ntLaTh4AOrOYot"], "fldRQaQ5s5w8z5pjvzDPIziW": "Pixzel cooperation", "fldW0Cti1yTX9ysQS9COz0ka": "2024-12-31T00:00:00.000Z", "fldXY9FIwaLf2lqpkg2aEXKz": "PixelCraft Studios", "fldq81K0aY4sMB1IYqyRjlcR": "", "fldxibAWoSgnoqiUJUyu41fq": ["optQ4gHcX3NZlA7NfY4PmAsl"]}, "values": {"fldECiogGwC45TVKtLkOOZ7F": ["Employment/HR"], "fldGqcl0Pp1KPF1zKTaVKLWE": "2024-12-24", "fldLE6mP55HwK7S5GBm99p2I": ["Non-Compete Agreement"], "fldMjxUqII4yMKVoMDUC4zpz": ["headshot-cyan-1.png"], "fldQFXh3y16o9c3oqvHmV7JO": ["Revise <PERSON>'s offer - increase salary to $55K"], "fldRQaQ5s5w8z5pjvzDPIziW": "Pixzel cooperation", "fldW0Cti1yTX9ysQS9COz0ka": "2024-12-31", "fldXY9FIwaLf2lqpkg2aEXKz": "PixelCraft Studios", "fldq81K0aY4sMB1IYqyRjlcR": "", "fldxibAWoSgnoqiUJUyu41fq": ["Approved/accepted"]}}, {"templateId": "rec3v36qSqKEkydXzlHDlptU", "data": {"fldECiogGwC45TVKtLkOOZ7F": ["optdwiz2v5WxiETcvHZoccoD"], "fldGqcl0Pp1KPF1zKTaVKLWE": "2024-07-30T16:00:00.000Z", "fldLE6mP55HwK7S5GBm99p2I": ["optpXC4n5VopZcAdZKSpWyOc"], "fldMjxUqII4yMKVoMDUC4zpz": [{"name": "headshot-pink-1.png", "id": "tplattA0jeZg8PzLUYsPi17nGxz", "path": "template/tplattA0jeZg8PzLUYsPi17nGxz.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 215411}], "fldQFXh3y16o9c3oqvHmV7JO": ["recCSMByEAtW0liouLQD9u5g"], "fldRQaQ5s5w8z5pjvzDPIziW": "Title Transfer", "fldW0Cti1yTX9ysQS9COz0ka": "2024-12-31T00:00:00.000Z", "fldXY9FIwaLf2lqpkg2aEXKz": "MetroGuard Insurance", "fldq81K0aY4sMB1IYqyRjlcR": "", "fldxibAWoSgnoqiUJUyu41fq": ["optQ4gHcX3NZlA7NfY4PmAsl"]}, "values": {"fldECiogGwC45TVKtLkOOZ7F": ["Insurance"], "fldGqcl0Pp1KPF1zKTaVKLWE": "2024-07-30", "fldLE6mP55HwK7S5GBm99p2I": ["Insurance Policy"], "fldMjxUqII4yMKVoMDUC4zpz": ["headshot-pink-1.png"], "fldQFXh3y16o9c3oqvHmV7JO": ["Modify bill of sale for Amerivan"], "fldRQaQ5s5w8z5pjvzDPIziW": "Title Transfer", "fldW0Cti1yTX9ysQS9COz0ka": "2024-12-31", "fldXY9FIwaLf2lqpkg2aEXKz": "MetroGuard Insurance", "fldq81K0aY4sMB1IYqyRjlcR": "", "fldxibAWoSgnoqiUJUyu41fq": ["Approved/accepted"]}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}