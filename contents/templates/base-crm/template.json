{"schemaVersion": "v1", "templateId": "base-crm", "name": "Base CRM", "cover": "/assets/template/template-cover-no-image-color.png", "description": "Base Template in Every New Space", "category": "official", "version": "0.0.7", "copyright": "Copyright by Vika Limited, AGPL", "installOnce": true, "initMissions": [], "newMemberJoinMissions": [{"type": "INVITE_MEMBER", "name": "{to.name}, 您好。欢迎加入BIKA", "description": "{to.name}, 您好，感谢您加入BIKA，BIKA是一个帮助您更好的管理团队，提高工作效率的应用。\nBIKA的价值来源于协同工作和自动化工作，我们希望您能邀请一位新用户加入BIKA，即是为了让您在BIKA中有更好的体验，也是为了让您的团队更好的协同工作。\n这里有一个小小请求，请问您可以邀请一位新伙伴加入BIKA吗？\n这是您的邀请链接👇\n{to.urlInvite}\n再次感谢您的加入，祝您工作愉快！\n", "to": [{"type": "ADMIN"}]}], "resources": [{"resourceType": "AI", "templateId": "ai", "name": "AI", "aiModel": "", "prompt": ""}, {"resourceType": "DATABASE", "templateId": "people", "databaseType": "DATUM", "name": "People", "records": [], "fields": [{"type": "SINGLE_TEXT", "templateId": "name", "name": "Name"}, {"type": "EMAIL", "templateId": "email", "name": "Email"}, {"type": "LONG_TEXT", "templateId": "description", "name": "描述"}, {"type": "PHONE", "templateId": "phone", "name": "电话号码"}, {"type": "LONG_TEXT", "templateId": "department", "name": "部门"}, {"type": "LONG_TEXT", "templateId": "position", "name": "职位"}, {"type": "ONE_WAY_LINK", "templateId": "organization", "name": "Organization", "property": {"foreignDatabaseTemplateId": "base-crm:organization"}}, {"templateId": "created_time", "name": "Created Time", "type": "CREATED_TIME", "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}}]}, {"resourceType": "DATABASE", "templateId": "organization", "databaseType": "DATUM", "name": "Organization", "records": [], "fields": [{"type": "SINGLE_TEXT", "templateId": "name", "name": "公司名"}, {"type": "SINGLE_SELECT", "templateId": "stage", "name": "线索来源", "property": {"options": [{"id": "1", "name": "网页客服"}, {"id": "2", "name": "自媒体"}, {"id": "3", "name": "线上活动"}, {"id": "4", "name": "线下活动"}, {"id": "5", "name": "空间站认证"}, {"id": "6", "name": "百度SEM"}, {"id": "7", "name": "潜客池"}, {"id": "8", "name": "PQL库"}, {"id": "9", "name": "EDU-AI课转化"}, {"id": "10", "name": "模板订单"}, {"id": "11", "name": "机器人申请"}, {"id": "12", "name": "小程序申请"}, {"id": "13", "name": "新手问卷留资"}, {"id": "14", "name": "创业扶持计划"}, {"id": "15", "name": "企业微信"}, {"id": "16", "name": "钉钉"}, {"id": "17", "name": "飞书"}, {"id": "18", "name": "腾讯千帆"}, {"id": "19", "name": "华为云市场"}, {"id": "20", "name": "华为云市场"}, {"id": "21", "name": "客户转介绍"}, {"id": "22", "name": "自有线索"}, {"id": "23", "name": "社群"}, {"id": "24", "name": "生态渠道"}, {"id": "25", "name": "SDR外呼（陌拜）"}]}}, {"type": "LONG_TEXT", "templateId": "description", "name": "描述"}, {"type": "NUMBER", "templateId": "scale", "name": "公司规模", "property": {"precision": 0, "symbol": ""}}, {"type": "SINGLE_TEXT", "templateId": "address", "name": "公司所在地址"}, {"templateId": "created_time", "name": "Created Time", "type": "CREATED_TIME", "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}}]}, {"resourceType": "DATABASE", "templateId": "crm-task", "databaseType": "TASK", "name": "Tasks", "fields": []}], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}