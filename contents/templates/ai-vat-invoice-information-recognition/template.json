{"templateId": "ai-vat-invoice-information-recognition", "name": {"zh-CN": "AI 增值税发票（中国）信息识别", "zh-TW": "AI 增值稅發票（中國）信息識別", "en": "AI VAT Invoice Information Recognition (China)", "ja": "AI 増値税発票情報認識（中国）"}, "description": {"zh-CN": "本模板利用百度智能云的财务识别OCR自动提取发票中的关键信息，并支持发票验真。帮助企业或个人减少手动录入，提高财务数据管理效率。优化工作流程，减少人为错误，提高数据准确性。", "zh-TW": "本模板利用百度智能云的財務識別OCR自動提取發票中的關鍵信息，並支持發票驗真。幫助企業或個人減少手動錄入，提高財務數據管理效率。優化工作流程，減少人為錯誤，提高數據準確性。", "en": "This template uses the financial recognition OCR of Baidu AI Cloud to automatically extract the key information from the invoice and support invoice verification. It helps enterprises or individuals reduce manual input, improve the efficiency of financial data management. Optimize the work process, reduce human errors, and improve data accuracy.", "ja": "本テンプレートは、百度スマートクラウドの財務認識OCRを利用して、発票の重要な情報を自動的に抽出し、発票の真偽確認をサポートします。企業や個人が手動入力を減らし、財務データ管理の効率を向上させるのに役立ちます。作業プロセスを最適化し、人為的なエラーを減らし、データの正確性を向上させます。"}, "cover": "/assets/template/template-cover-ai-vat-invoice-information-recognition.png", "author": "Na<PERSON>a<PERSON>on <<EMAIL>>", "category": ["ai", "operation"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.4", "resources": [{"resourceType": "FORM", "templateId": "fomd113CTqhWmWDPVIhVk3I6", "name": {"en": "Invoice Upload Form", "ja": "発票アップロード認識", "zh-CN": "发票上传识别", "zh-TW": "發票上傳識別"}, "description": {"en": "Please fill in the invoice name and upload the invoice", "ja": "請填寫發票名稱並上傳發票", "zh-CN": "请填写发票名称并上传发票", "zh-TW": "請填寫發票名稱並上傳發票"}, "formType": "DATABASE", "databaseTemplateId": "dat0fHaAOKZxlCza7O6acIeE", "metadata": {"type": "VIEW", "viewTemplateId": "viwOHez8Ut6wFSbd65i9qhJq"}}, {"resourceType": "AUTOMATION", "templateId": "atoPixaWZVzzgE8cp0c3vaSj", "name": {"en": "Automatic Invoice Recognition and Processing", "ja": "発票自動認識と処理", "zh-CN": "发票自动识别与处理", "zh-TW": "發票自動識別與處理"}, "triggers": [{"triggerType": "RECORD_MATCH", "templateId": "trgqeNiYsxYOwqSoWXqyQ6k3", "description": {"en": "When the invoice is uploaded", "ja": "発票がアップロードされたとき", "zh-CN": "当发票上传时", "zh-TW": "當發票上傳時"}, "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldRkUl5YvSa3OjqBngwG3LO", "fieldType": "ATTACHMENT", "clause": {"operator": "IsNotEmpty"}}]}, "databaseTemplateId": "dat0fHaAOKZxlCza7O6acIeE"}}], "actions": [{"templateId": "actMqTRWxMLbNyieYv6CCk8U", "description": {"en": "Concatenate the attachment address", "ja": "添付ファイルのアドレスを連結", "zh-CN": "拼接附件地址", "zh-TW": "拼接附件地址"}, "actionType": "RUN_SCRIPT", "input": {"type": "SCRIPT", "language": "python", "script": "fileData = <%= JSON.stringify(_triggers.trgqeNiYsxYOwqSoWXqyQ6k3.record.cells.fldRkUl5YvSa3OjqBngwG3LO.data) %>\n\nif len(fileData) == 1:\n  fileURL = \"https://s1.bika.ai/\" + fileData[0][\"path\"]\nelse: \n  raise Exception(\"file: \" + len(fileData))"}}, {"templateId": "actU2CgVwDkFAuNakiodbVGG", "description": {"en": "Invoice Recognition and Verification", "ja": "発票認識と検証", "zh-CN": "发票识别和验真", "zh-TW": "發票識別與驗真"}, "actionType": "TOOLSDK_AI", "input": {"type": "TOOLSDK_AI", "packageKey": "invoice_recognition", "toolKey": "invoice_recognition", "inputData": {"client_id": "", "file_data": "<%= _actions.actMqTRWxMLbNyieYv6CCk8U.fileURL %>", "client_secret": "", "invoice_verify": false}}}, {"templateId": "actmEz02lGbR2ZY7H9htnLqb", "actionType": "UPDATE_RECORD", "description": {"en": "Update the invoice data", "ja": "発票データを更新", "zh-CN": "更新发票数据", "zh-TW": "更新發票數據"}, "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _triggers.trgqeNiYsxYOwqSoWXqyQ6k3.record.id %>", "data": {"fld48ZCkxxaaexUYBq1QQzkU": "<%= _actions.actU2CgVwDkFAuNakiodbVGG.words_result.AmountInFiguers %>", "fldApFMxc7Lbsb8k6mnNFLWY": "<%= _actions.actU2CgVwDkFAuNakiodbVGG.InvoiceNum %><%= _actions.actU2CgVwDkFAuNakiodbVGG.words_result.InvoiceNum %>", "fldEMN66DwZgO1dRwUuaalMr": "<%= _actions.actU2CgVwDkFAuNakiodbVGG.InvoiceDate %><%= _actions.actU2CgVwDkFAuNakiodbVGG.words_result.InvoiceDate %>", "fldPeOy7CqYCGt3Jfdv7SgF4": "<%= _actions.actU2CgVwDkFAuNakiodbVGG.words_result.Remarks %>", "fldTpe2ITJb4pQUIT9PuScia": "<%= _actions.actU2CgVwDkFAuNakiodbVGG.InvoiceType %><%= _actions.actU2CgVwDkFAuNakiodbVGG.words_result.InvoiceType %>", "fldX6HPEnaYcfILb5n2VqMle": "<%= _actions.actU2CgVwDkFAuNakiodbVGG.words_result.CommodityName[0].word %>", "fldj7nt6DOkl0ftIJoDtiprh": "<%= _actions.actU2CgVwDkFAuNakiodbVGG.words_result.TotalTax %>", "fldldXpVPsqDQIzmutZ0w2UB": "<%= _actions.actU2CgVwDkFAuNakiodbVGG.words_result.SellerName %>", "fldrvLE3omiRmfHMaQ5im0T9": "<%= _actions.actU2CgVwDkFAuNakiodbVGG.VerifyResult %> <%= _actions.actU2CgVwDkFAuNakiodbVGG.VerifyMessage %>"}, "databaseTemplateId": "dat0fHaAOKZxlCza7O6acIeE"}}]}, {"resourceType": "DATABASE", "templateId": "dat0fHaAOKZxlCza7O6acIeE", "name": {"en": "Invoice Data", "ja": "発票データ", "zh-CN": "发票数据", "zh-TW": "發票數據"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwUCK0dooMmXm7M0GGR7O9D", "name": {"en": "Invoice Data", "ja": "発票データ", "zh-CN": "发票数据", "zh-TW": "發票數據"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldrtuFF2rrv59BcbbmE6Gut", "hidden": false}, {"templateId": "fldRkUl5YvSa3OjqBngwG3LO", "hidden": false, "width": 172}, {"templateId": "fldApFMxc7Lbsb8k6mnNFLWY", "hidden": false, "width": 210}, {"templateId": "fldEMN66DwZgO1dRwUuaalMr", "hidden": false, "width": 143}, {"templateId": "fldldXpVPsqDQIzmutZ0w2UB", "hidden": false}, {"templateId": "fldX6HPEnaYcfILb5n2VqMle", "hidden": false}, {"templateId": "fldTpe2ITJb4pQUIT9PuScia", "hidden": false}, {"templateId": "fldj7nt6DOkl0ftIJoDtiprh", "hidden": false, "width": 120}, {"templateId": "fld48ZCkxxaaexUYBq1QQzkU", "hidden": false, "width": 124}, {"templateId": "fldPeOy7CqYCGt3Jfdv7SgF4", "hidden": false}, {"templateId": "fldrvLE3omiRmfHMaQ5im0T9", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwOHez8Ut6wFSbd65i9qhJq", "name": {"en": "Form", "ja": "フォーム", "zh-CN": "表单", "zh-TW": "表單"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldrtuFF2rrv59BcbbmE6Gut", "hidden": false}, {"templateId": "fldRkUl5YvSa3OjqBngwG3LO", "hidden": false}, {"templateId": "fldApFMxc7Lbsb8k6mnNFLWY", "hidden": true}, {"templateId": "fldEMN66DwZgO1dRwUuaalMr", "hidden": true}, {"templateId": "fldldXpVPsqDQIzmutZ0w2UB", "hidden": true}, {"templateId": "fldX6HPEnaYcfILb5n2VqMle", "hidden": true}, {"templateId": "fldTpe2ITJb4pQUIT9PuScia", "hidden": true}, {"templateId": "fldj7nt6DOkl0ftIJoDtiprh", "hidden": true}, {"templateId": "fld48ZCkxxaaexUYBq1QQzkU", "hidden": true}, {"templateId": "fldPeOy7CqYCGt3Jfdv7SgF4", "hidden": true}, {"templateId": "fldrvLE3omiRmfHMaQ5im0T9", "hidden": true}], "groups": []}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldrtuFF2rrv59BcbbmE6Gut", "privilege": "TYPE_EDIT", "name": {"en": "Title", "ja": "タイトル", "zh-CN": "标题", "zh-TW": "標題"}, "required": true, "primary": true}, {"type": "ATTACHMENT", "templateId": "fldRkUl5YvSa3OjqBngwG3LO", "privilege": "FULL_EDIT", "name": {"en": "Invoice Attachment", "ja": "発票添付", "zh-CN": "发票附件", "zh-TW": "發票附件"}, "description": {"en": "Please upload a PDF or image format of the VAT invoice attachment, only one invoice can be recognized at a time", "ja": "PDFまたは画像形式の増値税発票添付をアップロードしてください。一度に1枚の発票しか認識できません。", "zh-CN": "请上传PDF或图片格式的增值税发票附件，一次只支持识别一张发票", "zh-TW": "請上傳PDF或圖片格式的增值稅發票附件，一次只支持識別一張發票"}, "required": true, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldApFMxc7Lbsb8k6mnNFLWY", "privilege": "FULL_EDIT", "name": {"en": "Invoice Number", "ja": "発票番号", "zh-CN": "发票号码", "zh-TW": "發票號碼"}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldEMN66DwZgO1dRwUuaalMr", "privilege": "FULL_EDIT", "name": {"en": "Invoice Date", "ja": "開票日", "zh-CN": "开票日期", "zh-TW": "開票日期"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldldXpVPsqDQIzmutZ0w2UB", "privilege": "FULL_EDIT", "name": {"en": "Sales Party", "ja": "銷售方", "zh-CN": "销售方", "zh-TW": "銷售方"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldX6HPEnaYcfILb5n2VqMle", "privilege": "FULL_EDIT", "name": {"en": "Project Name", "ja": "プロジェクト名", "zh-CN": "项目名称", "zh-TW": "項目名稱"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldTpe2ITJb4pQUIT9PuScia", "privilege": "FULL_EDIT", "name": {"en": "Invoice Type", "ja": "発票タイプ", "zh-CN": "发票类型", "zh-TW": "發票類型"}, "property": {"options": [{"id": "optY40nSJxRv2g8cJ1xQgp51", "name": "电子发票(专用发票)", "color": "deepPurple"}, {"id": "optAISt72dpBr0gauTGTOANl", "name": "电子发票(普通发票)", "color": "indigo"}, {"id": "optHDha5FjmqsBxEmrpeY7x8", "name": "全电发票（专用发票）", "color": "blue"}, {"id": "opt0YZag6zgwxu2TM61A72ON", "name": "全电发票（普通发票）", "color": "teal"}, {"id": "opt9C50oG360Tt0w7Mtlpkis", "name": "专用发票", "color": "green"}], "defaultValue": ""}, "primary": false}, {"type": "CURRENCY", "templateId": "fldj7nt6DOkl0ftIJoDtiprh", "privilege": "FULL_EDIT", "name": {"en": "Tax Amount", "ja": "税額", "zh-CN": "税额", "zh-TW": "稅額"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "￥", "symbolAlign": "left"}, "primary": false}, {"type": "CURRENCY", "templateId": "fld48ZCkxxaaexUYBq1QQzkU", "privilege": "FULL_EDIT", "name": {"en": "Amount", "ja": "金額", "zh-CN": "金额", "zh-TW": "金額"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "￥", "symbolAlign": "left"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldPeOy7CqYCGt3Jfdv7SgF4", "privilege": "FULL_EDIT", "name": {"en": "Remark", "ja": "備考", "zh-CN": "备注", "zh-TW": "備註"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldrvLE3omiRmfHMaQ5im0T9", "privilege": "FULL_EDIT", "name": {"en": "Invoice Verification Result", "ja": "発票検証結果", "zh-CN": "发票验真结果", "zh-TW": "發票驗真結果"}, "primary": false}], "records": [{"templateId": "recl7OZBt9cSInubV0LidkBB", "data": {"fld48ZCkxxaaexUYBq1QQzkU": 53.16, "fldApFMxc7Lbsb8k6mnNFLWY": "25447200000198518479", "fldEMN66DwZgO1dRwUuaalMr": "2025年03月24日", "fldPeOy7CqYCGt3Jfdv7SgF4": "zp77284196318(00001,309899720780)", "fldRkUl5YvSa3OjqBngwG3LO": [{"id": "tplatt55eGAcJnpKUtk6lqPpcDA", "name": "电子专用发票.pdf", "path": "template/tplatt55eGAcJnpKUtk6lqPpcDA.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 99259}], "fldTpe2ITJb4pQUIT9PuScia": ["optY40nSJxRv2g8cJ1xQgp51"], "fldX6HPEnaYcfILb5n2VqMle": "*乳制品*优诺(yoplait)4.0+优质乳蛋白鲜牛奶巴氏杀菌鲜奶950ml/盒营养早餐", "fldj7nt6DOkl0ftIJoDtiprh": 4.39, "fldldXpVPsqDQIzmutZ0w2UB": "广州晶东贸易有限公司", "fldrtuFF2rrv59BcbbmE6Gut": "电子专票", "fldrvLE3omiRmfHMaQ5im0T9": " "}, "values": {"fld48ZCkxxaaexUYBq1QQzkU": "￥53.16", "fldApFMxc7Lbsb8k6mnNFLWY": "25447200000198518479", "fldEMN66DwZgO1dRwUuaalMr": "2025年03月24日", "fldPeOy7CqYCGt3Jfdv7SgF4": "zp77284196318(00001,309899720780)", "fldRkUl5YvSa3OjqBngwG3LO": ["电子专用发票.pdf"], "fldTpe2ITJb4pQUIT9PuScia": ["电子发票(专用发票)"], "fldX6HPEnaYcfILb5n2VqMle": "*乳制品*优诺(yoplait)4.0+优质乳蛋白鲜牛奶巴氏杀菌鲜奶950ml/盒营养早餐", "fldj7nt6DOkl0ftIJoDtiprh": "￥4.39", "fldldXpVPsqDQIzmutZ0w2UB": "广州晶东贸易有限公司", "fldrtuFF2rrv59BcbbmE6Gut": "电子专票", "fldrvLE3omiRmfHMaQ5im0T9": " "}}, {"templateId": "recQ4hYRprYyZNZfhpAsUa31", "data": {"fld48ZCkxxaaexUYBq1QQzkU": 66.58, "fldApFMxc7Lbsb8k6mnNFLWY": "25447200000194935637", "fldEMN66DwZgO1dRwUuaalMr": "20250323", "fldPeOy7CqYCGt3Jfdv7SgF4": "订单号:311078169880", "fldRkUl5YvSa3OjqBngwG3LO": [{"id": "tplatt2N5EGFglAqP3PTnCroRmi", "name": "电子普通发票.pdf", "path": "template/tplatt2N5EGFglAqP3PTnCroRmi.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 100388}], "fldTpe2ITJb4pQUIT9PuScia": ["opt0YZag6zgwxu2TM61A72ON"], "fldX6HPEnaYcfILb5n2VqMle": "*纸制品*爱酷熊(AIKUUBEAR)舒薄全包拉拉裤XXL84加大码尿不湿超薄透气瞬吸(13.5-16kg)【品牌直供安心品质】", "fldj7nt6DOkl0ftIJoDtiprh": 7.66, "fldldXpVPsqDQIzmutZ0w2UB": "东莞京东利昇贸易有限公司", "fldrtuFF2rrv59BcbbmE6Gut": "电子普票（验真）", "fldrvLE3omiRmfHMaQ5im0T9": "0001 查验成功发票一致"}, "values": {"fld48ZCkxxaaexUYBq1QQzkU": "￥66.58", "fldApFMxc7Lbsb8k6mnNFLWY": "25447200000194935637", "fldEMN66DwZgO1dRwUuaalMr": "20250323", "fldPeOy7CqYCGt3Jfdv7SgF4": "订单号:311078169880", "fldRkUl5YvSa3OjqBngwG3LO": ["电子普通发票.pdf"], "fldTpe2ITJb4pQUIT9PuScia": ["全电发票（普通发票）"], "fldX6HPEnaYcfILb5n2VqMle": "*纸制品*爱酷熊(AIKUUBEAR)舒薄全包拉拉裤XXL84加大码尿不湿超薄透气瞬吸(13.5-16kg)【品牌直供安心品质】", "fldj7nt6DOkl0ftIJoDtiprh": "￥7.66", "fldldXpVPsqDQIzmutZ0w2UB": "东莞京东利昇贸易有限公司", "fldrtuFF2rrv59BcbbmE6Gut": "电子普票（验真）", "fldrvLE3omiRmfHMaQ5im0T9": "0001 查验成功发票一致"}}, {"templateId": "recxZnpex79WjK0q8XI2LvuH", "data": {"fld48ZCkxxaaexUYBq1QQzkU": 368, "fldApFMxc7Lbsb8k6mnNFLWY": "39518848", "fldEMN66DwZgO1dRwUuaalMr": "2023年07月19日", "fldRkUl5YvSa3OjqBngwG3LO": [{"id": "tplattQvilTSORkv5uQHlLfjueD", "name": "纸质专票.jpg", "path": "template/tplattQvilTSORkv5uQHlLfjueD.jpeg", "bucket": "bika-dev", "mimeType": "image/jpeg", "size": 6033908}], "fldTpe2ITJb4pQUIT9PuScia": ["opt9C50oG360Tt0w7Mtlpkis"], "fldX6HPEnaYcfILb5n2VqMle": "*信息技术服务*维格云办公解决方案核心平台VI.0", "fldj7nt6DOkl0ftIJoDtiprh": 20.83, "fldldXpVPsqDQIzmutZ0w2UB": "深圳维格云科技有限公司", "fldrtuFF2rrv59BcbbmE6Gut": "纸质专票"}, "values": {"fld48ZCkxxaaexUYBq1QQzkU": "￥368", "fldApFMxc7Lbsb8k6mnNFLWY": "39518848", "fldEMN66DwZgO1dRwUuaalMr": "2023年07月19日", "fldRkUl5YvSa3OjqBngwG3LO": ["纸质专票.jpg"], "fldTpe2ITJb4pQUIT9PuScia": ["专用发票"], "fldX6HPEnaYcfILb5n2VqMle": "*信息技术服务*维格云办公解决方案核心平台VI.0", "fldj7nt6DOkl0ftIJoDtiprh": "￥20.83", "fldldXpVPsqDQIzmutZ0w2UB": "深圳维格云科技有限公司", "fldrtuFF2rrv59BcbbmE6Gut": "纸质专票"}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}