//
// /*
//     0. 状态跟进：定时发送客户状态邮件，当空间站DAU=0时，发送邮件提醒并创建跟进任务
//     1. 定期培训：付费成功后的第二天，生成一个，培训支持Training
//     2. 上钟任务：培训之后，生成一个协助落地Onboarding Mission（甚至会根据客户的详情，用AI量身订造一个场景任务列表）
//     3. 内部推广：Onboarding之后，Internal Promotion第二个是内部推广活动方案
//     4. 月度报告：定时，每个月，Monthly Report 第四个是固定生成的月度报告
//     5. 续费准备：客户合同过期前90天，发送提醒邮件，推荐续费方案与优惠，提醒安排回访并填写表单，识别和解决潜在风险
//     6. 增值服务：在表单或AI chatbot 填写客户使用情况、新需求与产品反馈等增值机会，提供增值服务、以往产品使用场景案例
//     7. 客户反馈：客户付费后推送满意度调查表给销售，可发送给客户填写，填写结果推送给销售
//     8. 产品更新：定期推送新版本功能、场景解决方案给销售，销售发给客户
// */
// import { CustomTemplate } from '@bika/types/template/bo';
// const template: CustomTemplate = {
//   templateId: 'vika_csm',
//   name: 'Vika CSM客户成功管理模板',
//   category: 'Sales',
//   // mirrors: [],
//   dashboards: [],
//   forms: [],
//   automations: [
//     // 发送客户状态邮件
//     // 定时早上6点
//     // 读付费客户Customer表 和 CustomerUsageSnapshot
//     // 通过view视图，筛选CustomerUsageSnapshot
//     // 生成邮件
//     // 判断谁是谁，进行发送邮件（自己跟进的）
//     {
//       templateId: 'send_customer_status_email',
//       name: '发送客户状态邮件',
//       triggers: [
//         {
//           type: 'CRON',
//           config: {
//             type: 'Cron',
//             timezone: 'AUTO',
//             cron: '0 6 * * *',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'find_records_filter_paid_customer',
//           name: '筛选付费客户',
//           type: 'find_records',
//           config: {
//             database: 'customer',
//             view: '付费客户',
//           },
//         },
//         {
//           templateId: 'send_customer_status_email',
//           name: '发送客户状态邮件',
//           type: 'advanced_send_email',
//           // value: "https://xxxx/api/send_customer_status_email",
//         },
//       ],
//     },
//     // 生成客户跟进任务
//     // 定时早上8点
//     // 读取CustomerUsageSnapshot，如发现Space DAU为0
//     // 生成Mission任务
//     // 发送邮件给对应的销售警告
//     {
//       templateId: 'generate_customer_follow_up_mission',
//       name: '生成客户跟进任务',
//       triggers: [
//         {
//           type: 'SCHEDULER',
//           config: {
//             type: 'CRON',
//             timezone: 'AUTO',
//             cron: '0 8 * * *',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'find_paid_customer_status',
//           name: '筛选付费客户状态，DAU=0',
//           type: 'find_records',
//           config: {
//             database: 'customer',
//             view: '客户状态',
//           },
//         },
//         {
//           templateId: 'send_customer_status_warnning_email',
//           name: '发送客户状态警告邮件',
//           type: 'advanced_send_email',
//           // value: "https://xxxx/api/send_customer_status_email",
//         },
//         {
//           templateId: 'generate_customer_follow_up_mission',
//           name: '生成客户跟进任务',
//           type: 'create_record',
//           // value: "",
//         },
//       ],
//     },
//     // 客户成交后的1天，创建每周的直播培训training任务 - 邀请客户参与集体上钟，发会收到一封邮件
//     // 他要回复一个任务，如果一直没有回复，任务没结束，之后每天通知，一直到做完为止
//     {
//       templateId: 'generate_customer_training_brief',
//       name: '生成客户培训Training任务',
//       triggers: [
//         {
//           type: 'find_records', // TODO After record 状态变成（大于）成交后的1 days
//           templateId: 'find_transaction_customer_new',
//           name: '筛选新成交且未完成培训的客户',
//           config: {
//             database: 'customer',
//             view: '新付费客户',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'generate_customer_training_brief',
//           name: '生成客户培训任务',
//           type: 'create_record',
//           // value: "",
//         },
//       ],
//     },
//     // 衔接直播培训，客户完成培训之后，提醒填写一个表单更新培训状态，并生成一个协助落地Onboarding Mission
//     {
//       templateId: 'generate_customer_onboarding_brief',
//       name: '生成客户培训Training任务',
//       triggers: [
//         {
//           type: 'find_records',
//           templateId: 'find_after_trainning_customer',
//           name: '筛选已参与培训且未进行Onboarding的客户',
//           config: {
//             database: 'customer',
//             view: '待上钟的付费客户',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'send_update_training_status_email',
//           name: '发送更新培训状态邮件',
//           type: 'advanced_send_email',
//           // value: "https://xxxx/api/send_customer_status_email",
//         },
//         {
//           templateId: 'generate_customer_onboarding_mission',
//           name: '生成客户上钟任务',
//           type: 'create_record',
//           // value: "",
//         },
//       ],
//     },
//     // 完成Onboarding且已录入客户上钟录屏和描述需求，根据需求生成场景案例、成功客户案例、与建议内部推广活动方案
//     {
//       templateId: 'generate_customer_promotion_brief',
//       name: '生成客户培训Training任务',
//       triggers: [
//         {
//           type: 'find_records',
//           templateId: 'find_after_onboarding_customer',
//           name: '筛选已Onboarding且已提交需求的客户',
//           config: {
//             database: 'customer',
//             view: '待Onboarding的付费客户',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'send_customer_promotion_email',
//           name: '发送场景案例、成功客户案例与建议的推广活动方案邮件',
//           type: 'advanced_send_email',
//           // value: "https://xxxx/api/send_customer_status_email",
//         },
//         {
//           templateId: 'generate_customer_promotion_mission',
//           name: '生成客户Promotion任务',
//           type: 'create_record',
//           // value: "",
//         },
//       ],
//     },
//     // 完成Onboarding且已录入客户上钟录屏和描述需求，根据需求生成场景案例、成功客户案例、与建议内部推广活动方案
//     {
//       templateId: 'generate_customer_promotion_brief',
//       name: '生成客户培训Training任务',
//       triggers: [
//         {
//           type: 'find_records',
//           templateId: 'find_after_onboarding_customer',
//           name: '筛选已Onboarding且已提交需求的客户',
//           config: {
//             database: 'customer',
//             view: '待Onboarding的付费客户',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'send_customer_promotion_email',
//           name: '发送场景案例、成功客户案例与建议的推广活动方案邮件',
//           type: 'advanced_send_email',
//           // value: "https://xxxx/api/send_customer_status_email",
//         },
//         {
//           templateId: 'generate_customer_promotion_mission',
//           name: '生成客户Promotion任务',
//           type: 'create_record',
//           // value: "",
//         },
//       ],
//     },
//     // 完成Onboarding且已录入客户上钟录屏和描述需求，根据需求生成场景案例、成功客户案例、与建议内部推广活动方案
//     {
//       templateId: 'generate_customer_promotion_brief',
//       name: '生成客户培训Training任务',
//       triggers: [
//         {
//           type: 'find_records',
//           templateId: 'find_after_onboarding_customer',
//           name: '筛选已Onboarding且已提交需求的客户',
//           config: {
//             database: 'customer',
//             view: '待Onboarding的付费客户',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'send_customer_promotion_email',
//           name: '发送场景案例、成功客户案例与建议的推广活动方案邮件',
//           type: 'advanced_send_email',
//           // value: "https://xxxx/api/send_customer_status_email",
//         },
//         {
//           templateId: 'generate_customer_promotion_mission',
//           name: '生成客户Promotion任务',
//           type: 'create_record',
//           // value: "",
//         },
//       ],
//     },
//     // 定时，每个月1号，自动生成Monthly Report 月度报告
//     {
//       templateId: 'generate_customer_promotion_brief',
//       name: '生成客户培训Training任务',
//       triggers: [
//         {
//           type: 'SCHEDULER',
//           config: {
//             type: 'CRON',
//             timezone: 'AUTO',
//             cron: '1 0 * * *', // 每个月1号
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'generate_customer_monthly_report',
//           name: '生成客户月度报告，销售可点击查看活跃度、新增空间站附件容量、表格数、席位数等',
//           type: 'create_record',
//           // value: "",
//         },
//       ],
//     },
//     // 客户合同过期前90天，发送提醒邮件，推荐续费方案与优惠，提醒安排回访并填写表单，识别和解决潜在风险
//     {
//       templateId: 'generate_customer_promotion_brief',
//       name: '生成客户培训Training任务',
//       triggers: [
//         {
//           type: 'find_records',
//           templateId: 'find_renewing_customer',
//           name: '筛选90天内即将续费的客户',
//           config: {
//             database: 'customer',
//             view: '90天内待续费客户',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'send_customer_renewing_alert_email',
//           name: '发送续费提醒邮件，推荐续费方案与优惠',
//           type: 'advanced_send_email',
//           // value: "https://xxxx/api/send_customer_status_email",
//         },
//         {
//           templateId: 'generate_customer_monthly_brief',
//           name: '创建回访任务，提醒填写表单，提前识别和解决潜在风险',
//           type: 'create_record',
//           // value: "",
//         },
//       ],
//     },
//     // 客户付费后推送满意度调查表给销售，可发送给客户填写，填写结果推送给销售
//     {
//       templateId: 'generate_customer_satisfaction_survey_brief',
//       name: '满意度调查提醒',
//       triggers: [
//         {
//           type: 'find_records',
//           templateId: 'find_finished_rsatisfaction_survey_customer',
//           name: '筛选已填写满意度调查的客户',
//           config: {
//             database: 'customer',
//             view: '满意度调查',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'send_satisfaction_survey_results_email',
//           name: '发送满意度调查结果',
//           type: 'advanced_send_email',
//           // value: "https://xxxx/api/send_customer_status_email",
//         },
//       ],
//     },
//     // 定期推送新版本功能、场景解决方案给销售，销售发给客户
//     {
//       templateId: 'generate_new_function_brief',
//       name: '推送新功能与场景提醒',
//       triggers: [
//         {
//           type: 'find_records',
//           templateId: 'find_new_function_record',
//           name: '筛选新发布功能与场景解决方案',
//           config: {
//             database: 'product_new_function_solutions',
//             view: '产品新功能与场景',
//           },
//         },
//       ],
//       actions: [
//         {
//           templateId: 'send_new_version_email',
//           name: '推送新版本功能与场景提醒',
//           type: 'advanced_send_email',
//           // value: "https://xxxx/api/send_customer_status_email",
//         },
//       ],
//     },
//   ],

//   databases: [
//     {
//       templateId: 'customer',
//       name: 'Customer',
//       type: 'BASE_EXTERNAL',
//       config: {
//         external_host: 'vika.cn',
//         external_api_key: 'KEY',
//         external_datasheet_id: 'dstnlPk6tR3FfwXViQ',
//       },
//       records: [],
//     },
//     {
//       templateId: 'customer_usage_snapshot',
//       name: 'customerUsageSnapshot',
//       type: 'BASE_EXTERNAL',
//       config: {
//         external_host: 'vika.cn',
//         external_api_key: 'KEY',
//         external_datasheet_id: 'dstCmXMS8gEvYvHRTR',
//       },
//       records: [],
//     },
//     {
//       templateId: 'customer_success',
//       databaseType: 'DATUM',
//       config: {
//         type: 'vika',
//         apiKey: 'VIKA API KEY',
//       },
//       name: 'Customer Success',
//       records: [
//         {
//           'In trial': 'xx',
//           Onboarding: 'yy',
//           Steady: 'cc',
//           Expansion: 'bb',
//           Renewal: 'yy',
//           Churned: 'zz',
//         },
//       ],
//     },
//   ],
// };

// export default template;
