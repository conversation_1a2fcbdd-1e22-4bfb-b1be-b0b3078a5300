import type { ILocaleContext } from '@bika/contents/i18n/context';
import type { To, ToType } from '@bika/types/unit/bo';

export function getToConfig(locale: ILocaleContext): Record<
  ToType,
  {
    label: string;
    description: string;
    defaultValue: To;
  }
> {
  const { t } = locale;

  return {
    ADMIN: {
      label: t.unit.to.admin,
      description: t.unit.to.admin_description,
      defaultValue: {
        type: 'ADMIN',
      },
    },
    ALL_MEMBERS: {
      label: t.unit.to.all_members,
      description: t.unit.to.all_members_description,
      defaultValue: {
        type: 'ALL_MEMBERS',
      },
    },
    CURRENT_OPERATOR: {
      label: t.unit.to.current_operator,
      description: t.unit.to.current_operator_description,
      defaultValue: {
        type: 'CURRENT_OPERATOR',
      },
    },
    SPECIFY_UNITS: {
      label: t.unit.to.specify_units,
      description: t.unit.to.specify_units_description,
      defaultValue: {
        type: 'SPECIFY_UNITS',
        unitIds: [],
      },
    },
    USER: {
      label: t.unit.to.user,
      description: t.unit.to.user_description,
      defaultValue: {
        type: 'USER',
        userId: 'user_1',
      },
    },
    EMAIL_STRING: {
      label: t.unit.to.email_string,
      description: t.automation.action.find_members.to_email_addresses_description,
      defaultValue: {
        type: 'EMAIL_STRING',
        email: '',
      },
    },
    EMAIL_FIELD: {
      label: t.unit.to.email_field,
      description: t.unit.to.email_field_description,
      defaultValue: {
        type: 'EMAIL_FIELD',
        databaseId: '',
        viewId: '',
        fieldId: '',
      },
    },
    MEMBER_FIELD: {
      label: t.unit.to.member_field,
      description: t.unit.to.member_field_description,
      defaultValue: {
        type: 'MEMBER_FIELD',
        databaseId: '',
        viewId: '',
        fieldId: '',
      },
    },
    RECIPIENT: {
      label: t.unit.to.recipient,
      description: t.unit.to.recipient_description,
      defaultValue: {
        type: 'RECIPIENT',
        recipientType: 'USER',
        recipientId: '',
      },
    },
    UNIT_MEMBER: {
      label: t.unit.to.unit_member,
      description: t.unit.to.unit_member_description,
      defaultValue: {
        type: 'UNIT_MEMBER',
      },
    },
    UNIT_ROLE: {
      label: t.unit.to.unit_role,
      description: t.unit.to.unit_role_description,
      defaultValue: {
        type: 'UNIT_ROLE',
        roleId: '',
      },
    },
    UNIT_TEAM: {
      label: t.unit.to.unit_team,
      description: t.unit.to.unit_team_description,
      defaultValue: {
        type: 'UNIT_TEAM',
        teamId: '',
      },
    },
  };
}
