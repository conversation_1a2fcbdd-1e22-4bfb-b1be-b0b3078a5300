{"templateId": "contractor-freelancer-management", "name": {"en": "Contractor / Freelancer Management", "zh-CN": "承包商/自由职业者管理"}, "description": {"zh-CN": "承包商/自由职业者管理模板简化了从筛选到完成的管理。集中资源管理，链接任务，自动化发送邮件，并跟踪项目费用以实现透明度。", "en": "The Contractor/Freelancer Management simplifies management from screening to completion. Centralizes resource management, links tasks, automates interview feedback emails, and tracks project expenses for transparency and budget control."}, "cover": "/assets/template/template-cover-contractor-freelancer-management.png", "author": "Na<PERSON>aKon <<EMAIL>>", "category": ["project", "operation"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.13", "resources": [{"resourceType": "DATABASE", "templateId": "dati99SZ06GB3ojBqZu4kkDI", "name": {"en": "Projects", "zh-CN": "项目"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwZcbSCJECiHTLcSDF0jzV2", "name": {"en": "All Projects", "zh-CN": "所有项目"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldrTtsQOkRhoh4EEvXAn4bj", "hidden": false, "width": 290}, {"templateId": "fldeI2PjBSQYthKmahG3cEon", "hidden": false}, {"templateId": "fldI6fIhbU3GecieaJoF2CCx", "hidden": false}, {"templateId": "fldAdEFTNsbsGl3yM7WrcCp7", "hidden": false}, {"templateId": "fldxAvu18b9w4yMllqXFunTO", "hidden": false}, {"templateId": "fldfexVJm8bZcOqzWl1G0w8H", "hidden": false}]}, {"type": "TABLE", "templateId": "viwZ6PO1DGWElO2iFP8yl32p", "name": {"en": "Unfinished Projects", "zh-CN": "未完成项目"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldfexVJm8bZcOqzWl1G0w8H", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optrcmtzUpaNn"}}]}, "sorts": [], "fields": [{"templateId": "fldrTtsQOkRhoh4EEvXAn4bj", "hidden": false}, {"templateId": "fldeI2PjBSQYthKmahG3cEon", "hidden": false}, {"templateId": "fldI6fIhbU3GecieaJoF2CCx", "hidden": false}, {"templateId": "fldAdEFTNsbsGl3yM7WrcCp7", "hidden": false}, {"templateId": "fldxAvu18b9w4yMllqXFunTO", "hidden": false}, {"templateId": "fldfexVJm8bZcOqzWl1G0w8H", "hidden": false}], "groups": []}], "fields": [{"type": "FORMULA", "templateId": "fldrTtsQOkRhoh4EEvXAn4bj", "privilege": "TYPE_EDIT", "name": {"en": "Project", "zh-CN": "项目"}, "required": false, "property": {"expressionTemplate": "{fldfexVJm8bZcOqzWl1G0w8H}&\" - \"&{fldeI2PjBSQYthKmahG3cEon}"}, "primary": true}, {"type": "SINGLE_SELECT", "templateId": "fldI6fIhbU3GecieaJoF2CCx", "privilege": "NAME_EDIT", "name": {"en": "Category", "zh-CN": "类别"}, "property": {"options": [{"id": "optbhut4P9Omwxdq0oQ7E5RS", "name": "Marketing 📢", "color": "deepPurple"}, {"id": "optuoU9qJUADKteqvaVEgjp4", "name": "Content 📚", "color": "indigo"}, {"id": "optrlofR5DatDKKAuUudpzh3", "name": "Local 🎈", "color": "blue"}, {"id": "opttb6hfW5B4RmERXxlrwYLg", "name": "Tech 🖥️", "color": "teal"}, {"id": "optyPJ5z5xxcDHPwgtZG1jPt", "name": "Launch 🎙️", "color": "green"}, {"id": "optrHAy67JnH0KxueePTv8Qw", "name": "Design 🎨", "color": "yellow"}], "defaultValue": ""}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldAdEFTNsbsGl3yM7WrcCp7", "privilege": "NAME_EDIT", "name": {"en": "Description", "zh-CN": "描述"}, "primary": false}, {"type": "LINK", "templateId": "fldxAvu18b9w4yMllqXFunTO", "privilege": "NAME_EDIT", "name": {"en": "Contractors", "zh-CN": "承包商"}, "property": {"foreignDatabaseTemplateId": "dat918n7U5Cg8GdGbdlnoWAd", "brotherFieldTemplateId": "fldmEJ46EyIsr6HAJzAjilRH"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldfexVJm8bZcOqzWl1G0w8H", "privilege": "FULL_EDIT", "name": {"en": "Project Status", "zh-CN": "项目状态"}, "property": {"options": [{"id": "optizADkErBC4", "name": "Completed, not selectable.", "color": "deepPurple"}, {"id": "optrcmtzUpaNn", "name": "In progress", "color": "indigo"}], "defaultValue": ""}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldeI2PjBSQYthKmahG3cEon", "privilege": "FULL_EDIT", "name": {"en": "Project Name", "zh-CN": "项目名称"}, "primary": false}], "records": [{"templateId": "recTT42zOIDqOAY49AYIGK83", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Expand the brand ambassador program to reach a wider audience", "fldI6fIhbU3GecieaJoF2CCx": ["optbhut4P9Omwxdq0oQ7E5RS"], "fldeI2PjBSQYthKmahG3cEon": "Brand Ambassador Program Expansion", "fldfexVJm8bZcOqzWl1G0w8H": ["optrcmtzUpaNn"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Brand Ambassador Program Expansion", "fldxAvu18b9w4yMllqXFunTO": ["recMNdpC9yrECYj9sYi2Wo2a"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Expand the brand ambassador program to reach a wider audience", "fldI6fIhbU3GecieaJoF2CCx": ["Marketing 📢"], "fldeI2PjBSQYthKmahG3cEon": "Brand Ambassador Program Expansion", "fldfexVJm8bZcOqzWl1G0w8H": ["In progress"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Brand Ambassador Program Expansion", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}, {"templateId": "rec3xCfVsA7wOTEGsf1sLeZS", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Create and launch an online course related to the brand's offerings", "fldI6fIhbU3GecieaJoF2CCx": ["optuoU9qJUADKteqvaVEgjp4"], "fldeI2PjBSQYthKmahG3cEon": "Online Course Creation", "fldfexVJm8bZcOqzWl1G0w8H": ["optizADkErBC4"], "fldrTtsQOkRhoh4EEvXAn4bj": "Completed, not selectable. - Online Course Creation", "fldxAvu18b9w4yMllqXFunTO": ["recGlqYtBmR01f7lNxeafmco"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Create and launch an online course related to the brand's offerings", "fldI6fIhbU3GecieaJoF2CCx": ["Content 📚"], "fldeI2PjBSQYthKmahG3cEon": "Online Course Creation", "fldfexVJm8bZcOqzWl1G0w8H": ["Completed, not selectable."], "fldrTtsQOkRhoh4EEvXAn4bj": "Completed, not selectable. - Online Course Creation", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}, {"templateId": "recoSDCboExLWMh1vkXCqQYG", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Organize a grand opening event for a new local store", "fldI6fIhbU3GecieaJoF2CCx": ["optrlofR5DatDKKAuUudpzh3"], "fldeI2PjBSQYthKmahG3cEon": "Local Store Grand Opening", "fldfexVJm8bZcOqzWl1G0w8H": ["optizADkErBC4"], "fldrTtsQOkRhoh4EEvXAn4bj": "Completed, not selectable. - Local Store Grand Opening", "fldxAvu18b9w4yMllqXFunTO": ["recxolNmaO4GXOKuGGKFHPfe"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Organize a grand opening event for a new local store", "fldI6fIhbU3GecieaJoF2CCx": ["Local 🎈"], "fldeI2PjBSQYthKmahG3cEon": "Local Store Grand Opening", "fldfexVJm8bZcOqzWl1G0w8H": ["Completed, not selectable."], "fldrTtsQOkRhoh4EEvXAn4bj": "Completed, not selectable. - Local Store Grand Opening", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}, {"templateId": "rec5BrjYctz0DU9krYucBm1Y", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Implement a customer service chatbot for quicker response times", "fldI6fIhbU3GecieaJoF2CCx": ["opttb6hfW5B4RmERXxlrwYLg"], "fldeI2PjBSQYthKmahG3cEon": "Customer Service Chatbot Implementation", "fldfexVJm8bZcOqzWl1G0w8H": ["optizADkErBC4"], "fldrTtsQOkRhoh4EEvXAn4bj": "Completed, not selectable. - Customer Service Chatbot Implementation", "fldxAvu18b9w4yMllqXFunTO": ["recFcKInlccaaDF0rY9wpVWv"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Implement a customer service chatbot for quicker response times", "fldI6fIhbU3GecieaJoF2CCx": ["Tech 🖥️"], "fldeI2PjBSQYthKmahG3cEon": "Customer Service Chatbot Implementation", "fldfexVJm8bZcOqzWl1G0w8H": ["Completed, not selectable."], "fldrTtsQOkRhoh4EEvXAn4bj": "Completed, not selectable. - Customer Service Chatbot Implementation", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}, {"templateId": "recf7MjcNRCSgE48qCQcN4Be", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Initiate a series of webinars to engage with the audience", "fldI6fIhbU3GecieaJoF2CCx": ["optyPJ5z5xxcDHPwgtZG1jPt"], "fldeI2PjBSQYthKmahG3cEon": "Webinar Series Launch", "fldfexVJm8bZcOqzWl1G0w8H": ["optrcmtzUpaNn"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Webinar Series Launch", "fldxAvu18b9w4yMllqXFunTO": ["rechorkQwwRGEeFBZOAaVuTm"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Initiate a series of webinars to engage with the audience", "fldI6fIhbU3GecieaJoF2CCx": ["Launch 🎙️"], "fldeI2PjBSQYthKmahG3cEon": "Webinar Series Launch", "fldfexVJm8bZcOqzWl1G0w8H": ["In progress"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Webinar Series Launch", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}, {"templateId": "recLOV2H4uFyJqVa5xIYF6Ge", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Redesign the product packaging for better visual appeal", "fldI6fIhbU3GecieaJoF2CCx": ["optrHAy67JnH0KxueePTv8Qw"], "fldeI2PjBSQYthKmahG3cEon": "Product Packaging Redesign", "fldfexVJm8bZcOqzWl1G0w8H": ["optrcmtzUpaNn"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Product Packaging Redesign", "fldxAvu18b9w4yMllqXFunTO": ["rectrWuMg5lw3h4h3V3veZPR"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Redesign the product packaging for better visual appeal", "fldI6fIhbU3GecieaJoF2CCx": ["Design 🎨"], "fldeI2PjBSQYthKmahG3cEon": "Product Packaging Redesign", "fldfexVJm8bZcOqzWl1G0w8H": ["In progress"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Product Packaging Redesign", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}, {"templateId": "recQSVkdLdzJtLatUnoSoagF", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Revamp the email marketing campaign strategy and templates", "fldI6fIhbU3GecieaJoF2CCx": ["optbhut4P9Omwxdq0oQ7E5RS"], "fldeI2PjBSQYthKmahG3cEon": "Email Marketing Campaign Refresh", "fldfexVJm8bZcOqzWl1G0w8H": ["optrcmtzUpaNn"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Email Marketing Campaign Refresh", "fldxAvu18b9w4yMllqXFunTO": ["recURSMfCmIzNnmRka9VeYgR"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Revamp the email marketing campaign strategy and templates", "fldI6fIhbU3GecieaJoF2CCx": ["Marketing 📢"], "fldeI2PjBSQYthKmahG3cEon": "Email Marketing Campaign Refresh", "fldfexVJm8bZcOqzWl1G0w8H": ["In progress"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Email Marketing Campaign Refresh", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}, {"templateId": "reclm1vqrgAbNMXaaN2SM85z", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Improve the e - commerce checkout process to reduce cart abandonment", "fldI6fIhbU3GecieaJoF2CCx": ["optrHAy67JnH0KxueePTv8Qw"], "fldeI2PjBSQYthKmahG3cEon": "E - commerce Checkout Optimization", "fldfexVJm8bZcOqzWl1G0w8H": ["optrcmtzUpaNn"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - E - commerce Checkout Optimization", "fldxAvu18b9w4yMllqXFunTO": ["rec6BVBBi01L3lDc54zQhs7A"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Improve the e - commerce checkout process to reduce cart abandonment", "fldI6fIhbU3GecieaJoF2CCx": ["Design 🎨"], "fldeI2PjBSQYthKmahG3cEon": "E - commerce Checkout Optimization", "fldfexVJm8bZcOqzWl1G0w8H": ["In progress"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - E - commerce Checkout Optimization", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}, {"templateId": "rec4B30VogctGf8XVFekoQLs", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Establish partnerships with key influencers for brand promotion", "fldI6fIhbU3GecieaJoF2CCx": ["optbhut4P9Omwxdq0oQ7E5RS"], "fldeI2PjBSQYthKmahG3cEon": "Influencer Collaboration Program", "fldfexVJm8bZcOqzWl1G0w8H": ["optrcmtzUpaNn"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Influencer Collaboration Program", "fldxAvu18b9w4yMllqXFunTO": ["recZOiAABEEbrsv17QIXyh5l"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Establish partnerships with key influencers for brand promotion", "fldI6fIhbU3GecieaJoF2CCx": ["Marketing 📢"], "fldeI2PjBSQYthKmahG3cEon": "Influencer Collaboration Program", "fldfexVJm8bZcOqzWl1G0w8H": ["In progress"], "fldrTtsQOkRhoh4EEvXAn4bj": "In progress - Influencer Collaboration Program", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}, {"templateId": "recObKYwZtN63M8mfcBPe9KN", "data": {"fldAdEFTNsbsGl3yM7WrcCp7": "Rollout of new features for the mobile application", "fldI6fIhbU3GecieaJoF2CCx": ["opttb6hfW5B4RmERXxlrwYLg"], "fldeI2PjBSQYthKmahG3cEon": "Mobile App Feature Update", "fldfexVJm8bZcOqzWl1G0w8H": ["optizADkErBC4"], "fldrTtsQOkRhoh4EEvXAn4bj": "Completed, not selectable. - Mobile App Feature Update", "fldxAvu18b9w4yMllqXFunTO": ["recxiRT7gArtuF9RWi9kmScu"]}, "values": {"fldAdEFTNsbsGl3yM7WrcCp7": "Rollout of new features for the mobile application", "fldI6fIhbU3GecieaJoF2CCx": ["Tech 🖥️"], "fldeI2PjBSQYthKmahG3cEon": "Mobile App Feature Update", "fldfexVJm8bZcOqzWl1G0w8H": ["Completed, not selectable."], "fldrTtsQOkRhoh4EEvXAn4bj": "Completed, not selectable. - Mobile App Feature Update", "fldxAvu18b9w4yMllqXFunTO": ["<PERSON>"]}}]}, {"resourceType": "DATABASE", "templateId": "dat918n7U5Cg8GdGbdlnoWAd", "name": {"en": "Contractors", "zh-CN": "承包商"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwiggjJ6S3c4yj04JX4nsIu", "name": {"en": "All Contractors", "zh-CN": "所有承包商"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldH39qvtq28G9udmn0Qpt8n", "hidden": false}, {"templateId": "fld7QvmB8Hz9BNZreGVo6XOK", "hidden": false}, {"templateId": "fldWGXZJCo2Awl2qNc5nr6ww", "hidden": false}, {"templateId": "fldFsX3thCeIEQxQ3TSuMLna", "hidden": false}, {"templateId": "fldcxcizKkFa4lAD7NMMKlBR", "hidden": false}, {"templateId": "fldAswZ3QRHMAdjxjkgxwajl", "hidden": false}, {"templateId": "fld0DY8cwJbrlpm1Yw9dHnI9", "hidden": false}, {"templateId": "fldVI0bPJBxw42ISw8ynkErq", "hidden": false}, {"templateId": "fldzPA1hS1exH0UnPN7gWg14", "hidden": false}, {"templateId": "fldzU0HVSvLTXntEozRj24qv", "hidden": false}, {"templateId": "fldKXgSVA2suiupK7sAjfRiM", "hidden": false}, {"templateId": "fldmEJ46EyIsr6HAJzAjilRH", "hidden": false}]}, {"type": "TABLE", "templateId": "viwz4qQIOIrwKeBSSbIRlt42", "name": {"en": "Preferred Contractors", "zh-CN": "优选承包商"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldKXgSVA2suiupK7sAjfRiM", "fieldType": "CHECKBOX", "clause": {"operator": "Is", "value": true}}]}, "sorts": [], "fields": [{"templateId": "fldH39qvtq28G9udmn0Qpt8n", "hidden": false}, {"templateId": "fld7QvmB8Hz9BNZreGVo6XOK", "hidden": false}, {"templateId": "fldWGXZJCo2Awl2qNc5nr6ww", "hidden": false}, {"templateId": "fldFsX3thCeIEQxQ3TSuMLna", "hidden": false}, {"templateId": "fldcxcizKkFa4lAD7NMMKlBR", "hidden": false}, {"templateId": "fldAswZ3QRHMAdjxjkgxwajl", "hidden": false}, {"templateId": "fld0DY8cwJbrlpm1Yw9dHnI9", "hidden": false}, {"templateId": "fldVI0bPJBxw42ISw8ynkErq", "hidden": false}, {"templateId": "fldzPA1hS1exH0UnPN7gWg14", "hidden": false}, {"templateId": "fldzU0HVSvLTXntEozRj24qv", "hidden": false}, {"templateId": "fldKXgSVA2suiupK7sAjfRiM", "hidden": false}, {"templateId": "fldmEJ46EyIsr6HAJzAjilRH", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwgTuwrAckpyIfI299B8VHw", "name": {"en": "Active Contractors", "zh-CN": "活跃承包商"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldzU0HVSvLTXntEozRj24qv", "fieldType": "CHECKBOX", "clause": {"operator": "Is", "value": true}}]}, "sorts": [], "fields": [{"templateId": "fldH39qvtq28G9udmn0Qpt8n", "hidden": false}, {"templateId": "fld7QvmB8Hz9BNZreGVo6XOK", "hidden": false}, {"templateId": "fldWGXZJCo2Awl2qNc5nr6ww", "hidden": false}, {"templateId": "fldFsX3thCeIEQxQ3TSuMLna", "hidden": false}, {"templateId": "fldcxcizKkFa4lAD7NMMKlBR", "hidden": false}, {"templateId": "fldAswZ3QRHMAdjxjkgxwajl", "hidden": false}, {"templateId": "fld0DY8cwJbrlpm1Yw9dHnI9", "hidden": false}, {"templateId": "fldVI0bPJBxw42ISw8ynkErq", "hidden": false}, {"templateId": "fldzPA1hS1exH0UnPN7gWg14", "hidden": false}, {"templateId": "fldzU0HVSvLTXntEozRj24qv", "hidden": false}, {"templateId": "fldKXgSVA2suiupK7sAjfRiM", "hidden": false}, {"templateId": "fldmEJ46EyIsr6HAJzAjilRH", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwpd9xcMUEs8fDIaGxBddrf", "name": {"en": "By Area of Expertise", "zh-CN": "按专业领域"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldH39qvtq28G9udmn0Qpt8n", "hidden": false}, {"templateId": "fld7QvmB8Hz9BNZreGVo6XOK", "hidden": false}, {"templateId": "fldWGXZJCo2Awl2qNc5nr6ww", "hidden": false}, {"templateId": "fldFsX3thCeIEQxQ3TSuMLna", "hidden": false}, {"templateId": "fldcxcizKkFa4lAD7NMMKlBR", "hidden": false}, {"templateId": "fldAswZ3QRHMAdjxjkgxwajl", "hidden": false}, {"templateId": "fld0DY8cwJbrlpm1Yw9dHnI9", "hidden": false}, {"templateId": "fldVI0bPJBxw42ISw8ynkErq", "hidden": false}, {"templateId": "fldzPA1hS1exH0UnPN7gWg14", "hidden": false}, {"templateId": "fldzU0HVSvLTXntEozRj24qv", "hidden": false}, {"templateId": "fldKXgSVA2suiupK7sAjfRiM", "hidden": false}, {"templateId": "fldmEJ46EyIsr6HAJzAjilRH", "hidden": false}], "groups": [{"fieldTemplateId": "fldWGXZJCo2Awl2qNc5nr6ww", "asc": true}]}, {"type": "KANBAN", "templateId": "viw8d0zjrY0eF5Cq3cXJZDT0", "name": {"en": "Contractor Screening", "zh-CN": "承包商筛选"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldH39qvtq28G9udmn0Qpt8n", "hidden": false}, {"templateId": "fld7QvmB8Hz9BNZreGVo6XOK", "hidden": false}, {"templateId": "fldWGXZJCo2Awl2qNc5nr6ww", "hidden": false}, {"templateId": "fldFsX3thCeIEQxQ3TSuMLna", "hidden": false}, {"templateId": "fldcxcizKkFa4lAD7NMMKlBR", "hidden": false}, {"templateId": "fldAswZ3QRHMAdjxjkgxwajl", "hidden": false}, {"templateId": "fld0DY8cwJbrlpm1Yw9dHnI9", "hidden": false}, {"templateId": "fldVI0bPJBxw42ISw8ynkErq", "hidden": false}, {"templateId": "fldzPA1hS1exH0UnPN7gWg14", "hidden": false}, {"templateId": "fldzU0HVSvLTXntEozRj24qv", "hidden": false}, {"templateId": "fldKXgSVA2suiupK7sAjfRiM", "hidden": false}, {"templateId": "fldmEJ46EyIsr6HAJzAjilRH", "hidden": false}], "groups": [], "extra": {"kanbanGroupingFieldTemplateId": "fldzPA1hS1exH0UnPN7gWg14", "displayFieldName": true}}, {"type": "TABLE", "templateId": "viwQqMnajbzvc6SaRCOHJJVH", "name": {"en": "Form View", "zh-CN": "表单视图"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldH39qvtq28G9udmn0Qpt8n", "hidden": false}, {"templateId": "fld7QvmB8Hz9BNZreGVo6XOK", "hidden": false}, {"templateId": "fldWGXZJCo2Awl2qNc5nr6ww", "hidden": false}, {"templateId": "fldFsX3thCeIEQxQ3TSuMLna", "hidden": false}, {"templateId": "fldcxcizKkFa4lAD7NMMKlBR", "hidden": false}, {"templateId": "fldAswZ3QRHMAdjxjkgxwajl", "hidden": false}, {"templateId": "fld0DY8cwJbrlpm1Yw9dHnI9", "hidden": false}, {"templateId": "fldVI0bPJBxw42ISw8ynkErq", "hidden": false}, {"templateId": "fldzPA1hS1exH0UnPN7gWg14", "hidden": true}, {"templateId": "fldzU0HVSvLTXntEozRj24qv", "hidden": true}, {"templateId": "fldKXgSVA2suiupK7sAjfRiM", "hidden": true}, {"templateId": "fldmEJ46EyIsr6HAJzAjilRH", "hidden": false}], "groups": []}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldH39qvtq28G9udmn0Qpt8n", "privilege": "TYPE_EDIT", "name": {"en": "Name", "zh-CN": "姓名"}, "primary": true}, {"type": "EMAIL", "templateId": "fld7QvmB8Hz9BNZreGVo6XOK", "description": {"zh-CN": "请务必填写准确邮箱，申请结果将发送到该邮箱", "en": "Please ensure the email is accurate. The application result will be sent to this email."}, "privilege": "NAME_EDIT", "name": {"en": "Email", "zh-CN": "电子邮件"}, "primary": false, "required": true}, {"type": "SINGLE_SELECT", "templateId": "fldWGXZJCo2Awl2qNc5nr6ww", "privilege": "NAME_EDIT", "name": {"en": "Primary Area of Expertise", "zh-CN": "主要专业领域"}, "property": {"options": [{"id": "opthGAeIBTjnC7xgKAnc1R6W", "name": "IT and Programming", "color": "deepPurple"}, {"id": "optHXenobkaqF4pxFFompvLK", "name": "Engineering", "color": "indigo"}, {"id": "optBYGSzx7cYMozfnsySdPz3", "name": "Product Management", "color": "blue"}, {"id": "optiaOLp779u3puRqn8mIcjN", "name": "Training and Development", "color": "teal"}, {"id": "opt1OKq8FcZ5sXI3KHTxVH5P", "name": "E - commerce", "color": "green"}, {"id": "optu12TCzqIwlutC0O9wO1QV", "name": "Digital Marketing", "color": "yellow"}, {"id": "opt21aZ1cYKrY2hp5QrNIhkU", "name": "Customer Service", "color": "orange"}, {"id": "optQwicREbXdPyAylgVSJ0He", "name": "Project Management", "color": "tangerine"}, {"id": "opt0E52dwDNgWkxUgK47GGdm", "name": "Human Resources", "color": "pink"}, {"id": "opt8tSdYbYc1L3ABgYHvj1fr", "name": "Event Planning", "color": "red"}, {"id": "optB69olWBEr9oF4u66yxosS", "name": "Data Analysis", "color": "brown"}, {"id": "optNB9KmEQkNFJlniDbxHQYB", "name": "Content Creation", "color": "gray"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldFsX3thCeIEQxQ3TSuMLna", "privilege": "NAME_EDIT", "name": {"en": "Other Areas of Expertise", "zh-CN": "其他专业领域"}, "property": {"options": [{"id": "opt0hVIyj78qingfQuxLnPCc", "name": "Design and Media", "color": "deepPurple"}, {"id": "opt0BDb92E9HDTdBGOSf2f2t", "name": "Finances and Management", "color": "indigo"}, {"id": "optoV6CMazgkaYoLDmP46eJy", "name": "Market Research", "color": "blue"}, {"id": "optYD6XJdsjpSNHLmUUUeHKM", "name": "Leadership", "color": "teal"}, {"id": "optfQGusl2vUHMbFuWSA4RQY", "name": "Web Development", "color": "green"}, {"id": "opt1xRN32ocsFXqKSQWSK6qB", "name": "Social Media Management", "color": "yellow"}, {"id": "optdZH8jvsnCXMmskcSkHKNQ", "name": "Problem Solving", "color": "orange"}, {"id": "opt0iphXcefYjVV1vDhmPLxZ", "name": "Budgeting", "color": "tangerine"}, {"id": "opt69kTI8fY9VmlrMqES6Z43", "name": "Recruitment", "color": "pink"}, {"id": "optxTQNJpmhWZHuPafAkrdI0", "name": "Graphic Design", "color": "red"}, {"id": "optSTC5H9ghFYrqQMO28Gfx8", "name": "Marketing Strategy", "color": "brown"}, {"id": "opt0ayL5yiysiZy71hIzNTc5", "name": "SEO Optimization", "color": "gray"}], "defaultValue": ""}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldcxcizKkFa4lAD7NMMKlBR", "privilege": "NAME_EDIT", "name": {"en": "Occupation", "zh-CN": "职业"}, "primary": false}, {"type": "URL", "templateId": "fldAswZ3QRHMAdjxjkgxwajl", "privilege": "NAME_EDIT", "name": {"en": "Portfolio Link", "zh-CN": "作品集链接"}, "primary": false}, {"type": "URL", "templateId": "fld0DY8cwJbrlpm1Yw9dHnI9", "privilege": "NAME_EDIT", "name": {"en": "Work Samples", "zh-CN": "工作样本"}, "primary": false}, {"type": "ATTACHMENT", "templateId": "fldVI0bPJBxw42ISw8ynkErq", "privilege": "NAME_EDIT", "name": {"en": "Work Sample Uploads", "zh-CN": "工作样本上传"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldzPA1hS1exH0UnPN7gWg14", "privilege": "NAME_EDIT", "name": {"en": "Approval Status", "zh-CN": "批准状态"}, "property": {"options": [{"id": "optTh2j7g0LsWFv7mKalyK8G", "name": "Rejected", "color": "deepPurple"}, {"id": "optod7PtnmRrViP2SzmgTs5t", "name": "Accepted", "color": "indigo5"}, {"id": "opt4rX2GlX6cpvHgFVloZcF0", "name": "Final Screen", "color": "blue5"}, {"id": "optrjTRELowWpW7EdYFwGFaU", "name": "Take - Home Assignment", "color": "teal"}, {"id": "optDIW8OPhCmt7mSj6pDFosD", "name": "Initial Phone Screen", "color": "green"}], "defaultValue": ""}, "primary": false}, {"type": "CHECKBOX", "templateId": "fldzU0HVSvLTXntEozRj24qv", "privilege": "NAME_EDIT", "name": {"en": "Active?", "zh-CN": "活跃？"}, "primary": false}, {"type": "CHECKBOX", "templateId": "fldKXgSVA2suiupK7sAjfRiM", "privilege": "NAME_EDIT", "name": {"en": "Preferred?", "zh-CN": "优选？"}, "primary": false}, {"type": "LINK", "templateId": "fldmEJ46EyIsr6HAJzAjilRH", "privilege": "NAME_EDIT", "name": {"en": "Applicable Projects", "zh-CN": "可申请项目"}, "property": {"foreignDatabaseTemplateId": "dati99SZ06GB3ojBqZu4kkDI", "brotherFieldTemplateId": "fldxAvu18b9w4yMllqXFunTO"}, "primary": false}], "records": [{"templateId": "recMNdpC9yrECYj9sYi2Wo2a", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://jessicoproduct.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://jessicoproduct.com", "fldFsX3thCeIEQxQ3TSuMLna": ["optoV6CMazgkaYoLDmP46eJy"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": null, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["optBYGSzx7cYMozfnsySdPz3"], "fldcxcizKkFa4lAD7NMMKlBR": "Product Manager", "fldmEJ46EyIsr6HAJzAjilRH": ["recTT42zOIDqOAY49AYIGK83"], "fldzPA1hS1exH0UnPN7gWg14": ["opt4rX2GlX6cpvHgFVloZcF0"], "fldzU0HVSvLTXntEozRj24qv": null}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://jessicoproduct.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://jessicoproduct.com", "fldFsX3thCeIEQxQ3TSuMLna": ["Market Research"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "0", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["Product Management"], "fldcxcizKkFa4lAD7NMMKlBR": "Product Manager", "fldmEJ46EyIsr6HAJzAjilRH": ["In progress - Brand Ambassador Program Expansion"], "fldzPA1hS1exH0UnPN7gWg14": ["Final Screen"], "fldzU0HVSvLTXntEozRj24qv": "0"}}, {"templateId": "recGlqYtBmR01f7lNxeafmco", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://alextraining.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://alextraining.com", "fldFsX3thCeIEQxQ3TSuMLna": ["optYD6XJdsjpSNHLmUUUeHKM"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": null, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["optiaOLp779u3puRqn8mIcjN"], "fldcxcizKkFa4lAD7NMMKlBR": "Training Coordinator", "fldmEJ46EyIsr6HAJzAjilRH": ["rec3xCfVsA7wOTEGsf1sLeZS"], "fldzPA1hS1exH0UnPN7gWg14": ["optTh2j7g0LsWFv7mKalyK8G"], "fldzU0HVSvLTXntEozRj24qv": null}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://alextraining.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://alextraining.com", "fldFsX3thCeIEQxQ3TSuMLna": ["Leadership"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "0", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["Training and Development"], "fldcxcizKkFa4lAD7NMMKlBR": "Training Coordinator", "fldmEJ46EyIsr6HAJzAjilRH": ["Completed, not selectable. - Online Course Creation"], "fldzPA1hS1exH0UnPN7gWg14": ["Rejected"], "fldzU0HVSvLTXntEozRj24qv": "0"}}, {"templateId": "recxolNmaO4GXOKuGGKFHPfe", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://jenniferecom.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://jenniferecom.com", "fldFsX3thCeIEQxQ3TSuMLna": ["optfQGusl2vUHMbFuWSA4RQY"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": null, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["opt1OKq8FcZ5sXI3KHTxVH5P"], "fldcxcizKkFa4lAD7NMMKlBR": "E - commerce Manager", "fldmEJ46EyIsr6HAJzAjilRH": ["recoSDCboExLWMh1vkXCqQYG"], "fldzPA1hS1exH0UnPN7gWg14": ["optod7PtnmRrViP2SzmgTs5t"], "fldzU0HVSvLTXntEozRj24qv": true}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://jenniferecom.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://jenniferecom.com", "fldFsX3thCeIEQxQ3TSuMLna": ["Web Development"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "0", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["E - commerce"], "fldcxcizKkFa4lAD7NMMKlBR": "E - commerce Manager", "fldmEJ46EyIsr6HAJzAjilRH": ["Completed, not selectable. - Local Store Grand Opening"], "fldzPA1hS1exH0UnPN7gWg14": ["Accepted"], "fldzU0HVSvLTXntEozRj24qv": "1"}}, {"templateId": "recFcKInlccaaDF0rY9wpVWv", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://robertdigital.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://robertdigital.com", "fldFsX3thCeIEQxQ3TSuMLna": ["opt1xRN32ocsFXqKSQWSK6qB"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": true, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["optu12TCzqIwlutC0O9wO1QV"], "fldcxcizKkFa4lAD7NMMKlBR": "Digital Marketing Specialist", "fldmEJ46EyIsr6HAJzAjilRH": ["rec5BrjYctz0DU9krYucBm1Y"], "fldzPA1hS1exH0UnPN7gWg14": ["optrjTRELowWpW7EdYFwGFaU"], "fldzU0HVSvLTXntEozRj24qv": true}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://robertdigital.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://robertdigital.com", "fldFsX3thCeIEQxQ3TSuMLna": ["Social Media Management"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "1", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["Digital Marketing"], "fldcxcizKkFa4lAD7NMMKlBR": "Digital Marketing Specialist", "fldmEJ46EyIsr6HAJzAjilRH": ["Completed, not selectable. - Customer Service Chatbot Implementation"], "fldzPA1hS1exH0UnPN7gWg14": ["Take - Home Assignment"], "fldzU0HVSvLTXntEozRj24qv": "1"}}, {"templateId": "rechorkQwwRGEeFBZOAaVuTm", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://lisacustserv.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://lisacustserv.com", "fldFsX3thCeIEQxQ3TSuMLna": ["optdZH8jvsnCXMmskcSkHKNQ"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": null, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["opt21aZ1cYKrY2hp5QrNIhkU"], "fldcxcizKkFa4lAD7NMMKlBR": "Customer Service Representative", "fldmEJ46EyIsr6HAJzAjilRH": ["recf7MjcNRCSgE48qCQcN4Be"], "fldzPA1hS1exH0UnPN7gWg14": ["optDIW8OPhCmt7mSj6pDFosD"], "fldzU0HVSvLTXntEozRj24qv": null}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://lisacustserv.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://lisacustserv.com", "fldFsX3thCeIEQxQ3TSuMLna": ["Problem Solving"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "0", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["Customer Service"], "fldcxcizKkFa4lAD7NMMKlBR": "Customer Service Representative", "fldmEJ46EyIsr6HAJzAjilRH": ["In progress - Webinar Series Launch"], "fldzPA1hS1exH0UnPN7gWg14": ["Initial Phone Screen"], "fldzU0HVSvLTXntEozRj24qv": "0"}}, {"templateId": "rectrWuMg5lw3h4h3V3veZPR", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://davidprojects.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://davidprojects.com", "fldFsX3thCeIEQxQ3TSuMLna": ["opt0iphXcefYjVV1vDhmPLxZ"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": null, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["optQwicREbXdPyAylgVSJ0He"], "fldcxcizKkFa4lAD7NMMKlBR": "Project Manager", "fldmEJ46EyIsr6HAJzAjilRH": ["recLOV2H4uFyJqVa5xIYF6Ge"], "fldzPA1hS1exH0UnPN7gWg14": ["opt4rX2GlX6cpvHgFVloZcF0"], "fldzU0HVSvLTXntEozRj24qv": null}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://davidprojects.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://davidprojects.com", "fldFsX3thCeIEQxQ3TSuMLna": ["Budgeting"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "0", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["Project Management"], "fldcxcizKkFa4lAD7NMMKlBR": "Project Manager", "fldmEJ46EyIsr6HAJzAjilRH": ["In progress - Product Packaging Redesign"], "fldzPA1hS1exH0UnPN7gWg14": ["Final Screen"], "fldzU0HVSvLTXntEozRj24qv": "0"}}, {"templateId": "recURSMfCmIzNnmRka9VeYgR", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://sarahhr.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://sarahhr.com", "fldFsX3thCeIEQxQ3TSuMLna": ["opt69kTI8fY9VmlrMqES6Z43"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": null, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["opt0E52dwDNgWkxUgK47GGdm"], "fldcxcizKkFa4lAD7NMMKlBR": "HR Specialist", "fldmEJ46EyIsr6HAJzAjilRH": ["recQSVkdLdzJtLatUnoSoagF"], "fldzPA1hS1exH0UnPN7gWg14": ["optTh2j7g0LsWFv7mKalyK8G"], "fldzU0HVSvLTXntEozRj24qv": null}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://sarahhr.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://sarahhr.com", "fldFsX3thCeIEQxQ3TSuMLna": ["Recruitment"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "0", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["Human Resources"], "fldcxcizKkFa4lAD7NMMKlBR": "HR Specialist", "fldmEJ46EyIsr6HAJzAjilRH": ["In progress - Email Marketing Campaign Refresh"], "fldzPA1hS1exH0UnPN7gWg14": ["Rejected"], "fldzU0HVSvLTXntEozRj24qv": "0"}}, {"templateId": "rec6BVBBi01L3lDc54zQhs7A", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://michaelevents.com", "fld7QvmB8Hz9BNZreGVo6XOK": "mi<PERSON><PERSON>@example.com", "fldAswZ3QRHMAdjxjkgxwajl": "http://michaelevents.com", "fldFsX3thCeIEQxQ3TSuMLna": ["optxTQNJpmhWZHuPafAkrdI0"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": true, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["opt8tSdYbYc1L3ABgYHvj1fr"], "fldcxcizKkFa4lAD7NMMKlBR": "Event Planner", "fldmEJ46EyIsr6HAJzAjilRH": ["reclm1vqrgAbNMXaaN2SM85z"], "fldzPA1hS1exH0UnPN7gWg14": ["optod7PtnmRrViP2SzmgTs5t"], "fldzU0HVSvLTXntEozRj24qv": true}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://michaelevents.com", "fld7QvmB8Hz9BNZreGVo6XOK": "mi<PERSON><PERSON>@example.com", "fldAswZ3QRHMAdjxjkgxwajl": "http://michaelevents.com", "fldFsX3thCeIEQxQ3TSuMLna": ["Graphic Design"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "1", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["Event Planning"], "fldcxcizKkFa4lAD7NMMKlBR": "Event Planner", "fldmEJ46EyIsr6HAJzAjilRH": ["In progress - E - commerce Checkout Optimization"], "fldzPA1hS1exH0UnPN7gWg14": ["Accepted"], "fldzU0HVSvLTXntEozRj24qv": "1"}}, {"templateId": "recZOiAABEEbrsv17QIXyh5l", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://elizabethdata.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://elizabethdata.com", "fldFsX3thCeIEQxQ3TSuMLna": ["optSTC5H9ghFYrqQMO28Gfx8"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": null, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["optB69olWBEr9oF4u66yxosS"], "fldcxcizKkFa4lAD7NMMKlBR": "Data Analyst", "fldmEJ46EyIsr6HAJzAjilRH": ["rec4B30VogctGf8XVFekoQLs"], "fldzPA1hS1exH0UnPN7gWg14": ["optrjTRELowWpW7EdYFwGFaU"], "fldzU0HVSvLTXntEozRj24qv": null}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://elizabethdata.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://elizabethdata.com", "fldFsX3thCeIEQxQ3TSuMLna": ["Marketing Strategy"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "0", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["Data Analysis"], "fldcxcizKkFa4lAD7NMMKlBR": "Data Analyst", "fldmEJ46EyIsr6HAJzAjilRH": ["In progress - Influencer Collaboration Program"], "fldzPA1hS1exH0UnPN7gWg14": ["Take - Home Assignment"], "fldzU0HVSvLTXntEozRj24qv": "0"}}, {"templateId": "recxiRT7gArtuF9RWi9kmScu", "data": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://jamesportfolio.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://jamesportfolio.com", "fldFsX3thCeIEQxQ3TSuMLna": ["opt0ayL5yiysiZy71hIzNTc5"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": null, "fldVI0bPJBxw42ISw8ynkErq": [{"id": "tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM", "name": "Sample.pdf", "path": "template/tpltpltpltplattQaXJG0mE4jRJUQMsYA3DM.pdf", "bucket": "bika-dev", "mimeType": "application/pdf", "size": 16737}], "fldWGXZJCo2Awl2qNc5nr6ww": ["optNB9KmEQkNFJlniDbxHQYB"], "fldcxcizKkFa4lAD7NMMKlBR": "Content Writer", "fldmEJ46EyIsr6HAJzAjilRH": ["recObKYwZtN63M8mfcBPe9KN"], "fldzPA1hS1exH0UnPN7gWg14": ["optod7PtnmRrViP2SzmgTs5t"], "fldzU0HVSvLTXntEozRj24qv": true}, "values": {"fld0DY8cwJbrlpm1Yw9dHnI9": "http://jamesportfolio.com", "fld7QvmB8Hz9BNZreGVo6XOK": "<EMAIL>", "fldAswZ3QRHMAdjxjkgxwajl": "http://jamesportfolio.com", "fldFsX3thCeIEQxQ3TSuMLna": ["SEO Optimization"], "fldH39qvtq28G9udmn0Qpt8n": "<PERSON>", "fldKXgSVA2suiupK7sAjfRiM": "0", "fldVI0bPJBxw42ISw8ynkErq": ["Sample.pdf"], "fldWGXZJCo2Awl2qNc5nr6ww": ["Content Creation"], "fldcxcizKkFa4lAD7NMMKlBR": "Content Writer", "fldmEJ46EyIsr6HAJzAjilRH": ["Completed, not selectable. - Mobile App Feature Update"], "fldzPA1hS1exH0UnPN7gWg14": ["Accepted"], "fldzU0HVSvLTXntEozRj24qv": "1"}}]}, {"resourceType": "FORM", "templateId": "fomCZp39IHgE9PZTH6xAabgx", "name": {"en": "Submit Application Immediately", "zh-CN": "立即提交申请"}, "description": {"en": "Hi, welcome to apply to become our partner! Please fill out the following form and submit your detailed information, including personal profile, professional skills, past work experience, etc. We will review your application as soon as possible after receiving it and get in touch with you.", "zh-CN": "嗨，欢迎申请成为我们的合作伙伴！请填写以下表格并提交您的详细信息，包括个人简介、专业技能、过往工作经验等。我们在收到您的申请后会尽快审核，并与您取得联系。"}, "formType": "DATABASE", "databaseTemplateId": "dat918n7U5Cg8GdGbdlnoWAd", "metadata": {"type": "VIEW", "viewTemplateId": "viwQqMnajbzvc6SaRCOHJJVH"}}, {"resourceType": "AUTOMATION", "templateId": "ato45HHpbLG7A7J3cvlvvsMH", "name": {"en": "Contractor Submits Application Reminder", "zh-CN": "承包商提交申请提醒"}, "triggers": [{"triggerType": "FORM_SUBMITTED", "templateId": "trgB9UFITjb5jwEZ6fm1fqRX", "description": {"en": "A new contractor has submitted an application", "zh-CN": "有新的承包商提交申请"}, "input": {"type": "FORM", "formTemplateId": "fomCZp39IHgE9PZTH6xAabgx"}}], "actions": [{"templateId": "acthaJ31UYisHpFrG9ELsWli", "description": {"en": "Send an email to the team", "zh-CN": "给团队发送邮件"}, "actionType": "SEND_EMAIL", "input": {"subject": "A new contractor has submitted an application", "body": {"markdown": "Hi Team,      \nA new contractor has submitted an application. Please review the details:    \n      \nName: <%= _triggers.trgB9UFITjb5jwEZ6fm1fqRX.record.cells.fldH39qvtq28G9udmn0Qpt8n.value %>    \nPrimary Area of Expertise: <%= _joinArrayAsText(_triggers.trgB9UFITjb5jwEZ6fm1fqRX.record.cells.fldWGXZJCo2Awl2qNc5nr6ww.value) %>    \nOther Areas of Expertise: <%= _joinArrayAsText(_triggers.trgB9UFITjb5jwEZ6fm1fqRX.record.cells.fldFsX3thCeIEQxQ3TSuMLna.value) %>    \nApplied project: <%= _joinArrayAsText(_triggers.trgB9UFITjb5jwEZ6fm1fqRX.record.cells.fldmEJ46EyIsr6HAJzAjilRH.value) %>    \n      \n[Click the link](<%= _triggers.trgB9UFITjb5jwEZ6fm1fqRX.record.url %>) above to review and complete the approval process.", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "Hi Team,    ", "type": "text"}, {"type": "hardBreak"}, {"text": "A new contractor has submitted an application. Please review the details:  ", "type": "text"}, {"type": "hardBreak"}, {"text": "    ", "type": "text"}, {"type": "hardBreak"}, {"text": "name: ", "type": "text"}, {"type": "variable", "attrs": {"ids": "_triggers,trgjRjdlexnkrV2Dl2Qgke66,record,cells,fld76zaHKT84mWD67qKCY483,value", "tips": "", "names": "Triggers,Form Submitted,Record,Fields,Name,Field value"}}, {"text": "  ", "type": "text"}, {"type": "hardBreak"}, {"text": "Primary Area of Expertise: ", "type": "text"}, {"type": "variable", "attrs": {"ids": "_joinArrayAsText(_triggers.trgB9UFITjb5jwEZ6fm1fqRX.record.cells.fldWGXZJCo2Awl2qNc5nr6ww.value)", "tips": ": ", "names": "Triggers,Form Submitted,Record,Fields,Primary Area of Expertise,Join value"}}, {"text": "  ", "type": "text"}, {"type": "hardBreak"}, {"text": "Other Areas of Expertise: ", "type": "text"}, {"type": "variable", "attrs": {"ids": "_joinArrayAsText(_triggers.trgB9UFITjb5jwEZ6fm1fqRX.record.cells.fldFsX3thCeIEQxQ3TSuMLna.value)", "tips": ": ", "names": "Triggers,Form Submitted,Record,Fields,Other Areas of Expertise,Join value"}}, {"text": "  ", "type": "text"}, {"type": "hardBreak"}, {"text": "Applied project: ", "type": "text"}, {"type": "variable", "attrs": {"ids": "_joinArrayAsText(_triggers.trgB9UFITjb5jwEZ6fm1fqRX.record.cells.fldmEJ46EyIsr6HAJzAjilRH.value)", "tips": ": ", "names": "Triggers,Form Submitted,Record,Fields,Project,Join value"}}, {"text": "  ", "type": "text"}, {"type": "hardBreak"}, {"text": "    ", "type": "text"}, {"type": "hardBreak"}, {"text": "[Click the link](", "type": "text"}, {"type": "variable", "attrs": {"ids": "_triggers,trgjRjdlexnkrV2Dl2Qgke66,record,url", "tips": "", "names": "Triggers,Form Submitted,Record,URL"}}, {"text": ") above to review and complete the approval process.", "type": "text"}]}]}}, "to": [{"type": "EMAIL_STRING", "email": "<EMAIL>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}, {"resourceType": "AUTOMATION", "templateId": "atosEdr5EA9xdqHGeucxptGq", "name": {"en": "Application Approval Notice", "zh-CN": "申请通过通知"}, "triggers": [{"triggerType": "RECORD_MATCH", "templateId": "trg3UrObbj1NHny31SfSbnpi", "description": {"en": "When the Approval Status is Accepted", "zh-CN": "当审批状态为接受时"}, "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldzPA1hS1exH0UnPN7gWg14", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optod7PtnmRrViP2SzmgTs5t"}}]}, "databaseTemplateId": "dat918n7U5Cg8GdGbdlnoWAd"}}], "actions": [{"templateId": "actCAR8RaHlXZYHLMoWcXwuA", "description": {"en": "Find project status", "zh-CN": "查找项目情况"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldrTtsQOkRhoh4EEvXAn4bj", "fieldType": "FORMULA", "clause": {"operator": "Contains", "value": "<%= _joinArrayAsText(_triggers.trg3UrObbj1NHny31SfSbnpi.record.cells.fldmEJ46EyIsr6HAJzAjilRH.value) %>"}}]}, "databaseTemplateId": "dati99SZ06GB3ojBqZu4kkDI"}}, {"templateId": "actuSIDEHW2heB4QoHwNEgRc", "description": {"en": "Send an email to the Contractor", "zh-CN": "发送邮件给承包商"}, "actionType": "SEND_EMAIL", "input": {"subject": "Congratulations! Your Application Result", "body": {"markdown": "Dear <%= _triggers.trg3UrObbj1NHny31SfSbnpi.record.cells.fldH39qvtq28G9udmn0Qpt8n.value %>,          \n          \nWe are pleased to inform you that your application has been approved. Congratulations!          \nNext Steps:            \nAttached to this email, you will find the detailed work tasks and responsibilities for your role. Please review the document and let us know if you have any questions.      \n      \n<%= _renderRecordsAsList(_actions.actCAR8RaHlXZYHLMoWcXwuA.records, ['fldeI2PjBSQYthKmahG3cEon','fldfexVJm8bZcOqzWl1G0w8H', 'fldAdEFTNsbsGl3yM7WrcCp7']) %>\n        \nWe are excited to collaborate with you and look forward to working together.          \n          \nBest regards,            \n[Your Name]            \n[Your Position]            \n[Your Contact Information]"}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trg3UrObbj1NHny31SfSbnpi.record.cells.fld7QvmB8Hz9BNZreGVo6XOK.value %>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}, {"resourceType": "AUTOMATION", "templateId": "atorIezyQCmUGUa59U6z6Cnz", "name": {"en": "Application Rejection Notice", "zh-CN": "申请拒绝通知"}, "triggers": [{"triggerType": "RECORD_MATCH", "templateId": "trgDW5iWNS6tY6Al9zJkqT1E", "description": {"en": "When the Approval Status is Rejected", "zh-CN": "当审批状态为拒绝时"}, "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldzPA1hS1exH0UnPN7gWg14", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optTh2j7g0LsWFv7mKalyK8G"}}]}, "databaseTemplateId": "dat918n7U5Cg8GdGbdlnoWAd"}}], "actions": [{"templateId": "actsiGpymubNehvw7XQYyW7P", "description": {"en": "Send an email to the Contractor", "zh-CN": "发送邮件给承包商"}, "actionType": "SEND_EMAIL", "input": {"subject": "Application Result", "body": {"markdown": "Dear <%= _triggers.trgDW5iWNS6tY6Al9zJkqT1E.record.cells.fldH39qvtq28G9udmn0Qpt8n.value %>,  \n    \nThank you for submitting your application. After careful consideration, we regret to inform you that your application was not approved at this time.    \nWe appreciate the time and effort you invested in applying and encourage you to apply for other opportunities with us in the future.    \nWe wish you all the best in your career endeavors.    \n    \nBest regards,      \n[Your Name]      \n[Your Position]      \n[Your Contact Information]", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "Dear ", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trgSyWOxbTbl4XoD8j9kaMQC", "record", "cells", "fldko6bLXL8y8q2Z2Z2lwxLQ", "value"], "tips": "", "names": ["触发器", "有记录满足条件时", "记录", "单元格", "姓名", "字段值"]}}, {"text": ",", "type": "text"}, {"type": "hardBreak"}, {"text": "  ", "type": "text"}, {"type": "hardBreak"}, {"text": "Thank you for submitting your application. After careful consideration, we regret to inform you that your application was not approved at this time.  ", "type": "text"}, {"type": "hardBreak"}, {"text": "We appreciate the time and effort you invested in applying and encourage you to apply for other opportunities with us in the future.  ", "type": "text"}, {"type": "hardBreak"}, {"text": "We wish you all the best in your career endeavors.  ", "type": "text"}, {"type": "hardBreak"}, {"text": "  ", "type": "text"}, {"type": "hardBreak"}, {"text": "Best regards,    ", "type": "text"}, {"type": "hardBreak"}, {"text": "[Your Name]    ", "type": "text"}, {"type": "hardBreak"}, {"text": "[Your Position]    ", "type": "text"}, {"type": "hardBreak"}, {"text": "[Your Contact Information]", "type": "text"}]}]}}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trgDW5iWNS6tY6Al9zJkqT1E.record.cells.fld7QvmB8Hz9BNZreGVo6XOK.value %>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}