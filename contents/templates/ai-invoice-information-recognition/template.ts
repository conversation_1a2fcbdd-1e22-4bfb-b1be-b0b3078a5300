import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'ai-invoice-information-recognition',
  name: {
    en: 'AI Invoice Information Recognition',
    'zh-CN': 'AI发票信息识别',
  },
  description: {
    en: "This template uses OpenAI's GPT-4o model to automatically extract key information from invoices, helping businesses or individuals reduce manual entry and improve financial data management efficiency. ",
    'zh-CN':
      '本模板利用 OpenAI 的 gpt-4o模型自动提取发票中的关键信息，帮助企业或个人减少手动录入，提高财务数据管理效率。',
  },
  cover: '/assets/template/template-cover-ai-invoice-information-recognition.png',
  author: '<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>',
  category: ['ai', 'script'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.5',
  resources: [
    {
      resourceType: 'FORM',
      templateId: 'fombayokrhbIbXBAVBQZDXe1',
      name: {
        en: 'Invoice Upload Form',
        'zh-CN': '发票上传识别',
      },
      description: {
        en: 'Please fill in the invoice name and upload the invoice.',
        'zh-CN': '请填写发票名称并上传发票',
      },
      formType: 'DATABASE',
      databaseTemplateId: 'dat6RL1D9fxQveURYSLUd7Ju',
      metadata: {
        type: 'VIEW',
        viewTemplateId: 'viwr8XqxjHccFPZLePttUpF1',
      },
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoxQcC7nz9MGyRoSmX89QHj',
      name: {
        en: 'Invoice Automatic Recognition And Processing',
        'zh-CN': '发票自动识别与处理',
      },
      triggers: [
        {
          triggerType: 'RECORD_MATCH',
          templateId: 'trg6c79JVwZDz7kIHoy8H9Ee',
          description: {
            en: 'When uploading an invoice image',
            'zh-CN': '上传发票图片时',
          },
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldDfZeZCcLBmYjWGNR9u5Kz',
                  fieldType: 'ATTACHMENT',
                  clause: {
                    operator: 'IsNotEmpty',
                  },
                },
              ],
            },
            databaseTemplateId: 'dat6RL1D9fxQveURYSLUd7Ju',
          },
        },
      ],
      actions: [
        {
          templateId: 'actHipJikl8i086ctyx4yeGi',
          description: {
            en: 'Extract invoice information through OpenAI',
            'zh-CN': '通过 OpenAI 提取发票信息',
          },
          actionType: 'RUN_SCRIPT',
          input: {
            type: 'SCRIPT',
            language: 'javascript',
            script:
              "const API_URL = 'https://api.openai.com/v1/chat/completions';\n" +
              "const API_KEY = '_YOUR_OPEN_API_KEY_'; // Please fill in your Open API key\n" +
              'const invoiceImageData = <%= JSON.stringify(_triggers.trg6c79JVwZDz7kIHoy8H9Ee.record.cells.fldDfZeZCcLBmYjWGNR9u5Kz.data) %>;\n' +
              '\n' +
              'const invoiceImageUrl = "https://s1.bika.ai/" + invoiceImageData[0].path;\n' +
              '\n' +
              'console.log(invoiceImageUrl);\n' +
              '\n' +
              'async function extractInvoiceData(imageUrl) {\n' +
              '    try {\n' +
              '        const response = await axios.post(\n' +
              '            API_URL,\n' +
              '            {\n' +
              "                model: 'gpt-4o',\n" +
              '                messages: [\n' +
              '                    {\n' +
              "                        role: 'user',\n" +
              '                        content: [\n' +
              '                            {\n' +
              "                                type: 'text',\n" +
              "                                text: 'This is an Invoice. Please extract the Invoice number, Amount, Date, and Due date from the picture, and tell me who the invoice is billed to and what product or service it is from, Please return it in the following format. Both the Date and Due date need to be converted to the format of YYYY-MM-DD: (Only return the following content, do not add any additional format, and do not return any other content). format: Invoice_number: 123456, Amount: $100.00, Date: 2025-02-27, Due_date: 2025-06-27, Product_service: Bika.ai, Billed_to: Nagisa'\n" +
              '                            },\n' +
              '                            {\n' +
              "                                type: 'image_url',\n" +
              '                                image_url: {\n' +
              '                                    url: imageUrl\n' +
              '                                }\n' +
              '                            }\n' +
              '                        ]\n' +
              '                    }\n' +
              '                ]\n' +
              '            },\n' +
              '            {\n' +
              '                headers: {\n' +
              "                    'Content-Type': 'application/json',\n" +
              "                    'Authorization': 'Bearer ' + API_KEY\n" +
              '                }\n' +
              '            }\n' +
              '        );\n' +
              '\n' +
              '        return response.data;\n' +
              '    } catch (error) {\n' +
              '        return { error: error.response ? error.response.data : error.message };\n' +
              '    }\n' +
              '}\n' +
              '\n' +
              'extractInvoiceData(invoiceImageUrl)\n' +
              '    .then((data) => {\n' +
              '        console.log(data);\n' +
              '        return data;\n' +
              '    })\n' +
              '    .catch((error) => {\n' +
              '        console.error(error);\n' +
              '        return error;\n' +
              '    });\n',
          },
        },
        {
          templateId: 'actBg6MKhXUF5TIWC0SfM1Ko',
          description: {
            en: 'Split invoice information',
            'zh-CN': '拆分发票信息',
          },
          actionType: 'RUN_SCRIPT',
          input: {
            type: 'SCRIPT',
            language: 'javascript',
            script:
              'function parseInvoice() {\n' +
              '    const invoiceText = <%= JSON.stringify(_actions.actHipJikl8i086ctyx4yeGi.choices[0].message.content) %>\n' +
              '    const result = invoiceText.trim().split(", ").reduce((acc, line) => {\n' +
              '        const [key, value] = line.split(": ").map(part => part.trim());\n' +
              '        acc[key] = value;\n' +
              '        return acc;\n' +
              '    }, {});\n' +
              '    console.log(result);\n' +
              '    return result;\n' +
              '}\n' +
              '\n' +
              'parseInvoice();',
          },
        },
        {
          templateId: 'actUpDqG7xMmWorc0VyjSX48',
          description: {
            en: 'Update the result to the record',
            'zh-CN': '更新结果到记录',
          },
          actionType: 'UPDATE_RECORD',
          input: {
            type: 'SPECIFY_RECORD_BODY',
            recordId: '<%= _triggers.trg6c79JVwZDz7kIHoy8H9Ee.record.id %>',
            data: {
              fldB5swZqLrXh4nfzbI4KiyZ: '<%= _actions.actBg6MKhXUF5TIWC0SfM1Ko.Billed_to %>',
              fldGZCp5PElqjecWeBLpb3Wx: '<%= _actions.actBg6MKhXUF5TIWC0SfM1Ko.Due_date %>',
              fldSNhXaYgDWSejPvotptIDu: '<%= _actions.actBg6MKhXUF5TIWC0SfM1Ko.Date %>',
              fldnZXhp5rteVFV7api0cXGv: '<%= _actions.actBg6MKhXUF5TIWC0SfM1Ko.Invoice_number %>',
              fldu7NoSr9QTKW2UxGmjI6vg: '<%= _actions.actBg6MKhXUF5TIWC0SfM1Ko.Amount %>',
              fldzma4FKJfN12LxDGgeIuzn: '<%= _actions.actBg6MKhXUF5TIWC0SfM1Ko.Product_service %>',
            },
            databaseTemplateId: 'dat6RL1D9fxQveURYSLUd7Ju',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'dat6RL1D9fxQveURYSLUd7Ju',
      name: {
        en: 'Invoice Data',
        'zh-CN': '发票数据',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwU5Pi4B9Gygufw1DaE8uQi',
          name: {
            en: 'Invoice Data',
            'zh-CN': '发票数据',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldouV6tyuRwzwiyC9QYoKTl',
              hidden: false,
            },
            {
              templateId: 'fldDfZeZCcLBmYjWGNR9u5Kz',
              hidden: false,
            },
            {
              templateId: 'fldnZXhp5rteVFV7api0cXGv',
              hidden: false,
            },
            {
              templateId: 'fldu7NoSr9QTKW2UxGmjI6vg',
              hidden: false,
            },
            {
              templateId: 'fldSNhXaYgDWSejPvotptIDu',
              hidden: false,
            },
            {
              templateId: 'fldGZCp5PElqjecWeBLpb3Wx',
              hidden: false,
            },
            {
              templateId: 'fldzma4FKJfN12LxDGgeIuzn',
              hidden: false,
            },
            {
              templateId: 'fldB5swZqLrXh4nfzbI4KiyZ',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwr8XqxjHccFPZLePttUpF1',
          name: {
            en: 'Form',
            'zh-CN': '表单',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldouV6tyuRwzwiyC9QYoKTl',
              hidden: false,
            },
            {
              templateId: 'fldDfZeZCcLBmYjWGNR9u5Kz',
              hidden: false,
            },
            {
              templateId: 'fldnZXhp5rteVFV7api0cXGv',
              hidden: true,
            },
            {
              templateId: 'fldu7NoSr9QTKW2UxGmjI6vg',
              hidden: true,
            },
            {
              templateId: 'fldSNhXaYgDWSejPvotptIDu',
              hidden: true,
            },
            {
              templateId: 'fldGZCp5PElqjecWeBLpb3Wx',
              hidden: true,
            },
            {
              templateId: 'fldzma4FKJfN12LxDGgeIuzn',
              hidden: true,
            },
            {
              templateId: 'fldB5swZqLrXh4nfzbI4KiyZ',
              hidden: true,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldouV6tyuRwzwiyC9QYoKTl',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Title',
            'zh-CN': '标题',
          },
          primary: true,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldDfZeZCcLBmYjWGNR9u5Kz',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Invoice image',
            'zh-CN': '发票图片',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldnZXhp5rteVFV7api0cXGv',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Invoice number',
            'zh-CN': '发票编号',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldu7NoSr9QTKW2UxGmjI6vg',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Amount',
            'zh-CN': '总计',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldSNhXaYgDWSejPvotptIDu',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Date',
            'zh-CN': '日期',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldGZCp5PElqjecWeBLpb3Wx',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Due date',
            'zh-CN': '截止日期',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldzma4FKJfN12LxDGgeIuzn',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Product service',
            'zh-CN': '产品服务',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldB5swZqLrXh4nfzbI4KiyZ',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Billed to',
            'zh-CN': '开票对象',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recY7EnzDN7BMgnOwvQJUHWk',
          data: {
            fldB5swZqLrXh4nfzbI4KiyZ: 'kon',
            fldDfZeZCcLBmYjWGNR9u5Kz: [
              {
                id: 'tplattVD7JhTQvjG53YcNdRCZGg',
                name: 'invoice-stripe.png',
                path: 'template/tplattVD7JhTQvjG53YcNdRCZGg.png',
                bucket: 'bika-dev',
                mimeType: 'image/png',
                size: 84770,
              },
            ],
            fldGZCp5PElqjecWeBLpb3Wx: '2023-10-17',
            fldSNhXaYgDWSejPvotptIDu: '2023-10-17',
            fldnZXhp5rteVFV7api0cXGv: '9455DABc-0003',
            fldouV6tyuRwzwiyC9QYoKTl: 'AITable  Invoice',
            fldu7NoSr9QTKW2UxGmjI6vg: '$22.93',
            fldzma4FKJfN12LxDGgeIuzn: 'AITable',
          },
          values: {
            fldB5swZqLrXh4nfzbI4KiyZ: 'kon',
            fldDfZeZCcLBmYjWGNR9u5Kz: ['invoice-stripe.png'],
            fldGZCp5PElqjecWeBLpb3Wx: '2023-10-17',
            fldSNhXaYgDWSejPvotptIDu: '2023-10-17',
            fldnZXhp5rteVFV7api0cXGv: '9455DABc-0003',
            fldouV6tyuRwzwiyC9QYoKTl: 'AITable  Invoice',
            fldu7NoSr9QTKW2UxGmjI6vg: '$22.93',
            fldzma4FKJfN12LxDGgeIuzn: 'AITable',
          },
        },
        {
          templateId: 'recyf54Fc972izNk4ZB0sGRT',
          data: {
            fldB5swZqLrXh4nfzbI4KiyZ: 'Kelly Chan',
            fldDfZeZCcLBmYjWGNR9u5Kz: [
              {
                id: 'tplattqEsQlKhzOaDKuatBDll1c',
                name: 'Invoice-nero.png',
                path: 'template/tplattqEsQlKhzOaDKuatBDll1c.png',
                bucket: 'bika-dev',
                mimeType: 'image/png',
                size: 109666,
              },
            ],
            fldGZCp5PElqjecWeBLpb3Wx: '2024-03-19',
            fldSNhXaYgDWSejPvotptIDu: '2024-03-18',
            fldnZXhp5rteVFV7api0cXGv: 'KLHLWP-00004',
            fldouV6tyuRwzwiyC9QYoKTl: 'Neon Invoice',
            fldu7NoSr9QTKW2UxGmjI6vg: '$69.51',
            fldzma4FKJfN12LxDGgeIuzn: 'Neon Inc.',
          },
          values: {
            fldB5swZqLrXh4nfzbI4KiyZ: 'Kelly Chan',
            fldDfZeZCcLBmYjWGNR9u5Kz: ['Invoice-nero.png'],
            fldGZCp5PElqjecWeBLpb3Wx: '2024-03-19',
            fldSNhXaYgDWSejPvotptIDu: '2024-03-18',
            fldnZXhp5rteVFV7api0cXGv: 'KLHLWP-00004',
            fldouV6tyuRwzwiyC9QYoKTl: 'Neon Invoice',
            fldu7NoSr9QTKW2UxGmjI6vg: '$69.51',
            fldzma4FKJfN12LxDGgeIuzn: 'Neon Inc.',
          },
        },
        {
          templateId: 'reclmnVBlwVUaSbjiRU6xvf2',
          data: {
            fldB5swZqLrXh4nfzbI4KiyZ: 'MineralTree',
            fldDfZeZCcLBmYjWGNR9u5Kz: [
              {
                id: 'tplattQzKN3cb9zIGSqGz0mESRa',
                name: 'Invoice-slack.png',
                path: 'template/tplattQzKN3cb9zIGSqGz0mESRa.png',
                bucket: 'bika-dev',
                mimeType: 'image/png',
                size: 137163,
              },
            ],
            fldGZCp5PElqjecWeBLpb3Wx: '2024-01-15',
            fldSNhXaYgDWSejPvotptIDu: '2023-12-01',
            fldnZXhp5rteVFV7api0cXGv: '1223113',
            fldouV6tyuRwzwiyC9QYoKTl: 'Slack Invoice',
            fldu7NoSr9QTKW2UxGmjI6vg: '$1,725.00',
            fldzma4FKJfN12LxDGgeIuzn: 'Slack',
          },
          values: {
            fldB5swZqLrXh4nfzbI4KiyZ: 'MineralTree',
            fldDfZeZCcLBmYjWGNR9u5Kz: ['Invoice-slack.png'],
            fldGZCp5PElqjecWeBLpb3Wx: '2024-01-15',
            fldSNhXaYgDWSejPvotptIDu: '2023-12-01',
            fldnZXhp5rteVFV7api0cXGv: '1223113',
            fldouV6tyuRwzwiyC9QYoKTl: 'Slack Invoice',
            fldu7NoSr9QTKW2UxGmjI6vg: '$1,725.00',
            fldzma4FKJfN12LxDGgeIuzn: 'Slack',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
