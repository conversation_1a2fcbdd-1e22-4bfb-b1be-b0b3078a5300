import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: '<PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>',

  // 如果你正在制作“Comming Soon”类型的模板，则需要下方属性配置。如果是正式的模板，请移除下方这一行
  visibility: 'WAITING_LIST',

  // 根据你的模板作用，起一个系统唯一的templateId，模板文件夹的名称与templateId 必须相同！
  templateId: 'automatic-subtitling-for-youtube-urls',

  // 模板的名称，会出现在模板中心，安装后也会显示该名称
  name: {
    en: 'Automatic subtitling for YouTube URLs',
    'zh-CN': 'YouTube链接自动转字幕',
    'zh-TW': 'YouTube連結自動轉字幕',
    ja: 'YouTubeリンクの自動字幕変換',
  },

  // 模板的封面图
  cover: '/assets/template/template-cover-automatic-subtitling-for-youtube-urls.png',

  // 模板的简短描述，会出现在模板中心和模板文件夹详情页
  description: {
    en: 'Paste the YouTube video link into the data table, and automatically recognize and update the video subtitles in the data table using the YouTube subtitle recognition tool.',
    'zh-CN': '在数据表中粘贴YouTube视频链接，通过YouTube字幕识别工具，自动识别视频字幕并更新到数据表中。',
    'zh-TW': '在資料表中粘貼上傳YouTube影片連結，透過YouTube字幕識別工具，自動識別影片字幕並更新到資料表中。',
    ja: 'データ表にYouTube動画のリンクを貼り付け、YouTube字幕認識ツールを使用して動画の字幕を自動的に認識し、データ表に更新します。',
  },

  // 模板的SEO关键词
  keywords: {
    'zh-CN': 'YouTube视频, 字幕识别, 自动转录, 数据表更新, AI自动化',
    'zh-TW': 'YouTube視頻, 字幕識別, 自動轉錄, 數據表更新, AI自動化',
    ja: 'YouTube動画, 字幕認識, 自動トランスクリプション, データテーブル更新, AI自動化',
    en: 'YouTube video, Subtitle recognition, Automated transcription, Data table update, AI automation',
  },

  // 用户画像关键词
  personas: {
    'zh-CN': '视频创作者, 营销人员, 数据经理, 内容经理',
    'zh-TW': '視頻創作者, 營銷人員, 數據經理, 內容經理',
    ja: 'ビデオクリエーター, デジタルマーケター, データマネージャー, コンテンツマネージャー',
    en: 'Video content creator, Digital marketer, Data manager, Content manager',
  },

  // Use Cases关键词
  useCases: {
    en: 'Upload YouTube links, Recognize subtitles, Update data table, Automate transcription, Enhance accessibility, Improve management, Streamline documentation, Store subtitles, Analyze content, Create searchable archives, Track performance, Automate metadata, Facilitate translation, Manage video library, Optimize SEO, Generate summaries, Maintain accuracy, Integrate with systems, Automate reporting, Enhance engagement, Improve user experience, Simplify workflows, Reduce manual tasks, Automate updates',
    'zh-CN':
      '上传视频链接, 识别视频字幕, 更新数据表, 自动转录过程, 提高可访问性, 改进内容管理, 简化视频文档, 存储视频字幕, 分析视频内容, 创建视频档案, 跟踪视频表现, 自动化元数据, 促进内容翻译, 管理视频库, 优化视频SEO, 生成视频摘要, 保持字幕准确, 系统集成, 自动生成报告, 增强参与度, 改善体验, 简化流程, 减少任务, 自动更新',
    'zh-TW':
      '上傳視頻鏈接, 識別視頻字幕, 更新數據表, 自動轉錄過程, 提高可訪問性, 改進內容管理, 簡化視頻文檔, 存儲視頻字幕, 分析視頻內容, 創建視頻檔案, 跟踪視頻表現, 自動化元數據, 促進內容翻譯, 管理視頻庫, 優化視頻SEO, 生成視頻摘要, 保持字幕準確, 系統集成, 自動生成報告, 增強參與度, 改善體驗, 簡化流程, 減少任務, 自動更新',
    ja: 'YouTubeリンクをアップロード, 字幕を認識, データを更新, トランスクリプションの自動化, アクセシビリティの向上, 管理の改善, ドキュメントの簡素化, 字幕を保存, コンテンツを分析, 検索可能なアーカイブを作成, パフォーマンスを追跡, メタデータの自動化, 翻訳を促進, ライブラリを管理, SEOの最適化, 要約を生成, 字幕の正確性を維持, システムと統合, レポートを自動生成, エンゲージメントの向上, ユーザー体験を改善, ワークフローを簡素化, 手動作業を削減, コンテンツを更新',
  },

  // 模板的当前迭代版本
  version: '0.1.3',

  // 模板的分类，不能随意填写，请从 packages/types/src/template/template.ts 声明的schema 中选择合适的分类。模板支持同时存在于多个分类。
  category: 'marketing',

  // 模板内置的新手任务。"Comming Soon" 类型的模板，可忽略不填
  initMissions: [],

  // 模板包含的资源节点，例如 automation、database。"Comming Soon" 类型的模板，可忽略不填，但建议填写，以令模板详情页的内容更加丰富
  resources: [
    {
      resourceType: 'AUTOMATION',
      name: {
        'zh-CN': '视频自动转字幕',
        'zh-TW': '影片自動轉字幕',
        ja: 'ビデオの自動字幕変換',
        en: 'Auto subtitle conversion',
      },
      templateId: 'to_be_updated',
      description: {
        'zh-CN': '将视频链接自动转录字幕并写回表格中',
        'zh-TW': '將影片連結自動轉錄字幕並寫回表格中',
        ja: 'ビデオリンクから自動的に字幕を書き起こし、それをテーブルに書き戻します',
        en: 'Automatically transcribe subtitles from video links and write them back into the table',
      },
      status: 'INACTIVE',
      triggers: [
        {
          triggerType: 'DUMMY_TRIGGER',
          description: {
            'zh-CN': '新YouTube链接输入时触发',
            'zh-TW': '新YouTube連結輸入時觸發',
            ja: '新しいYouTubeリンクが入力されたときにトリガーされます',
            en: 'Triggered when a new YouTube link is entered',
          },
        },
      ],
      actions: [
        {
          actionType: 'DUMMY_ACTION',
          description: {
            en: 'Transcribe subtitles for videos',
            'zh-CN': '对视频进行字幕转录',
            'zh-TW': '對影片進行字幕轉錄',
            ja: 'ビデオの字幕を書き起こす',
          },
        },
        {
          actionType: 'DUMMY_ACTION',
          description: {
            en: 'Write the subtitles back into the datasheet',
            'zh-CN': '将字幕写回数据表中',
            'zh-TW': '將字幕寫回數據表中',
            ja: '字幕をデータテーブルに書き戻す',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'to_be_updated',
      databaseType: 'DATUM',
      name: {
        en: 'Store YouTube information',
        'zh-CN': 'YouTube信息存储',
        'zh-TW': 'YouTube資訊儲存',
        ja: 'YouTube情報を保存する',
      },
      description: {
        en: 'Store new YouTube video information',
        'zh-CN': '存储新YouTube视频信息',
        'zh-TW': '存儲新YouTube視頻信息',
        ja: '新しいYouTube動画情報を保存',
      },
      views: [
        {
          templateId: 'all',
          name: {
            en: '',
            'zh-CN': 'Coming Soon',
            'zh-TW': '',
            ja: '',
          },
          type: 'TABLE',
        },
      ],
      fields: [],
    },
  ],
};

export default template;
