{"schemaVersion": "v1", "templateId": "base-team", "name": {"en": "Base team"}, "cover": "/assets/template/template-cover-no-image-color.png", "description": {"en": "Base Template In Every Need Members Database"}, "category": "official", "version": "0.0.6", "copyright": "Copyright by Vika Limited, AGPL", "installOnce": true, "resources": [{"resourceType": "DATABASE", "templateId": "employee", "databaseType": "DATUM", "name": {"zh-CN": "花名册", "en": "Employees"}, "views": [{"templateId": "all-employees", "name": "所有员工", "type": "TABLE"}, {"templateId": "sales-employees", "name": "销售员工", "type": "TABLE", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "description", "fieldType": "LONG_TEXT", "clause": {"operator": "Contains", "value": "Sales"}}]}}], "fields": [{"type": "LONG_TEXT", "templateId": "name", "name": "Name"}, {"type": "EMAIL", "templateId": "email", "name": "Email"}, {"type": "LONG_TEXT", "templateId": "description", "name": "描述"}, {"type": "PHONE", "templateId": "phone", "name": "Phone"}, {"type": "MEMBER", "templateId": "member", "name": "Member", "property": {"many": false}}]}, {"resourceType": "DATABASE", "templateId": "team-task", "type": "TASK", "name": "Tasks", "fields": []}], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}