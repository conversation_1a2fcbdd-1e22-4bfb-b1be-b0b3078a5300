import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, PageProps } from '@bika/types/content/bo';
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { mapDirectoriesToFiles } from 'sharelib/fs-utils';
import { mdxToHtml } from 'sharelib/mdx-utils';
import type { Locale } from '@bika/types/i18n/bo';
import _ from 'lodash';
import assert from 'assert';

const articlesDirectory = path.join(process.cwd(), './help');

/**
 * fs读取 apps/toolsdk.ai/help 文件夹下的mdx文件
 * 获取信息
 * @returns
 */
export function getHelps(langFilter: Locale): PageProps[] {
  const fileNames: string[] = mapDirectoriesToFiles(articlesDirectory);

  const filterFileNames = fileNames.filter((fileName) => {
    const lastSegment = path.basename(fileName);
    return !lastSegment.startsWith('_');
  });

  const helps = filterFileNames.map((fileName) => {
    const slugs = fileName.split('/');
    let fullPath = path.join(articlesDirectory, fileName + '.mdx');
    if (!fs.existsSync(fullPath)) {
      fullPath = path.join(articlesDirectory, fileName + '.md');
    }
    assert(fs.existsSync(fullPath), `File not found: ${fullPath}`);

    const fileText = fs.readFileSync(fullPath, 'utf8');
    const { data } = matter(fileText);
    const lang = slugs[0];
    if (langFilter) {
      if (lang !== langFilter) {
        return undefined;
      }
    }
    return {
      metadata: data,
      lang: lang,
      slugs: slugs.slice(1),
    } as PageProps;
  });
  return _.compact(helps);
}

export async function getHelpDetail(lang: string, slugs: string[]): Promise<MarkdownRenderer> {
  const fullSlug = path.join(lang, slugs.join('/'));
  const fullPath = path.join(articlesDirectory, fullSlug);
  const fileText = fs.readFileSync(fullPath + '.mdx', 'utf8');
  const mdx = await import(`help/${lang}/${slugs.join('/')}.mdx`);
  const html = await mdxToHtml(mdx.default);
  const { data } = matter(fileText);
  return {
    meta: data,
    slug: fullSlug,
    content: html,
  };
}

export function getStaticParams() {
  const helps = getHelps('en');
  const params = [];
  for (const help of helps) {
    params.push({
      params: {
        lang: help.lang,
        slugs: help.slugs,
      },
    });
  }
  return params;
}
