{"templateId": "agile-workflow", "name": {"en": "Agile Workflow", "zh-CN": "敏捷工作流程"}, "description": {"en": "Streamlines project management for teams, enhancing collaboration and visibility in Agile practices", "zh-CN": "为团队的项目管理提供支持，增强敏捷实践中的协作和可见性"}, "cover": "/assets/template/agile-workflow/agile-workflow.png", "author": "<PERSON> <<EMAIL>>", "category": ["project"], "keywords": "Agile Project Management, Team Collaboration, Sprint Planning, Task Visibility, Scrum Workflow, Kanban Boards, Agile Practices, Project Tracking, Iterative Development, Workflow Automation", "personas": "Project Manager, Software Developer, Product Owner, QA Tester", "useCases": "Plan Sprints Efficiently, Track Project Progress, Assign Tasks Seamlessly, Generate Project Reports, Manage Team Capacity, Facilitate Daily Stand-ups, View Sprint Tasks, Update Task Status, Collaborate with <PERSON><PERSON>, Access Project Documentation, Estimate Task Effort, Review Code Changes, Define Product Backlog, Prioritize Features, Review Completed Sprints, Align Teams with Goals, Communicate Requirements, Plan Release Cycles, Track Testing Tasks, Log Bugs and Issues, Verify Fixes, Prepare Test Reports, Collaborate with Developers, Ensure Quality Standards", "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.10", "resources": [{"resourceType": "FORM", "templateId": "fomAfpXkJAeGmHDwR9oTjCOT", "name": {"en": "Add Backlog", "zh-CN": "添加待办事项"}, "formType": "DATABASE", "databaseTemplateId": "datpvKL22JCKzr9PDB6PNehi", "metadata": {"type": "VIEW", "viewTemplateId": "viwuqLJ3OCijX6yVIITuJ9hm"}}, {"resourceType": "DATABASE", "templateId": "datpvKL22JCKzr9PDB6PNehi", "name": {"en": "Backlog", "zh-CN": "待办事项"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwproWuh6tlTPWdalCDmtBL", "name": {"en": "Function (Priority Sorted)", "zh-CN": "功能（按优先级排序）"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [{"fieldTemplateId": "fldiRaMK0jzhWrsLjSGlNmKX", "asc": false}], "fields": [{"templateId": "fldGAJCUZXiiooQbVZQSkFSj", "hidden": false, "width": 274}, {"templateId": "fldiRaMK0jzhWrsLjSGlNmKX", "hidden": false}, {"templateId": "fld7cs2j37HcDS8yGARpRCkW", "hidden": false}, {"templateId": "fldqZMfoAdVVlZUdcdPz2DfZ", "hidden": false}, {"templateId": "fldrz662hg2KLIO5lgxMPhWJ", "hidden": false}, {"templateId": "fldyluobmiMFcNgEahTxYp6u", "hidden": false}, {"templateId": "fldNwUgvElIr77p77pa4J8fs"}, {"templateId": "fldmdkrWCMn5kFDpqQUPFWu5"}]}, {"type": "TABLE", "templateId": "viw3u5HbiyzkMpvjHmuS4uW2", "name": {"en": "Completed Function", "zh-CN": "已完成的功能"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld7cs2j37HcDS8yGARpRCkW", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optjNty03vkYSsIQGRBo9dHP"}}]}, "sorts": [], "fields": [{"templateId": "fldGAJCUZXiiooQbVZQSkFSj", "hidden": false}, {"templateId": "fldiRaMK0jzhWrsLjSGlNmKX", "hidden": false}, {"templateId": "fldyluobmiMFcNgEahTxYp6u", "hidden": false}, {"templateId": "fld7cs2j37HcDS8yGARpRCkW", "hidden": false}, {"templateId": "fldqZMfoAdVVlZUdcdPz2DfZ", "hidden": false}, {"templateId": "fldrz662hg2KLIO5lgxMPhWJ", "hidden": false}, {"templateId": "fldNwUgvElIr77p77pa4J8fs", "hidden": false}, {"templateId": "fldmdkrWCMn5kFDpqQUPFWu5", "hidden": false}]}, {"type": "TABLE", "templateId": "viwZOE2UGN9KVcHk6X2OleIt", "name": {"en": "Function Need Reviewing", "zh-CN": "需要审查的功能"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld7cs2j37HcDS8yGARpRCkW", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optStKVnsDgShi2NGRax3Btt"}}]}, "sorts": [], "fields": [{"templateId": "fldGAJCUZXiiooQbVZQSkFSj", "hidden": false}, {"templateId": "fldiRaMK0jzhWrsLjSGlNmKX", "hidden": false}, {"templateId": "fldyluobmiMFcNgEahTxYp6u", "hidden": false}, {"templateId": "fld7cs2j37HcDS8yGARpRCkW", "hidden": false}, {"templateId": "fldqZMfoAdVVlZUdcdPz2DfZ", "hidden": false}, {"templateId": "fldrz662hg2KLIO5lgxMPhWJ", "hidden": false}, {"templateId": "fldNwUgvElIr77p77pa4J8fs", "hidden": false}, {"templateId": "fldmdkrWCMn5kFDpqQUPFWu5", "hidden": false}]}, {"type": "TABLE", "templateId": "viwuqLJ3OCijX6yVIITuJ9hm", "name": {"en": "Add Function", "zh-CN": "添加功能"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldGAJCUZXiiooQbVZQSkFSj", "hidden": false}, {"templateId": "fldyluobmiMFcNgEahTxYp6u", "hidden": false}, {"templateId": "fldqZMfoAdVVlZUdcdPz2DfZ", "hidden": false}, {"templateId": "fldiRaMK0jzhWrsLjSGlNmKX", "hidden": false}, {"templateId": "fld7cs2j37HcDS8yGARpRCkW", "hidden": true}, {"templateId": "fldrz662hg2KLIO5lgxMPhWJ", "hidden": true}, {"templateId": "fldNwUgvElIr77p77pa4J8fs", "hidden": true}, {"templateId": "fldmdkrWCMn5kFDpqQUPFWu5", "hidden": true}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldGAJCUZXiiooQbVZQSkFSj", "privilege": "TYPE_EDIT", "name": {"en": "Function", "zh-CN": "功能"}, "required": false, "primary": true}, {"type": "RATING", "templateId": "fldiRaMK0jzhWrsLjSGlNmKX", "privilege": "NAME_EDIT", "name": {"en": "Priority", "zh-CN": "优先级"}, "property": {"icon": {"type": "EMOJI", "emoji": "⭐️"}, "max": 5}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fld7cs2j37HcDS8yGARpRCkW", "privilege": "NAME_EDIT", "name": {"en": "Status", "zh-CN": "状态"}, "property": {"options": [{"id": "optxW3FQz5v8KoJnpSq3Z8hh", "name": "Not Started", "color": "deepPurple"}, {"id": "optRqVr8bM303xrZTaFvJ4HR", "name": "In Progress", "color": "indigo"}, {"id": "optStKVnsDgShi2NGRax3Btt", "name": "Done (Needs Review)", "color": "blue"}, {"id": "optjNty03vkYSsIQGRBo9dHP", "name": "Finished", "color": "teal"}], "defaultValue": ""}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldqZMfoAdVVlZUdcdPz2DfZ", "privilege": "NAME_EDIT", "name": {"en": "Use Case", "zh-CN": "用例"}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldrz662hg2KLIO5lgxMPhWJ", "privilege": "NAME_EDIT", "name": {"en": "Notes", "zh-CN": "备注"}, "primary": false}, {"type": "LOOKUP", "templateId": "fldNwUgvElIr77p77pa4J8fs", "privilege": "NAME_EDIT", "name": {"en": "Sprint Release Date", "zh-CN": "冲刺发布日期"}, "required": false, "property": {"databaseTemplateId": "datRHdx8TPbXDiNLq3SpYN0v", "relatedLinkFieldTemplateId": "fldyluobmiMFcNgEahTxYp6u", "lookupTargetFieldTemplateId": "fldXHDzQv1SUrsELlA87eMIp", "lookupTargetFieldType": "DATETIME", "dataType": "DATETIME", "lookUpLimit": "FIRST", "rollUpType": "VALUES"}, "primary": false}, {"type": "MEMBER", "templateId": "fldmdkrWCMn5kFDpqQUPFWu5", "privilege": "NAME_EDIT", "name": {"en": "DRI", "zh-CN": "责任人"}, "property": {}, "primary": false}, {"type": "LINK", "templateId": "fldyluobmiMFcNgEahTxYp6u", "privilege": "NAME_EDIT", "name": {"en": "Sprint", "zh-CN": "冲刺"}, "property": {"foreignDatabaseTemplateId": "datRHdx8TPbXDiNLq3SpYN0v", "brotherFieldTemplateId": "fldhNr1EfutilP6UXQ2WQpAH"}, "primary": false}], "records": [{"templateId": "recQHyFgpWijczbmG1t8qb5p", "data": {"fld7cs2j37HcDS8yGARpRCkW": ["optjNty03vkYSsIQGRBo9dHP"], "fldGAJCUZXiiooQbVZQSkFSj": "User authentication\t", "fldiRaMK0jzhWrsLjSGlNmKX": 4, "fldmdkrWCMn5kFDpqQUPFWu5": ["mebOu4584YOsijJUE0rBFGyq"], "fldqZMfoAdVVlZUdcdPz2DfZ": "Secure login process\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["recOtMgafrOzA2SZPx6AXkxk"]}, "values": {"fld7cs2j37HcDS8yGARpRCkW": ["Finished"], "fldGAJCUZXiiooQbVZQSkFSj": "User authentication\t", "fldNwUgvElIr77p77pa4J8fs": ["2024-12-30"], "fldiRaMK0jzhWrsLjSGlNmKX": "4", "fldmdkrWCMn5kFDpqQUPFWu5": ["<PERSON>"], "fldqZMfoAdVVlZUdcdPz2DfZ": "Secure login process\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["Develop new features\t"]}}, {"templateId": "recAuWGDnHieTi5UkCyObI8z", "data": {"fld7cs2j37HcDS8yGARpRCkW": ["optRqVr8bM303xrZTaFvJ4HR"], "fldGAJCUZXiiooQbVZQSkFSj": "User interface\t", "fldiRaMK0jzhWrsLjSGlNmKX": 4, "fldmdkrWCMn5kFDpqQUPFWu5": ["mebLEjuvm9b3itUY2DFh8P18"], "fldqZMfoAdVVlZUdcdPz2DfZ": "Customizable dashboard\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["reciOe1tT1bImwjiEOesKW9H"]}, "values": {"fld7cs2j37HcDS8yGARpRCkW": ["In Progress"], "fldGAJCUZXiiooQbVZQSkFSj": "User interface\t", "fldNwUgvElIr77p77pa4J8fs": ["2024-12-19"], "fldiRaMK0jzhWrsLjSGlNmKX": "4", "fldmdkrWCMn5kFDpqQUPFWu5": ["<PERSON>"], "fldqZMfoAdVVlZUdcdPz2DfZ": "Customizable dashboard\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["Improve UI/UX\t"]}}, {"templateId": "reclYcq9MOz4YhpAPohhGZpx", "data": {"fld7cs2j37HcDS8yGARpRCkW": ["optStKVnsDgShi2NGRax3Btt"], "fldGAJCUZXiiooQbVZQSkFSj": "Data reporting\t", "fldiRaMK0jzhWrsLjSGlNmKX": 3, "fldmdkrWCMn5kFDpqQUPFWu5": ["mebMbZZdGKPYOJwZdxSSPKgI"], "fldqZMfoAdVVlZUdcdPz2DfZ": "Generate reports\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["recVoQFIjBiUxHQV3O23cyrp"]}, "values": {"fld7cs2j37HcDS8yGARpRCkW": ["Done (Needs Review)"], "fldGAJCUZXiiooQbVZQSkFSj": "Data reporting\t", "fldNwUgvElIr77p77pa4J8fs": ["2025-01-30"], "fldiRaMK0jzhWrsLjSGlNmKX": "3", "fldmdkrWCMn5kFDpqQUPFWu5": ["<PERSON><PERSON><PERSON><PERSON>"], "fldqZMfoAdVVlZUdcdPz2DfZ": "Generate reports\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["Fix bugs"]}}, {"templateId": "rec8rx71x3WhhpvUaKOlTIs2", "data": {"fld7cs2j37HcDS8yGARpRCkW": ["optRqVr8bM303xrZTaFvJ4HR"], "fldGAJCUZXiiooQbVZQSkFSj": "Implement user registration functionality\t", "fldiRaMK0jzhWrsLjSGlNmKX": 4, "fldmdkrWCMn5kFDpqQUPFWu5": ["mebFPLdV9cZ5F4ktpTa5Pjzl"], "fldqZMfoAdVVlZUdcdPz2DfZ": "User fills in registration information\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["reckt3DzBEsM3CslPZTpy7et"]}, "values": {"fld7cs2j37HcDS8yGARpRCkW": ["In Progress"], "fldGAJCUZXiiooQbVZQSkFSj": "Implement user registration functionality\t", "fldNwUgvElIr77p77pa4J8fs": ["2025-01-23"], "fldiRaMK0jzhWrsLjSGlNmKX": "4", "fldmdkrWCMn5kFDpqQUPFWu5": ["<PERSON><PERSON>"], "fldqZMfoAdVVlZUdcdPz2DfZ": "User fills in registration information\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["Implement data export and user permission management\t"]}}, {"templateId": "recFxspHQ22kfrmkU20ihE7W", "data": {"fld7cs2j37HcDS8yGARpRCkW": ["optjNty03vkYSsIQGRBo9dHP"], "fldGAJCUZXiiooQbVZQSkFSj": "Implement user login functionality\t", "fldiRaMK0jzhWrsLjSGlNmKX": 5, "fldmdkrWCMn5kFDpqQUPFWu5": ["meb44LqJEPtK8dj7rPWs2H5r"], "fldqZMfoAdVVlZUdcdPz2DfZ": "User enters username and password\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["reczajefO9mzK5rLTDfy6hJW"]}, "values": {"fld7cs2j37HcDS8yGARpRCkW": ["Finished"], "fldGAJCUZXiiooQbVZQSkFSj": "Implement user login functionality\t", "fldNwUgvElIr77p77pa4J8fs": ["2025-01-23"], "fldiRaMK0jzhWrsLjSGlNmKX": "5", "fldmdkrWCMn5kFDpqQUPFWu5": ["<PERSON>"], "fldqZMfoAdVVlZUdcdPz2DfZ": "User enters username and password\t", "fldrz662hg2KLIO5lgxMPhWJ": "None", "fldyluobmiMFcNgEahTxYp6u": ["Complete login and registration features\t"]}}]}, {"resourceType": "DATABASE", "templateId": "datRHdx8TPbXDiNLq3SpYN0v", "name": {"en": "Sprints", "zh-CN": "冲刺"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwTP84jPCqJEacVnzxPv6G2", "name": {"en": "Main View", "zh-CN": "主视图"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldrDSQz4HmnxUiJ5E1wyUpx", "hidden": false, "width": 207}, {"templateId": "fldHOwhaIj6HfsadF7oyzR6l", "hidden": false}, {"templateId": "fldAdk5guHwQSTH0bnmQAVTG", "hidden": false}, {"templateId": "fldXHDzQv1SUrsELlA87eMIp", "hidden": false}, {"templateId": "fldhNr1EfutilP6UXQ2WQpAH", "hidden": false, "width": 228}, {"templateId": "fldgg0h7bqwJz4I7Pd3sIOkh", "hidden": false}, {"templateId": "fldDskcEAOs61zuwsFo3BfBm", "hidden": false, "width": 281}, {"templateId": "fldhYsRz9zO62hgTe9XljIX9", "hidden": false}, {"templateId": "fldYVguWMAHALuS1BsEA6C3i", "hidden": false}]}, {"type": "TABLE", "templateId": "viwqk52JuDQ2dwy8PPkfkJzG", "name": {"en": "Sprint Status Grouping View", "zh-CN": "冲刺状态分组视图"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldrDSQz4HmnxUiJ5E1wyUpx", "hidden": false}, {"templateId": "fldHOwhaIj6HfsadF7oyzR6l", "hidden": false}, {"templateId": "fldAdk5guHwQSTH0bnmQAVTG", "hidden": false}, {"templateId": "fldhYsRz9zO62hgTe9XljIX9", "hidden": false}, {"templateId": "fldXHDzQv1SUrsELlA87eMIp", "hidden": false}, {"templateId": "fldYVguWMAHALuS1BsEA6C3i", "hidden": false}, {"templateId": "fldgg0h7bqwJz4I7Pd3sIOkh", "hidden": false}, {"templateId": "fldDskcEAOs61zuwsFo3BfBm", "hidden": false}, {"templateId": "fldhNr1EfutilP6UXQ2WQpAH", "hidden": false}], "groups": [{"fieldTemplateId": "fldHOwhaIj6HfsadF7oyzR6l", "asc": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldrDSQz4HmnxUiJ5E1wyUpx", "privilege": "TYPE_EDIT", "name": {"en": "Goal", "zh-CN": "目标"}, "primary": true}, {"type": "SINGLE_SELECT", "templateId": "fldHOwhaIj6HfsadF7oyzR6l", "privilege": "NAME_EDIT", "name": {"en": "Status", "zh-CN": "状态"}, "property": {"options": [{"id": "optdifEIlipHzHuBzMMhwChH", "name": "Planning", "color": "deepPurple"}, {"id": "opt3z3eGSJdOm4NhrrMD8Apq", "name": "In Progress", "color": "indigo"}, {"id": "optmyZhTbowR4GYWJhWc01mx", "name": "Completed", "color": "blue"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldAdk5guHwQSTH0bnmQAVTG", "privilege": "NAME_EDIT", "name": {"en": "Name", "zh-CN": "名称"}, "primary": false}, {"type": "DATETIME", "templateId": "fldXHDzQv1SUrsELlA87eMIp", "privilege": "NAME_EDIT", "name": {"en": "Target Release Date", "zh-CN": "目标发布日期"}, "description": {"en": "In the default settings, the standard time zone (UTC±0) is adopted. If you need to adjust the time zone, you can turn on the \"Show Time\" switch, and then select the time zone you need from the \"Time Zone\" options below it\n", "zh-CN": "在默认设置里，采用标准时区（0时区）。若您有调整时区的需求，可开启 “显示时间” 开关，随后在其下方 “时区” 的选项中，挑选您需要的时区"}, "property": {"timeZone": "AUTO", "dateFormat": "YYYY-MM-DD", "includeTime": false, "showTimezone": false}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldgg0h7bqwJz4I7Pd3sIOkh", "privilege": "NAME_EDIT", "name": {"en": "Notes", "zh-CN": "备注"}, "primary": false}, {"type": "DATETIME", "templateId": "fldhYsRz9zO62hgTe9XljIX9", "privilege": "NAME_EDIT", "name": {"en": "Start Date", "zh-CN": "开始日期"}, "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "MEMBER", "templateId": "fldYVguWMAHALuS1BsEA6C3i", "privilege": "NAME_EDIT", "name": {"en": "Person in charge", "zh-CN": "负责人"}, "property": {}, "primary": false}, {"type": "LINK", "templateId": "fldDskcEAOs61zuwsFo3BfBm", "privilege": "NAME_EDIT", "name": {"en": "Standups", "zh-CN": "站立会议"}, "property": {"foreignDatabaseTemplateId": "datidQQkjAbCqaBfO38oDMvE", "brotherFieldTemplateId": "fldOaMPoZbGP64iCuS0QDQoj"}, "primary": false}, {"type": "LINK", "templateId": "fldhNr1EfutilP6UXQ2WQpAH", "privilege": "NAME_EDIT", "name": {"en": "Backlog", "zh-CN": "待办事项"}, "property": {"foreignDatabaseTemplateId": "datpvKL22JCKzr9PDB6PNehi", "brotherFieldTemplateId": "fldyluobmiMFcNgEahTxYp6u"}, "primary": false}], "records": [{"templateId": "recVoQFIjBiUxHQV3O23cyrp", "data": {"fldAdk5guHwQSTH0bnmQAVTG": "Bug", "fldDskcEAOs61zuwsFo3BfBm": ["recimw4typ6hqO9zm8qeBnTK"], "fldHOwhaIj6HfsadF7oyzR6l": ["optdifEIlipHzHuBzMMhwChH"], "fldXHDzQv1SUrsELlA87eMIp": "2025-01-30T16:00:00.000Z", "fldYVguWMAHALuS1BsEA6C3i": ["mebYTyD52sAMtTtcdxum1ixz"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["reclYcq9MOz4YhpAPohhGZpx"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-24T16:00:00.000Z", "fldrDSQz4HmnxUiJ5E1wyUpx": "Fix bugs"}, "values": {"fldAdk5guHwQSTH0bnmQAVTG": "Bug", "fldDskcEAOs61zuwsFo3BfBm": ["Fix bugs Retro 12-9-2024"], "fldHOwhaIj6HfsadF7oyzR6l": ["Planning"], "fldXHDzQv1SUrsELlA87eMIp": "2025-01-30", "fldYVguWMAHALuS1BsEA6C3i": ["<PERSON><PERSON><PERSON>"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["Data reporting\t"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-24", "fldrDSQz4HmnxUiJ5E1wyUpx": "Fix bugs"}}, {"templateId": "reciOe1tT1bImwjiEOesKW9H", "data": {"fldAdk5guHwQSTH0bnmQAVTG": "UI", "fldDskcEAOs61zuwsFo3BfBm": ["recZvgZToa4G0vBTWjAF99vT"], "fldHOwhaIj6HfsadF7oyzR6l": ["optmyZhTbowR4GYWJhWc01mx"], "fldXHDzQv1SUrsELlA87eMIp": "2024-12-19T16:00:00.000Z", "fldYVguWMAHALuS1BsEA6C3i": ["mebMbZZdGKPYOJwZdxSSPKgI"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["recAuWGDnHieTi5UkCyObI8z"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-17T16:00:00.000Z", "fldrDSQz4HmnxUiJ5E1wyUpx": "Improve UI/UX\t"}, "values": {"fldAdk5guHwQSTH0bnmQAVTG": "UI", "fldDskcEAOs61zuwsFo3BfBm": ["Improve UI/UX\t Weekly 12-10-2024"], "fldHOwhaIj6HfsadF7oyzR6l": ["Completed"], "fldXHDzQv1SUrsELlA87eMIp": "2024-12-19", "fldYVguWMAHALuS1BsEA6C3i": ["<PERSON><PERSON><PERSON><PERSON>"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["User interface\t"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-17", "fldrDSQz4HmnxUiJ5E1wyUpx": "Improve UI/UX\t"}}, {"templateId": "recOtMgafrOzA2SZPx6AXkxk", "data": {"fldAdk5guHwQSTH0bnmQAVTG": "Feat", "fldDskcEAOs61zuwsFo3BfBm": ["recAeTa8k8gybw2OutKGP01x"], "fldHOwhaIj6HfsadF7oyzR6l": ["opt3z3eGSJdOm4NhrrMD8Apq"], "fldXHDzQv1SUrsELlA87eMIp": "2024-12-30T16:00:00.000Z", "fldYVguWMAHALuS1BsEA6C3i": ["meb44LqJEPtK8dj7rPWs2H5r"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["recQHyFgpWijczbmG1t8qb5p"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-04T16:00:00.000Z", "fldrDSQz4HmnxUiJ5E1wyUpx": "Develop new features\t"}, "values": {"fldAdk5guHwQSTH0bnmQAVTG": "Feat", "fldDskcEAOs61zuwsFo3BfBm": ["Develop new features\t Planning 12-3-2024"], "fldHOwhaIj6HfsadF7oyzR6l": ["In Progress"], "fldXHDzQv1SUrsELlA87eMIp": "2024-12-30", "fldYVguWMAHALuS1BsEA6C3i": ["<PERSON>"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["User authentication\t"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-04", "fldrDSQz4HmnxUiJ5E1wyUpx": "Develop new features\t"}}, {"templateId": "reckt3DzBEsM3CslPZTpy7et", "data": {"fldAdk5guHwQSTH0bnmQAVTG": "Feat", "fldDskcEAOs61zuwsFo3BfBm": ["recVUgXJJtDg2BFrgQiRwEq4"], "fldHOwhaIj6HfsadF7oyzR6l": ["optmyZhTbowR4GYWJhWc01mx"], "fldXHDzQv1SUrsELlA87eMIp": "2025-01-23T16:00:00.000Z", "fldYVguWMAHALuS1BsEA6C3i": ["mebLEjuvm9b3itUY2DFh8P18"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["rec8rx71x3WhhpvUaKOlTIs2"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-31T16:00:00.000Z", "fldrDSQz4HmnxUiJ5E1wyUpx": "Implement data export and user permission management\t"}, "values": {"fldAdk5guHwQSTH0bnmQAVTG": "Feat", "fldDskcEAOs61zuwsFo3BfBm": ["Implement data export and user permission management\t Weekly 12-31-2024"], "fldHOwhaIj6HfsadF7oyzR6l": ["Completed"], "fldXHDzQv1SUrsELlA87eMIp": "2025-01-23", "fldYVguWMAHALuS1BsEA6C3i": ["<PERSON>"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["Implement user registration functionality\t"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-31", "fldrDSQz4HmnxUiJ5E1wyUpx": "Implement data export and user permission management\t"}}, {"templateId": "reczajefO9mzK5rLTDfy6hJW", "data": {"fldAdk5guHwQSTH0bnmQAVTG": "Feat", "fldDskcEAOs61zuwsFo3BfBm": ["recLSWByqLBR5gVJj4YyWMml"], "fldHOwhaIj6HfsadF7oyzR6l": ["opt3z3eGSJdOm4NhrrMD8Apq"], "fldXHDzQv1SUrsELlA87eMIp": "2025-01-23T16:00:00.000Z", "fldYVguWMAHALuS1BsEA6C3i": ["mebOu4584YOsijJUE0rBFGyq"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["recFxspHQ22kfrmkU20ihE7W"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-31T16:00:00.000Z", "fldrDSQz4HmnxUiJ5E1wyUpx": "Complete login and registration features\t"}, "values": {"fldAdk5guHwQSTH0bnmQAVTG": "Feat", "fldDskcEAOs61zuwsFo3BfBm": ["Complete login and registration features\t Planning 12-31-2024"], "fldHOwhaIj6HfsadF7oyzR6l": ["In Progress"], "fldXHDzQv1SUrsELlA87eMIp": "2025-01-23", "fldYVguWMAHALuS1BsEA6C3i": ["<PERSON>"], "fldgg0h7bqwJz4I7Pd3sIOkh": "None", "fldhNr1EfutilP6UXQ2WQpAH": ["Implement user login functionality\t"], "fldhYsRz9zO62hgTe9XljIX9": "2024-12-31", "fldrDSQz4HmnxUiJ5E1wyUpx": "Complete login and registration features\t"}}]}, {"resourceType": "DATABASE", "templateId": "datidQQkjAbCqaBfO38oDMvE", "name": {"en": "Standups", "zh-CN": "站立会议"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwDcmhqr4KY1ymQC9e5qup3", "name": {"en": "Main Sprint View", "zh-CN": "主冲刺视图"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldN6Cc5u3clwtVkIj1zjc1h", "width": 269}, {"templateId": "fldOaMPoZbGP64iCuS0QDQoj", "hidden": false}, {"templateId": "fldSl30CtdJBYXZRbjUZrJqt"}, {"templateId": "fldyZO9gcYAsYZvUqmTwhoeU", "hidden": false}, {"templateId": "fldNcNln2WSzwfaGWBnG60ke", "hidden": false}, {"templateId": "fldD0cVy0ATNVJj08uShbPyn", "hidden": false}, {"templateId": "fld32CHSkRdrBczCfUkcqcD2", "hidden": false}]}], "fields": [{"type": "FORMULA", "templateId": "fldN6Cc5u3clwtVkIj1zjc1h", "privilege": "TYPE_EDIT", "name": {"en": "Name", "zh-CN": "名称"}, "required": false, "property": {"expressionTemplate": "{fldOaMPoZbGP64iCuS0QDQoj} & \"  \" & DATETIME_FORMAT({fldSl30CtdJBYXZRbjUZrJqt}, 'M-D-YYYY')"}, "primary": true}, {"type": "DATETIME", "templateId": "fldSl30CtdJBYXZRbjUZrJqt", "privilege": "NAME_EDIT", "name": {"en": "Date", "zh-CN": "日期"}, "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldD0cVy0ATNVJj08uShbPyn", "privilege": "NAME_EDIT", "name": {"en": "Meeting Notes", "zh-CN": "会议记录"}, "primary": false}, {"type": "LINK", "templateId": "fldOaMPoZbGP64iCuS0QDQoj", "privilege": "NAME_EDIT", "name": {"en": "Sprints", "zh-CN": "冲刺"}, "property": {"foreignDatabaseTemplateId": "datRHdx8TPbXDiNLq3SpYN0v", "brotherFieldTemplateId": "fldDskcEAOs61zuwsFo3BfBm"}, "primary": false}, {"type": "LOOKUP", "templateId": "fldyZO9gcYAsYZvUqmTwhoeU", "privilege": "NAME_EDIT", "name": {"en": "Sprint Start", "zh-CN": "冲刺开始"}, "required": false, "property": {"relatedLinkFieldTemplateId": "fldOaMPoZbGP64iCuS0QDQoj", "lookupTargetFieldTemplateId": "fldhYsRz9zO62hgTe9XljIX9", "lookUpLimit": "ALL", "rollUpType": "VALUES", "formatting": {"type": "DATETIME", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}}}, "primary": false}, {"type": "LOOKUP", "templateId": "fldNcNln2WSzwfaGWBnG60ke", "privilege": "NAME_EDIT", "name": {"en": "Sprint End", "zh-CN": "冲刺结束"}, "property": {"databaseTemplateId": "datRHdx8TPbXDiNLq3SpYN0v", "relatedLinkFieldTemplateId": "fldOaMPoZbGP64iCuS0QDQoj", "lookupTargetFieldTemplateId": "fldXHDzQv1SUrsELlA87eMIp", "lookupTargetFieldType": "DATETIME", "dataType": "DATETIME", "lookUpLimit": "ALL", "rollUpType": "VALUES", "formatting": {"type": "DATETIME", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}}}, "primary": false}, {"type": "MEMBER", "templateId": "fld32CHSkRdrBczCfUkcqcD2", "privilege": "NAME_EDIT", "name": {"en": "People", "zh-CN": "人员"}, "property": {}, "primary": false}], "records": [{"templateId": "recimw4typ6hqO9zm8qeBnTK", "data": {"fld32CHSkRdrBczCfUkcqcD2": ["mebLEjuvm9b3itUY2DFh8P18"], "fldD0cVy0ATNVJj08uShbPyn": "Retrospective\t", "fldN6Cc5u3clwtVkIj1zjc1h": "Fix bugs  12-9-2024", "fldOaMPoZbGP64iCuS0QDQoj": ["recVoQFIjBiUxHQV3O23cyrp"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-09T16:00:00.000Z"}, "values": {"fld32CHSkRdrBczCfUkcqcD2": ["<PERSON>"], "fldD0cVy0ATNVJj08uShbPyn": "Retrospective\t", "fldN6Cc5u3clwtVkIj1zjc1h": "Fix bugs  12-9-2024", "fldNcNln2WSzwfaGWBnG60ke": ["2025-01-30"], "fldOaMPoZbGP64iCuS0QDQoj": ["Fix bugs"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-09", "fldyZO9gcYAsYZvUqmTwhoeU": ["2024-12-24"]}}, {"templateId": "recZvgZToa4G0vBTWjAF99vT", "data": {"fld32CHSkRdrBczCfUkcqcD2": ["mebOu4584YOsijJUE0rBFGyq"], "fldD0cVy0ATNVJj08uShbPyn": "Review progress\t", "fldN6Cc5u3clwtVkIj1zjc1h": "Improve UI/UX\t  12-10-2024", "fldOaMPoZbGP64iCuS0QDQoj": ["reciOe1tT1bImwjiEOesKW9H"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-10T16:00:00.000Z"}, "values": {"fld32CHSkRdrBczCfUkcqcD2": ["<PERSON>"], "fldD0cVy0ATNVJj08uShbPyn": "Review progress\t", "fldN6Cc5u3clwtVkIj1zjc1h": "Improve UI/UX\t  12-10-2024", "fldNcNln2WSzwfaGWBnG60ke": ["2024-12-19"], "fldOaMPoZbGP64iCuS0QDQoj": ["Improve UI/UX\t"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-10", "fldyZO9gcYAsYZvUqmTwhoeU": ["2024-12-17"]}}, {"templateId": "recAeTa8k8gybw2OutKGP01x", "data": {"fld32CHSkRdrBczCfUkcqcD2": ["meb44LqJEPtK8dj7rPWs2H5r"], "fldD0cVy0ATNVJj08uShbPyn": "Discuss project goals\t", "fldN6Cc5u3clwtVkIj1zjc1h": "Develop new features\t  12-3-2024", "fldOaMPoZbGP64iCuS0QDQoj": ["recOtMgafrOzA2SZPx6AXkxk"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-03T16:00:00.000Z"}, "values": {"fld32CHSkRdrBczCfUkcqcD2": ["<PERSON>"], "fldD0cVy0ATNVJj08uShbPyn": "Discuss project goals\t", "fldN6Cc5u3clwtVkIj1zjc1h": "Develop new features\t  12-3-2024", "fldNcNln2WSzwfaGWBnG60ke": ["2024-12-30"], "fldOaMPoZbGP64iCuS0QDQoj": ["Develop new features\t"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-03", "fldyZO9gcYAsYZvUqmTwhoeU": ["2024-12-04"]}}, {"templateId": "recVUgXJJtDg2BFrgQiRwEq4", "data": {"fld32CHSkRdrBczCfUkcqcD2": ["mebMbZZdGKPYOJwZdxSSPKgI"], "fldD0cVy0ATNVJj08uShbPyn": "Reviewed progress of login feature", "fldN6Cc5u3clwtVkIj1zjc1h": "Implement data export and user permission management\t  12-31-2024", "fldOaMPoZbGP64iCuS0QDQoj": ["reckt3DzBEsM3CslPZTpy7et"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-31T16:00:00.000Z"}, "values": {"fld32CHSkRdrBczCfUkcqcD2": ["<PERSON><PERSON><PERSON><PERSON>"], "fldD0cVy0ATNVJj08uShbPyn": "Reviewed progress of login feature", "fldN6Cc5u3clwtVkIj1zjc1h": "Implement data export and user permission management\t  12-31-2024", "fldNcNln2WSzwfaGWBnG60ke": ["2025-01-23"], "fldOaMPoZbGP64iCuS0QDQoj": ["Implement data export and user permission management\t"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-31", "fldyZO9gcYAsYZvUqmTwhoeU": ["2024-12-31"]}}, {"templateId": "recLSWByqLBR5gVJj4YyWMml", "data": {"fld32CHSkRdrBczCfUkcqcD2": ["mebYTyD52sAMtTtcdxum1ixz"], "fldD0cVy0ATNVJj08uShbPyn": "Discussed goals for Sprint 1\t", "fldN6Cc5u3clwtVkIj1zjc1h": "Complete login and registration features\t  12-31-2024", "fldOaMPoZbGP64iCuS0QDQoj": ["reczajefO9mzK5rLTDfy6hJW"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-31T16:00:00.000Z"}, "values": {"fld32CHSkRdrBczCfUkcqcD2": ["<PERSON><PERSON><PERSON>"], "fldD0cVy0ATNVJj08uShbPyn": "Discussed goals for Sprint 1\t", "fldN6Cc5u3clwtVkIj1zjc1h": "Complete login and registration features\t  12-31-2024", "fldNcNln2WSzwfaGWBnG60ke": ["2025-01-23"], "fldOaMPoZbGP64iCuS0QDQoj": ["Complete login and registration features\t"], "fldSl30CtdJBYXZRbjUZrJqt": "2024-12-31", "fldyZO9gcYAsYZvUqmTwhoeU": ["2024-12-31"]}}]}, {"resourceType": "AUTOMATION", "templateId": "atoMS63NQBSRoYbgNiHD0q0W", "name": {"en": "Daily Stand-Up Meeting Reminder", "zh-CN": "每日站会提醒"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trgb8VKqwJHXXZCGuYRujRWh", "description": {"en": "at 9 AM every morning", "zh-CN": "每天早上9点"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "DAY", "interval": 1}}, "datetime": "2025-02-12T01:00:28.629Z"}}}], "actions": [{"templateId": "act3OZY4s9RQ2ZXH7XMpQgDN", "description": {"en": "Create a stand-up meeting task notification to remind team members", "zh-CN": "创建一个站立会议的任务通知，以提醒团队成员"}, "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "ENTER_VIEW", "name": "Good morning, everyone! Our stand-up meeting for today will start at 10 a.m", "description": "Please take turns to share the progress of your respective tasks, any issues encountered, and your next steps. During this process, please also update this information in the records related to the \"stand-up meeting,\" so that we can clearly grasp the overall situation and better advance our work", "canCompleteManually": true, "dueDate": {}, "assignType": "DEDICATED", "to": [{"type": "MEMBER_FIELD", "fieldTemplateId": "fld32CHSkRdrBczCfUkcqcD2", "viewTemplateId": "viwDcmhqr4KY1ymQC9e5qup3", "databaseTemplateId": "datidQQkjAbCqaBfO38oDMvE"}], "buttonText": {"en": "Go to the \"Standups\" Database", "zh-CN": "前往“站立会议”数据表"}, "viewTemplateId": "viwDcmhqr4KY1ymQC9e5qup3", "databaseTemplateId": "datidQQkjAbCqaBfO38oDMvE"}}}]}, {"resourceType": "AUTOMATION", "templateId": "ato7PKJaYhChij2QXaIGU6HE", "name": {"en": "Sprint Start and End Notification", "zh-CN": "冲刺开始和结束通知"}, "triggers": [{"triggerType": "RECORD_MATCH", "templateId": "trgSDIcAVyL5ClgcTdoZXPF7", "description": {"en": "When a new sprint In Progress", "zh-CN": "当新的冲刺开始进行时"}, "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldHOwhaIj6HfsadF7oyzR6l", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opt3z3eGSJdOm4NhrrMD8Apq"}}]}, "databaseTemplateId": "datRHdx8TPbXDiNLq3SpYN0v"}}, {"triggerType": "RECORD_MATCH", "templateId": "trgoZ2lwP1zX2tOBWhe0PcPa", "description": {"en": "When a sprint Completed", "zh-CN": "当冲刺完成时"}, "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldHOwhaIj6HfsadF7oyzR6l", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optmyZhTbowR4GYWJhWc01mx"}}]}, "databaseTemplateId": "datRHdx8TPbXDiNLq3SpYN0v"}}], "actions": [{"templateId": "actc23o3NMiQWvgzznK0UuQG", "description": {"en": "Automatically notify team members and provide sprint status and sprint goals", "zh-CN": "自动通知团队成员并提供冲刺状态和冲刺目标"}, "actionType": "SLACK_WEBHOOK", "input": {"type": "SLACK_WEBHOOK", "data": {"msgtype": "text", "text": "💡 Sprint Status Update                   \nCurrent Sprint Status:   <%= JSON.stringify(_triggers.trgSDIcAVyL5ClgcTdoZXPF7.record.cells.fldHOwhaIj6HfsadF7oyzR6l.value) %><%= JSON.stringify(_triggers.trgoZ2lwP1zX2tOBWhe0PcPa.record.cells.fldHOwhaIj6HfsadF7oyzR6l.value) %>    \nSprint Goal: <%= _triggers.trgSDIcAVyL5ClgcTdoZXPF7.record.cells.fldrDSQz4HmnxUiJ5E1wyUpx.value %><%= _triggers.trgoZ2lwP1zX2tOBWhe0PcPa.record.cells.fldrDSQz4HmnxUiJ5E1wyUpx.value %>  \nRecord Details: <%= _triggers.trgSDIcAVyL5ClgcTdoZXPF7.record.url %><%= _triggers.trgoZ2lwP1zX2tOBWhe0PcPa.record.url %>"}, "urlType": "URL", "url": ""}}]}, {"resourceType": "AUTOMATION", "templateId": "ato0kbX9cTzCSi9nZQ7Ocd1j", "name": {"en": "Overdue Task Reminder", "zh-CN": "逾期任务提醒"}, "triggers": [{"triggerType": "DATETIME_FIELD_REACHED", "templateId": "trgS12RhVN08TITQ6AudenYE", "description": {"en": "When a task is overdue", "zh-CN": "当任务逾期时"}, "input": {"type": "DATETIME_FIELD_REACHED", "datetime": {"hour": 12, "minute": 0, "type": "TODAY"}, "fieldTemplateId": "fldXHDzQv1SUrsELlA87eMIp", "databaseTemplateId": "datRHdx8TPbXDiNLq3SpYN0v"}}], "actions": [{"templateId": "act9BszsMY36Rt6E7OvESiqP", "description": {"en": "Find expired tasks", "zh-CN": "找到过期的任务"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldHOwhaIj6HfsadF7oyzR6l", "fieldType": "SINGLE_SELECT", "clause": {"operator": "IsNot", "value": "optmyZhTbowR4GYWJhWc01mx"}}]}, "databaseTemplateId": "datRHdx8TPbXDiNLq3SpYN0v"}}, {"templateId": "actGji3DbodxDYr3gbbL1gYe", "description": {"en": "Send reminder message", "zh-CN": "发送提醒信息"}, "actionType": "SLACK_WEBHOOK", "input": {"type": "SLACK_WEBHOOK", "data": {"msgtype": "text", "text": "Reminder: Task『<%= _triggers.trgS12RhVN08TITQ6AudenYE.record.cells.fldrDSQz4HmnxUiJ5E1wyUpx.value %>』Overdue. Please complete the task or update its status as soon as possible"}, "urlType": "URL", "url": ""}}]}, {"resourceType": "AUTOMATION", "templateId": "atoHi9fB1pHdcFi1SN3x9iqs", "name": {"en": "Automated Form Feedback and Confirmation", "zh-CN": "自动化表单反馈和确认"}, "triggers": [{"triggerType": "FORM_SUBMITTED", "templateId": "trg9IB0ZjGGfK23vwIKdvMWA", "description": {"en": "When a team member submits a new story using the \"Add Story\" form", "zh-CN": "当团队成员使用“添加故事”表单提交新故事时"}, "input": {"type": "FORM", "formTemplateId": "fomAfpXkJAeGmHDwR9oTjCOT"}}], "actions": [{"templateId": "actYrq4RnytZJZ0W4jO68UAA", "description": {"en": "Find the Current operator", "zh-CN": "找到当前操作者"}, "actionType": "FIND_MEMBERS", "input": {"type": "MEMBER", "by": [{"type": "CURRENT_OPERATOR"}]}}, {"templateId": "actQHMrwpQPLQHzNYleKLhxV", "description": {"en": "Send feedback information", "zh-CN": "发送反馈信息"}, "actionType": "SLACK_WEBHOOK", "input": {"type": "SLACK_WEBHOOK", "data": {"msgtype": "text", "text": "<%= _actions.actYrq4RnytZJZ0W4jO68UAA.members[0].name %> , Your user story has been successfully added to the backlog! We will review it during the next backlog refinement meeting"}, "urlType": "URL", "url": ""}}]}], "initMissions": []}