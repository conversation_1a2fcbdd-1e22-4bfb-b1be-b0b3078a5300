---
sidebar_position: -909
sidebar_label: v0.1.0
title: v0.1.0 Release Notes
---

# v0.1.0 Release Notes

Release Date: [Date]

---

## 🚀 新功能

- **ToolSDK.ai 介绍**：发布了 ToolSDK.ai 平台，这是一个面向托管 MCP 服务器的开源 AI 工具 SDK。
- **MCP 服务器托管**：平台目前托管了超过 5000 个 MCP 服务器，提供“MCP 即服务”。
- **MCP 中枢服务器（Hub Server）**：引入了 MCP Hub Server，它是一个中枢代理服务器，可以连接所有托管的 MCP 服务器 —— 实现“一个 MCP 服务器统领所有服务器”。

---

## ✨ 核心亮点

- **快速接入**：AI 客户端（如 Claude Desktop、Cursor、OpenAI Desktop）可以轻松接入 5000+ 托管的 MCP 服务器。
- **开发者友好**：提供 SDK 支持，帮助开发者快速集成第三方应用和 AI 工具。
- **MCP 路由服务器**：只需配置一个 MCP 服务器，即可自动访问你收藏的其他 MCP 服务器。

---

## 🔧 主要功能

- **轻松连接**：无需运行本地代码，即可连接托管的 MCP 服务器。
- **远程访问 MCP 服务**：在 AI 客户端（如 Claude 或 Cursor）中使用便于灵活扩展。
-一个 MCP 服务器，即可访问整个生态系统 **安全认证**：中的 AI 工具ier 兼容）。
- **可通过凭证登录，和 MCP Server 包，扩展架构**：支持 ToolApp（与 Zap保障工具使用的安全性。

---

## 📖 文档说明

- 初始文档包括：
  - [什么是 ToolSDK.ai？](#)
  - 面向开发者和 AI 客户端的快速入门指南。
  - 示例项目，帮助用户快速上手集成。

---

## 📎 示例项目

提供了一个基础示例项目，演示如何将 ToolSDK.ai 与 Vercel AI SDK 等结合使用：

```bash
git clone https://github.com/toolsdk-ai/toolsdk
npm install
npm run example
```

---

## 📌 安装方式

你可以使用 npm 或 yarn 来安装 SDK：

```bash
npm install toolsdk
# 或者
yarn add toolsdk
```

如需了解更多内容，请查看 [ToolSDK.ai 官方文档](#)。

--- 

这个版本是平台的初始发布版本，重点在于介绍核心功能和提供基本使用方式。