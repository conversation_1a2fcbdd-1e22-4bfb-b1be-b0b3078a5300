import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'content-review',
  name: {
    en: ' Content Review',
    ja: 'コンテンツ審査テンプレート',
    'zh-CN': '内容审核',
    'zh-TW': '內容審核',
  },
  description: {
    en: 'This template is suitable for the review management of any content pending publication or promotion. It achieves this through an automated workflow that: reminds reviewers to review content in a timely manner, automatically notifies progress on the review, thereby improving efficiency and transparency, reducing manual intervention, and ensuring the timeliness and accuracy of the review process. ',
    ja: 'このテンプレートは、公開またはプロモーション予定のコンテンツのレビュー管理に適しています。自動化されたワークフローを通じて、レビュアーにコンテンツをタイムリーにレビューするようリマインドし、レビュー進捗を自動で通知することで、効率と透明性を向上させ、手動介入を減らし、レビュープロセスのタイムリーさと正確性を確保します.',
    'zh-CN':
      '该模板适用于任何待发布推广内容的审核管理，通过自动化的工作流实现：提醒审核人员及时审核内容、自动通知审核进度，从而提升审核效率与透明度，减少人工干预，确保审核流程的及时性与准确性。\n',
    'zh-TW':
      '该模板適用於任何待發布推廣內容的審核管理，通過自動化的工作流程實現：提醒審核人員及時審核內容、 自動通知審核進度，從而提升審核效率與透明度，減少人工干預，確保審核流程的及時性與準確性。',
  },
  keywords: {
    en: 'Content Review, Review Reminder, Real-time Status Update, 	Improved Review Efficiency, Automated Workflow, Reduced Manual Intervention',
    ja: 'コンテンツ審査, 審査リマインダー, リアルタイムステータス更新, 審査効率向上, 自動化ワークフロー, 人的介入の削減',
    'zh-TW': '內容審核, 審核提醒, 即時狀態更新, 審核效率提升, 自動化工作流, 減少人工干預',
    'zh-CN': '内容审核, 审核提醒, 实时状态更新, 审核效率提升, 自动化工作流, 减少人工干预',
  },
  personas: {
    en: '	Content Review Specialist, Review Manager, Legal Compliance Officer, Community Operations Manager, Internal Control Manager, Training Content Manager, 	Community Administrator, User/Creator',
    'zh-CN':
      '内容审核专员, 审核主管, 法务合规人员, 社区运营经理, 企业内控管理人员, 培训内容负责人, 社区管理员, 用户/创作者',
    'zh-TW':
      '內容審核專員, 審核主管, 法務合規人員, 企業內控管理人員, 社區營運經理, 培訓內容負責人, 社區管理員, 使用者／創作者',
    ja: 'コンテンツ審査担当者, 審査マネージャー, 	法務コンプライアンス担当者, コミュニティ運営マネージャー, 企業内部統制担当者, 研修コンテンツ責任者, コミュニティ管理者, ユーザー／クリエイター',
  },
  useCases: {
    en: 'Review Promotion Articles Pending Release, Review User-Generated Content, Review Community Posts or Comments, Review Social Media Content, 	Review E-commerce Product Descriptions and Images, Review Policy Compliance, Review Internal Knowledge Base or Training Materials',
    'zh-CN':
      '审核待发布的推广文章, 审核用户生成内容, 社区帖子或评论审核, 品牌广告素材审核, 社交媒体内容审核, 电商商品描述和图片审核, 政策合规性审核, 内部知识库或培训资料的内容审核',
    'zh-TW':
      '審核待發布的推廣文章, 	審核使用者生成內容, 社區貼文或評論審核, 品牌廣告素材審核, 社交媒體內容審核, 電商商品描述和圖片審核, 政策合規性審核, 內部知識庫或培訓資料的內容審核',
    ja: '公開予定のプロモーション記事の審査, ユーザー生成コンテンツの審査, コミュニティ投稿・コメントの審査, ブランド広告素材の審査, ソーシャルメディアコンテンツの審査, EC商品説明・画像の審査, 	ポリシーコンプライアンスの審査, 社内ナレッジベース・研修資料の内容審査',
  },
  cover: '/assets/template/template-cover-content-review.png',
  author: 'pengjin <<EMAIL>>',
  category: ['project', 'marketing'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.8',
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoYBdIurmY6AweFm90x2ofv',
      name: {
        en: 'Content Review Reminder',
        ja: 'コンテンツしんさのリマインダー',
        'zh-CN': '内容审核提醒',
        'zh-TW': '內容審核提醒',
      },
      triggers: [
        {
          triggerType: 'RECORD_MATCH',
          templateId: 'trgctiW5JIROLoA3ZddCID5b',
          description: {
            en: 'There is new review content',
            ja: 'あたらしい しんさ ないよう が あります',
            'zh-CN': '有新的审核内容',
            'zh-TW': '有新的審核內容',
          },
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldTd1Og7TSB5APhuOSgHe1y',
                  fieldType: 'SINGLE_SELECT',
                  clause: {
                    operator: 'Is',
                    value: 'optNVnOcHBQ0g',
                  },
                },
              ],
            },
            databaseTemplateId: 'datRF93lgLiTf5zH8116d9eg',
          },
        },
      ],
      actions: [
        {
          templateId: 'actNgb80RKUwbLQjDpIVYhYi',
          description: {
            en: 'Notify The Reviewer',
            ja: 'しんさいん に つうち する',
            'zh-CN': '通知评审人',
            'zh-TW': '通知評審人',
          },
          actionType: 'CREATE_MISSION',
          input: {
            type: 'MISSION_BODY',
            mission: {
              type: 'UPDATE_RECORD',
              name: '📢 You have new content that needs review. Please review it as soon as possible 🙏🏻',
              description:
                'New review content has been submitted for review. Please review it as soon as possible. Thank you',
              assignType: 'SHARE',
              to: [
                {
                  type: 'SPECIFY_UNITS',
                  unitIds: [],
                },
              ],
              buttonText: {
                en: 'Click here to review',
                ja: 'クリックして審査へ進む',
                'zh-CN': '点击前往审核',
                'zh-TW': '點擊前往審核',
              },
              recordId: '<%= _triggers.trgctiW5JIROLoA3ZddCID5b.record.id %>',
              databaseTemplateId: 'datRF93lgLiTf5zH8116d9eg',
            },
          },
        },
        {
          templateId: 'actapNIKPUknZ43Huivoilw8',
          description: {
            en: 'Message push to Slack',
            ja: 'メッセージをSlackにプッシュする',
            'zh-CN': '消息推送至slack',
            'zh-TW': '消息推送到 Slack',
          },
          actionType: 'SLACK_WEBHOOK',
          input: {
            type: 'SLACK_WEBHOOK',
            data: {
              msgtype: 'text',
              text:
                '📢 You have new content pending review. Please confirm as soon as possible. 🙏🏻         \n' +
                '                             \n' +
                '---                                \n' +
                '<%= _triggers.trgctiW5JIROLoA3ZddCID5b.record.cells.fldHyFhkKYDw88747REk4OXa.value %>                          \n' +
                '                \n' +
                '              \n' +
                '                  \n' +
                '<<%= _triggers.trgctiW5JIROLoA3ZddCID5b.record.url %>|Click here to view the full record>',
            },
            urlType: 'URL',
            url: '',
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoSI7nYGAC8bWxC9R1vOusD',
      name: {
        en: 'Review Status Change Notification',
        ja: 'しんさステータスへんこうつうち',
        'zh-CN': '审核状态变更通知',
        'zh-TW': '審核狀態變更通知',
      },
      triggers: [
        {
          triggerType: 'RECORD_MATCH',
          templateId: 'trgC5QaGXKT8qnNEtc1XeQXo',
          description: {
            en: 'Review status change',
            ja: '審査状況の変更',
            'zh-CN': '审核状态变更',
            'zh-TW': '審核狀態變更',
          },
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'Or',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldTd1Og7TSB5APhuOSgHe1y',
                  fieldType: 'SINGLE_SELECT',
                  clause: {
                    operator: 'IsNot',
                    value: 'optNVnOcHBQ0g',
                  },
                },
              ],
            },
            databaseTemplateId: 'datRF93lgLiTf5zH8116d9eg',
          },
        },
      ],
      actions: [
        {
          templateId: 'act0bFc5nA6GAEGg3KolHAF1',
          description: {
            en: 'Send email reminder',
            ja: 'メールリマインダーを送信する',
            'zh-CN': '发送邮件提醒',
            'zh-TW': ' 發送郵件提醒',
          },
          actionType: 'SEND_EMAIL',
          input: {
            type: 'SERVICE',
            subject: '📢 The review status of your content has been updated',
            body: {
              markdown:
                'Dear <%= _triggers.trgC5QaGXKT8qnNEtc1XeQXo.record.cells.fldy8XWPns8Zu1FqnN6XWB9F.value %>         \n' +
                '                \n' +
                'Your content review status has changed, The details are as follows：                \n' +
                '            \n' +
                '          \n' +
                'Review Status：<%= JSON.stringify(_triggers.trgC5QaGXKT8qnNEtc1XeQXo.record.cells.fldTd1Og7TSB5APhuOSgHe1y.value) %>  \n' +
                '          \n' +
                '                \n' +
                ' View detailed information，[please click here](<%= _triggers.trgC5QaGXKT8qnNEtc1XeQXo.record.url %>)',
            },
            to: [
              {
                type: 'USER',
                userId: '<%= _triggers.trgC5QaGXKT8qnNEtc1XeQXo.record.cells.fldy8XWPns8Zu1FqnN6XWB9F.data %>',
              },
              {
                type: 'EMAIL_STRING',
                email: '<EMAIL>',
              },
            ],
            senderName: '',
            cc: [],
            bcc: [],
            replyTo: [],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datRF93lgLiTf5zH8116d9eg',
      name: {
        en: 'Content Review',
        ja: 'コンテンツしんさひょう',
        'zh-CN': '内容审核表',
        'zh-TW': '內容審核表',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwoDV4foAE7ntdUmw6D8Exj',
          name: {
            en: 'ALL',
            ja: 'ぜんたい',
            'zh-CN': '整体',
            'zh-TW': '整體',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldHyFhkKYDw88747REk4OXa',
              hidden: false,
              width: 343,
            },
            {
              templateId: 'fldPdLyWoU1OCPQRgRybExoN',
              hidden: false,
              width: 125,
            },
            {
              templateId: 'fldNwvfUuIE9cbGftRvxcaC3',
              hidden: false,
            },
            {
              templateId: 'fldTd1Og7TSB5APhuOSgHe1y',
              hidden: false,
              width: 175,
            },
            {
              templateId: 'fldx7N3JTxUIb8KFzq7eGWiB',
              hidden: false,
            },
            {
              templateId: 'fldwVfsPEStR1H5WWFXQNTs0',
              hidden: false,
              width: 199,
            },
            {
              templateId: 'fldQ0qRpOmAtGBh727zhpPOD',
              hidden: false,
              width: 185,
            },
            {
              templateId: 'fldy8XWPns8Zu1FqnN6XWB9F',
              hidden: false,
              width: 333,
            },
            {
              templateId: 'fldmwuaNen6ZeFBJ49xqN9Cd',
              hidden: false,
            },
            {
              templateId: 'fld7QXf8rzbcNaa0kcy3LN7x',
              hidden: false,
              width: 167,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwNNkfP1IxS8XuWsHUoynuv',
          name: {
            en: 'Request review',
            ja: 'しんさまち',
            'zh-CN': '待审核',
            'zh-TW': '待審核',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldTd1Og7TSB5APhuOSgHe1y',
                fieldType: 'SINGLE_SELECT',
                clause: {
                  operator: 'Is',
                  value: 'optNVnOcHBQ0g',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldHyFhkKYDw88747REk4OXa',
              hidden: false,
            },
            {
              templateId: 'fldPdLyWoU1OCPQRgRybExoN',
              hidden: false,
            },
            {
              templateId: 'fldNwvfUuIE9cbGftRvxcaC3',
              hidden: false,
            },
            {
              templateId: 'fldTd1Og7TSB5APhuOSgHe1y',
              hidden: false,
            },
            {
              templateId: 'fldx7N3JTxUIb8KFzq7eGWiB',
              hidden: false,
            },
            {
              templateId: 'fldwVfsPEStR1H5WWFXQNTs0',
              hidden: false,
            },
            {
              templateId: 'fldy8XWPns8Zu1FqnN6XWB9F',
              hidden: false,
            },
            {
              templateId: 'fldmwuaNen6ZeFBJ49xqN9Cd',
              hidden: false,
            },
            {
              templateId: 'fldQ0qRpOmAtGBh727zhpPOD',
              hidden: false,
            },
            {
              templateId: 'fld7QXf8rzbcNaa0kcy3LN7x',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fldHyFhkKYDw88747REk4OXa',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Pending Review Content',
            ja: 'しんさまちないよう',
            'zh-CN': '待评审内容',
            'zh-TW': '待評審內容',
          },
          primary: true,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldPdLyWoU1OCPQRgRybExoN',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Attachment (if )',
            ja: 'てんぷファイル (あるばあい)',
            'zh-CN': '附件(如有)',
            'zh-TW': '附件(如有)',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldTd1Og7TSB5APhuOSgHe1y',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Review Status',
            ja: 'しんさじょうきょう',
            'zh-CN': '审核状态',
            'zh-TW': '審核狀態',
          },
          property: {
            options: [
              {
                id: 'optfa77aWL9W8',
                name: 'PASS',
                color: 'green5',
              },
              {
                id: 'opty3EvVPvLr6',
                name: 'Rejected',
                color: 'red5',
              },
              {
                id: 'optdMTCEYKwDS',
                name: 'Pending',
                color: 'yellow3',
              },
              {
                id: 'optNVnOcHBQ0g',
                name: 'Request review',
                color: 'deepPurple',
              },
            ],
            defaultValue: 'optNVnOcHBQ0g',
          },
          primary: false,
        },
        {
          type: 'URL',
          templateId: 'fldNwvfUuIE9cbGftRvxcaC3',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Document Link',
            ja: 'ドキュメントリンク',
            'zh-CN': '文档链接',
            'zh-TW': '文檔鏈接',
          },
          primary: false,
        },
        {
          type: 'URL',
          templateId: 'fldx7N3JTxUIb8KFzq7eGWiB',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Content 1',
            ja: 'サブこうもく いち',
            'zh-CN': '内容1',
            'zh-TW': '内容 1',
          },
          primary: false,
        },
        {
          type: 'URL',
          templateId: 'fldwVfsPEStR1H5WWFXQNTs0',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Content 2',
            ja: 'サブこうもく に',
            'zh-CN': '内容2',
            'zh-TW': '内容 2',
          },
          primary: false,
        },
        {
          type: 'CREATED_TIME',
          templateId: 'fld7QXf8rzbcNaa0kcy3LN7x',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Creation Time',
            ja: 'さくせいにちじ',
            'zh-CN': '创建时间',
            'zh-TW': '創建時間',
          },
          required: false,
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: true,
          },
          primary: false,
        },
        {
          type: 'CREATED_BY',
          templateId: 'fldy8XWPns8Zu1FqnN6XWB9F',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Submitter',
            ja: 'ていしゅつしゃ',
            'zh-CN': '送审人',
            'zh-TW': '送審人',
          },
          required: false,
          primary: false,
        },
        {
          type: 'MODIFIED_BY',
          templateId: 'fldmwuaNen6ZeFBJ49xqN9Cd',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Editor',
            ja: 'しゅうせいしゃ',
            'zh-CN': '修改人',
            'zh-TW': '修改人',
          },
          required: false,
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldQ0qRpOmAtGBh727zhpPOD',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Reviewer Comments',
            ja: 'しゅうせいていあん',
            'zh-CN': '修改建议',
            'zh-TW': '修改建議',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recpMJJv1ksOLZuGw68HsHSy',
          data: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-11T07:19:22.790Z',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.19 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.19 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attached image\n' +
              '\n' +
              'Email Content: Refer to the document link',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/YEv-WHYWAkFAZgzVqjZFKY55Nrm_91aREQNRuGAp',
            fldPdLyWoU1OCPQRgRybExoN: [
              {
                id: 'tplattJh7Pu9GKVyhb3jp5edL8g',
                name: 'Spark-【test】Bika-ai-v0-8-19-Release-Notes-Update-–-Discover-What-s-New-.png',
                path: 'template/tplattJh7Pu9GKVyhb3jp5edL8g.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 607652,
              },
            ],
            fldTd1Og7TSB5APhuOSgHe1y: ['optNVnOcHBQ0g'],
            fldmwuaNen6ZeFBJ49xqN9Cd: 'usrEJXVBqVqSE4tETURYhgo2',
            fldy8XWPns8Zu1FqnN6XWB9F: 'usr04CQqKlWHlOkII5agWnLL',
          },
          values: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-05 10:58',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.19 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.19 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attached image\n' +
              '\n' +
              'Email Content: Refer to the document link',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/YEv-WHYWAkFAZgzVqjZFKY55Nrm_91aREQNRuGAp',
            fldPdLyWoU1OCPQRgRybExoN: ['Spark-【测试】Bika-ai-v0-8-19-Release-Notes-Update-–-Discover-What-s-New-.png'],
            fldTd1Og7TSB5APhuOSgHe1y: ['Request review'],
            fldmwuaNen6ZeFBJ49xqN9Cd: 'pengjin',
            fldy8XWPns8Zu1FqnN6XWB9F: 'pengjin',
          },
        },
        {
          templateId: 'rechemlZU3Kv2m3ZQ3RvlHhB',
          data: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-11T07:19:22.790Z',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.20 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.20 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email Content: Refer to [Document Link]\n' +
              'Jump Link: Refer to [Next Article 1]\n' +
              '\n' +
              'Sending Time: October 28, 2024 @ 11:00 PM',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/jjYecV7otxO6nAS-k0MZn1GeORDhvlOpWEU65DwF',
            fldPdLyWoU1OCPQRgRybExoN: [
              {
                id: 'tplattHdHyyq4hci3V4zFrTO8RM',
                name: 'Spark-【test】Bika-ai-v0-8-20-Release-Notes-Update-–-Discover-What-s-New-.png',
                path: 'template/tplattHdHyyq4hci3V4zFrTO8RM.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 471411,
              },
            ],
            fldTd1Og7TSB5APhuOSgHe1y: ['optdMTCEYKwDS'],
            fldmwuaNen6ZeFBJ49xqN9Cd: 'usrEJXVBqVqSE4tETURYhgo2',
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://bika.ai/en/help/release-notes/v0.8.20',
            fldy8XWPns8Zu1FqnN6XWB9F: 'usr04CQqKlWHlOkII5agWnLL',
          },
          values: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-05 10:59',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.20 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.20 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email Content: Refer to [Document Link]\n' +
              'Jump Link: Refer to [Next Article 1]\n' +
              '\n' +
              'Sending Time: October 28, 2024 @ 11:00 PM',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/jjYecV7otxO6nAS-k0MZn1GeORDhvlOpWEU65DwF',
            fldPdLyWoU1OCPQRgRybExoN: ['Spark-【test】Bika-ai-v0-8-20-Release-Notes-Update-–-Discover-What-s-New-.png'],
            fldTd1Og7TSB5APhuOSgHe1y: ['Pending'],
            fldmwuaNen6ZeFBJ49xqN9Cd: 'pengjin',
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://bika.ai/en/help/release-notes/v0.8.20',
            fldy8XWPns8Zu1FqnN6XWB9F: 'pengjin',
          },
        },
        {
          templateId: 'recr5xWF9r8sBBu9aO2m4GTf',
          data: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-11T07:19:22.790Z',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.21 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.21 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email Content: Refer to [Document Link]\n' +
              'Jump Link: Refer to [Next Article 1]\n' +
              '\n' +
              'Sending Time: November 04, 2024 @ 11:00 PM',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/A__Am8oo2bvnGk0qwuHDoLuhTqcfl-yqWn2RpVS2',
            fldPdLyWoU1OCPQRgRybExoN: [
              {
                id: 'tplatt8kBhSH1yfLyjyLPxdKmyG',
                name: 'Spark-【test】Bika-ai-v0-8-21-Release-Notes-Update-–-Discover-What-s-New-.png',
                path: 'template/tplatt8kBhSH1yfLyjyLPxdKmyG.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 465355,
              },
            ],
            fldQ0qRpOmAtGBh727zhpPOD: 'The content description layout is incorrect. Please revise it',
            fldTd1Og7TSB5APhuOSgHe1y: ['optfa77aWL9W8'],
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://bika.ai/en/help/release-notes/v0.8.21',
            fldy8XWPns8Zu1FqnN6XWB9F: 'usr04CQqKlWHlOkII5agWnLL',
          },
          values: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-05 10:59',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.21 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.21 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email Content: Refer to [Document Link]\n' +
              'Jump Link: Refer to [Next Article 1]\n' +
              '\n' +
              'Sending Time: November 04, 2024 @ 11:00 PM',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/A__Am8oo2bvnGk0qwuHDoLuhTqcfl-yqWn2RpVS2',
            fldPdLyWoU1OCPQRgRybExoN: ['Spark-【test】Bika-ai-v0-8-21-Release-Notes-Update-–-Discover-What-s-New-.png'],
            fldQ0qRpOmAtGBh727zhpPOD: 'The content description layout is incorrect. Please revise it',
            fldTd1Og7TSB5APhuOSgHe1y: ['PASS'],
            fldmwuaNen6ZeFBJ49xqN9Cd: 'pengjin',
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://bika.ai/en/help/release-notes/v0.8.21',
            fldy8XWPns8Zu1FqnN6XWB9F: 'pengjin',
          },
        },
        {
          templateId: 'recdoH5sYdBMOpORL9f3IUVb',
          data: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-11T07:19:22.791Z',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.22 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.22 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email Content: Refer to [Document Link]\n' +
              'Jump Link: Refer to [Next Article 1] (Since the release has not been deployed yet, view the content using the staging environment link; the email will contain the official environment link)\n' +
              '\n' +
              'Sending Time: November 11, 2024 @ 11:00 PM (If the release is delayed, manually trigger the email after deployment)',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/oNFd68Aqzp7mW1ak_-nnpckGw_Hq1EVqEk5pqxzu',
            fldPdLyWoU1OCPQRgRybExoN: [
              {
                id: 'tplattwHeLkxUxsC92D5zlSY8ZZ',
                name: 'Spark-【test】Bika-aiv0-8-22ReleaseNotesUpdate–DiscoverWhat-sNew- (2).png',
                path: 'template/tplattwHeLkxUxsC92D5zlSY8ZZ.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 618976,
              },
            ],
            fldTd1Og7TSB5APhuOSgHe1y: ['opty3EvVPvLr6'],
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://staging.bika.ai/en/help/release-notes/v0.8.22',
            fldy8XWPns8Zu1FqnN6XWB9F: 'usr04CQqKlWHlOkII5agWnLL',
          },
          values: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-05 10:59',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.22 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.22 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email Content: Refer to [Document Link]\n' +
              'Jump Link: Refer to [Next Article 1] (Since the release has not been deployed yet, view the content using the staging environment link; the email will contain the official environment link)\n' +
              '\n' +
              'Sending Time: November 11, 2024 @ 11:00 PM (If the release is delayed, manually trigger the email after deployment)',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/oNFd68Aqzp7mW1ak_-nnpckGw_Hq1EVqEk5pqxzu',
            fldPdLyWoU1OCPQRgRybExoN: ['Spark-【test】Bika-aiv0-8-22ReleaseNotesUpdate–DiscoverWhat-sNew- (2).png'],
            fldTd1Og7TSB5APhuOSgHe1y: ['Rejected'],
            fldmwuaNen6ZeFBJ49xqN9Cd: 'pengjin',
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://staging.bika.ai/en/help/release-notes/v0.8.22',
            fldy8XWPns8Zu1FqnN6XWB9F: 'pengjin',
          },
        },
        {
          templateId: 'recDAv37MTuX3z2KWmATXXI3',
          data: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-11T07:19:22.791Z',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.23 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.23 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email content: Refer to [Document Link]\n' +
              'Jump link: Refer to [Next Article 1] (Since the release has not been deployed yet, view the content using the staging environment link; the email will contain the official environment link)\n' +
              '\n' +
              'Sending Time: November 18, 2024 @ 11:00 PM (If the release is delayed, manually trigger the email after deployment)',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/WTekidyVjQIAWDsw9m0-sSSqs5_T-KTkNUsB5eVx',
            fldPdLyWoU1OCPQRgRybExoN: [
              {
                id: 'tplattMAk1FmXiGVTxBHmlLMgp7',
                name: 'Spark-【test】Bika-aiv0-8-23ReleaseNotesUpdate–DiscoverWhat-sNew-.png',
                path: 'template/tplattMAk1FmXiGVTxBHmlLMgp7.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 700948,
              },
            ],
            fldQ0qRpOmAtGBh727zhpPOD: '',
            fldTd1Og7TSB5APhuOSgHe1y: ['optfa77aWL9W8'],
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://staging.bika.ai/en/help/release-notes/v0.8.23',
            fldy8XWPns8Zu1FqnN6XWB9F: 'usr04CQqKlWHlOkII5agWnLL',
          },
          values: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-05 11:15',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.8.23 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.8.23 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email content: Refer to [Document Link]\n' +
              'Jump link: Refer to [Next Article 1] (Since the release has not been deployed yet, view the content using the staging environment link; the email will contain the official environment link)\n' +
              '\n' +
              'Sending Time: November 18, 2024 @ 11:00 PM (If the release is delayed, manually trigger the email after deployment)',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/WTekidyVjQIAWDsw9m0-sSSqs5_T-KTkNUsB5eVx',
            fldPdLyWoU1OCPQRgRybExoN: ['Spark-【test】Bika-aiv0-8-23ReleaseNotesUpdate–DiscoverWhat-sNew-.png'],
            fldQ0qRpOmAtGBh727zhpPOD: '',
            fldTd1Og7TSB5APhuOSgHe1y: ['PASS'],
            fldmwuaNen6ZeFBJ49xqN9Cd: 'pengjin',
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://staging.bika.ai/en/help/release-notes/v0.8.23',
            fldy8XWPns8Zu1FqnN6XWB9F: 'pengjin',
          },
        },
        {
          templateId: 'recKgX7Fh0iepyZGQC9FAjG2',
          data: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-11T07:19:22.791Z',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.9.1 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.9.1 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email Content: Refer to [Document Link]\n' +
              'Jump Link: Refer to [Next Article 1] (Since the release has not been deployed yet, view the content using the staging environment link; the email will contain the official environment link)\n' +
              '\n' +
              'Sending Time: December 02, 2024 @ 11:00 PM (If the release is delayed, manually trigger the email after deployment)',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/2WJarUQItEsvnIar0djYkwNdB5gxR0Zb_HcfVqjt',
            fldPdLyWoU1OCPQRgRybExoN: [
              {
                id: 'tplatt0URNXro7w9tR02BhbYp5H',
                name: 'Spark-【test】Bika-aiv0-9-1ReleaseNotesUpdate–DiscoverWhat-sNew-.png',
                path: 'template/tplatt0URNXro7w9tR02BhbYp5H.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 596008,
              },
            ],
            fldQ0qRpOmAtGBh727zhpPOD: 'The content description layout is incorrect. Please revise it',
            fldTd1Og7TSB5APhuOSgHe1y: ['optfa77aWL9W8'],
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://staging.bika.ai/en/help/release-notes/v0.9.1',
            fldy8XWPns8Zu1FqnN6XWB9F: 'usr04CQqKlWHlOkII5agWnLL',
          },
          values: {
            fld7QXf8rzbcNaa0kcy3LN7x: '2025-03-05 11:41',
            fldHyFhkKYDw88747REk4OXa:
              '[Bika] v0.9.1 Version Update Email\n' +
              '\n' +
              "Subject: Bika.ai v0.9.1 Release Notes Update – Discover What's New!\n" +
              '\n' +
              'Style: See attachment\n' +
              '\n' +
              'Email Content: Refer to [Document Link]\n' +
              'Jump Link: Refer to [Next Article 1] (Since the release has not been deployed yet, view the content using the staging environment link; the email will contain the official environment link)\n' +
              '\n' +
              'Sending Time: December 02, 2024 @ 11:00 PM (If the release is delayed, manually trigger the email after deployment)',
            fldNwvfUuIE9cbGftRvxcaC3: 'https://app.sparkmailapp.com/web-share/2WJarUQItEsvnIar0djYkwNdB5gxR0Zb_HcfVqjt',
            fldPdLyWoU1OCPQRgRybExoN: ['Spark-【test】Bika-aiv0-9-1ReleaseNotesUpdate–DiscoverWhat-sNew-.png'],
            fldQ0qRpOmAtGBh727zhpPOD: 'The content description layout is incorrect. Please revise it',
            fldTd1Og7TSB5APhuOSgHe1y: ['PASS'],
            fldmwuaNen6ZeFBJ49xqN9Cd: 'pengjin',
            fldx7N3JTxUIb8KFzq7eGWiB: 'https://staging.bika.ai/en/help/release-notes/v0.9.1',
            fldy8XWPns8Zu1FqnN6XWB9F: 'pengjin',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
