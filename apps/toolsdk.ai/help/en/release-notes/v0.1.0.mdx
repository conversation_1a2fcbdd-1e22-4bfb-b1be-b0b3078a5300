---
sidebar_position: -909
sidebar_label: v0.1.0
title: v0.1.0 Release Notes
---

# v0.1.0 Release Notes

Release Date: [Date]

## 🚀 New Features

- **ToolSDK.ai Introduction**: Initial release of the ToolSDK.ai platform, an open-source AI Tools SDK for Hosted MCP Servers.
- **MCP Server Hosting**: ToolSDK.ai now hosts 5000+ MCP servers as MCP-as-a-Service.
- **MCP Hub Server**: Introduction of the MCP Hub Server, acting as a proxy to connect all hosted MCP servers — one MCP server to rule them all.

## ✨ Highlights

- **Easy Integration**: Instant access to 5000+ hosted MCP servers for popular AI clients like Claude Desktop, Cursor, and OpenAI Desktop.
- **Developer-Friendly**: SDK support for rapid integration of third-party applications and AI tools.
- **MCP Router Server**: Streamlined access to preferred MCP servers via a single configuration point — automatically routes to your starred servers.

## 🔧 Core Features

- **Easy Connection**: Connect to hosted MCP servers without running local code.
- **Remote MCP Server Access**: Use a single MCP server in clients like <PERSON> or <PERSON>urs<PERSON> to access AI tools across the ecosystem.
- **Extensible Architecture**: Support for ToolApp (Zapier-compatible) and MCP Server packages.
- **Secure Authentication**: Credential-based login to ensure secure usage of tools.

## 📖 Documentation

- Initial documentation setup including:
  - [What is ToolSDK.ai?](#)
  - Getting started guides for developers and AI clients.
  - Example project for quick setup and integration.

## 📎 Example Project

- A basic example project is available to demonstrate how to use ToolSDK.ai with AI SDKs like Vercel AI SDK:
  ```bash
  git clone https://github.com/toolsdk-ai/toolsdk
  npm install
  npm run example
  ```

## 📌 Installation

Install the SDK using npm or yarn:
```bash
npm install toolsdk
# or
yarn add toolsdk
```

For more information, see the [ToolSDK.ai Documentation](#).