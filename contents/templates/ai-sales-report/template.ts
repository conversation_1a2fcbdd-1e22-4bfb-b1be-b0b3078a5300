import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'ai-sales-report',
  name: {
    en: 'AI Sales Report',
    'zh-CN': 'AI 销售报告',
    'zh-TW': 'AI 銷售報告',
    ja: 'AI 売上レポート',
  },
  description: {
    en: 'Automatically generate a sales report for store managers based on the past 7 days of sales data.',
    'zh-CN': '根据门店过去7天的销售数据，自动生成店长专属的销售报告。',
    'zh-TW': '根據門店過去7天的銷售數據，自動生成店長專屬的銷售報告。',
    ja: '過去7日間の売上データに基づいて、店舗マネージャー向けの売上レポートを自動的に生成します。',
  },
  cover: '/assets/template/cover-ai-sales-report.jpg',
  author: 'Kelvin Poon <<EMAIL>>',
  category: ['ai', 'automation', 'sales'],
  schemaVersion: 'v1',
  version: '1.0.2',
  resources: [
    {
      resourceType: 'FORM',
      templateId: 'form_submit_sales_data',
      name: {
        en: 'Submit Sales Data',
        'zh-CN': '提交销售数据',
        'zh-TW': '提交銷售數據',
        ja: '売上データを提出する',
      },
      description: {
        en: 'Submit daily sales data to track store performance.',
        'zh-CN': '提交每日销售数据，跟踪门店业绩。',
        'zh-TW': '提交每日銷售數據，跟蹤門店業績。',
        ja: '毎日の売上データを提出して、店舗のパフォーマンスを追跡します。',
      },
      formType: 'DATABASE',
      databaseTemplateId: 'database_sales_record',
      metadata: {
        type: 'VIEW',
        viewTemplateId: 'view_for_form',
      },
    },
    {
      resourceType: 'DATABASE',
      templateId: 'database_sales_record',
      name: {
        en: 'Sales Record',
        'zh-CN': '销售记录',
        'zh-TW': '銷售記錄',
        ja: '売上記録',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'view_all',
          name: {
            en: 'All',
            'zh-CN': '全部',
            'zh-TW': '全部',
            ja: 'すべて',
          },
        },
        {
          type: 'TABLE',
          templateId: 'view_for_form',
          name: {
            en: 'For Form',
            'zh-CN': '表单用',
            'zh-TW': '表單用',
            ja: 'フォーム用',
          },
          sorts: [
            {
              fieldTemplateId: 'field_date',
              asc: false,
            },
          ],
          fields: [
            {
              templateId: 'field_date',
              hidden: false,
            },
            {
              templateId: 'field_store_name',
              hidden: false,
            },
            {
              templateId: 'field_sales_volume',
              hidden: false,
            },
            {
              templateId: 'field_sales_amount',
              hidden: false,
            },
            {
              templateId: 'field_sales_target',
              hidden: false,
            },
            {
              templateId: 'field_sales_target_achieved',
              hidden: true,
            },
            {
              templateId: 'field_manager',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'view_last_week',
          name: {
            en: 'Last Week',
            'zh-CN': '上周',
            'zh-TW': '上週',
            ja: '先週',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'field_date',
                fieldType: 'DATETIME',
                clause: {
                  operator: 'Is',
                  value: ['PreviousWeek'],
                },
              },
            ],
          },
        },
      ],
      fields: [
        {
          type: 'DATETIME',
          templateId: 'field_date',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Date',
            'zh-CN': '日期',
            'zh-TW': '日期',
            ja: '日付',
          },
          required: true,
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: true,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'field_store_name',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Store Name',
            'zh-CN': '门店名称',
            'zh-TW': '門店名稱',
            ja: '店舗名',
          },
          primary: false,
        },
        {
          type: 'NUMBER',
          templateId: 'field_sales_volume',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Quantity Sold',
            'zh-CN': '销量',
            'zh-TW': '銷量',
            ja: '売上数量',
          },
          description: {
            en: 'The number of items sold.',
            'zh-CN': '售出的商品数量。',
            'zh-TW': '售出的商品數量。',
            ja: '販売された商品の数。',
          },
          property: {
            precision: 0,
            commaStyle: 'thousand',
            symbol: '',
            symbolAlign: 'right',
          },
          primary: false,
        },
        {
          type: 'CURRENCY',
          templateId: 'field_sales_amount',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Sales Amount',
            'zh-CN': '销售额',
            'zh-TW': '銷售額',
            ja: '売上',
          },
          description: {
            en: 'The total amount of sales in one day.',
            'zh-CN': '一天内的销售总额。',
            'zh-TW': '一天內的銷售總額。',
            ja: '1日の売上総額。',
          },
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '$',
            symbolAlign: 'left',
          },
          primary: false,
        },
        {
          type: 'CURRENCY',
          templateId: 'field_sales_target',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Sales Target',
            'zh-CN': '销售额目标',
            'zh-TW': '銷售額目標',
            ja: '売上目標',
          },
          description: {
            en: 'The sales target for the day.',
            'zh-CN': '当天的销售目标。',
            'zh-TW': '當天的銷售目標。',
            ja: 'その日の売上目標。',
          },
          required: true,
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '$',
            symbolAlign: 'left',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'field_sales_target_achieved',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Sales Target Achieved (%)',
            'zh-CN': '销售额达成率',
            'zh-TW': '銷售額達成率',
            ja: '売上達成率',
          },
          property: {
            expressionTemplate: 'Round({field_sales_amount}/{field_sales_target}*100, 2) + "%"',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'field_manager',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Manager',
            'zh-CN': '店长',
            'zh-TW': '店長',
            ja: '店長',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recfM0tLzoRCfxeuYwOHlPtJ',
          data: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-03T06:34:18.409Z',
            field_sales_target_achieved: '83.33%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: 150,
            field_sales_target: 15000,
            field_sales_amount: 12500,
          },
          values: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-03',
            field_sales_target_achieved: '83.33%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: '150',
            field_sales_target: '$15000',
            field_sales_amount: '$12500',
          },
        },
        {
          templateId: 'recoJRaqDlfWq6mJEaLNJ6KF',
          data: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-04T06:34:18.409Z',
            field_sales_target_achieved: '92%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: 160,
            field_sales_target: 15000,
            field_sales_amount: 13800,
          },
          values: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-04',
            field_sales_target_achieved: '92%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: '160',
            field_sales_target: '$15000',
            field_sales_amount: '$13800',
          },
        },
        {
          templateId: 'recEIVDs0F8ddSDR5q7R15tj',
          data: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-05T06:34:18.409Z',
            field_sales_target_achieved: '109.33%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: 180,
            field_sales_target: 15000,
            field_sales_amount: 16400,
          },
          values: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-05',
            field_sales_target_achieved: '109.33%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: '180',
            field_sales_target: '$15000',
            field_sales_amount: '$16400',
          },
        },
        {
          templateId: 'recEMTsDyTwwLcjvs2GI3xcB',
          data: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-06T06:34:18.409Z',
            field_sales_target_achieved: '84%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: 220,
            field_sales_target: 15000,
            field_sales_amount: 12600,
          },
          values: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-06',
            field_sales_target_achieved: '84%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: '220',
            field_sales_target: '$15000',
            field_sales_amount: '$12600',
          },
        },
        {
          templateId: 'recIP8t9CAIafm943CqVchTq',
          data: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-07T06:34:18.409Z',
            field_sales_target_achieved: '104.67%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: 190,
            field_sales_target: 15000,
            field_sales_amount: 15700,
          },
          values: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-07',
            field_sales_target_achieved: '104.67%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: '190',
            field_sales_target: '$15000',
            field_sales_amount: '$15700',
          },
        },
        {
          templateId: 'rec0lOH2gbqkMhiv4D49MHRC',
          data: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-08T06:34:18.409Z',
            field_sales_target_achieved: '74%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: 170,
            field_sales_target: 15000,
            field_sales_amount: 11100,
          },
          values: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-08',
            field_sales_target_achieved: '74%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: '170',
            field_sales_target: '$15000',
            field_sales_amount: '$11100',
          },
        },
        {
          templateId: 'recGFYTghRFPtX5zPIXVOo0l',
          data: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-09T06:34:18.409Z',
            field_sales_target_achieved: '106.67%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: 200,
            field_sales_target: 15000,
            field_sales_amount: 16000,
          },
          values: {
            field_manager: 'Amy Miller',
            field_date: '2025-01-09',
            field_sales_target_achieved: '106.67%',
            field_store_name: "Amy's Bakehouse",
            field_sales_volume: '200',
            field_sales_target: '$15000',
            field_sales_amount: '$16000',
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atodurzXT3iDP1DWMHtj431e',
      name: {
        en: 'Generate Weekly AI Sales Report',
        'zh-CN': '生成AI销售周报',
        'zh-TW': '生成AI銷售週報',
        ja: '週次AI売上レポートを生成',
      },
      description: {
        en: 'Automatically generate a sales report for store managers based on the past 7 days of sales data.',
        'zh-CN': '根据门店过去7天的销售数据，自动生成店长专属的销售报告。',
        'zh-TW': '根據門店過去7天的銷售數據，自動生成店長專屬的銷售報告。',
        ja: '過去7日間の売上データに基づいて、店舗マネージャー向けの売上レポートを自動的に生成します。',
      },
      triggers: [
        {
          triggerType: 'SCHEDULER',
          description: {
            en: 'Every Monday at 8:00 AM',
            'zh-CN': '每周一上午8点',
            'zh-TW': '每週一上午8點',
            ja: '毎週月曜日の朝8時',
          },
          templateId: 'trigger_scheduler',
          input: {
            type: 'SCHEDULER',
            scheduler: {
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['MON'],
                },
              },
              timezone: 'AUTO',
              datetime: {
                type: 'TODAY',
                hour: 8,
                minute: 0,
              },
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'action_retrieve_sales_records',
          description: {
            en: 'Retrieve sales records from the past 7 days.',
            'zh-CN': '获取过去7天的销售记录。',
            'zh-TW': '獲取過去7天的銷售記錄。',
            ja: '過去7日間の売上記録を取得します。',
          },
          actionType: 'FIND_RECORDS',
          input: {
            interruptIfNoRecord: true,
            type: 'DATABASE_VIEW',
            viewTemplateId: 'view_last_week',
            databaseTemplateId: 'database_sales_record',
          },
        },
        {
          templateId: 'action_generate_report_with_gpt',
          description: {
            en: 'Generate a sales report with GPT-4o-mini.',
            'zh-CN': '使用GPT-4o-mini生成销售报告。',
            'zh-TW': '使用GPT-4o-mini生成銷售報告。',
            ja: 'GPT-4o-miniを使用して売上レポートを生成します。',
          },
          actionType: 'OPENAI_GENERATE_TEXT',
          input: {
            urlType: 'INTEGRATION',
            type: 'OPENAI_GENERATE_TEXT',
            prompt: `
# Character
You're a data analyst specialized in generating sales reports for retail stores. You excel in analyzing sales data and providing actionable insights to store managers.      

# Skills      
Skill 1: Generate weekly sales report          
- Analyze the past 7 days of sales data.          
Skill 2: Trend analysis          
- Assess whether sales targets need adjustment based on performance.          
Skill 3: Provide improvement suggestions          
- Offer actionable recommendations based on the analysis.            
- Address specific questions related to sales performance.      

# Constraints:      
- Follow the provided report template format.            
- Keep suggestions relevant and actionable.            
- Use clear and concise language for the report.         

# Example
- **Time Range**: <Past 7 days dates>
- **Total Sales Amount**: <Total sales amount>
- **Average Sales Amount**: <Average sales amount>
- **Total Sales Volume**: <Total sales volume>
## Trend Analysis:
- The highest sales occurred on <highest sales date>, possibly due to <possible reasons>.
- The lowest sales occurred on <lowest sales date>, possibly due to <possible reasons>.
- The median achievement rate is <median achievement rate>, suggesting a review of sales targets may be needed.
- Is sales volume trending up, down, or experiencing sudden peaks?
## Areas for Improvement and Suggestions:
- To address <specific issue>, consider <suggestion> to enhance sales performance.
- Consider <other suggestions> to optimize store operations.

# Sales Data

Daily sales data for the store:
<%= _renderRecordsAsGrid(_actions.action_retrieve_sales_records.records, ['field_date','field_store_name','field_sales_volume','field_sales_amount','field_sales_target','field_sales_target_achieved','field_manager']) %>
`,
            model: 'gpt-4o-mini',
          },
        },
        {
          templateId: 'action_send_report',
          description: {
            en: 'Send the generated sales report to store managers.',
            'zh-CN': '将生成的销售报告发送给店长。',
            'zh-TW': '將生成的銷售報告發送給店長。',
            ja: '生成された売上レポートを店舗マネージャーに送信します。',
          },
          actionType: 'SEND_REPORT',
          input: {
            to: [
              {
                type: 'ADMIN',
              },
              {
                type: 'CURRENT_OPERATOR',
              },
            ],
            markdown: `
Hi, store manager. Here is the summary of the store's performance over the past 7 days:
<%= _actions.action_generate_report_with_gpt.body.choices[0].message.content %>

## Store Data
Sales data for the store over the past 7 days:
<%= _renderRecordsAsGrid(_actions.action_retrieve_sales_records.records, ['field_date','field_store_name','field_sales_volume','field_sales_target','field_sales_amount','fldOB1swQC3FUYTg94ohmojh','field_sales_target_achieved','field_manager']) %>
`,
            subject: 'Weekly Sales Report',
            type: 'MARKDOWN',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
