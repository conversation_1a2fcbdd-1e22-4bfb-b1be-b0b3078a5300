import type { Locale } from '@bika/contents/i18n';
import { getServerDictionary } from '@bika/contents/i18n/server';
import { AuthController } from '@bika/domains/auth/apis';
import { generateLanguageUrls } from '@bika/domains/shared/client/utils';
import { SiteSsoSO } from '@bika/domains/system/server/site-sso-so';
import { UserSO } from '@bika/domains/user/server/user-so';
import { postprocessPageMetadata } from '@bika/domains/website/server/utils';
import type { Metadata } from 'next';
import { headers } from 'next/headers';
import { redirect } from 'next/navigation';
import React from 'react';
import { getLocaleByHeaders } from 'sharelib/next-utils/get-locale-from-headers';
import AuthLoginClientPage from './client-auth-login-page';

interface Props {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

/**
 * Server Component里嵌入Client Component
 * 没会话的情况下，都会到这里判断
 */
export default async function AuthLoginServerPage(props: Props) {
  const auth = await AuthController.getAuthByNextRequest(await headers());
  const searchParams = await props.searchParams;
  const redirectPath = (searchParams.redirect as string) || '/space';
  const referralCode = searchParams.referralCode as string | undefined;

  // /space/join 开头，就不快速登录
  // (带有referral code时，可以快速登录，但要绑定社交账号后才激活奖励)
  // 好像这里不会存在
  const ignoreQuickLogin = redirectPath.startsWith('/space/join');
  let inviteName: string | undefined;
  // 有邀请码？定制一个title给登录界面
  if (referralCode) {
    const referralUser = await UserSO.getUserByReferralCode(referralCode);
    inviteName = referralUser ? referralUser.name : undefined;
  }

  if (auth && auth.user && auth.session) {
    const user = await UserSO.init(auth.user.id);
    if (user.hasAccountBindRequired()) {
      redirect(redirectPath);
    } else {
      return (
        <AuthLoginClientPage
          ignoreQuickLogin={ignoreQuickLogin}
          referralCode={referralCode}
          inviteName={inviteName}
          redirect={redirectPath}
        />
      );
    }
    // 已登录，直接跳走 （默认/space)
  } else {
    // 是否开启了站点SSO登录, 开启则跳转到SSO登录, 这里是实时获取
    const siteSSOInfo = await SiteSsoSO.getSiteSsoInfo();
    const siteSSOLogin = siteSSOInfo && siteSSOInfo.enabled;

    // 如果是站点SSO登录,则跳转到SSO登录. 仅允许 auth?redirect=/bika-admin 打开登录页面
    if (redirectPath !== '/bika-admin' && siteSSOLogin) {
      // 站点SSO登录，跳转到SSO登录
      redirect(`/auth/saml/login?redirect=${redirectPath}`);
    }

    return (
      <AuthLoginClientPage
        ignoreQuickLogin={ignoreQuickLogin}
        siteSSOLogin={siteSSOLogin}
        referralCode={referralCode}
        inviteName={inviteName}
        redirect={redirectPath}
      />
    );
  }
}

export async function generateMetadata(): Promise<Metadata> {
  const locale = await getLocaleByHeaders(await headers());
  const lang = locale as Locale;
  const dict = getServerDictionary(lang);

  const sloganTitle = dict.slogan.slogan_title;
  const metadata = postprocessPageMetadata(
    {
      title: sloganTitle,
      description: dict.slogan.slogan_prd_xl,
      keywords: dict.slogan.keywords,
    },
    lang,
    false,
  );

  return {
    ...metadata,
    alternates: {
      languages: await generateLanguageUrls(`blog`),
    },
  };
}
