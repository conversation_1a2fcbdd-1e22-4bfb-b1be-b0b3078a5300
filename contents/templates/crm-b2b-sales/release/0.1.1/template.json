{"templateId": "crm-b2b-sales", "name": {"en": "B2B AI CRM", "zh-CN": "B2B客户管理与销售追踪", "zh-TW": "B2B客戶管理與銷售追蹤", "ja": "B2B顧客管理と販売追跡"}, "description": {"zh-CN": "AI自动化管理好你的客户，敦促您或您的销售团队每周写好拜访记录，适合B2B面向企业组织的销售团队。", "en": "AI automation to manage your clients, prompt you or your sales team to write visit records every week, suitable for B2B sales teams targeting enterprise organizations.", "zh-TW": "AI自動化管理好你的客戶，敦促您或您的銷售團隊每周寫好拜訪記錄，適合B2B面向企業組織的銷售團隊。", "ja": "AI自動化で顧客を管理し、あなたまたはあなたの営業チームに毎週訪問記録を書くよう促します。企業組織をターゲットにしたB2B営業チームに適しています。"}, "cover": "/assets/template/template-cover-crm-b2b-sales.png", "copyright": "Copyright by Vika Limited, AGPL", "author": "<PERSON> <<EMAIL>>, <PERSON> <<PERSON><PERSON><PERSON><PERSON><PERSON>@vikadata.com>, <PERSON><PERSON> <<EMAIL>>", "category": ["official", "sales"], "keywords": {"en": "B2B CRM, customer management, sales tracking, enterprise sales, client management, business-to-business", "zh-CN": "B2B客户管理, 销售追踪, 企业销售, 客户管理, 工商业务", "zh-TW": "B2B客戶管理, 銷售追蹤, 企業銷售, 客戶管理, 工商業務", "ja": "B2B顧客管理, 販売追跡, 企業営業, クライアント管理, ビジネス対ビジネス"}, "personas": {"en": "Manufacturing Sales Manager, Finance Marketing Director, Healthcare Business Development Manager, IT Sales Manager, Retail Regional Sales Manager, Real Estate Sales Director, Education Marketing Operations Specialist, Transportation & Logistics Sales Manager, Government Procurement Officer, Energy Business Development Specialist, Hospitality Marketing Manager, Construction Marketing Manager, Agriculture Sales Manager, Food & Beverage Sales Specialist, Chemical Industry Sales Director, Media & Entertainment Sales Manager, Automotive Market Development Specialist, Telecommunications Marketing Director, Medical Devices Sales Manager, Advertising & Marketing Sales Manager, SaaS Sales, Telesales, SDR, Sales Manager, Enterprise Executive, SMB Owner, Startup Founder, Sales Team Manager, Marketing Manager, Business Development Manager, Customer Relationship Management Specialist, Marketing Analyst", "zh-CN": "制造业销售经理,金融业营销总监,医疗行业业务拓展经理,IT行业销售主管,零售业区域销售经理,房地产销售总监,教育行业市场运营专员,运输物流销售主管,政府采购人员,能源行业业务拓展专员,酒店行业市场部经理,建筑业营销主管,农业销售经理,餐饮行业销售专员,化工行业销售总监,文化传媒销售经理,汽车行业市场开发专员,通信行业营销总监,医疗器械销售主管,广告营销销售经理,SaaS软件销售,电话销售,SDR,销售经理,大型企业高管,中小企业老板,初创公司创始人,销售团队主管,营销主管,业务拓展经理,客户关系管理专员,营销分析师", "zh-TW": "製造業銷售經理,金融業行銷總監,醫療行業業務拓展經理,IT行業銷售主管,零售業區域銷售經理,房地產銷售總監,教育行業市場運營專員,運輸物流銷售主管,政府採購人員,能源行業業務拓展專員,酒店行業市場部經理,建築業行銷主管,農業銷售經理,餐飲行業銷售專員,化工行業銷售總監,文化傳媒銷售經理,汽車行業市場開發專員,通信行業行銷總監,醫療器械銷售主管,廣告行銷銷售經理,SaaS軟體銷售,電話銷售,SDR,銷售經理,大型企業高管,中小企業老闆,初創公司創始人,銷售團隊主管,行銷主管,業務拓展經理,客戶關係管理專員,行銷分析師", "ja": "製造業セールスマネージャー,金融業マーケティングディレクター,ヘルスケア事業開発マネージャー,ITセールスマネージャー,小売地域セールスマネージャー,不動産販売ディレクター,教育マーケティング オペレーションスペシャリスト,輸送物流セールスマネージャー,政府調達オフィサー,エネルギー事業開発スペシャリスト,ホテルマーケティングマネージャー,建設マーケティングマネージャー,農業セールスマネージャー,飲食セールススペシャリスト,化学工業セールスディレクター,メディア&エンターテイメントセールスマネージャー,自動車市場開発スペシャリスト,通信マーケティングディレクター,医療機器セールスマネージャー,広告&マーケティングセールスマネージャー,SaaSセールス,テレセールス,SDR,セールスマネージャー,企業エグゼクティブ,中小企業オーナー,スタートアップ創業者,セールスチームマネージャー,マーケティングマネージャー,事業開発マネージャー,顧客関係管理スペシャリスト,マーケティング分析師"}, "useCases": {"zh-CN": "制造业销售经理-管理销售流程, 预测销售, 跟踪客户互动, 优化销售策略,金融业营销总监-构建客户关系网络, 规划营销活动, 分析客户画像, 个性化营销,医疗行业业务拓展经理-开发新客户, 跟踪销售线索, 管理合作伙伴关系, 提升销售转化率,IT行业销售主管-管理销售团队, 制定销售目标, 监控销售指标, 优化销售流程,零售业区域销售经理-协调门店销售, 管理客户关系, 分析客户消费习惯, 制定促销策略,房地产销售总监-管理销售团队, 预测销售业绩, 开发营销渠道, 提升客户满意度,教育行业市场运营专员-开展市场调研, 规划营销活动, 管理学生/家长信息, 优化招生渠道,运输物流销售主管-开发新客户, 维护客户关系, 跟踪销售机会, 提升服务质量,政府采购人员-管理供应商信息, 跟踪采购需求, 优化采购流程, 提高采购效率,能源行业业务拓展专员-开发潜在客户, 管理销售线索, 跟踪销售进度, 提高成交率,酒店行业市场部经理-制定营销策略, 管理客户数据, 优化预订渠道, 提升客户忠诚度,建筑业营销主管-管理投标信息, 跟踪项目进度, 维护供应商关系, 提高中标率,农业销售经理-管理经销商/代理商关系, 分析市场需求, 优化产品组合, 提高销量,餐饮行业销售专员-开拓新客户, 管理订单信息, 分析客户消费习惯, 提升客户满意度,化工行业销售总监-制定销售目标, 管理销售团队, 优化销售渠道, 提高销售业绩", "en": "Manufacturing Sales Manager - manage the sales process, forecast sales, track customer interactions, optimize sales strategies, Financial Marketing Director - build customer relationship network, plan marketing activities, analyze customer profiles, implement personalized marketing, Medical Industry Business Development Manager - develop new customers, track sales leads, manage partner relationships, improve sales conversion rate, IT Sales Supervisor - manage sales team, set sales targets, monitor sales metrics, optimize sales processes, Retail Regional Sales Manager - coordinate store sales, manage customer relationships, analyze customer buying habits, develop promotional strategies, Real Estate Sales Director - manage sales team, forecast sales performance, develop marketing channels, improve customer satisfaction, Education Market Operations Specialist - conduct market research, plan marketing activities, manage student/parent information, optimize enrollment channels, Transportation & Logistics Sales Supervisor - develop new customers, maintain customer relationships, track sales opportunities, improve service quality, Government Procurement Specialist - manage supplier information, track procurement needs, optimize procurement processes, improve procurement efficiency, Energy Industry Business Development Specialist - develop potential customers, manage sales leads, track sales progress, increase conversion rate, Hotel Marketing Manager - develop marketing strategies, manage customer data, optimize booking channels, improve customer loyalty, Construction Marketing Supervisor - manage bidding information, track project progress, maintain supplier relationships, improve win rate, Agricultural Sales Manager - manage distributor/agent relationships, analyze market demand, optimize product portfolio, increase sales volume, Food & Beverage Sales Specialist - develop new customers, manage order information, analyze customer buying habits, improve customer satisfaction, Chemical Industry Sales Director - set sales targets, manage sales team, optimize sales channels, improve sales performance.", "zh-TW": "製造業銷售經理 - 管理銷售流程, 預測銷售, 追蹤客戶互動, 優化銷售策略, 財務行銷總監 - 建立客戶關係網路, 規劃行銷活動, 分析客戶概況, 實施個人化行銷, 醫療行業業務發展經理 - 開發新客戶, 追蹤銷售線索, 管理合作夥伴關係, 提高銷售轉化率, IT銷售主管 - 管理銷售團隊, 設定銷售目標, 監控銷售指標, 優化銷售流程, 零售區域銷售經理 - 協調門店銷售, 管理客戶關係, 分析客戶購買習慣, 制定促銷策略, 房地產銷售總監 - 管理銷售團隊, 預測銷售表現, 開發行銷渠道, 提高客戶滿意度, 教育市場營運專員 - 進行市場調研, 規劃行銷活動, 管理學生/家長資訊, 優化招生管道, 運輸物流銷售主管 - 開發新客戶, 維護客戶關係, 跟蹤銷售機會, 提高服務品質, 政府採購專員 - 管理供應商資訊, 跟蹤採購需求, 優化採購流程, 提高採購效率, 能源行業業務發展專員 - 開發潛在客戶, 管理銷售線索, 跟蹤銷售進度, 提高轉化率, 酒店行銷經理 - 制定行銷策略, 管理客戶數據, 優化訂房管道, 提高客戶忠誠度, 建築行銷主管 - 管理投標資訊, 跟蹤項目進度, 維護供應商關係, 提高中標率, 農業銷售經理 - 管理經銷商/代理商關係, 分析市場需求, 優化產品組合, 提高銷量, 食品飲料銷售專員 - 開發新客戶, 管理訂單資訊, 分析客戶購買習慣, 提高客戶滿意度, 化工行業銷售總監 - 設定銷售目標, 管理銷售團隊, 優化銷售渠道, 提高銷售績效", "ja": "製造業販売マネージャー - 販売プロセスを管理, 売上予測, 顧客対応を追跡, 販売戦略を最適化, 金融マーケティングディレクター - 顧客関係ネットワークを構築, マーケティング活動を計画, 顧客プロファイルを分析, パーソナライズドマーケティングを実施, 医療業界ビジネス開発マネージャー - 新規顧客を開発, 販売リードを追跡, パートナー関係を管理, 販売コンバージョン率を改善, ITセールスリーダー - 販売チームを管理, 販売目標を設定, 販売メトリクスを監視, 販売プロセスを最適化, 小売地域販売マネージャー - 店舗販売を調整, 顧客関係を管理, 顧客の購買習慣を分析, プロモーション戦略を開発, 不動産販売ディレクター - 販売チームを管理, 販売実績を予測, マーケティングチャネルを開発, 顧客満足度を向上, 教育市場オペレーションスペシャリスト - 市場調査を実施, マーケティング活動を計画, 学生/保護者情報を管理, エンロールチャネルを最適化, 運輸物流販売リーダー - 新規顧客を開発, 顧客関係を維持, 販売機会を追跡, サービス品質を改善, 政府調達スペシャリスト - サプライヤー情報を管理, 調達ニーズを追跡, 調達プロセスを最適化, 調達効率を向上, エネルギー業界ビジネス開発スペシャリスト - 潜在顧客を開発, 販売リードを管理, 販売進捗を追跡, コンバージョン率を向上, ホテルマーケティングマネージャー - マーケティング戦略を開発, 顧客データを管理, 予約チャネルを最適化, 顧客ロイヤルティを向上, 建設マーケティングスーパーバイザー - 入札情報を管理, プロジェクトの進捗を追跡, サプライヤー関係を維持, 受注率を向上, 農業販売マネージャー - 販売代理店/代理店の関係を管理, 市場需要を分析, 製品ポートフォリオを最適化, 売上高を増加, 飲料食品販売スペシャリスト - 新規顧客を開発, 注文情報を管理, 顧客の購買習慣を分析, 顧客満足度を向上, 化学業界販売ディレクター - 販売目標を設定, 販売チームを管理, 販売チャネルを最適化, 販売成績を向上"}, "installOnce": true, "schemaVersion": "v1", "version": "0.1.1", "resources": [{"resourceType": "DATABASE", "templateId": "database_people", "name": {"en": "People", "zh-CN": "联系人", "zh-TW": "聯絡人", "ja": "連絡先"}, "description": {"en": "The communication person in the customer enterprise. This database records the contact information, position, department, etc. of the contact person.", "zh-CN": "联系人是客户企业里的对接沟通人，本库收录联系人的联系方式、职位、部门等信息。", "zh-TW": "聯絡人是客戶企業裡的對接溝通人，本庫收錄聯絡人的聯絡方式、職位、部門等信息。", "ja": "連絡先は、顧客企業のコミュニケーション担当者です。このデータベースには、連絡先の連絡先情報、役職、部署などが記録されています。"}, "type": "DATUM", "fields": [{"templateId": "field_name", "name": {"en": "Name", "zh-CN": "姓名", "zh-TW": "姓名", "ja": "名前"}, "type": "SINGLE_TEXT"}, {"templateId": "field_email", "name": {"en": "Email", "zh-CN": "邮箱", "zh-TW": "郵箱", "ja": "メールアドレス"}, "type": "EMAIL"}, {"templateId": "field_description", "name": {"zh-CN": "介绍", "en": "Description", "zh-TW": "介紹", "ja": "紹介"}, "type": "LONG_TEXT"}, {"templateId": "field_phone", "name": {"zh-CN": "电话号码", "en": "Phone", "zh-TW": "電話號碼", "ja": "電話番号"}, "type": "PHONE"}, {"templateId": "field_department", "name": {"zh-CN": "部门", "en": "Department", "zh-TW": "部門", "ja": "部署"}, "type": "LONG_TEXT"}, {"templateId": "field_position", "name": {"zh-CN": "职位", "en": "Position", "zh-TW": "職位", "ja": "ポジション"}, "type": "LONG_TEXT"}, {"templateId": "field_organization", "name": {"en": "Organization", "zh-CN": "所属企业", "zh-TW": "所屬企業", "ja": "組織"}, "description": {"en": "The organization to which the contact belongs.", "zh-CN": "联系人所属的企业。", "zh-TW": "聯絡人所屬的企業。", "ja": "連絡先が所属する組織。"}, "type": "ONE_WAY_LINK", "property": {"foreignDatabaseTemplateId": "crm-b2b-sales:database_organization"}}, {"templateId": "field_created_time", "name": {"en": "Created Time", "zh-CN": "创建时间", "zh-TW": "創建時間", "ja": "作成日時"}, "type": "CREATED_TIME", "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}}], "records": [{"data": {"field_name": "<PERSON>", "field_email": "<EMAIL>"}}, {"data": {"field_name": "<PERSON>", "field_email": "<EMAIL>"}}, {"data": {"field_name": "<PERSON>", "field_email": "<EMAIL>"}}]}, {"resourceType": "DATABASE", "templateId": "database_organization", "name": {"en": "Organization", "zh-CN": "客户企业", "zh-TW": "客戶企業", "ja": "組織"}, "description": {"en": "Records the basic information of the customer enterprise, such as the company name, scale, address, etc.", "zh-CN": "记录客户企业的基本信息，如公司名、规模、地址等。", "zh-TW": "記錄客戶企業的基本信息，如公司名、規模、地址等。", "ja": "顧客企業の基本情報を記録します。企業名、規模、住所など。"}, "permissions": [{"unit": {"type": "ROLE", "templateId": "role_sales_managers"}, "privilege": "FULL_ACCESS"}], "type": "DATUM", "views": [{"templateId": "view_my", "name": {"en": "My Clients", "zh-CN": "我的客户", "zh-TW": "我的客戶", "ja": "私の顧客"}, "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "field_creator", "operator": "Is", "fieldType": "CREATED_BY", "value": ["Self"]}]}, "type": "TABLE"}, {"templateId": "view_all", "name": {"en": "All", "zh-CN": "全部", "zh-TW": "全部", "ja": "すべて"}, "type": "TABLE"}], "fields": [{"templateId": "field_name", "name": {"en": "Name", "zh-CN": "公司名称", "zh-TW": "公司名稱", "ja": "会社名"}, "description": {"en": "Customer is an enterprise, organization, or individual with business dealings with us.", "zh-CN": "客户是与我方有业务来往的企业、团体或者个人。", "zh-TW": "客戶是與我方有業務來往的企業、團體或者個人。", "ja": "顧客は、当社と取引のある企業、団体、または個人です。"}, "type": "SINGLE_TEXT"}, {"templateId": "field_leads", "name": {"zh-CN": "线索来源", "en": "Lead Source", "zh-TW": "線索來源", "ja": "リードソース"}, "type": "SINGLE_SELECT", "property": {"options": [{"templateId": "1", "name": "网页客服", "color": "deepPurple"}, {"templateId": "2", "name": "自媒体", "color": "deepPurple"}, {"templateId": "3", "name": "线上活动", "color": "deepPurple"}, {"templateId": "4", "name": "线下活动", "color": "deepPurple"}, {"templateId": "6", "name": "搜索引擎", "color": "deepPurple"}, {"templateId": "7", "name": "潜客池", "color": "deepPurple"}, {"templateId": "10", "name": "模板订单", "color": "deepPurple"}, {"templateId": "11", "name": "机器人申请", "color": "deepPurple"}, {"templateId": "12", "name": "小程序申请", "color": "deepPurple"}, {"templateId": "13", "name": "新手问卷留资", "color": "deepPurple"}, {"templateId": "21", "name": "客户转介绍", "color": "deepPurple"}, {"templateId": "22", "name": "自有线索", "color": "deepPurple"}, {"templateId": "23", "name": "社群", "color": "deepPurple"}, {"templateId": "24", "name": "生态渠道", "color": "deepPurple"}, {"templateId": "25", "name": "SDR外呼（陌拜）", "color": "deepPurple"}]}}, {"templateId": "field_description", "name": {"en": "Description", "zh-CN": "描述", "zh-TW": "描述", "ja": "説明"}, "type": "LONG_TEXT"}, {"templateId": "field_scale", "name": {"zh-CN": "公司规模", "en": "Scale", "zh-TW": "公司規模", "ja": "会社規模"}, "type": "NUMBER", "property": {"precision": 0, "symbol": ""}}, {"templateId": "field_address", "name": {"zh-CN": "公司所在地址", "en": "Address", "zh-TW": "公司所在地址", "ja": "会社所在地"}, "type": "SINGLE_TEXT"}, {"templateId": "field_created_time", "name": {"en": "Created Time", "zh-CN": "创建时间", "zh-TW": "創建時間", "ja": "作成日時"}, "type": "CREATED_TIME", "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}}, {"templateId": "field_creator", "name": {"en": "Creator", "zh-CN": "创建人", "zh-TW": "創建人", "ja": "作成者"}, "type": "CREATED_BY"}], "records": [{"data": {"field_name": "Bika.Ai"}}, {"data": {"field_name": "Nana Tech"}}, {"data": {"field_name": "Alaxi Travel"}}]}, {"resourceType": "DATABASE", "templateId": "database_visit", "name": {"en": "Visit", "zh-CN": "拜访记录", "zh-TW": "拜訪記錄", "ja": "訪問記録"}, "description": {"en": "Record the visit records of the salesperson to the customer enterprise.", "zh-CN": "记录销售人员对客户企业的拜访记录。", "zh-TW": "記錄銷售人員對客戶企業的拜訪記錄。", "ja": "営業担当者が顧客企業を訪問した記録"}, "type": "DATUM", "views": [{"templateId": "view_my", "name": {"en": "My visits", "zh-CN": "我的拜访", "zh-TW": "我的拜訪", "ja": "私の訪問記録"}, "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "field_creator", "operator": "Is", "fieldType": "CREATED_BY", "value": ["Self"]}]}, "type": "TABLE"}, {"templateId": "view_this_week", "name": {"en": "This Week", "zh-CN": "本周拜访", "zh-TW": "本週拜訪", "ja": "今週の訪問"}, "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "field_created_time", "operator": "Is", "fieldType": "DATETIME", "value": ["ThisWeek"]}]}, "type": "TABLE"}, {"templateId": "view_all", "name": {"en": "All", "zh-CN": "全部", "zh-TW": "全部", "ja": "すべて"}, "type": "TABLE"}], "fields": [{"templateId": "field_name", "name": {"zh-CN": "拜访记录", "en": "Visit Record", "zh-TW": "拜訪記錄", "ja": "訪問記録"}, "type": "FORMULA", "property": {"expressionTemplate": "{field_created_time} & \" \" & {field_organization} & \"-\" & {field_people}"}}, {"templateId": "field_organization", "name": {"en": "Organization", "zh-CN": "客户企业", "zh-TW": "客戶企業", "ja": "組織"}, "type": "ONE_WAY_LINK", "property": {"foreignDatabaseTemplateId": "crm-b2b-sales:database_organization"}}, {"templateId": "field_people", "name": {"en": "People", "zh-CN": "联系人", "zh-TW": "聯絡人", "ja": "連絡先"}, "type": "ONE_WAY_LINK", "property": {"foreignDatabaseTemplateId": "crm-b2b-sales:database_people"}}, {"templateId": "field_content", "name": {"en": "Content", "zh-CN": "内容摘要", "zh-TW": "內容摘要", "ja": "内容の要約"}, "type": "LONG_TEXT"}, {"templateId": "field_creator", "name": {"en": "Creator", "zh-CN": "创建人", "zh-TW": "創建人", "ja": "作成者"}, "type": "CREATED_BY"}, {"templateId": "field_created_time", "name": {"en": "Created Time", "zh-CN": "创建时间", "zh-TW": "創建時間", "ja": "作成日時"}, "type": "CREATED_TIME", "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}}], "records": []}, {"resourceType": "AUTOMATION", "templateId": "automation_sales_weekly_summary", "name": {"en": "Auto Send Sales Weekly Report", "zh-CN": "自动发送销售周报", "zh-TW": "自動發送銷售週報", "ja": "自動的に営業週報を送信"}, "description": {"zh-CN": "每周自动汇总销售团队的拜访记录，发送给销售经理", "en": "Weekly summary of sales team visit records, sent to sales manager", "zh-TW": "每周自動匯總銷售團隊的拜訪記錄，發送給銷售經理", "ja": "毎週、営業チームの訪問記録を自動的に集計し、営業マネージャーに送信します"}, "status": "INACTIVE", "triggers": [{"triggerType": "SCHEDULER", "templateId": "trigger_scheduler_summary", "description": {"zh-CN": "每周五下午5点", "zh-TW": "每周五下午5點", "en": "Every Friday 5pm", "ja": "毎週金曜日午後5時"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["FRI"]}}, "timezone": "AUTO", "datetime": {"hour": 17, "minute": 0, "type": "TODAY"}}}}], "actions": [{"templateId": "action_get_list_sales_team", "description": {"zh-CN": "获取销售团队的成员名单", "zh-TW": "獲取銷售團隊的成員名單", "en": "Get the list of sales team members", "ja": "営業チームのメンバーのリストを取得"}, "actionType": "FIND_MEMBERS", "input": {"type": "MEMBER", "by": [{"type": "UNIT_ROLE", "roleTemplateId": "role_sales"}]}}, {"templateId": "action_count_visit_records", "description": {"zh-CN": "统计一周内所有成员的拜访记录", "zh-TW": "統計一周內所有成員的拜訪記錄", "en": "Count all members' visit records in a week", "ja": "一週間のすべてのメンバーの訪問記録をカウント"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "databaseTemplateId": "database_visit", "viewTemplateId": "view_this_week"}}, {"templateId": "report_generate_summary", "description": {"zh-CN": "生成并推送销售周报", "zh-TW": "生成並推送銷售周報", "en": "Generate and push sales weekly report", "ja": "営業週報を生成してプッシュ"}, "actionType": "SEND_REPORT", "input": {"to": [{"type": "UNIT_ROLE", "roleTemplateId": "role_sales_managers"}, {"type": "CURRENT_OPERATOR"}], "type": "MARKDOWN", "subject": "销售周报 - <%= new Date().toLocaleDateString() %>", "markdown": "\n<%\n  const salesPeople = _actions.action_get_list_sales_team.members;\n  const visitRecords = _actions.action_count_visit_records.records;\n  const visitSubmitters = _.uniqWith(visitRecords, (a,b) => a.cells.field_creator.data[0]===b.cells.field_creator.data[0]?true:false);\n  const slicedRecords = visitRecords.map(record => ({\n    memberName: record.cells.field_creator.value\n  }));\n  const groupedRecords = _.groupBy(slicedRecords, 'memberName');\n  const visitCount = _.mapValues(groupedRecords, (records) => records.length);\n%>\n### 📊 概览\n\n时间匆匆，一周又过去了，让我们来看看销售团队这周的表现吧！🎉\n\n- 销售团队当前共有： **<%= salesPeople.length %> 名成员**\n- 本周合计完成： **<%= visitRecords.length %> 份拜访记录**\n<% if(salesPeople.length-visitSubmitters.length>0) { %>*本周还有 **<%= salesPeople.length-visitSubmitters.length %> 位成员** 未提交拜访记录<% } %>\n\n### 🎯 KPI 进度 \n\n团队当前制定的目标是：每人每周至少拜访 **5 位客户**，下面是每位成员的进度：\n\n| 成员 | 完成数量 | 本周表现 |\n| --- | --- | --- |\n<% \n_.forEach(visitCount, (count, memberName) => {\n%>| **<%= memberName %>** | **<%= count %> 份** | **<%= count>=5?'👏 优秀':'😟 待提升' %>** |\n<% }); %>\n"}}]}, {"resourceType": "AUTOMATION", "templateId": "automation-dispatch-visit-mission", "name": {"zh-CN": "每日销售任务派发", "en": "Daily Sales Mission Dispatch", "zh-TW": "每日銷售任務派發", "ja": "毎日セールスミッションディスパッチ"}, "description": {"zh-CN": "每日自动派发拜访客户的任务给销售团队", "en": "Automatically assign missions to the sales team to visit customers every day", "zh-TW": "每日自動派發拜訪客戶的任務給銷售團隊", "ja": "毎日、営業チームに顧客を訪問するためのタスクを自動的に割り当てます"}, "status": "INACTIVE", "triggers": [{"triggerType": "SCHEDULER", "templateId": "trigger_scheduler", "description": {"zh-CN": "每天早上09点", "en": "Every day at 9am", "zh-TW": "每天早上09點", "ja": "毎日午前9時"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["MON", "TUE", "WED", "THU", "FRI"]}}, "timezone": "AUTO", "datetime": {"hour": 9, "minute": 0, "type": "TODAY"}}}}], "actions": [{"templateId": "action_create_visit_mission", "description": {"zh-CN": "创建拜访客户任务", "en": "Create a mission to visit customers", "zh-TW": "創建拜訪客戶任務", "ja": "顧客を訪問するミッションを作成"}, "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "CREATE_RECORD", "name": {"zh-CN": "拜访客户", "en": "Visit Customers", "zh-TW": "拜訪客戶", "ja": "顧客を訪問"}, "description": {"zh-CN": "客户关系管理是销售工作的重要一环，每天至少拜访一位客户，记录拜访内容。好的拜访记录可以帮助团队更好的了解客户需求，提高销售效率。", "en": "Customer relationship management is an important part of sales work. Visit at least one customer every day and record the visit content. Good visit records can help the team better understand customer needs and improve sales efficiency.", "zh-TW": "客戶關係管理是銷售工作的重要一環，每天至少拜訪一位客戶，記錄拜訪內容。好的拜訪記錄可以幫助團隊更好的了解客戶需求，提高銷售效率。", "ja": "顧客関係管理は営業活動の重要な部分です。毎日少なくとも1人の顧客を訪問し、訪問内容を記録します。良い訪問記録は、チームが顧客のニーズをよりよく理解し、営業効率を向上させるのに役立ちます。"}, "canReject": false, "canCompleteManually": false, "canTransfer": false, "assignType": "DEDICATED", "to": [{"type": "UNIT_ROLE", "roleTemplateId": "role_sales"}, {"type": "CURRENT_OPERATOR"}], "buttonText": {"en": "Fill in the visit record", "zh-CN": "填写拜访记录", "zh-TW": "填寫拜訪記錄", "ja": "訪問記録を記入"}, "databaseTemplateId": "database_visit", "viewTemplateId": "view_my"}}}]}], "initMissions": [{"type": "INVITE_MEMBER", "name": "{to.name}, 您好。欢迎加入BIKA", "description": "{to.name}, 您好，感谢您来到BIKA，BIKA是一个帮助您更好的管理团队，提高工作效率的应用。\nBIKA的价值来源于协同工作和自动化工作，我们希望您能邀请一位新用户加入BIKA，即是为了让您在BIKA中有更好的体验，也是为了让您的团队更好的协同工作。\n这里有一个小小请求，请问您可以邀请一位新伙伴加入BIKA吗？\n这是您的邀请链接👇\n{to.urlInvite}\n再次感谢您的加入，祝您工作愉快！\n", "to": [{"type": "ADMIN"}]}, {"type": "READ_TEMPLATE_README", "templateId": "crm-b2b-sales", "name": {"en": "🚀 Template Guide", "zh-CN": "🚀 模板指南", "zh-TW": "🚀 模板指南", "ja": "🚀 テンプレートガイド"}, "forcePopup": true, "assignType": "DEDICATED", "to": [{"type": "CURRENT_OPERATOR"}], "time": 10, "beforeText": {"title": {"zh-CN": "恭喜您，模板已经安装完毕", "zh-TW": "恭喜您，模板已經安裝完畢", "ja": "おめでとうございます, テンプレートのインストールが完了しました", "en": "Congratulations, the template has been installed"}, "description": {"zh-CN": "下一步请您花几分钟阅读模板的使用指南。", "zh-TW": "下一步請您花幾分鐘閱讀模板的使用指南。", "en": "Next, please take a few minutes to read the guide on how to use the template.", "ja": "次に, テンプレートの使用方法についてのチュートリアルを数分間お読みください。"}}, "redirect": {"type": "SPACE_NODE", "nodeTemplateId": "database_people"}, "wizardGuideId": "COMMON_AUTOMATION_TUTORIAL"}], "newMemberJoinMissions": [], "presetUnits": [{"type": "ROLE", "name": {"en": "Sales Managers", "zh-CN": "销售经理", "zh-TW": "銷售經理", "ja": "セールスマネージャー"}, "templateId": "role_sales_managers"}, {"type": "ROLE", "name": {"en": "Sales", "zh-CN": "销售人员", "zh-TW": "銷售人員", "ja": "セールス"}, "templateId": "role_sales"}]}