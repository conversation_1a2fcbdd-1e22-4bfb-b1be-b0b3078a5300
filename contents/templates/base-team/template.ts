// 内部使用，主要有个“花名册”雇员表
import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  templateId: 'base-team',
  name: {
    en: 'Base team',
  },
  cover: '/assets/template/template-cover-no-image-color.png',
  description: {
    en: 'Base Template In Every Need Members Database',
  },
  category: 'official',
  version: '0.0.6',
  copyright: 'Copyright by Vika Limited, AGPL',
  installOnce: true,
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'employee',
      databaseType: 'DATUM',
      name: {
        'zh-CN': '花名册',
        en: 'Employees',
      },
      views: [
        {
          templateId: 'all-employees',
          name: '所有员工',
          type: 'TABLE',
        },
        {
          templateId: 'sales-employees',
          name: '销售员工',
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'description',
                fieldType: 'LONG_TEXT',
                clause: {
                  operator: 'Contains',
                  value: 'Sales', // 包含“销售”字符的员工，筛选进员工
                },
              },
            ],
          },
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'name',
          name: 'Name',
        },
        {
          type: 'EMAIL',
          templateId: 'email',
          name: 'Email',
        },
        {
          type: 'LONG_TEXT',
          templateId: 'description',
          name: '描述',
        },
        {
          type: 'PHONE',
          templateId: 'phone',
          name: 'Phone',
        },
        {
          type: 'MEMBER',
          templateId: 'member',
          name: 'Member',
          property: {
            many: false,
          },
        },
      ],
    },
    {
      // 标准事项(task)表，用于存放内部任务
      resourceType: 'DATABASE',
      templateId: 'team-task',
      type: 'TASK',
      name: 'Tasks',
      fields: [],
    },
  ],
};

export default template;
