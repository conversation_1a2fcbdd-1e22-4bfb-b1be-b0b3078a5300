{"templateId": "daily-transaction-summary", "name": {"zh-CN": "每日出纳流水汇总自动化", "en": "Daily Transaction Summary Automation"}, "description": {"zh-CN": "利用Bika自动化智能提取银行邮件的收支情况，AI 汇总分析当日资金以及现金流情况。", "en": "Use Bika to automatically extract the income and expenditure of bank emails, and AI summarizes the daily cash flow and cash flow situation."}, "cover": "/assets/template/template-cover-daily-transaction-summary.png", "author": "Nagisakon <<EMAIL>>", "category": ["finance", "automation"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.5", "resources": [{"resourceType": "DATABASE", "templateId": "datnGpd55jnaFCPEiROc1xdL", "name": {"zh-CN": "银行每日记账", "en": "Bank Daily Accounting"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viw5uB3AjTiODvaJ42JPW8qX", "name": {"zh-CN": "银行每日记账", "en": "Bank Daily Accounting"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldGz8Ke8jWAVhFiybjPdwz6", "hidden": false, "width": 270}, {"templateId": "fldWOKF4pEbT6BmAIFGPGXbR", "hidden": false}, {"templateId": "fld5LBx9tBwHXFDolbftFLiv", "hidden": false, "width": 155}, {"templateId": "fldtlxjGanKNdHezc9R5kmNc", "hidden": false}, {"templateId": "fld0r5r0lF5ZykWc0jqHP9XR", "hidden": false, "width": 163}, {"templateId": "fldusg2GHfSWw0f8BJu4gT52", "hidden": false}, {"templateId": "fldpmso0qBTpPGOxUdb2Q8xs", "hidden": false}], "groups": [], "extra": {"isHideAllItems": false}}, {"type": "TABLE", "templateId": "viwH3wrUjzuL5Mv2vyTjiz9l", "name": {"zh-CN": "今日流水", "en": "Today's Transactions"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldpmso0qBTpPGOxUdb2Q8xs", "fieldType": "CREATED_TIME", "clause": {"operator": "Is", "value": ["Today"]}}]}, "sorts": [], "fields": [{"templateId": "fldGz8Ke8jWAVhFiybjPdwz6", "hidden": false}, {"templateId": "fldusg2GHfSWw0f8BJu4gT52", "hidden": false}, {"templateId": "fldWOKF4pEbT6BmAIFGPGXbR", "hidden": false}, {"templateId": "fld5LBx9tBwHXFDolbftFLiv", "hidden": false}, {"templateId": "fldtlxjGanKNdHezc9R5kmNc", "hidden": false}, {"templateId": "fld0r5r0lF5ZykWc0jqHP9XR", "hidden": false}, {"templateId": "fldpmso0qBTpPGOxUdb2Q8xs", "hidden": false}], "groups": []}], "fields": [{"type": "FORMULA", "templateId": "fldGz8Ke8jWAVhFiybjPdwz6", "privilege": "TYPE_EDIT", "name": {"en": "Account", "zh-CN": "账目"}, "required": false, "property": {"expressionTemplate": "{fldpmso0qBTpPGOxUdb2Q8xs}&\"_\"&{fldWOKF4pEbT6BmAIFGPGXbR}&{fld5LBx9tBwHXFDolbftFLiv}"}, "primary": true}, {"type": "LONG_TEXT", "templateId": "fldusg2GHfSWw0f8BJu4gT52", "privilege": "FULL_EDIT", "name": {"zh-CN": "原始邮件", "en": "Original Email"}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldWOKF4pEbT6BmAIFGPGXbR", "privilege": "FULL_EDIT", "name": {"zh-CN": "账户", "en": "Account"}, "primary": false}, {"type": "NUMBER", "templateId": "fld5LBx9tBwHXFDolbftFLiv", "privilege": "FULL_EDIT", "name": {"zh-CN": "收支金额", "en": "Amount"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "", "symbolAlign": "right"}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldtlxjGanKNdHezc9R5kmNc", "privilege": "FULL_EDIT", "name": {"zh-CN": "货币代码", "en": "Currency Code"}, "primary": false}, {"type": "NUMBER", "templateId": "fld0r5r0lF5ZykWc0jqHP9XR", "privilege": "FULL_EDIT", "name": {"zh-CN": "余额", "en": "Balance"}, "property": {"precision": 2, "commaStyle": "thousand", "symbol": "", "symbolAlign": "right"}, "primary": false}, {"type": "DATETIME", "templateId": "fldpmso0qBTpPGOxUdb2Q8xs", "privilege": "FULL_EDIT", "name": {"zh-CN": "创建时间", "en": "Created Time"}, "required": false, "property": {"dateFormat": "YYYY/MM/DD", "includeTime": true, "autofill": true}, "primary": false}], "records": [{"templateId": "recT3TnAxWToUeHgSD1XLqBR", "data": {"fld5LBx9tBwHXFDolbftFLiv": -11, "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/22 11:06_USD account-11", "fldWOKF4pEbT6BmAIFGPGXbR": "USD account", "fldpmso0qBTpPGOxUdb2Q8xs": "2025-04-22T11:06:57.514Z", "fldtlxjGanKNdHezc9R5kmNc": "USD", "fldusg2GHfSWw0f8BJu4gT52": "Hello,\n\nA withdrawal of $11.00&nbsp; was debited from your bank account USD account. The full details of this transaction are below:\n\n\nAccount:\n\nUSD account\n\n\n\nWithdrawal Amount:\n\n$11.00\n\n\n\nTransaction Date:\n\nApril 03, 2025\n\n\n\n\nThank you!"}, "values": {"fld5LBx9tBwHXFDolbftFLiv": "-11.00", "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/22 11:06_USD account-11", "fldWOKF4pEbT6BmAIFGPGXbR": "USD account", "fldpmso0qBTpPGOxUdb2Q8xs": "2025/04/22", "fldtlxjGanKNdHezc9R5kmNc": "USD", "fldusg2GHfSWw0f8BJu4gT52": "Hello,\n\nA withdrawal of $11.00&nbsp; was debited from your bank account USD account. The full details of this transaction are below:\n\n\nAccount:\n\nUSD account\n\n\n\nWithdrawal Amount:\n\n$11.00\n\n\n\nTransaction Date:\n\nApril 03, 2025\n\n\n\n\nThank you!"}}, {"templateId": "recuzM9murinmMjeeNyhIXnL", "data": {"fld5LBx9tBwHXFDolbftFLiv": -111, "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/22 11:06_Mercury-111", "fldWOKF4pEbT6BmAIFGPGXbR": "Mercury", "fldpmso0qBTpPGOxUdb2Q8xs": "2025-04-22T11:06:57.895Z", "fldtlxjGanKNdHezc9R5kmNc": "USD", "fldusg2GHfSWw0f8BJu4gT52": "Hi Finance,\n\n\n\n\n\nYour Mercury credit card ••3110 was preauthorized for&nbsp;$111by Shanghai Huacheng Sout.\n\n\n\n\n\nThe settled amount may be different from this initial charge, and will be withdrawn from APITable's Mercury credit account.\n\n\n\n\n\nIf this transaction is in error, you can&nbsp;raise a dispute at Mercury.com.\n\n\n\n\n\nIf you have any questions, just reply to this email.\nThe Mercury Team\n\n\n\n\n\n\n\n\n \n\nSent with care from\n\nMercury Technologies, Inc.\n333 Bush St, San Francisco, CA 94104"}, "values": {"fld5LBx9tBwHXFDolbftFLiv": "-111.00", "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/22 11:06_Mercury-111", "fldWOKF4pEbT6BmAIFGPGXbR": "Mercury", "fldpmso0qBTpPGOxUdb2Q8xs": "2025/04/22", "fldtlxjGanKNdHezc9R5kmNc": "USD", "fldusg2GHfSWw0f8BJu4gT52": "Hi Finance,\n\n\n\n\n\nYour Mercury credit card ••3110 was preauthorized for&nbsp;$111by Shanghai Huacheng Sout.\n\n\n\n\n\nThe settled amount may be different from this initial charge, and will be withdrawn from APITable's Mercury credit account.\n\n\n\n\n\nIf this transaction is in error, you can&nbsp;raise a dispute at Mercury.com.\n\n\n\n\n\nIf you have any questions, just reply to this email.\nThe Mercury Team\n\n\n\n\n\n\n\n\n \n\nSent with care from\n\nMercury Technologies, Inc.\n333 Bush St, San Francisco, CA 94104"}}, {"templateId": "recEpSxDBc1rYUuDCCZCFToc", "data": {"fld5LBx9tBwHXFDolbftFLiv": -30000, "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/22 11:07_The Hongkong and Shanghai Banking Corporation Limited-30000", "fldWOKF4pEbT6BmAIFGPGXbR": "The Hongkong and Shanghai Banking Corporation Limited", "fldpmso0qBTpPGOxUdb2Q8xs": "2025-04-22T11:07:05.564Z", "fldtlxjGanKNdHezc9R5kmNc": "HKD", "fldusg2GHfSWw0f8BJu4gT52": "The Hongkong and Shanghai Banking Corporation Limited, 18/F, Tower 1, HSBC Centre, 1 Sham Mong Road, Kowloon, Hong Kong.\nIf you cannot view this email properly, please configure your email programme so that it can support HTML formatted emails.\n\n\n\n\n\n\nDear Customer,\n\nYour HKD30000.00 payment instruction (Ref: N30477664364) is completed. EN: Payment Tracker on BIB or call +852 ********.\n\nPlease refer other details of the payment below:\nDebit Account: 143-837XXX-XXX\nBeneficiary Account: 795XXX261\nStatus Updated Time: 2025-04-22 09:46:41\n\n\nFor any enquiries, please contact us at (852) 2748 8288.\n\nYours faithfully,\nHSBC Commercial Banking\n\n\n\n\n\n\n\nPlease do not reply to this email. The information in this email alert is not and should not be construed as a recommendation, an offer to sell or the solicitation of an offer to purchase or subscribe for any investment. HSBC makes no guarantee, representation or warranty and accepts no responsibility or liability as to its accuracy or completeness. The information is for reference only and are subject to change without notice.\n\nThe information contained in this email alert is confidential. It may also be legally privileged. If you are not the intended addressee, you may not copy, forward, disclose or use any part of this message. If you have received this message in error, please delete it and all copies from your system and notify the Bank immediately by contacting our customer service hotline on (852) 2748 8288.\n\nEmail communications cannot be guaranteed to be timely, secure, error or virus-free. The sender does not accept liability for any error or omissions which arise as a result.\n\n\nPrivacy and SecurityTerms of UseHyperlink Policy\n\n\n©&nbsp;Copyright. The Hongkong and Shanghai Banking Corporation Limited . All rights reserved."}, "values": {"fld5LBx9tBwHXFDolbftFLiv": "-30000.00", "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/22 11:07_The Hongkong and Shanghai Banking Corporation Limited-30000", "fldWOKF4pEbT6BmAIFGPGXbR": "The Hongkong and Shanghai Banking Corporation Limited", "fldpmso0qBTpPGOxUdb2Q8xs": "2025/04/22", "fldtlxjGanKNdHezc9R5kmNc": "HKD", "fldusg2GHfSWw0f8BJu4gT52": "The Hongkong and Shanghai Banking Corporation Limited, 18/F, Tower 1, HSBC Centre, 1 Sham Mong Road, Kowloon, Hong Kong.\nIf you cannot view this email properly, please configure your email programme so that it can support HTML formatted emails.\n\n\n\n\n\n\nDear Customer,\n\nYour HKD30000.00 payment instruction (Ref: N30477664364) is completed. EN: Payment Tracker on BIB or call +852 ********.\n\nPlease refer other details of the payment below:\nDebit Account: 143-837XXX-XXX\nBeneficiary Account: 795XXX261\nStatus Updated Time: 2025-04-22 09:46:41\n\n\nFor any enquiries, please contact us at (852) 2748 8288.\n\nYours faithfully,\nHSBC Commercial Banking\n\n\n\n\n\n\n\nPlease do not reply to this email. The information in this email alert is not and should not be construed as a recommendation, an offer to sell or the solicitation of an offer to purchase or subscribe for any investment. HSBC makes no guarantee, representation or warranty and accepts no responsibility or liability as to its accuracy or completeness. The information is for reference only and are subject to change without notice.\n\nThe information contained in this email alert is confidential. It may also be legally privileged. If you are not the intended addressee, you may not copy, forward, disclose or use any part of this message. If you have received this message in error, please delete it and all copies from your system and notify the Bank immediately by contacting our customer service hotline on (852) 2748 8288.\n\nEmail communications cannot be guaranteed to be timely, secure, error or virus-free. The sender does not accept liability for any error or omissions which arise as a result.\n\n\nPrivacy and SecurityTerms of UseHyperlink Policy\n\n\n©&nbsp;Copyright. The Hongkong and Shanghai Banking Corporation Limited . All rights reserved."}}, {"templateId": "recAAGOU98YBSK234ycepwSn", "data": {"fld5LBx9tBwHXFDolbftFLiv": -8000, "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/22 11:08_Bank of China (Hong Kong) Limited-8000", "fldWOKF4pEbT6BmAIFGPGXbR": "Bank of China (Hong Kong) Limited", "fldpmso0qBTpPGOxUdb2Q8xs": "2025-04-22T11:08:53.890Z", "fldtlxjGanKNdHezc9R5kmNc": "HKD", "fldusg2GHfSWw0f8BJu4gT52": "您给 CHINA HONG KONG SECRETARY SERCIES LIMITED 的付款已汇出\n\n\n\n\n\n\n\n尊敬的&nbsp;tracy ma，\n\n\n\n\n\n\n\n您给 CHINA HONG KONG SECRETARY SERCIES LIMITED 的付款将于自 2025-04-22 起的三个工作日内到账。该笔付款的概要如下：\n\n\n\n\n\n\n\n \n\n\n\n\n\nAirwallex 账户\n\n\nVika Limited (Vika Limited (HK))\n\n\n\n\n\n&nbsp;金额\n\n\n8,000.00 HKD\n\n\n\n\n\n&nbsp;收款人\n\n\nCHINA HONG KONG SECRETARY SERCIES LIMITED\n\n\n\n\n\n&nbsp;付款日期\n\n\n2025-04-22\n\n\n\n\n\n&nbsp;付款方式\n\n\nFPS\n\n\n\n\n\n&nbsp;交易编号\n\n\nP250314-HTUVSZ0\n\n\n\n\n\n&nbsp;\n\n\n\n\n&nbsp;交易附言\n\n\nAudit fee\n\n\n\n\n\n&nbsp;银行\n\n\n012 - Bank of China (Hong Kong) Limited\n\n\n\n\n\n&nbsp;银行账号\n\n\n•••• 5486"}, "values": {"fld5LBx9tBwHXFDolbftFLiv": "-8000.00", "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/22 11:08_Bank of China (Hong Kong) Limited-8000", "fldWOKF4pEbT6BmAIFGPGXbR": "Bank of China (Hong Kong) Limited", "fldpmso0qBTpPGOxUdb2Q8xs": "2025/04/22", "fldtlxjGanKNdHezc9R5kmNc": "HKD", "fldusg2GHfSWw0f8BJu4gT52": "您给 CHINA HONG KONG SECRETARY SERCIES LIMITED 的付款已汇出\n\n\n\n\n\n\n\n尊敬的&nbsp;tracy ma，\n\n\n\n\n\n\n\n您给 CHINA HONG KONG SECRETARY SERCIES LIMITED 的付款将于自 2025-04-22 起的三个工作日内到账。该笔付款的概要如下：\n\n\n\n\n\n\n\n \n\n\n\n\n\nAirwallex 账户\n\n\nVika Limited (Vika Limited (HK))\n\n\n\n\n\n&nbsp;金额\n\n\n8,000.00 HKD\n\n\n\n\n\n&nbsp;收款人\n\n\nCHINA HONG KONG SECRETARY SERCIES LIMITED\n\n\n\n\n\n&nbsp;付款日期\n\n\n2025-04-22\n\n\n\n\n\n&nbsp;付款方式\n\n\nFPS\n\n\n\n\n\n&nbsp;交易编号\n\n\nP250314-HTUVSZ0\n\n\n\n\n\n&nbsp;\n\n\n\n\n&nbsp;交易附言\n\n\nAudit fee\n\n\n\n\n\n&nbsp;银行\n\n\n012 - Bank of China (Hong Kong) Limited\n\n\n\n\n\n&nbsp;银行账号\n\n\n•••• 5486"}}, {"templateId": "rec96F2kbxOtRxMOFJrEpKlc", "data": {"fld0r5r0lF5ZykWc0jqHP9XR": 3500, "fld5LBx9tBwHXFDolbftFLiv": 500, "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/23 02:48_招商银行500", "fldWOKF4pEbT6BmAIFGPGXbR": "招商银行", "fldpmso0qBTpPGOxUdb2Q8xs": "2025-04-23T02:48:34.184Z", "fldtlxjGanKNdHezc9R5kmNc": "CNY", "fldusg2GHfSWw0f8BJu4gT52": "招行银讯通-付款通知：账号 ***************,户名深圳市****科技有限公司（结汇待支付）,于2025年04月23日-10:47:01收到人民币 500.00, 余额为3500.00, 收方账号 *************** 深圳市****科技有限公司,用途：汇款接单（企业银行）"}, "values": {"fld0r5r0lF5ZykWc0jqHP9XR": "3500.00", "fld5LBx9tBwHXFDolbftFLiv": "500.00", "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/23 02:48_招商银行500", "fldWOKF4pEbT6BmAIFGPGXbR": "招商银行", "fldpmso0qBTpPGOxUdb2Q8xs": "2025/04/23 02:48", "fldtlxjGanKNdHezc9R5kmNc": "CNY", "fldusg2GHfSWw0f8BJu4gT52": "招行银讯通-付款通知：账号 ***************,户名深圳市****科技有限公司（结汇待支付）,于2025年04月23日-10:47:01收到人民币 500.00, 余额为3500.00, 收方账号 *************** 深圳市****科技有限公司,用途：汇款接单（企业银行）"}}, {"templateId": "recTucWbXXOY1TWIevRUlAr0", "data": {"fld0r5r0lF5ZykWc0jqHP9XR": 3600, "fld5LBx9tBwHXFDolbftFLiv": -100, "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/23 02:49_招商银行-100", "fldWOKF4pEbT6BmAIFGPGXbR": "招商银行", "fldpmso0qBTpPGOxUdb2Q8xs": "2025-04-23T02:49:35.604Z", "fldtlxjGanKNdHezc9R5kmNc": "CNY", "fldusg2GHfSWw0f8BJu4gT52": "招行银讯通-付款通知：账号 ***************,户名深圳市****科技有限公司（结汇待支付）,于2025年04月23日-10:47:49支出人民币 -100.00, 余额为3600.00, 收方账号 *************** 深圳市****科技有限公司,用途：汇款接单（企业银行）"}, "values": {"fld0r5r0lF5ZykWc0jqHP9XR": "3600.00", "fld5LBx9tBwHXFDolbftFLiv": "-100.00", "fldGz8Ke8jWAVhFiybjPdwz6": "2025/04/23 02:49_招商银行-100", "fldWOKF4pEbT6BmAIFGPGXbR": "招商银行", "fldpmso0qBTpPGOxUdb2Q8xs": "2025/04/23 02:49", "fldtlxjGanKNdHezc9R5kmNc": "CNY", "fldusg2GHfSWw0f8BJu4gT52": "招行银讯通-付款通知：账号 ***************,户名深圳市****科技有限公司（结汇待支付）,于2025年04月23日-10:47:49支出人民币 -100.00, 余额为3600.00, 收方账号 *************** 深圳市****科技有限公司,用途：汇款接单（企业银行）"}}]}, {"resourceType": "AUTOMATION", "templateId": "atoj1vqtKjzB6wKE3LokwVqC", "name": {"zh-CN": "检索银行邮件", "en": "Retrieve Bank Email"}, "triggers": [{"triggerType": "INBOUND_EMAIL", "templateId": "trgtYVLjO8bnqrBLwEjqbcRZ", "description": {"en": "Extract the email", "zh-CN": "提取邮件"}, "input": {"mailboxName": "", "searchCriteria": "", "downloadAttachments": false, "type": "IMAP_INTEGRATION", "integrationId": ""}}], "actions": [{"templateId": "actgaNemy4dxGUA15hYWPIQC", "description": {"en": "AI analysis of emails", "zh-CN": "AI分析邮件"}, "actionType": "OPENAI_GENERATE_TEXT", "input": {"urlType": "INTEGRATION", "type": "OPENAI_GENERATE_TEXT", "integrationId": "", "prompt": "Please help me extract information from a bank transaction email, specifically the account/bank name, transaction amount, currency code, and balance. The currency code should follow the ISO 4217 standard. For the transaction amount, any amount I receive should be recorded as a positive number, and any amount I spend should be recorded as a negative number (e.g., received amount +200.00, spent amount -30.00).\n\nThe email content is as follows:\n\n<%= _triggers.trgtYVLjO8bnqrBLwEjqbcRZ.text %>\n\nUse the following format as a reference. If certain data cannot be retrieved, it should be omitted (only return the following content, do not add any extra formatting or include any other content):  \naccount: Bank of China (Hong Kong) Limited, deal_amount: -200.00, currency_code: USD, balance: 184734.33\n", "baseUrl": "https://openai.bika.ltd/v1", "apiKey": "", "model": "gpt-4o", "timeout": 300}}, {"templateId": "actFZHPVikxrjMbNH29YBg9E", "description": {"en": "Bank data split", "zh-CN": "银行数据拆分"}, "actionType": "RUN_SCRIPT", "input": {"type": "SCRIPT", "language": "javascript", "script": "function bankDataSplit() {\n    const bankData = \"<%= _actions.actgaNemy4dxGUA15hYWPIQC.body.choices[0].message.content %>\";\n    const result = bankData.trim().split(\", \").reduce((acc, line) => {\n        const [key, value] = line.split(\": \").map(part => part.trim());\n        acc[key] = value;\n        return acc;\n    }, {});\n    console.log(result);\n    return result;\n}\n\nbankDataSplit();"}}, {"templateId": "actdaXloVmhcxRsEzzT9T26n", "description": {"en": "Create email records", "zh-CN": "创建银行账单记录"}, "actionType": "CREATE_RECORD", "input": {"type": "RECORD_BODY", "data": {"fld0r5r0lF5ZykWc0jqHP9XR": "<%= _actions.actFZHPVikxrjMbNH29YBg9E.balance %>", "fld5LBx9tBwHXFDolbftFLiv": "<%= _actions.actFZHPVikxrjMbNH29YBg9E.deal_amount %>", "fldWOKF4pEbT6BmAIFGPGXbR": "<%= _actions.actFZHPVikxrjMbNH29YBg9E.account %>", "fldtlxjGanKNdHezc9R5kmNc": "<%= _actions.actFZHPVikxrjMbNH29YBg9E.currency_code %>", "fldusg2GHfSWw0f8BJu4gT52": "<%= _triggers.trgtYVLjO8bnqrBLwEjqbcRZ.text %>"}, "databaseTemplateId": "datnGpd55jnaFCPEiROc1xdL"}}]}, {"resourceType": "AUTOMATION", "templateId": "atoJ2D6lgF374PtW71BKOL8b", "name": {"zh-CN": "每日总结分析", "en": "Daily Summary Analysis"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trgSeECPshXjanTBugzhZGIW", "description": {"en": "Trigger automation at 20:00 every day", "zh-CN": "每天20点触发自动化"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "DAY", "interval": 1}}, "datetime": "2025-03-13T12:00:08.075Z"}}}], "actions": [{"templateId": "actrv8jdKBeUflF3kw7XxESS", "description": {"en": "Find today's deal amount", "zh-CN": "查找今日流水"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "viwH3wrUjzuL5Mv2vyTjiz9l", "databaseTemplateId": "datnGpd55jnaFCPEiROc1xdL"}}, {"templateId": "actcmUTQ5atC7w0tqBntiWUk", "description": {"en": "AI summarizes today's income and expenses", "zh-CN": "AI总结今日收支"}, "actionType": "OPENAI_GENERATE_TEXT", "input": {"urlType": "INTEGRATION", "type": "OPENAI_GENERATE_TEXT", "integrationId": "", "prompt": "You are a financial analyst, and the following is a sample of bank transaction data:  \nChina Merchants Bank, Transaction Amount: -1000.00, Balance: 3200.00, Currency Code: CNY, Created Time: 19:07  \nChina Merchants Bank, Transaction Amount: 200.00, Balance: 4200.00, Currency Code: CNY, Created Time: 16:55  \nChina Merchants Bank, Transaction Amount: 100.00, Balance: 4000.00, Currency Code: CNY, Created Time: 13:59  \nBank of China (Hong Kong), Transaction Amount: -100.00, Balance: 1300.00, Currency Code: HKD, Created Time: 16:58  \n\nThere are 3 rules to follow when analyzing the transaction data:  \n1. Transactions from the same account should be consolidated. Income and expenses must be calculated separately. For example, China Merchants Bank has an income of 100 + 200 = 300.00, and an expense of 1000.00.  \n2. The final balance should be taken from the transaction record with the latest creation time. For example, for China Merchants Bank with three records, the final balance is from the latest record at 19:07, which is 3200.00.  \n3. Accounts with no transaction data should not be displayed. For example, if Bank of China (Hong Kong) has no data, its balance should be left empty.\n\nBased on the transaction data, please return a transaction summary in the form of a table. The output should be similar to the following:\n\n#### April 3, 2025 Bank Transaction Summary\n\n| Account                    | Expense        | Income        | Balance       |\n|----------------------------|----------------|---------------|----------------|\n| China Merchants Bank       | ¥1000.00       | ¥300.00       | ¥3200.00       |\n| Bank of China (Hong Kong)  | HK$100.00      |               | HK$1300.00     |\n\nThis is our company’s bank transaction data for today. Please help summarize the bank transactions accordingly.\n\n<%= _renderRecordsAsGrid(_actions.actrv8jdKBeUflF3kw7XxESS.records, ['fldWOKF4pEbT6BmAIFGPGXbR','fld5LBx9tBwHXFDolbftFLiv','fldtlxjGanKNdHezc9R5kmNc','fld0r5r0lF5ZykWc0jqHP9XR','fldpmso0qBTpPGOxUdb2Q8xs']) %>", "model": "gpt-4o", "timeout": 300}}, {"templateId": "acti1FK7butGFcEbuy1emWip", "description": {"en": "Send today's report", "zh-CN": "发送今日报告"}, "actionType": "SEND_REPORT", "input": {"to": [{"type": "CURRENT_OPERATOR"}], "markdown": "<%= _actions.actcmUTQ5atC7w0tqBntiWUk.body.choices[0].message.content %>", "subject": "Today's Bank Statement Report", "type": "MARKDOWN"}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}