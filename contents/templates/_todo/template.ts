import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: 'Bika.ai',
  templateId: 'todo',
  name: {
    en: 'Easy To-do List',
    ja: 'Vika OKR',
    'zh-TW': '維格 OKR',
    'zh-CN': '维格 OKR ',
  },
  cover: '/assets/template/template-cover-no-image-color.png',
  description: {
    en: "Assist users in gathering task information, reminding them to complete tasks, and offering a recap of the previous day's activities in the morning.",
  },
  version: '1.0.1',
  category: 'project',
  resources: [
    // {
    //   resourceType: 'DATABASE',
    //   type: 'TASK',
    //   // DATABASE TYPE 为 TASK 里的记录，和 TODO 模块直接进行关联，TASK 里面的记录会自动添加到 TODO
    //   // 如果 TASK 里面的记录被删除，TODO 里面的记录也会被删除，反之亦然
    //   // 如果 TODO 里面的任务被完成，TASK 里面的记录也会被标记为完成，反之亦然
    //   templateId: 'tasks_database',
    //   name: {
    //     en: 'Tasks Database',
    //   },
    //   description: {
    //     en: 'The database used to store tasks',
    //   },
    //   fields: [
    //     {
    //       templateId: 'attachment',
    //       name: 'Task Attachment',
    //       type: 'ATTACHMENT',
    //       description: 'Attach files to tasks for easy access',
    //       privilege: 'NAME_EDIT',
    //     },
    //     {
    //       templateId: 'task_title',
    //       name: 'title',
    //       type: 'SINGLE_TEXT',
    //       description: 'Task title',
    //     },
    //     {
    //       templateId: 'updated_time',
    //       name: 'Task Update Time',
    //       type: 'MODIFIED_TIME',
    //       description: 'Record update times for easy generation of daily task reports',
    //       privilege: 'NAME_EDIT',
    //       property: {
    //         dateFormat: 'YYYY/MM/DD',
    //         timeFormat: 'HH:mm',
    //         includeTime: true,
    //       },
    //     },
    //     {
    //       templateId: 'task_status',
    //       name: 'TASK STATUS',
    //       type: 'SINGLE_SELECT',
    //       property: {
    //         options: [
    //           { name: 'Ready', templateId: 'task_ready', color: 'blue' },
    //           { name: 'In progress', templateId: 'task_in_progress', color: 'green' },
    //           { name: 'Done', templateId: 'task_done', color: 'red' },
    //           { name: 'Backlog', templateId: 'task_backlog', color: 'red' },
    //         ],
    //       },
    //       description: 'task status',
    //     },
    //     {
    //       templateId: 'task_creator',
    //       name: 'Creator',
    //       type: 'CREATED_BY',
    //     },
    //     {
    //       templateId: 'task_assignees',
    //       name: 'Members',
    //       type: 'MEMBER',
    //       property: {},
    //     },
    //   ],
    //   views: [
    //     {
    //       templateId: 'all_task_view',
    //       name: 'all task',
    //       type: 'TABLE',
    //     },
    //     {
    //       templateId: 'in_progress_task_view',
    //       name: 'in progress',
    //       type: 'TABLE',
    //       filters: {
    //         conditions: [],
    //         conds: [
    //           {
    //             fieldTemplateId: 'task_status',
    //             fieldType: 'SINGLE_SELECT',
    //             clause: {
    //               operator: 'Is',
    //               value: 'task_in_progress',
    //             },
    //           },
    //         ],
    //         conjunction: 'Or',
    //       },
    //     },
    //   ],
    // },
    // 每天早上任务
    {
      resourceType: 'AUTOMATION',
      name: {
        en: 'Report generation + task collection',
      },
      status: 'ACTIVE',
      triggers: [
        {
          triggerType: 'SCHEDULER',
          input: {
            type: 'SCHEDULER',
            // 每天早上9点
            scheduler: {
              datetime: '2024-01-01T09:00:00Z',
              timezone: 'AUTO',
              repeat: {
                every: {
                  type: 'DAY',
                  interval: 1,
                },
              },
            },
          },
        },
      ],
      actions: [
        {
          actionType: 'FIND_RECORDS',
          templateId: 'find_yesterday_missions',
          input: {
            type: 'DATABASE_WITH_FILTER',
            databaseTemplateId: 'tasks_database',
            filters: {
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'updated_time',
                  fieldType: 'MODIFIED_TIME',
                  clause: {
                    operator: 'Is',
                    value: ['Yesterday'],
                  },
                },
              ],
              conjunction: 'And',
            },
          },
        },
        {
          actionType: 'SEND_REPORT',
          templateId: 'send_daily_tasks_report',
          input: {
            type: 'MARKDOWN',
            subject: "<%= _to.name %>, please check the status of yesterday's task completion",
            markdown: `# Yesterday's task report
            <% _actions.find_yesterday_missions.records.forEach(function(task) { %>
              <h3><%= task.cells.TASK_TITLE.value %></h2>
              <% if (task.cells.TASK_STATUS.value) { %>
                Done
              <% } else { %>
                Undone
              <% } %>
            <% }); %> `,
            to: [
              {
                type: 'ALL_MEMBERS',
              },
            ],
          },
        },
        {
          actionType: 'CREATE_MISSION',
          templateId: 'create_task',
          input: {
            type: 'MISSION_BODY',
            mission: {
              name: 'Good morning, <%= _to.name %>, please remember to fill out your assignment for today.',
              description: 'Please fill in your tasks for today.',
              type: 'CREATE_TASK',
              to: [{ type: 'ALL_MEMBERS' }],
            },
          },
        },
      ],
    },
    // 每天晚上任务完成提醒
    {
      resourceType: 'AUTOMATION',
      name: {
        en: 'Task Update Alerts',
      },
      templateId: 'evening_tasks_reminder',
      status: 'ACTIVE',
      triggers: [
        {
          triggerType: 'SCHEDULER',
          input: {
            // 每天晚上8点
            type: 'SCHEDULER',
            scheduler: {
              datetime: '2024-01-01T20:00:00Z',
              repeat: {
                every: {
                  type: 'DAY',
                  interval: 1,
                },
              },
              timezone: 'AUTO',
            },
          },
        },
      ],
      actions: [
        {
          actionType: 'CREATE_MISSION',
          templateId: 'review_task',
          input: {
            type: 'MISSION_BODY',
            mission: {
              name: 'Good evening, <%= _to.name %>, and please remember to keep us updated on the status of your task.',
              description: 'Please update the status of your tasks for today.',
              type: 'UI_LAUNCHER',
              target: 'TODO',
              to: [{ type: 'ALL_MEMBERS' }],
            },
          },
        },
      ],
    },
  ],
};

export default template;
