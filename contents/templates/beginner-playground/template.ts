import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'beginner-playground',
  name: {
    en: "Beginner's Playground",
    ja: '初心者のための遊び場',
    'zh-CN': '初学者的游乐场',
    'zh-TW': '初學者的遊樂場',
  },
  description: {
    'zh-CN': '适合初学者入门学习的模板，内含 Bika 支持的所有资源类型的示例。你可以一边学习，一边修改成自己预期的效果。',
    'zh-TW': '適合初學者入門學習的模板，內含 Bika 支持的所有資源類型的示例。你可以一邊學習，一邊修改成自己預期的效果。',
    en: 'A template suitable for beginners to learn, containing examples of all resource types supported by Bika. You can learn and modify it to your desired effect.',
    ja: '初心者が学ぶのに適したテンプレートで、Bika がサポートするすべてのリソースタイプの例が含まれています。学習しながら、自分の望む効果に変更できます。',
  },
  cover: '/assets/template/template-cover-beginner-playground.jpg',
  author: '<PERSON><PERSON> <<EMAIL>>',
  category: ['project', 'official'],
  schemaVersion: 'v1',
  version: '1.1.7',
  resources: [
    {
      resourceType: 'MIRROR',
      templateId: 'res_mirror_example',
      name: {
        'zh-CN': '未完成事项（镜像示例）',
        'zh-TW': '未完成事項（鏡像示例）',
        en: 'Incomplete Items (Mirror Example)',
        ja: '未完了のタスク（ミラーの例）',
      },
      description: {
        'zh-CN': '镜像是一个数据表或某个视图的副本。当前镜像呈现的记录数据来自于源表“数据表示例”的“未完成事项”视图。',
        'zh-TW': '鏡像是一個數據表或某個視圖的副本。當前鏡像呈現的記錄數據來自於源表“數據表示例”的“未完成事項”視圖。',
        en: 'A mirror is a copy of a database or a view. The record data presented in the current mirror comes from the "Incomplete Items" view of the database "To-Do List (Database Sample)".',
        ja: 'ミラーはデータベースまたはビューのコピーです。現在のミラーに表示されるレコードデータは、ソーステーブル「データベースの例」の「未完了のタスク」ビューから取得されます。',
      },
      mirrorType: 'DATABASE_VIEW',
      databaseTemplateId: 'res_database_example',
      viewTemplateId: 'view_for_mirror',
    },
    {
      resourceType: 'FORM',
      templateId: 'res_form_example',
      name: {
        'zh-CN': '新增待办事项（表单示例）',
        'zh-TW': '新增待辦事項（表單示例）',
        en: 'Add To-Do Item (Form Example)',
        ja: 'タスクを追加（フォームの例）',
      },
      description: {
        en: 'The form feature allows you to create custom forms to collect and input data into specified databases. You can quickly generate a form by specifying a view of the database, and then share it to various social groups. The submitted data will automatically update to the corresponding database, making it easy to manage and analyze. The form feature supports various field types such as text, attachments, checkboxes, etc., to meet different data collection needs.',
        ja: 'フォーム機能により、指定されたデータテーブルにデータを収集および入力するためのカスタムフォームを作成できます。データテーブルのビューを指定することで、すばやくフォームを生成し、さまざまなソーシャルグループに共有できます。送信されたデータは自動的に対応するテーブルに更新され、管理および分析が容易になります。フォーム機能は、テキスト、添付ファイル、チェックボックスなど、さまざまなフィールドタイプをサポートしており、さまざまなデータ収集ニーズに対応します。',
        'zh-CN':
          '表单功能让你可以创建自定义的表单，以便收集和输入数据到指定的数据表。你可以通过指定数据表的一个视图，快速生成一个表单，然后分享至各类社交群组。提交的数据会自动更新到相应的表格中，方便管理和分析。表单功能支持文本、附件、复选框等多种字段类型，满足不同数据收集需求。',
        'zh-TW':
          '表單功能讓你可以創建自定義的表單，以便收集和輸入數據到指定的數據表。你可以通過指定數據表的一個視圖，快速生成一個表單，然後分享至各類社交群組。提交的數據會自動更新到相應的表格中，方便管理和分析。表單功能支持文本、附件、複選框等多種字段類型，滿足不同數據收集需求。',
      },
      brandLogo: {
        type: 'EMOJI',
        backgroundColor: '#7B67EE',
        emoji: '📨',
      },
      databaseTemplateId: 'res_database_example',
      metadata: {
        type: 'VIEW',
        viewTemplateId: 'view_all_todo_items',
      },
    },
    {
      resourceType: 'DASHBOARD',
      templateId: 'res_dashboard_example',
      name: {
        en: 'Dashboard Sample',
        'zh-CN': '仪表盘示例',
        'zh-TW': '儀表板示例',
        ja: 'ダッシュボードの例',
      },
      widgets: [
        {
          templateId: 'widget_help_document',
          type: 'EMBED',
          name: {
            'zh-CN': '帮助文档',
            'zh-TW': '幫助文檔',
            en: 'Help Document',
            ja: 'ヘルプドキュメント',
          },
          url: 'https://bika.ai/help/index',
        },
        {
          templateId: 'widget_completed_tasks_count',
          type: 'CHART',
          name: {
            'zh-CN': '待办事项完成情况一览',
            'zh-TW': '待辦事項完成情況一覽',
            en: 'Overview of To-Do List Completion',
            ja: 'タスクリストの完了状況',
          },
          datasource: {
            type: 'DATABASE',
            chartType: 'line',
            databaseTemplateId: 'res_database_example',
            viewTemplateId: 'view_all_todo_items', // 日期分组视图
            dimensionTemplateId: 'field_date', // 'template id of field 完成日期',
            metricsType: 'COUNT_RECORDS',
          },
        },
        {
          templateId: 'widget_total_tasks_count',
          type: 'NUMBER',
          name: {
            'zh-CN': '待办事项总数',
            'zh-TW': '待辦事項總數',
            en: 'Total Number of To-Do Items',
            ja: 'タスクの総数',
          },
          summaryDescription: {
            'zh-CN': '合计数量',
            'zh-TW': '合計數量',
            en: 'Total number',
            ja: '合計数',
          },
          datasource: {
            type: 'DATABASE',
            metricsType: 'COUNT_RECORDS',
            databaseTemplateId: 'res_database_example',
            viewTemplateId: 'view_all_todo_items',
          },
        },
        {
          templateId: 'widget_help_text',
          type: 'TEXT',
          name: {
            'zh-CN': '仪表盘简介',
            'zh-TW': '儀表板简介',
            en: 'Dashboard Introduction',
            ja: 'ダッシュボードの紹介',
          },
          datasource: {
            type: 'CUSTOM',
            text: {
              'zh-CN':
                '本仪表盘使用了多种小部件，展示了待办事项的完成情况。你可以根据自己的需求，添加、删除、修改小部件，定制自己的仪表盘。',
              'zh-TW':
                '本儀表板使用了多種小部件，展示了待辦事項的完成情況。你可以根據自己的需求，添加、刪除、修改小部件，定制自己的儀表板。',
              en: 'This dashboard uses various widgets to display the completion status of to-do items. You can add, delete, and modify widgets according to your needs to customize your dashboard.',
              ja: 'このダッシュボードは、タスクの完了状況を表示するためにさまざまなウィジェットを使用しています。必要に応じてウィジェットを追加、削除、変更して、独自のダッシュボードをカスタマイズできます。',
            },
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'res_automation_example',
      name: {
        'zh-CN': '生成周报（自动化示例）',
        'zh-TW': '生成周報（自動化示例）',
        en: 'Generate Weekly Summary (Automation Example)',
        ja: '週報の生成（自動化の例）',
      },
      triggers: [
        {
          templateId: 'trigger_scheduler_summary',
          triggerType: 'SCHEDULER',
          description: {
            'zh-CN': '每周五 20:00 生成一份周报，包含本周的待办事项完成情况。',
            'zh-TW': '每週五 20:00 生成一份周報，包含本週的待辦事項完成情況。',
            en: "Generate a weekly summary at 20:00 every Friday, including the completion status of this week's to-do items.",
            ja: '毎週金曜日の 20:00 に、今週のタスクリストの完了状況を含む週報を生成します。',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              timezone: 'AUTO',
              datetime: {
                type: 'TODAY',
                hour: 20,
                minute: 0,
              },
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['FRI'],
                },
              },
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'action_find_incomplete_items',
          description: {
            'zh-CN': '查找未完成事项。',
            'zh-TW': '查找未完成事項。',
            en: 'Find incomplete items.',
            ja: '未完了のタスクを検索します。',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_VIEW',
            databaseTemplateId: 'res_database_example',
            viewTemplateId: 'view_for_mirror',
          },
        },
        {
          templateId: 'action_find_completed_items',
          description: {
            'zh-CN': '查找已完成事项。',
            'zh-TW': '查找已完成事項。',
            en: 'Find completed items.',
            ja: '完了したタスクを検索します。',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_WITH_FILTER',
            databaseTemplateId: 'res_database_example',
            filters: {
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'field_status',
                  fieldType: 'SINGLE_SELECT',
                  clause: {
                    operator: 'Is',
                    value: 'completed',
                  },
                },
              ],
              conjunction: 'And',
            },
          },
        },
        {
          actionType: 'SEND_REPORT',
          templateId: 'send_report_weekly_summary',
          description: {
            'zh-CN': '发送周报摘要。',
            'zh-TW': '發送周報摘要。',
            en: 'Send a weekly summary.',
            ja: '週報の要約を送信します。',
          },
          input: {
            type: 'MARKDOWN',
            subject: 'Weekly To-Do List Summary',
            markdown: `
I hope you had a great week! Here’s the summary of this week's to-do list:

### Incomplete Items
<%= _renderRecordsAsGrid(_actions.action_find_incomplete_items.records, ['field_task']) %>

### Completed Items
<%= _renderRecordsAsGrid(_actions.action_find_completed_items.records, ['field_task']) %>     
`,
            to: [
              {
                type: 'ADMIN',
              },
              {
                type: 'CURRENT_OPERATOR', // 当手工触发的时候会 To 到这个操作的人
              },
            ],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'res_database_example',
      name: {
        en: 'To-Do List (Database Sample)',
        'zh-CN': '待办清单（数据表示例）',
        'zh-TW': '待辦清單（數據表示例）',
        ja: 'タスクリスト（データベースの例）',
      },
      description: {
        en: 'The database is similar to a spreadsheet but more versatile. Each database consists of rows and columns, where rows represent records and columns represent fields. You can create multiple databases within a folder to organize and categorize different data types. Databases support various field types, such as text, numbers, attachments, and links, allowing for diverse information storage. You can utilize views to filter, sort, and group data, enhancing data management and analysis efficiency.',
        'zh-CN':
          '数据表，类似于 Excel 电子表格，但功能更强大。每个数据表包含行和列，行代表记录，列代表字段。你可以在一个文件夹中创建多个数据表，以组织和分类不同类型的数据。\n数据表支持多种字段类型，如文本、数字、附件、链接等，便于存储多样化信息。你可以通过视图功能筛选、排序，提升数据管理和分析的效率。',
        'zh-TW':
          '數據表，類似於 Excel 電子表格，但功能更強大。每個數據表包含行和列，行代表記錄，列代表字段。你可以在一個文件夾中創建多個數據表，以組織和分類不同類型的數據。\n數據表支持多種字段類型，如文本、數字、附件、鏈接等，便於存儲多樣化信息。你可以通過視圖功能篩選、排序，提升數據管理和分析的效率。',
        ja: 'データベースは、スプレッドシートに似ていますが、より柔軟です。各データベースは行と列で構成され、行はレコードを、列はフィールドを表します。異なるデータタイプを整理して分類するために、1 つのフォルダ内に複数のデータベースを作成できます。\nデータベースは、テキスト、数値、添付ファイル、リンクなど、さまざまなフィールドタイプをサポートしており、多様な情報の保存が可能です。ビューを使用してデータをフィルタリング、並べ替え、グループ化することで、データ管理と分析の効率が向上します。',
      },
      databaseType: 'DATUM',
      views: [
        {
          templateId: 'view_all_todo_items',
          name: {
            'zh-CN': '全部',
            'zh-TW': '全部',
            en: 'All',
            ja: 'すべて',
          },
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
        },
        {
          templateId: 'view_for_mirror',
          name: {
            'zh-CN': '未完成事项',
            'zh-TW': '未完成事項',
            en: 'Incomplete items',
            ja: '未完了のタスク',
          },
          type: 'TABLE',
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'field_status',
                fieldType: 'SINGLE_SELECT',
                clause: {
                  operator: 'IsNot',
                  value: 'completed',
                },
              },
            ],
          },
          sorts: [],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'field_task',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Task',
            ja: 'タスク',
            'zh-CN': '待办事项',
            'zh-TW': '待辦事項',
          },
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'field_status',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Status',
            'zh-CN': '状态',
            'zh-TW': '狀態',
            ja: 'ステータス',
          },
          property: {
            options: [
              {
                templateId: 'pending',
                name: 'Pending',
                color: 'blue',
              },
              {
                templateId: 'in_progress',
                name: 'In Progress',
                color: 'orange',
              },
              {
                templateId: 'completed',
                name: 'Completed',
                color: 'green',
              },
            ],
          },
        },
        {
          templateId: 'field_date',
          name: {
            en: 'Completion date',
            'zh-CN': '完成日期',
            'zh-TW': '完成日期',
            ja: '完了日',
          },
          type: 'DATETIME',
          property: {
            autofill: false,
            timeZone: 'Asia/Shanghai',
            dateFormat: 'YYYY/MM/DD',
            includeTime: false,
          },
        },
      ],
      records: [
        {
          data: {
            field_task: 'Prepare for the meeting',
            field_status: ['pending'],
            field_date: '2025-04-20T16:00:00.000Z',
          },
          values: {
            field_status: ['Pending'],
          },
        },
        {
          data: {
            field_task: 'Review the project plan',
            field_status: ['in_progress'],
            field_date: '2025-04-05T16:00:00.000Z',
          },
          values: {
            field_status: ['In Progress'],
          },
        },
        {
          data: {
            field_task: 'Send the weekly report',
            field_status: ['completed'],
            field_date: '2025-04-11T16:00:00.000Z',
          },
          values: {
            field_status: ['Completed'],
          },
        },
        {
          data: {
            field_task: 'Attend team weekly meeting to share progress',
            field_status: ['completed'],
            field_date: '2025-04-11T16:00:00.000Z',
          },
          values: {
            field_status: ['Completed'],
          },
        },
        {
          data: {
            field_task: 'Go to the gym for strength training',
            field_status: ['pending'],
            field_date: '2025-04-11T16:00:00.000Z',
          },
          values: {
            field_status: ['Pending'],
          },
        },
        {
          data: {
            field_task: 'Create a shopping list for next week (including groceries)',
            field_status: ['in_progress'],
            field_date: '2025-04-14T16:00:00.000Z',
          },
          values: {
            field_status: ['In Progress'],
          },
        },
        {
          data: {
            field_task: 'Learn Python programming basics through an online course',
            field_status: ['in_progress'],
            field_date: '2025-04-14T16:00:00.000Z',
          },
          values: {
            field_status: ['In Progress'],
          },
        },
        {
          data: {
            field_task: 'Organize files and clutter on the desk',
            field_status: ['completed'],
            field_date: '2025-04-16T16:00:00.000Z',
          },
          values: {
            field_status: ['Completed'],
          },
        },
        {
          data: {
            field_task: "Reply to last week's unanswered work emails",
            field_status: ['pending'],
            field_date: '2025-04-17T16:00:00.000Z',
          },
          values: {
            field_status: ['Pending'],
          },
        },
        {
          data: {
            field_task: 'Plan the family trip itinerary for next month',
            field_status: ['pending'],
            field_date: '2025-04-18T16:00:00.000Z',
          },
          values: {
            field_status: ['Pending'],
          },
        },
      ],
    },
  ],
  initMissions: [
    {
      name: {
        en: '🚀 Template Guide',
        'zh-CN': '🚀 模板指南',
        'zh-TW': '🚀 模板指南',
        ja: '🚀 テンプレートガイド',
      },
      type: 'READ_TEMPLATE_README',
      templateId: 'beginner-playground',
      time: 10,
      beforeText: {
        title: {
          'zh-CN': '恭喜您，模板已经安装完毕',
          'zh-TW': '恭喜您，模板已經安裝完畢',
          ja: 'おめでとうございます, テンプレートのインストールが完了しました',
          en: 'Congratulations, the template has been installed',
        },
        description: {
          'zh-CN': '下一步请您花几分钟阅读模板的使用指南。',
          'zh-TW': '下一步請您花幾分鐘閱讀模板的使用指南。',
          en: 'Next, please take a few minutes to read the guide on how to use the template.',
          ja: '次に, テンプレートの使用方法についてのチュートリアルを数分間お読みください。',
        },
      },
      assignType: 'DEDICATED',
      forcePopup: true,
      wizardGuideId: 'COMMON_AUTOMATION_TUTORIAL',
      redirect: {
        type: 'SPACE_NODE',
        nodeTemplateId: 'res_database_example',
      },
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
  ],
};

export default template;
