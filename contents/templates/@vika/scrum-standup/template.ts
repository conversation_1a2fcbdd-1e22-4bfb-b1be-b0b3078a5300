import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: '@vika/scrum-standup',
  name: {
    en: 'Daily Standup（Wecom）',
    ja: 'デイリースクラム（WeCom）',
    'zh-CN': '每日站会（企业微信）',
    'zh-TW': '每日站會（企業微信）',
  },
  description: {
    en: 'By automating daily standup notifications to WeCom (WeChat Work) and integrating AI-powered weekly summaries, this solution empowers teams to gain decision-making insights while minimizing manual effort, achieving dual efficiency gains in meeting execution and strategic optimization.',
    ja: 'このテンプレートでは、自動的に毎日のスタンドアップミーティングの通知をWeChatに送信でき、チームメンバーが時間通りに会議のリマインダーを受け取れるようになります。通知の時間と内容をカスタマイズして、伝達効率を上げることもできます。従来型のオフィスチームに適しています。さらに、Bika.aiを使って、Slack、DingTalk、Telegramなどのインスタントメッセンジャートールと自動通知を実現することもできます。',
    'zh-CN':
      '通过自动化每日站会通知至企业微信，叠加AI总结周报，帮助团队在减少人工干预的同时获得决策洞察，实现会议执行与战略优化的双重提效。',
    'zh-TW':
      '该模板可自动发送每日站会通知至企业微信，确保团队成员及时收到会议提醒，并支持自訂通知時間和內容，有助於提高溝通效率，適用於常規辦公團隊。你還可以使用Bika.ai與更多即時通訊工具（如Slack、釘釘、Telegram等）實現自動化通知。',
  },
  cover: '/assets/template/template-cover-daily-standup.png',
  author: 'Arry <<EMAIL>>',
  category: ['project', 'automation'],
  presetUnits: [
    {
      type: 'ROLE',
      templateId: 'role_standup_attendee',
      name: {
        'zh-CN': '每日站会参与者',
        'zh-TW': '每日站會參與者',
        en: 'Daily Standup Attendee',
        ja: 'デイリースタンドアップ参加者',
      },
    },
  ],
  keywords: {
    en: 'scrum,project management,pmo',
    'zh-CN': 'scrum,项目管理,项目办公室',
    'zh-TW': 'scrum,專案管理,專案辦公室',
    ja: 'scrum,プロジェクト管理,PMO',
  },
  personas: {
    en: 'scrum master,scrum team member,product owner,project manager',
    'zh-CN': 'Scrum Master,Scrum 团队成员,产品负责人,项目经理',
    'zh-TW': 'Scrum Master,Scrum 團隊成員,產品負責人,專案經理',
    ja: 'スクラムマスター,スクラムチームメンバー,プロダクトオーナー,プロジェクトマネージャー',
  },
  useCases: {
    'zh-CN':
      '组织站会,确保准时,解决障碍,记录进展,回顾任务,遵循Scrum流程, 分享进展,计划任务,报告障碍,协作解决问题,提高效率,更新状态, 了解进展,确保一致,处理关键任务,解决业务问题,提供反馈,调整方向, 跟踪进展,协调工作,解决障碍,确保交付,与客户沟通,处理需求变更',
    'zh-TW':
      '組織站會,確保準時,解決障礙,記錄進展,回顧任務,遵循Scrum流程, 分享進展,計劃任務,報告障礙,協作解決問題,提高效率,更新狀態, 了解進展,確保一致,處理關鍵任務,解決業務問題,提供反饋,調整方向, 跟蹤進展,協調工作,解決障礙,確保交付,與客戶溝通,處理需求變更',
    en: 'Organize standups, ensure punctuality, resolve blockers, record progress, review tasks, follow Scrum, Share progress, plan tasks, report blockers, collaborate to solve issues, improve efficiency, update status, Understand progress, ensure alignment, handle key tasks, resolve business issues, provide feedback, adjust direction, Track progress, coordinate work, resolve blockers, ensure delivery, communicate with clients, handle changes',
    ja: 'スタンドアップを整理する、時間厳守を確保する、ブロッカーを解決する、進捗を記録する、タスクをレビューする、スクラムをフォローする、進捗を共有する、タスクを計画する、ブロッカーを報告する、問題解決のために協力する、効率を改善する、ステータスを更新する、進捗を理解する、整合性を確保する、主要タスクを処理する、ビジネス上の問題を解決する、フィードバックを提供する、方向性を調整する、進捗を追跡する、作業を調整する、ブロッカーを解決する、納品を確保する、クライアントとコミュニケーションを取る、変更を処理する',
  },
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.2',
  initMissions: [
    {
      name: {
        en: '🚀 Template Guide',
        'zh-CN': '🚀 模板指南',
        'zh-TW': '🚀 模板指南',
        ja: '🚀 テンプレートガイド',
      },
      type: 'READ_TEMPLATE_README',
      templateId: '@vika/scrum-standup',
      time: 10,
      beforeText: {
        title: {
          'zh-CN': '恭喜您，模板已经安装完毕',
          'zh-TW': '恭喜您，模板已經安裝完畢',
          ja: 'おめでとうございます, テンプレートのインストールが完了しました',
          en: 'Congratulations, the template has been installed',
        },
        description: {
          'zh-CN': '下一步请您花几分钟阅读模板的使用指南。',
          'zh-TW': '下一步請您花幾分鐘閱讀模板的使用指南。',
          en: 'Next, please take a few minutes to read the guide on how to use the template.',
          ja: '次に、テンプレートの使用方法についてのチュートリアルを数分間お読みください。',
        },
      },
      assignType: 'DEDICATED',
      forcePopup: true,
      wizardGuideId: 'COMMON_MY_TODO_TUTORIAL',
      redirect: {
        type: 'MY_MISSIONS',
      },
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
    {
      name: {
        'zh-CN': '💡每日站会设置向导 #1: 体验并开启每日晨会自动化提醒',
        'zh-TW': '💡每日站會設置向導 #1: 體驗並開啟每日晨會自動化提醒',
        en: '💡Daily Standup Guide #1: Enable Daily Standup Automation Reminder',
        ja: '💡デイリースタンドアップガイド #1: デイリースタンドアップ自動化リマインダーを有効にする',
      },
      description: {
        'zh-CN':
          '让 Bika 自动每天凌晨 1 点创建任务，并通过邮件提醒团队。点击下方按钮，前往开启自动化流程，并尝试立即发送一封填写报告提醒。在自动化页面，使用“手动执行”按钮立刻体验提醒效果；使用开关，开启或停用当前的自动化流程。',
        'zh-TW':
          '讓 Bika 自動每天凌晨 1 點創建任務，並通過郵件提醒團隊。點擊下方按鈕，前往開啟自動化流程，並嘗試立即發送一封填寫報告提醒。在自動化頁面，使用“手動執行”按鈕立刻體驗提醒效果；使用開關，開啟或停用當前的自動化流程。',
        en: 'Let Bika automatically create tasks at 1 am every day and remind the team by email. Click the button below to go to the automation page and try to send a mission immediately. On the automation page, use the "Manual Execution" button to experience the reminder effect immediately; use the switch to enable or disable the current automation process.',
        ja: 'Bikaに毎日午前1時にタスクを自動作成させ、チームにメールでリマインドさせます。 下のボタンをクリックして自動化ページに移動し、すぐにレポートリマインダーを送信してみてください。 自動化ページで、「手動実行」ボタンを使用してリマインダー効果をすぐに体験し、「スイッチ」を使用して現在の自動化プロセスを有効または無効にします。',
      },
      type: 'REDIRECT_SPACE_NODE',
      nodeTemplateId: 'request_standup_brief',
      canCompleteManually: true,
      buttonText: {
        'zh-CN': '开启自动化',
        'zh-TW': '開啟自動化',
        en: 'Enable Automation',
        ja: '自動化を有効にする',
      },
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
    {
      name: {
        'zh-CN': '💡每日站会设置向导 #2：邀请参与晨会的成员加入空间站',
        'zh-TW': '💡每日站會設置向導 #2：邀請參與晨會的成員加入空間站',
        en: '💡Daily Standup Guide #2: Invite members to join the space',
        ja: '💡デイリースタンドアップガイド #2：メンバーをスペースに招待する',
      },
      description: {
        'zh-CN':
          '只需创建并分享一个邀请链接，就能让团队成员轻松加入空间站。在设置链接时，请选择「每日报告参与者」角色，确保每位成员加入后即可接收相关任务和提醒。点击下方按钮开始。',
        'zh-TW':
          '只需創建並分享一個邀請鏈接，就能讓團隊成員輕鬆加入空間站。在設置鏈接時，請選擇「每日報告參與者」角色，確保每位成員加入後即可接收相關任務和提醒。點擊下方按鈕開始。',
        en: 'Just create and share an invitation link to easily invite team members to join the space. When setting the link, please select the "Daily Report Attendee" role to ensure that each member can receive relevant missions and reminders after joining. Click the button below to start.',
        ja: '招待リンクを作成して共有するだけで、チームメンバーを簡単にスペースに招待できます。 リンクを設定する際は、「デイリースタンドアップ参加者」の役割を選択して、各メンバーが参加後に関連するタスクとリマインダーを受け取れるようにしてください。 下のボタンをクリックして開始します。',
      },
      type: 'INVITE_MEMBER',
      canCompleteManually: true,
      buttonText: {
        'zh-CN': '创建链接',
        'zh-TW': '創建鏈接',
        en: 'Create Link',
        ja: 'リンクを作成',
      },
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
  ],
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'request_standup_brief',
      name: {
        en: 'Automation of Stand-up Briefing Filling Tasks',
        ja: 'スタンドアップブリーフミッション自動化',
        'zh-CN': '站会简报填写任务自动化',
        'zh-TW': '站會簡報填寫任務自動化',
      },
      description: {
        en: 'At 1 AM on weekdays, automatically create a meeting briefing task and send a WeChat notification to the business account at the designated time.',
        ja: '平日の午前1時に、ミーティングのブリーフィングタスクを自動で作成し、指定された時間にWecom通知を送信します。',
        'zh-CN': '工作日凌晨 1 点，自动创建站会简报填写任务，并在指定的时间点给成员发送提醒邮件',
        'zh-TW': '工作日凌晨 1 點，自動創建站會簡報填寫任務，並在指定的時間點發送企業微信通知。',
      },
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'trigger_scheduler',
          description: {
            en: '1 am on workdays',
            ja: '平日の午前1時',
            'zh-CN': '工作日凌晨 1 点',
            'zh-TW': '工作日凌晨 1 點',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['MON', 'TUE', 'WED', 'THU', 'FRI'],
                },
              },
              timezone: 'AUTO',
              datetime: '2025-02-05T17:00:00.000Z',
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'action_create_standup_mission',
          description: {
            en: 'Assign missions to members of the standup role',
            ja: 'スタンドアップロールのメンバーにミッションを割り当てる',
            'zh-CN': '分配站会角色的任务',
            'zh-TW': '分配站會角色的任務',
          },
          actionType: 'CREATE_MISSION',
          input: {
            type: 'MISSION_BODY',
            mission: {
              type: 'CREATE_RECORD',
              templateId: 'mission_create_brief',
              name: {
                en: "<%= _to.name %>, invite you to fill out today's stand-up brief",
                ja: '<%= _to.name %>, 今日のスタンドアップブリーフを記入するようお願いします',
                'zh-CN': '<%= _to.name %>, 邀请你填写今天的晨会简报',
                'zh-TW': '<%= _to.name %>, 邀請你填寫今天的晨會簡報',
              },
              description: {
                en: `Small tip: Use checklists to write "yesterday's completed items" and "today's plans" to make the information more organized and easier for everyone to remember! 😊`,
                ja: '小さなヒント：「昨日の完了事項」と「今日の計画」を書くためにチェックリストを使用して、情報を整理し、誰もが覚えやすくします！😊',
                'zh-CN': '小Tips: 用清单体来写“昨天完成事项”和“今日计划”，让信息更有条理性和便于大家记忆哦！😊',
                'zh-TW': '小Tips: 用清單體來寫“昨天完成事項”和“今日計劃”，讓信息更有條理性和便於大家記憶哦！😊',
              },
              dueDate: {
                end: {
                  type: 'DELAY',
                  unit: 'HOUR',
                  value: 9,
                },
                showEndTime: true,
              },
              assignType: 'DEDICATED',
              to: [
                {
                  type: 'CURRENT_OPERATOR',
                },
              ],
              reminders: [
                {
                  name: {
                    en: "Good morning, Bika reminds you to fill out today's standup brief",
                    ja: 'おはようございます、Bikaは今日のスタンドアップブリーフを記入するようお知らせします',
                    'zh-CN': '早上好，Bika提醒您填写今天的晨会简报',
                    'zh-TW': '早上好，Bika提醒您填寫今天的晨會簡報',
                  },
                  description: {
                    en: 'The daily standup will start at 09:30. Remember to submit the brief before that. Click the button to start filling it out👇',
                    ja: '09:30にデイリースタンドアップが始まります。その前に簡単な報告を提出することを忘れないでください。ボタンをクリックして記入を開始してください👇',
                    'zh-CN': '09:30 将开始每日站会，在这之前记得提交简报哦，点击按钮开始填写👇',
                    'zh-TW': '09:30 將開始每日站會，在這之前記得提交簡報哦，點擊按鈕開始填寫👇',
                  },
                  scheduler: {
                    timezone: 'AUTO',
                    datetime: {
                      hour: 8,
                      minute: 0,
                      type: 'TODAY',
                    },
                  },
                  to: [],
                },
                {
                  name: {
                    en: "You have not submitted today's standup brief",
                    ja: '今日のスタンドアップブリーフを提出していません',
                    'zh-CN': '您尚未提交今日晨会简报',
                    'zh-TW': '您尚未提交今日晨會簡報',
                  },
                  description: {
                    en: 'Reminder: Fill out the brief before 10 am. The system will automatically generate a summary report for everyone, so please fill it out in time.',
                    ja: 'お知らせ：午前10時までに簡単な報告を提出してください。システムは自動的に全員の集計レポートを生成しますので、時間内に提出してください。',
                    'zh-CN': '温馨提示：10点截止填写，系统会自动生成汇总报告给大家，请抓紧时间填写哦。',
                    'zh-TW': '溫馨提示：10點截止填寫，系統會自動生成匯總報告給大家，請抓緊時間填寫哦。',
                  },
                  scheduler: {
                    timezone: 'AUTO',
                    datetime: {
                      hour: 9,
                      minute: 30,
                      type: 'TODAY',
                    },
                  },
                  to: [],
                },
                {
                  name: {
                    en: 'You have not submitted the brief, the mission has expired',
                    ja: '報告を提出していません、ミッションは期限切れです',
                    'zh-CN': '你尚未提交简报，任务已过期',
                    'zh-TW': '你尚未提交簡報，任務已過期',
                  },
                  description: {
                    en: 'The brief should be submitted before 10 am. The system will generate a daily report for everyone later. If you are on leave, please ignore this message.',
                    ja: '午前10時までに報告を提出してください。システムは後で全員に日報を生成します。休暇中の場合は、このメッセージを無視してください。',
                    'zh-CN': '10点截止填写简报，系统稍后将会生成日报给大家。如果你请假了，请忽略本消息。',
                    'zh-TW': '10點截止填寫簡報，系統稍後將會生成日報給大家。如果你請假了，請忽略本消息。',
                  },
                  scheduler: {
                    timezone: 'AUTO',
                    datetime: {
                      hour: 10,
                      minute: 0,
                      type: 'TODAY',
                    },
                  },
                  to: [],
                },
              ],
              databaseTemplateId: 'standup',
            },
          },
        },
        {
          templateId: 'actm3ND8rYyVvUvN8qAXIUME',
          description: {
            en: 'Delay WeCom Push Notification by 7 Hours',
            ja: '企業微信の通知配信を 7 時間遅延する',
            'zh-CN': '延时7小时推送企微消息',
            'zh-TW': '將企業微信訊息推送延遲 7 小時',
          },
          actionType: 'DELAY',
          input: {
            type: 'DELAY',
            unit: 'HOUR',
            value: 7,
          },
        },
        {
          templateId: 'action_send_brief_notification_to_wecom',
          description: {
            en: 'Send a notification to WeCom',
            ja: '企業微信通知を送信',
            'zh-CN': '发送企业微信通知',
            'zh-TW': '發送企業微信通知',
          },
          actionType: 'WECOM_WEBHOOK',
          input: {
            type: 'WECOM_WEBHOOK',
            data: {
              msgtype: 'template_card',
              template_card: {
                source: {
                  desc: 'StandupBot',
                  icon_url: 'https://example.com/standup-icon.png',
                  desc_color: 0,
                },
                card_type: 'text_notice',
                jump_list: [
                  {
                    url: '<%= _space.todoPageUrl %>',
                    type: 1,
                    title: 'Submit Briefing',
                  },
                ],
                main_title: {
                  title: '🔔 Daily Standup Reminder',
                },
                quote_area: {
                  type: 0,
                  title: '⏰ Submit Briefing!',
                  quote_text: '📝 Please submit your daily standup briefing with key tasks before 09:00 AM.',
                },
                card_action: {
                  url: '<%= _space.todoPageUrl %>',
                  type: 1,
                },
                sub_title_text:
                  'Silent Protocol Steps:\n' +
                  ' 1. 📝 Submit 3-5 tasks.\n' +
                  ' 2. 🤫 Silent session.\n' +
                  ' 3. 💬 Comment during review.',
                emphasis_content: {
                  desc: '🕒 Daily',
                  title: '⏰ 09:00 AM',
                },
                horizontal_content_list: [
                  {
                    url: '<%= _space.todoPageUrl %>',
                    type: 1,
                    value: '👉 Submit Now',
                    keyname: 'quick_action',
                  },
                ],
              },
            },
            urlType: 'URL',
            url: '',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'standup',
      name: {
        en: 'Morning Meeting Briefing',
        ja: 'すべてのスタンドアップブリーフ',
        'zh-CN': '晨会简报',
        'zh-TW': '晨會簡報',
      },
      description: {
        en: 'The stand-up briefs of all team members are recorded here.',
        ja: 'すべてのチームメンバーのスタンドアップブリーフがここに記録されます',
        'zh-CN': '记录所有成员的站会简报',
        'zh-TW': '記錄所有成員的站會簡報',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'all',
          name: {
            en: 'All briefs',
            ja: 'すべてのブリーフ',
            'zh-CN': '所有简报',
            'zh-TW': '所有簡報',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'field_overview',
              hidden: false,
              width: 230,
            },
            {
              templateId: 'date',
              hidden: false,
            },
            {
              templateId: 'employee',
              hidden: false,
              width: 216,
            },
            {
              templateId: 'yesterday',
              hidden: false,
              width: 257,
            },
            {
              templateId: 'today',
              hidden: false,
              width: 269,
            },
            {
              templateId: 'challenges',
              hidden: false,
              width: 263,
            },
            {
              templateId: 'attachments',
              hidden: false,
            },
            {
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'view_my',
          name: {
            en: 'My Briefs',
            ja: '私のブリーフ',
            'zh-CN': '我的简报',
            'zh-TW': '我的簡報',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'employee',
                fieldType: 'CREATED_BY',
                clause: {
                  operator: 'Is',
                  value: 'Self',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'field_overview',
              hidden: false,
            },
            {
              templateId: 'date',
              hidden: false,
            },
            {
              templateId: 'employee',
              hidden: false,
            },
            {
              templateId: 'yesterday',
              hidden: false,
            },
            {
              templateId: 'today',
              hidden: false,
            },
            {
              templateId: 'challenges',
              hidden: false,
            },
            {
              templateId: 'attachments',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'view_today',
          name: {
            en: "Today's Briefs",
            ja: '今日のブリーフ',
            'zh-CN': '今日简报',
            'zh-TW': '今日簡報',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'date',
                fieldType: 'DATETIME',
                clause: {
                  operator: 'Is',
                  value: ['Today'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'field_overview',
              hidden: false,
            },
            {
              templateId: 'date',
              hidden: false,
            },
            {
              templateId: 'employee',
              hidden: false,
            },
            {
              templateId: 'yesterday',
              hidden: false,
            },
            {
              templateId: 'today',
              hidden: false,
            },
            {
              templateId: 'challenges',
              hidden: false,
            },
            {
              templateId: 'attachments',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'view_our_team',
          name: {
            en: "Today's Briefs of Our Team",
            ja: '本日のチームブリーフ',
            'zh-CN': '本组今日简报',
            'zh-TW': '本組今日簡報',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'employee',
                fieldType: 'CREATED_BY',
                clause: {
                  operator: 'Contains',
                  value: ['TEAM_AND_SUBTEAM_MEMBERS'],
                },
              },
              {
                fieldTemplateId: 'date',
                fieldType: 'DATETIME',
                clause: {
                  operator: 'Is',
                  value: ['Today'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'field_overview',
              hidden: false,
            },
            {
              templateId: 'date',
              hidden: false,
            },
            {
              templateId: 'employee',
              hidden: false,
            },
            {
              templateId: 'yesterday',
              hidden: false,
            },
            {
              templateId: 'today',
              hidden: false,
            },
            {
              templateId: 'challenges',
              hidden: false,
            },
            {
              templateId: 'attachments',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'FORMULA',
          templateId: 'field_overview',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Brief entry',
            ja: 'ブリーフ項目',
            'zh-CN': '简报条目',
            'zh-TW': '簡報條目',
          },
          property: {
            expressionTemplate: "{date}&'-'&{employee}",
          },
          primary: true,
        },
        {
          type: 'DATETIME',
          templateId: 'date',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Date',
            ja: '日付',
            'zh-CN': '晨会日期',
            'zh-TW': '晨會日期',
          },
          description: {
            en: 'The date of the standup meeting',
            ja: 'スタンドアップミーティングの日付',
            'zh-CN': '站会日期',
            'zh-TW': '站會日期',
          },
          property: {
            timeZone: 'Asia/Shanghai',
            dateFormat: 'YYYY/MM/DD',
            includeTime: false,
            autofill: true,
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'employee',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Member name',
            ja: 'メンバー名',
            'zh-CN': '成员姓名',
            'zh-TW': '成員姓名',
          },
          property: {
            options: [
              {
                id: 'optMDy0uCTApYQAuNeveOJFd',
                name: 'John Doe',
                color: 'deepPurple',
              },
              {
                id: 'optbXR4yHDbZjGI0nP8E8lS3',
                name: 'Jane Smith',
                color: 'indigo',
              },
              {
                id: 'optlXTfKSDPltC5gUt64qXbM',
                name: 'Bob Johnson',
                color: 'blue',
              },
              {
                id: 'opt43EJTSCTz6ETLCaLdrNJQ',
                name: 'Alice Williams',
                color: 'teal',
              },
              {
                id: 'optJjnk99t4XFtRT1x3XPO1i',
                name: 'Charlie Brown',
                color: 'green',
              },
              {
                id: 'optsX78SERG7npRaYicHUOVA',
                name: 'Eve Davis',
                color: 'yellow',
              },
              {
                id: 'optpOvb4SdDKmhsj6SxU9wtH',
                name: 'Frank Miller',
                color: 'orange',
              },
              {
                id: 'opty9Pn0mRV2BM0TRja5FZxx',
                name: 'Grace Taylor',
                color: 'tangerine',
              },
              {
                id: 'optbjjAqKNbwMEsBoiZ3D7oo',
                name: 'Henry Moore',
                color: 'pink',
              },
              {
                id: 'optUpFWiSDF75dttier8H9k1',
                name: 'Irene Wilson',
                color: 'red',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'yesterday',
          privilege: 'NAME_EDIT',
          name: {
            en: 'What did you do yesterday?',
            ja: '昨日の完了事項',
            'zh-CN': '昨日完成事项',
            'zh-TW': '昨日完成事項',
          },
          description: {
            en: 'Bullet points are recommended, (📢: Write a summary of last week on Monday)',
            ja: '箇条書きをお勧めします、月曜日に先週の要約を書いてください',
            'zh-CN': '建议用清单体写，周一写上周总结',
            'zh-TW': '建議用清單體寫，週一寫上週總結',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'today',
          privilege: 'NAME_EDIT',
          name: {
            en: 'What will you do today?',
            ja: '今日の計画',
            'zh-CN': '今日计划',
            'zh-TW': '今日計劃',
          },
          description: {
            en: 'Bullet points are recommended',
            ja: '箇条書きをお勧めします',
            'zh-CN': '建议用清单体写',
            'zh-TW': '建議用清單體寫',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'challenges',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Challenges and help needed',
            ja: '遭遇した障害と必要なヘルプ',
            'zh-CN': '遇到的障碍和需要帮助',
            'zh-TW': '遇到的障礙和需要幫助',
          },
          description: {
            en: 'If you have any obstacles or need help, please write them down here.',
            ja: '障害がある場合やヘルプが必要な場合は、ここに書いてください。',
            'zh-CN': '如果遇到任何障碍或需要帮助，请在这里写下来。',
            'zh-TW': '如果遇到任何障礙或需要幫助，請在這裡寫下來。',
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'attachments',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Attachments',
            ja: '添付ファイル',
            'zh-CN': '附件',
            'zh-TW': '附件',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recSz0bxXme5NLxFqvkvA7aJ',
          data: {
            date: '2025-04-28T07:50:38.578Z',
            today:
              '- Attend sales training  \n' +
              '- Contact 10 new leads  \n' +
              "- Prepare for tomorrow's client meeting  ",
            employee: ['optMDy0uCTApYQAuNeveOJFd'],
            yesterday:
              '- Completed client presentation  \n' +
              '- Followed up with 5 potential clients  \n' +
              '- Updated sales pipeline  ',
            challenges: 'Need more marketing materials for new products.  ',
            field_overview: '2025/04/28-John Doe',
          },
          values: {
            date: '2025/04/28',
            today:
              '- Attend sales training  \n' +
              '- Contact 10 new leads  \n' +
              "- Prepare for tomorrow's client meeting  ",
            employee: ['John Doe'],
            yesterday:
              '- Completed client presentation  \n' +
              '- Followed up with 5 potential clients  \n' +
              '- Updated sales pipeline  ',
            challenges: 'Need more marketing materials for new products.  ',
            field_overview: '2025/04/28-John Doe',
          },
        },
        {
          templateId: 'recESz6RGhAiSrau2aHmrPVs',
          data: {
            date: '2025-04-28T07:51:53.350Z',
            today:
              '- Launch new social media posts  \n' +
              '- Meet with design team for new project  \n' +
              '- Prepare marketing report  ',
            employee: ['optbXR4yHDbZjGI0nP8E8lS3'],
            yesterday:
              '- Finalized social media campaign  \n' +
              '- Designed 3 marketing posters  \n' +
              '- Analyzed website traffic  ',
            challenges: 'Need feedback from sales team for campaign adjustment.  ',
            field_overview: '2025/04/28-Jane Smith',
          },
          values: {
            date: '2025/04/28',
            today:
              '- Launch new social media posts  \n' +
              '- Meet with design team for new project  \n' +
              '- Prepare marketing report  ',
            employee: ['Jane Smith'],
            yesterday:
              '- Finalized social media campaign  \n' +
              '- Designed 3 marketing posters  \n' +
              '- Analyzed website traffic  ',
            challenges: 'Need feedback from sales team for campaign adjustment.  ',
            field_overview: '2025/04/28-Jane Smith',
          },
        },
        {
          templateId: 'recW73NgpvF2MLMQCg6mqlzN',
          data: {
            date: '2025-04-28T07:53:25.906Z',
            today:
              '- Continue product optimization  \n' +
              '- Attend technical discussion meeting  \n' +
              '- Start new prototype design  ',
            employee: ['optlXTfKSDPltC5gUt64qXbM'],
            yesterday:
              '- Conducted product testing        \n' +
              '- Fixed 2 software bugs  \n' +
              '- Reviewed technical documents',
            challenges: 'Need more testing equipment. ',
            field_overview: '2025/04/28-Bob Johnson',
          },
          values: {
            date: '2025/04/28',
            today:
              '- Continue product optimization  \n' +
              '- Attend technical discussion meeting  \n' +
              '- Start new prototype design  ',
            employee: ['Bob Johnson'],
            yesterday:
              '- Conducted product testing        \n' +
              '- Fixed 2 software bugs  \n' +
              '- Reviewed technical documents',
            challenges: 'Need more testing equipment. ',
            field_overview: '2025/04/28-Bob Johnson',
          },
        },
        {
          templateId: 'recNVu8QQzvbC6Udxr0tyPMs',
          data: {
            date: '2025-04-28T07:54:01.531Z',
            today:
              '- Review expense reports        \n' +
              '- Finalize financial report  \n' +
              '- Assist with budget planning',
            employee: ['opt43EJTSCTz6ETLCaLdrNJQ'],
            yesterday:
              '- Processed 10 invoices  \n' +
              '- Reconciled bank statements  \n' +
              '- Prepared financial report draft  ',
            challenges: 'Need clarification on some expense items.  ',
            field_overview: '2025/04/28-Alice Williams',
          },
          values: {
            date: '2025/04/28',
            today:
              '- Review expense reports        \n' +
              '- Finalize financial report  \n' +
              '- Assist with budget planning',
            employee: ['Alice Williams'],
            yesterday:
              '- Processed 10 invoices  \n' +
              '- Reconciled bank statements  \n' +
              '- Prepared financial report draft  ',
            challenges: 'Need clarification on some expense items.  ',
            field_overview: '2025/04/28-Alice Williams',
          },
        },
        {
          templateId: 'recG9GWUrYxNnjLNf6ffp8yn',
          data: {
            date: '2025-04-28T07:54:43.480Z',
            today:
              '- Schedule second - round interviews  \n' +
              '- Conduct new employee orientation  \n' +
              '- Review benefits plans',
            employee: ['optJjnk99t4XFtRT1x3XPO1i'],
            yesterday:
              '- Interviewed 3 job applicants  \n' +
              '- Updated employee records  \n' +
              '- Prepared training materials  ',
            challenges: 'Need more time to review all applications. ',
            field_overview: '2025/04/28-Charlie Brown',
          },
          values: {
            date: '2025/04/28',
            today:
              '- Schedule second - round interviews  \n' +
              '- Conduct new employee orientation  \n' +
              '- Review benefits plans',
            employee: ['Charlie Brown'],
            yesterday:
              '- Interviewed 3 job applicants  \n' +
              '- Updated employee records  \n' +
              '- Prepared training materials  ',
            challenges: 'Need more time to review all applications. ',
            field_overview: '2025/04/28-Charlie Brown',
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'auto_standup_summary',
      name: {
        en: 'Automated Summarization of Daily Stand-up Meetings',
        ja: 'スタンドアップサマリーの自動化',
        'zh-CN': '每日站会自动化汇总',
        'zh-TW': '每日站會自動化彙總',
      },
      description: {
        en: 'Automatically summarize the standup brief submission status at 10 am on workdays and send the report to all members',
        ja: '平日の朝10時に、スタンドアップブリーフの提出状況を自動的にまとめ、全員にレポートを送信します',
        'zh-CN': '工作日早晨 10 点，自动汇总站会简报填写情况，并发送报告给所有成员',
        'zh-TW': '工作日早晨 10 點，自動匯總站會簡報填寫情況，並發送報告給所有成員',
      },
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'trigger_scheduler_summary',
          description: {
            en: '10 am on workdays',
            ja: '平日の朝10時',
            'zh-CN': '工作日早晨 10 点',
            'zh-TW': '工作日早晨 10 點',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['MON', 'TUE', 'WED', 'THU', 'FRI'],
                },
              },
              timezone: 'AUTO',
              datetime: '2025-02-06T02:00:00.000Z',
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'action_find_standup_attendee',
          description: {
            en: 'Find members who attend the standup',
            ja: 'スタンドアップに参加するメンバーを検索',
            'zh-CN': '查找参与晨会的成员',
            'zh-TW': '查找參與晨會的成員',
          },
          actionType: 'FIND_MEMBERS',
          input: {
            type: 'MEMBER',
            by: [
              {
                type: 'SPECIFY_UNITS',
                unitIds: [],
              },
            ],
          },
        },
        {
          templateId: 'action_count_total_briefs',
          description: {
            en: "Count all members' standup briefs",
            ja: '全メンバーのスタンドアップブリーフをカウント',
            'zh-CN': '统计所有成员的站会简报',
            'zh-TW': '統計所有成員的站會簡報',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_VIEW',
            viewTemplateId: 'view_today',
            databaseTemplateId: 'standup',
          },
        },
        {
          templateId: 'summary_standup_report',
          description: {
            en: 'Send standup summary report',
            ja: 'スタンドアップサマリーレポートを送信',
            'zh-CN': '发送站会汇总报告',
            'zh-TW': '發送站會彙總報告',
          },
          actionType: 'SEND_REPORT',
          input: {
            to: [
              {
                type: 'ADMIN',
              },
            ],
            markdown:
              '## 1. Data Summary  \n' +
              '<%  \n' +
              'const participants = _.uniqWith(_actions.action_count_total_briefs.records, (a,b) => a.cells.employee.value===b.cells.employee.value?true:false);  \n' +
              'const members = _actions.action_find_standup_attendee.members;  \n' +
              'const absentees = _.differenceWith(members, participants, (a,b) => a.userId===b.cells.employee.data?true:false);  \n' +
              '%>\n' +
              '  \n' +
              '- Total attendees for the standup: <%= members.length %> people  \n' +
              '- Actual participants: <%= participants.length %> people  \n' +
              '- Attendance rate: <%= members.length > 0 ? (_.divide(participants.length, members.length).toFixed(2) * 100) : 0 %>%  \n' +
              '- Total reports submitted: <%= _actions.action_count_total_briefs.records.length %> reports  \n' +
              "- Absentee leave situation: <%= absentees.length %> people<% if(absentees.length>0) { %>, namely: <%= absentees.map(absentee => absentee.name).join('、') %><% } %>\n" +
              '  \n' +
              '## 2. Member Briefs Overview  \n' +
              '<%= _renderRecordsAsGrid(_actions.action_count_total_briefs.records, ["employee","yesterday","today"]) %>',
            json: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      text: '## 1. Data Summary',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '<%',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: 'const participants = _.uniqWith(_actions.action_count_total_briefs.records, (a,b) => a.cells.employee.value===b.cells.employee.value?true:false);',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: 'const members = _actions.action_find_standup_attendee.members;',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: 'const absentees = _.differenceWith(members, participants, (a,b) => a.userId===b.cells.employee.data?true:false);',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '%>',
                      type: 'text',
                    },
                  ],
                },
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '- Total attendees for the standup: <%= members.length %> people',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '- Actual participants: <%= participants.length %> people',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '- Attendance rate: <%= members.length > 0 ? (_.divide(participants.length, members.length).toFixed(2) * 100) : 0 %>%',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '- Total reports submitted: ',
                      type: 'text',
                    },
                    {
                      type: 'variable',
                      attrs: {
                        ids: '_actions,action_count_total_briefs,records,length',
                        tips: '',
                        names: '执行器,查找记录,records,length',
                      },
                    },
                    {
                      text: ' reports',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: "- Absentee leave situation: <%= absentees.length %> people<% if(absentees.length>0) { %>, namely: <%= absentees.map(absentee => absentee.name).join('、') %><% } %>",
                      type: 'text',
                    },
                  ],
                },
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '## 2. Member Briefs Overview',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      type: 'variable',
                      attrs: {
                        ids: '_renderRecordsAsGrid(_actions.action_count_total_briefs.records, ["employee","yesterday","today"])',
                        tips: '选中的字段: 记录列表, 记录列表, 记录列表',
                        names: '执行器,查找记录,网格列表',
                      },
                    },
                  ],
                },
              ],
            },
            subject: 'Daily Standup Report - <%= new Date().toLocaleDateString() %>',
            type: 'MARKDOWN',
          },
        },
        {
          templateId: 'action_send_report_to_wecom',
          description: {
            en: 'Send a report to WeCom',
            ja: 'WeComにレポートを送信する',
            'zh-CN': '发送报告到企业微信',
            'zh-TW': '發送報告到企業微信',
          },
          actionType: 'WECOM_WEBHOOK',
          input: {
            type: 'WECOM_WEBHOOK',
            data: {
              msgtype: 'markdown',
              markdown: {
                content:
                  '## Daily morning meeting report- <%= new Date().toLocaleDateString() %>\n' +
                  '<%\n' +
                  "// Define data if it doesn't exist in the context\n" +
                  'var _actions = _actions || {};\n' +
                  '_actions.action_count_total_briefs = _actions.action_count_total_briefs || { records: [] };\n' +
                  '_actions.action_find_standup_attendee = _actions.action_find_standup_attendee || { members: [] };\n' +
                  '// Add Lodash safety check\n' +
                  'var _ = _ || {\n' +
                  'uniqWith: function(arr, comp) { return arr; },\n' +
                  'differenceWith: function(arr1, arr2, comp) { return arr1; },\n' +
                  'divide: function(a, b) { return a / b; }\n' +
                  '};\n' +
                  '// Safely access data with default empty arrays\n' +
                  'var records = _actions.action_count_total_briefs.records || [];\n' +
                  'var members = _actions.action_find_standup_attendee.members || [];\n' +
                  '// Get unique participants\n' +
                  'var participants = _.uniqWith(records, function(a, b) {\n' +
                  'return (a && a.cells && a.cells.employee && a.cells.employee.value) ===\n' +
                  '(b && b.cells && b.cells.employee && b.cells.employee.value);\n' +
                  '});\n' +
                  '// Get absentees with null checks\n' +
                  'var absentees = _.differenceWith(members, records, function(member, record) {\n' +
                  'return (member && member.userId) ===\n' +
                  '(record && record.cells && record.cells.employee && record.cells.employee.data);\n' +
                  '});\n' +
                  '%>\n' +
                  '📊Scheduled attendee: <%= members.length %> people\n' +
                  '✅Actual participants: <%= participants.length %> People\n' +
                  '📈Attendance rate: <%= members.length > 0 ? (_.divide(participants.length, members.length) * 100).toFixed(2) : 0 %>%\n' +
                  '📝Briefings submitted: <%= records.length %> copies\n' +
                  "🚫Absentees and leave statuses: <%= absentees.length %> people<% if(absentees.length > 0) { %>，They are these people respectively：<%= absentees.map(function(absentee) { return absentee.name; }).join('、') %><% } %>",
              },
            },
            urlType: 'URL',
            url: '',
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoGXQJvlZWB88C6uB0Mu8wB',
      name: {
        en: 'AI to Analyze the Weekly Work Situation',
        ja: 'AIが每周の仕事状況を分析する',
        'zh-CN': 'AI分析每周工作情况',
        'zh-TW': 'AI 分析每週工作情況',
      },
      description: {
        en: 'Every Monday at 10:30 AM, AI generates a weekly report sent to WeCom and creates an email summary.',
        ja: '毎週月曜 10 時 30 分、AI が週次レポートを WeCom に送信し、メール報告書を自動作成します。',
        'zh-CN': '每周一10:30AI 生成周报发企微并生成报告邮件。',
        'zh-TW': '每週一 10:30 由 AI 生成週報發送至 WeCom，並產生郵件報告書。',
      },
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'trgJxitllus3a4TFAqIyvUfq',
          description: {
            en: 'Triggered every Monday morning at 10:30 AM',
            ja: '毎週月曜日の朝 10 時 30 分にトリガー',
            'zh-CN': '每周一早上10:30触发',
            'zh-TW': '每週一早上 10:30 觸發',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['MON'],
                },
              },
              datetime: '2025-02-12T02:30:10.986Z',
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'actvhqBEmXu0w1J92y6i2hJ5',
          description: {
            en: 'Find morning meeting participants',
            ja: '朝会の参加者を確認する',
            'zh-CN': '查找「每日站会参与者」角色成员',
            'zh-TW': '查找晨會參與者',
          },
          actionType: 'FIND_MEMBERS',
          input: {
            type: 'MEMBER',
            by: [
              {
                type: 'UNIT_ROLE',
                roleTemplateId: '',
              },
            ],
          },
        },
        {
          templateId: 'actD2xoxHaC6d2RsBhF9CjOp',
          description: {
            en: 'Find all tasks of the member from last week  ',
            ja: '該当メンバーの先週の全タスクを確認する',
            'zh-CN': '找出该成员上周的所有任务',
            'zh-TW': '找出該成員上週的所有任務',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'date',
                  fieldType: 'DATETIME',
                  clause: {
                    operator: 'Is',
                    value: ['PreviousWeek'],
                  },
                },
              ],
            },
            databaseTemplateId: 'standup',
          },
        },
        {
          templateId: 'act6bEgmoiTFEs6tERc7Ghm4',
          description: {
            en: " Generate a summary report of this week's tasks",
            ja: '今週のタスクのサマリーレポートを作成する',
            'zh-CN': '基于成员本周日报情况生成智能周报',
            'zh-TW': '生成本週任務總結報告',
          },
          actionType: 'DEEPSEEK',
          input: {
            urlType: 'URL',
            type: 'OPENAI_GENERATE_TEXT',
            integrationId: '',
            prompt:
              'Please generate a concise weekly summary based on the task records of all employees, highlighting key progress on tasks completed by each individual. The summary should include the following:\n' +
              'Key Achievements and Highlights of Completed Tasks\n' +
              "Briefly describe the key task progress completed by each employee this week, including specific tasks, timeframes, and results. Highlight each employee's name to make it easily identifiable. Keep the descriptions concise, avoiding lengthy or complex expressions, and ensure the task progress for each employee is clear at a glance.\n" +
              'Work Status Analysis, focusing on the following aspects:\n' +
              'Which tasks took longer and require further attention.\n' +
              'Any factors hindering task progress (e.g., lack of resources, external dependencies).\n' +
              'Any delayed tasks or tasks not completed as planned, which may need adjustment.\n' +
              'Please ensure the language is concise and avoid repeating content.\n' +
              'Output in English.\n' +
              'Example output format:\n' +
              'Zhang San (Department A)\n' +
              'Completed: Completed the initial design of Project A, submitted on time, and received team feedback.\n' +
              'Not Completed: Progressed with the department meeting records and submitted the summary report.\n' +
              "Analysis: Zhang San's task progress is on track; Project A's design was completed on time, but the department meeting records are progressing slowly. Further attention is needed to identify any delays or obstacles.\n" +
              '\n' +
              'Li Si (Department B)\n' +
              'Completed: Optimized the functionality of System B, improving overall efficiency, and passed the tests.\n' +
              'Not Completed: Followed up on customer requirements analysis, ongoing.\n' +
              "Analysis: System B's functionality optimization is completed; the requirements analysis is still in progress. Attention should be paid to whether customer feedback affects progress.\n" +
              '\n' +
              'Wang Wu (Department B)\n' +
              'Completed: Optimized the functionality of System B, improving overall efficiency, and passed the tests.\n' +
              'Not Completed: Followed up on customer requirements analysis, ongoing.\n' +
              "Analysis: System B's functionality optimization is completed; the requirements analysis is still in progress. Attention should be paid to whether customer feedback affects progress.\n" +
              '\n' +
              "The content between each employee's summary should be separated by a long divider line for easier distinction. (This sentence should not be included in the report content.)\n" +
              '\n' +
              "<%= _renderRecordsAsGrid(_actions.actD2xoxHaC6d2RsBhF9CjOp.records, ['fldmIee6wMwzUqXjsoMAW6MK','fld59uRhgOOZ7l29zXAxeOsO','fldhMdhuIdgvDTyCmLXSsjut','fldHsDBscqK7yBAfqhpCG0c4','fldLJbealNp8guwcG3OCliv2']) %> ",
            baseUrl: 'https://api.deepseek.com/',
            apiKey: '',
            model: 'deepseek-chat',
            timeout: 300,
          },
        },
        {
          templateId: 'act11ZDON1FzBVE6TS0VhS0U',
          description: {
            en: 'Send a customized report to the specified group chat ',
            ja: '指定されたグループチャットにカスタムレポートを送信する',
            'zh-CN': '给指定群聊发送定制报告',
            'zh-TW': '給指定群聊發送定制報告',
          },
          actionType: 'WECOM_WEBHOOK',
          input: {
            type: 'WECOM_WEBHOOK',
            data: {
              msgtype: 'markdown',
              markdown: {
                content:
                  '# ⭐  Weekly task summary report  ⭐          \n' +
                  '                    \n' +
                  "Hi, the following is a summary of the task progress of all employees in the company this week (AI analysis) to help you get a full picture of the team's work status and progress.                          \n" +
                  '              \n' +
                  '<%= _actions.act6bEgmoiTFEs6tERc7Ghm4.body.choices[0].message.content %>',
              },
            },
            urlType: 'URL',
            url: '',
          },
        },
        {
          templateId: 'actTrLX069YhHIJuQ0fNJ8Gz',
          description: {
            en: " Generate a summary report of this week's tasks",
            ja: '今週のタスクのサマリーレポートを作成する',
            'zh-CN': '给指定的管理者发送定制报告',
            'zh-TW': '給管理者發送定制報告',
          },
          actionType: 'SEND_REPORT',
          input: {
            to: [
              {
                type: 'ADMIN',
              },
            ],
            markdown:
              "Hi, the following is a summary of the task progress of all employees in the company this week (AI analysis) to help you get a full picture of the team's work status and progress.                                       \n" +
              '                \n' +
              '<%= _actions.act6bEgmoiTFEs6tERc7Ghm4.body.choices[0].message.content %>',
            json: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      text: "Hi, the following is a summary of the task progress of all employees in the company this week (AI analysis) to help you get a full picture of the team's work status and progress.                                     ",
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '                ',
                      type: 'text',
                    },
                  ],
                },
                {
                  type: 'paragraph',
                  content: [
                    {
                      type: 'variable',
                      attrs: {
                        ids: ['_actions', 'act6bEgmoiTFEs6tERc7Ghm4', 'body', 'choices', '[0]', 'message', 'content'],
                        tips: '',
                        names: ['执行器', 'OpenAI - 生成文本', 'body', 'choices', '#1', 'message', 'content'],
                      },
                    },
                  ],
                },
              ],
            },
            subject: '⭐  Weekly task summary report  ⭐',
            type: 'MARKDOWN',
          },
        },
      ],
    },
  ],
};

export default template;
