import type { Meta, StoryObj } from '@storybook/react';
import { NodeIcon } from '@bika/ui/node/icon';
import type { INodeIconValue } from '@bika/types/node/bo';
import { Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/text-components';

export default {
  title: '@bika/ui/NodeIcon/Icon Kind',
  component: NodeIcon,
  parameters: {
    layout: 'centered',
  },
  args: {},
  tags: ['autodocs'],
} satisfies Meta<typeof NodeIcon>;

type Story = StoryObj<typeof NodeIcon>;

// Basic icon rendering
export const BasicIcon: Story = {
  args: {
    value: {
      kind: 'icon',
      icon: 'components/add_outlined',
    } as INodeIconValue,
    size: 32,
  },
};

// Icon with custom color
export const IconWithCustomColor: Story = {
  args: {
    value: {
      kind: 'icon',
      icon: 'components/search_outlined',
    } as INodeIconValue,
    size: 32,
    color: '#ff6b6b',
  },
};

// Different sizes
export const DifferentSizes: Story = {
  render: () => (
    <Box display="flex" gap={2} alignItems="center">
      <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
        <NodeIcon
          value={{ kind: 'icon', icon: 'components/file_outlined' } as INodeIconValue}
          size={16}
        />
        <Typography level="body-xs">16px</Typography>
      </Box>
      <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
        <NodeIcon
          value={{ kind: 'icon', icon: 'components/file_outlined' } as INodeIconValue}
          size={24}
        />
        <Typography level="body-xs">24px</Typography>
      </Box>
      <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
        <NodeIcon
          value={{ kind: 'icon', icon: 'components/file_outlined' } as INodeIconValue}
          size={32}
        />
        <Typography level="body-xs">32px</Typography>
      </Box>
      <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
        <NodeIcon
          value={{ kind: 'icon', icon: 'components/file_outlined' } as INodeIconValue}
          size={48}
        />
        <Typography level="body-xs">48px</Typography>
      </Box>
    </Box>
  ),
};

// Round vs Square
export const RoundVsSquare: Story = {
  render: () => (
    <Box display="flex" gap={4} alignItems="center">
      <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
        <NodeIcon
          value={{ kind: 'icon', icon: 'components/robot_outlined' } as INodeIconValue}
          size={48}
          isRound={false}
        />
        <Typography level="body-xs">Square</Typography>
      </Box>
      <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
        <NodeIcon
          value={{ kind: 'icon', icon: 'components/robot_outlined' } as INodeIconValue}
          size={48}
          isRound={true}
        />
        <Typography level="body-xs">Round</Typography>
      </Box>
    </Box>
  ),
};

// Multiple different icons
export const MultipleIcons: Story = {
  render: () => (
    <Box display="flex" gap={2} flexWrap="wrap">
      {[
        'components/add_outlined',
        'components/search_outlined',
        'components/file_outlined',
        'components/folder_outlined',
        'components/delete_outlined',
        'components/robot_outlined',
        'components/service_outlined',
      ].map((iconType) => (
        <Box key={iconType} display="flex" flexDirection="column" alignItems="center" gap={1}>
          <NodeIcon
            value={{ kind: 'icon', icon: iconType } as INodeIconValue}
            size={32}
          />
          <Typography level="body-xs" textAlign="center" sx={{ maxWidth: 80, wordBreak: 'break-word' }}>
            {iconType.split('/')[1]}
          </Typography>
        </Box>
      ))}
    </Box>
  ),
};
