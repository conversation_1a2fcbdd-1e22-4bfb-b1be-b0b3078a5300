import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'contractor-time-tracker',
  name: {
    en: 'Contractor Time Tracker',
    'zh-CN': '承包商时间追踪器',
  },
  description: {
    en: 'Contractor Time Tracker streamlines work data management by connecting tables, simplifying task, personnel, and client tracking for improved efficiency and accuracy in managing projects.',
    'zh-CN': '承包商时间追踪器通过连接表格简化任务、人员和客户跟踪，提高项目管理的效率和准确性。',
  },
  cover: '/assets/template/template-cover-contractor-time-tracker.png',
  author: '<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>',
  category: ['project', 'operation'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.7',
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoF6Ucy06j2TfD2K8RJ5B0a',
      name: {
        en: 'Service completion notice',
        'zh-CN': '服务完成通知',
      },
      triggers: [
        {
          triggerType: 'RECORD_MATCH',
          templateId: 'trg75I8h8AROainwoa9kGobD',
          description: {
            en: 'When the work is completed',
            'zh-CN': '工作完成时',
          },
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldWq1298CFTGLlaWyRrGwPF',
                  fieldType: 'CHECKBOX',
                  clause: {
                    operator: 'Is',
                    value: true,
                  },
                },
              ],
            },
            databaseTemplateId: 'datmIG43x4jODfgnC9T3E8UI',
          },
        },
      ],
      actions: [
        {
          templateId: 'actfYBdJg2F8w0DZ2xqye9ZA',
          description: {
            en: "Find Worker's email address",
            'zh-CN': '查找工作者的电子邮件地址',
          },
          actionType: 'FIND_RECORDS',
          input: {
            interruptIfNoRecord: true,
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldt7xL1MdjafKQo73tu7F6t',
                  fieldType: 'LINK',
                  clause: {
                    operator: 'Contains',
                    value: ['<%= _triggers.trg75I8h8AROainwoa9kGobD.record.cells.fldWQQilfNfKNBNxfRRcocEF.value %>'],
                  },
                },
              ],
            },
            databaseTemplateId: 'datJZkfs9xC7LkgjO6xPfqLM',
          },
        },
        {
          templateId: 'actoTI2wf6sPsSR8KNL5lVXz',
          description: {
            en: 'Send pay slips to Worker',
            'zh-CN': '向工作者发送工资条',
          },
          actionType: 'SEND_EMAIL',
          input: {
            subject: 'Your work has been completed. Thank you for your service.',
            body: {
              markdown:
                'Dear <%= _actions.actfYBdJg2F8w0DZ2xqye9ZA.records[0].cells.fldClUcZWmzJzwamgjhHPaPh.value %>,\n' +
                'Thank you for your service. We are now sending you the wage settlement details. Please check and receive it.\n' +
                '\n' +
                "<%= _renderRecordsAsList(_triggers.trg75I8h8AROainwoa9kGobD.record, ['fldWQQilfNfKNBNxfRRcocEF','fld9F51yWXBHMIrW0zTWYCa2','fld1LxkShWTapdf26DRdPSv7','fld1DOMyVkXQg5M36sWqC9w3','fldYRgGqUo7YzvyFR7cat64o','fldox1nmGmR7Qxs61IiO2roc']) %>\n" +
                'Thanks again for your hard work. We look forward to having the opportunity to continue our cooperation in the future! If you have any questions about the settlement details, please contact us at [phone number].\n' +
                '\n' +
                '[Company Name]',
              json: {
                type: 'doc',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Dear ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_actions',
                            'actfYBdJg2F8w0DZ2xqye9ZA',
                            'records',
                            '[0]',
                            'cells',
                            'fldClUcZWmzJzwamgjhHPaPh',
                            'value',
                          ],
                          tips: '',
                          names: ['执行器', '查找记录', 'records', '#1', '单元格', 'Name', 'value'],
                        },
                      },
                      {
                        text: ',',
                        type: 'text',
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Thank you for your service. We are now sending you the wage settlement details. Please check and receive it.',
                        type: 'text',
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'variable',
                        attrs: {
                          ids: "_renderRecordsAsList(_triggers.trg75I8h8AROainwoa9kGobD.record, ['fldWQQilfNfKNBNxfRRcocEF','fld9F51yWXBHMIrW0zTWYCa2','fld1LxkShWTapdf26DRdPSv7','fld1DOMyVkXQg5M36sWqC9w3','fldYRgGqUo7YzvyFR7cat64o','fldox1nmGmR7Qxs61IiO2roc'])",
                          tips: '选中的字段: Work performed, Billable_hours, Hourly_rate, Total_billed, Started, Clients',
                          names: ['触发器', '有记录满足条件时', '记录列表'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Thanks again for your hard work. We look forward to having the opportunity to continue our cooperation in the future! If you have any questions about the settlement details, please contact us at [phone number].',
                        type: 'text',
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: '[Company Name]',
                        type: 'text',
                      },
                    ],
                  },
                ],
              },
            },
            to: [
              {
                type: 'EMAIL_STRING',
                email: '<%= _actions.actfYBdJg2F8w0DZ2xqye9ZA.records[0].cells.fldnSTVL1eRCtSmFeC2t4rtv.value %>',
              },
            ],
            senderName: '',
            cc: [],
            bcc: [],
            replyTo: [],
            type: 'SERVICE',
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atozk7vwZkEdEF4PYh2nAPgG',
      name: {
        en: 'Work Item Reminder',
        'zh-CN': '工作事项提醒',
      },
      triggers: [
        {
          triggerType: 'DATETIME_FIELD_REACHED',
          templateId: 'trgCL4HXxIiqI4WvZCQkGSh5',
          description: {
            en: 'Remind worker one day before work starts',
            'zh-CN': '工作开始前一天提醒工作者',
          },
          input: {
            type: 'DATETIME_FIELD_REACHED',
            datetime: {
              type: 'DELAY',
              unit: 'MINUTE',
              value: 1,
            },
            fieldTemplateId: 'fldYRgGqUo7YzvyFR7cat64o',
            databaseTemplateId: 'datmIG43x4jODfgnC9T3E8UI',
          },
        },
      ],
      actions: [
        {
          templateId: 'actfZ3HCwEIFY4BjrswIE6Jg',
          description: {
            en: "Find Worker's email address",
            'zh-CN': '查找工作者的电子邮件地址',
          },
          actionType: 'FIND_RECORDS',
          input: {
            interruptIfNoRecord: true,
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldt7xL1MdjafKQo73tu7F6t',
                  fieldType: 'LINK',
                  clause: {
                    operator: 'Contains',
                    value: ['<%= _triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldWQQilfNfKNBNxfRRcocEF.value %>'],
                  },
                },
              ],
            },
            databaseTemplateId: 'datJZkfs9xC7LkgjO6xPfqLM',
          },
        },
        {
          templateId: 'actj23NQhzBIgvnNP7Jeqh68',
          description: {
            en: 'Send email reminders to workers about work matters',
            'zh-CN': '向工作者发送工作事项相关的电子邮件提醒',
          },
          actionType: 'SEND_EMAIL',
          input: {
            subject: 'Your work will start soon. Please pay more attention.',
            body: {
              markdown:
                'Dear <%= _actions.actfZ3HCwEIFY4BjrswIE6Jg.records[0].cells.fldClUcZWmzJzwamgjhHPaPh.value %>,\n' +
                '\n' +
                'Your work will start tomorrow. The following are the work items and corresponding time schedules that you need to pay attention to. Please be sure to carefully check and make preparations.\n' +
                '\n' +
                '<%= _triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldYRgGqUo7YzvyFR7cat64o.name %>: <%= _triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldYRgGqUo7YzvyFR7cat64o.value %>\n' +
                '<%= _triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldWQQilfNfKNBNxfRRcocEF.name %>: <%= _triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldWQQilfNfKNBNxfRRcocEF.value %>\n' +
                '<%= _triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldSMG48Ch9UGJl6K8z7YsbC.value %>: <%= _triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldSMG48Ch9UGJl6K8z7YsbC.value %>\n' +
                '<%= _triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldjbXuZwbWKqIR3jKfhhv2d.name %>: <%= JSON.stringify(_triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldjbXuZwbWKqIR3jKfhhv2d.value) %>\n' +
                '\n' +
                'Wish you success in your work!',
              json: {
                type: 'doc',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Dear ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_actions',
                            'actfZ3HCwEIFY4BjrswIE6Jg',
                            'records',
                            '[0]',
                            'cells',
                            'fldClUcZWmzJzwamgjhHPaPh',
                            'value',
                          ],
                          tips: '',
                          names: ['执行器', '查找记录', 'records', '#1', '单元格', 'Name', 'value'],
                        },
                      },
                      {
                        text: ',',
                        type: 'text',
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Your work will start tomorrow. The following are the work items and corresponding time schedules that you need to pay attention to. Please be sure to carefully check and make preparations.',
                        type: 'text',
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trgCL4HXxIiqI4WvZCQkGSh5',
                            'record',
                            'cells',
                            'fldYRgGqUo7YzvyFR7cat64o',
                            'name',
                          ],
                          tips: '',
                          names: ['触发器', '日期字段到期触发', 'record', 'cells', 'Started', 'name'],
                        },
                      },
                      {
                        text: ':  ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trgCL4HXxIiqI4WvZCQkGSh5',
                            'record',
                            'cells',
                            'fldYRgGqUo7YzvyFR7cat64o',
                            'value',
                          ],
                          tips: '',
                          names: ['触发器', '日期字段到期触发', 'record', 'cells', 'Started', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trgCL4HXxIiqI4WvZCQkGSh5',
                            'record',
                            'cells',
                            'fldWQQilfNfKNBNxfRRcocEF',
                            'name',
                          ],
                          tips: '',
                          names: ['触发器', '日期字段到期触发', 'record', 'cells', 'Work performed', 'name'],
                        },
                      },
                      {
                        text: ':  ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trgCL4HXxIiqI4WvZCQkGSh5',
                            'record',
                            'cells',
                            'fldWQQilfNfKNBNxfRRcocEF',
                            'value',
                          ],
                          tips: '',
                          names: ['触发器', '日期字段到期触发', 'record', 'cells', 'Work performed', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trgCL4HXxIiqI4WvZCQkGSh5',
                            'record',
                            'cells',
                            'fldSMG48Ch9UGJl6K8z7YsbC',
                            'value',
                          ],
                          tips: '',
                          names: ['触发器', '日期字段到期触发', 'record', 'cells', 'Notes', 'value'],
                        },
                      },
                      {
                        text: ':  ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trgCL4HXxIiqI4WvZCQkGSh5',
                            'record',
                            'cells',
                            'fldSMG48Ch9UGJl6K8z7YsbC',
                            'value',
                          ],
                          tips: '',
                          names: ['触发器', '日期字段到期触发', 'record', 'cells', 'Notes', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        type: 'variable',
                        attrs: {
                          ids: [
                            '_triggers',
                            'trgCL4HXxIiqI4WvZCQkGSh5',
                            'record',
                            'cells',
                            'fldjbXuZwbWKqIR3jKfhhv2d',
                            'name',
                          ],
                          tips: '',
                          names: ['触发器', '日期字段到期触发', 'record', 'cells', 'Workers', 'name'],
                        },
                      },
                      {
                        text: ':  ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: 'JSON.stringify(_triggers.trgCL4HXxIiqI4WvZCQkGSh5.record.cells.fldjbXuZwbWKqIR3jKfhhv2d.value)',
                          tips: '',
                          names: ['触发器', '日期字段到期触发', 'record', 'cells', 'Workers', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Wish you success in your work!',
                        type: 'text',
                      },
                    ],
                  },
                ],
              },
            },
            to: [
              {
                type: 'EMAIL_STRING',
                email: '<%= _actions.actfZ3HCwEIFY4BjrswIE6Jg.records[0].cells.fldnSTVL1eRCtSmFeC2t4rtv.value %>',
              },
            ],
            senderName: '',
            cc: [],
            bcc: [],
            replyTo: [],
            type: 'SERVICE',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datmIG43x4jODfgnC9T3E8UI',
      name: {
        en: 'Work Items',
        'zh-CN': '工作事项',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viw4ExH9WPo5AzRvBzh3uiAj',
          name: {
            en: 'All Work Items',
            'zh-CN': '所有工作事项',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldWQQilfNfKNBNxfRRcocEF',
              hidden: false,
              width: 219,
            },
            {
              templateId: 'fldWq1298CFTGLlaWyRrGwPF',
              hidden: false,
            },
            {
              templateId: 'fld8illlrbxWFUYBTre7ku7y',
              hidden: false,
            },
            {
              templateId: 'fld9F51yWXBHMIrW0zTWYCa2',
              hidden: false,
            },
            {
              templateId: 'fld1LxkShWTapdf26DRdPSv7',
              hidden: false,
            },
            {
              templateId: 'fld1DOMyVkXQg5M36sWqC9w3',
              hidden: false,
            },
            {
              templateId: 'fldYRgGqUo7YzvyFR7cat64o',
              hidden: false,
            },
            {
              templateId: 'fldSMG48Ch9UGJl6K8z7YsbC',
              hidden: false,
            },
            {
              templateId: 'fldjbXuZwbWKqIR3jKfhhv2d',
              hidden: false,
            },
            {
              templateId: 'fldox1nmGmR7Qxs61IiO2roc',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwXl6hYEDP50ZrbKjSkwq5n',
          name: {
            en: 'Work in Progress',
            'zh-CN': '进行中的工作',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldWq1298CFTGLlaWyRrGwPF',
                fieldType: 'CHECKBOX',
                clause: {
                  operator: 'Is',
                  value: false,
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldWQQilfNfKNBNxfRRcocEF',
              hidden: false,
            },
            {
              templateId: 'fldWq1298CFTGLlaWyRrGwPF',
              hidden: false,
            },
            {
              templateId: 'fld8illlrbxWFUYBTre7ku7y',
              hidden: false,
            },
            {
              templateId: 'fld9F51yWXBHMIrW0zTWYCa2',
              hidden: false,
            },
            {
              templateId: 'fld1LxkShWTapdf26DRdPSv7',
              hidden: false,
            },
            {
              templateId: 'fld1DOMyVkXQg5M36sWqC9w3',
              hidden: false,
            },
            {
              templateId: 'fldYRgGqUo7YzvyFR7cat64o',
              hidden: false,
            },
            {
              templateId: 'fldSMG48Ch9UGJl6K8z7YsbC',
              hidden: false,
            },
            {
              templateId: 'fldjbXuZwbWKqIR3jKfhhv2d',
              hidden: false,
            },
            {
              templateId: 'fldox1nmGmR7Qxs61IiO2roc',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fldWQQilfNfKNBNxfRRcocEF',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Work performed',
            'zh-CN': '工作内容',
          },
          primary: true,
        },
        {
          type: 'CHECKBOX',
          templateId: 'fldWq1298CFTGLlaWyRrGwPF',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Done?',
            'zh-CN': '是否完成',
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fld8illlrbxWFUYBTre7ku7y',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Attachments',
            'zh-CN': '附件',
          },
          primary: false,
        },
        {
          type: 'NUMBER',
          templateId: 'fld9F51yWXBHMIrW0zTWYCa2',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Billable_hours',
            'zh-CN': '计费小时数',
          },
          property: {
            precision: 1,
            commaStyle: 'thousand',
            symbol: '',
            symbolAlign: 'default',
          },
          primary: false,
        },
        {
          type: 'CURRENCY',
          templateId: 'fld1LxkShWTapdf26DRdPSv7',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Hourly_rate',
            'zh-CN': '每小时费率',
          },
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '$',
            symbolAlign: 'left',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fld1DOMyVkXQg5M36sWqC9w3',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Total_billed',
            'zh-CN': '总计费',
          },
          required: false,
          property: {
            expressionTemplate: '{fld9F51yWXBHMIrW0zTWYCa2}*{fld1LxkShWTapdf26DRdPSv7}',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldYRgGqUo7YzvyFR7cat64o',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Started',
            'zh-CN': '开始时间',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: true,
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldSMG48Ch9UGJl6K8z7YsbC',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Notes',
            'zh-CN': '备注',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldjbXuZwbWKqIR3jKfhhv2d',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Workers',
            'zh-CN': '工作者',
          },
          property: {
            foreignDatabaseTemplateId: 'datJZkfs9xC7LkgjO6xPfqLM',
            brotherFieldTemplateId: 'fldt7xL1MdjafKQo73tu7F6t',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldox1nmGmR7Qxs61IiO2roc',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Clients',
            'zh-CN': '客户',
          },
          property: {
            foreignDatabaseTemplateId: 'dattMDMI5jdGUumof383iHI0',
            brotherFieldTemplateId: 'fldHZ5DJd68gFDrqiWAzkcEp',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'rec8XHT5J230e9hMtohiAC6L',
          data: {
            fld1DOMyVkXQg5M36sWqC9w3: null,
            fld1LxkShWTapdf26DRdPSv7: 38,
            fld8illlrbxWFUYBTre7ku7y: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fld9F51yWXBHMIrW0zTWYCa2: '7.0',
            fldSMG48Ch9UGJl6K8z7YsbC: 'Use special cleaner.',
            fldWQQilfNfKNBNxfRRcocEF: 'Clean air ducts',
            fldWq1298CFTGLlaWyRrGwPF: null,
            fldYRgGqUo7YzvyFR7cat64o: '2025-01-08T00:00:00.000Z',
            fldjbXuZwbWKqIR3jKfhhv2d: ['recjl8wjTwWJ9l2wAVQ2EjmU'],
            fldox1nmGmR7Qxs61IiO2roc: ['recO0ky7J73rkFQz0pPirzqN'],
          },
          values: {
            fld1DOMyVkXQg5M36sWqC9w3: '266',
            fld1LxkShWTapdf26DRdPSv7: '$38',
            fld8illlrbxWFUYBTre7ku7y: ['Sample.pdf'],
            fld9F51yWXBHMIrW0zTWYCa2: '7.0',
            fldSMG48Ch9UGJl6K8z7YsbC: 'Use special cleaner.',
            fldWQQilfNfKNBNxfRRcocEF: 'Clean air ducts',
            fldWq1298CFTGLlaWyRrGwPF: '0',
            fldYRgGqUo7YzvyFR7cat64o: '2025-01-08 00:00',
            fldjbXuZwbWKqIR3jKfhhv2d: ['Charlotte Lake'],
            fldox1nmGmR7Qxs61IiO2roc: ['Sebastian Clark'],
          },
        },
        {
          templateId: 'rec8pFhol2bY4Q8FXfT6dx2m',
          data: {
            fld1DOMyVkXQg5M36sWqC9w3: null,
            fld1LxkShWTapdf26DRdPSv7: 30,
            fld8illlrbxWFUYBTre7ku7y: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fld9F51yWXBHMIrW0zTWYCa2: '5.0',
            fldSMG48Ch9UGJl6K8z7YsbC: 'LED fixtures.',
            fldWQQilfNfKNBNxfRRcocEF: 'Install new light fixtures in bedroom',
            fldWq1298CFTGLlaWyRrGwPF: true,
            fldYRgGqUo7YzvyFR7cat64o: '2024-05-07T00:05:00.000Z',
            fldjbXuZwbWKqIR3jKfhhv2d: ['recdAgXJjScmtYEqVVIVwUKA'],
            fldox1nmGmR7Qxs61IiO2roc: ['recXQXr6jR2KUjGy9namY8li'],
          },
          values: {
            fld1DOMyVkXQg5M36sWqC9w3: '150',
            fld1LxkShWTapdf26DRdPSv7: '$30',
            fld8illlrbxWFUYBTre7ku7y: ['Sample.pdf'],
            fld9F51yWXBHMIrW0zTWYCa2: '5.0',
            fldSMG48Ch9UGJl6K8z7YsbC: 'LED fixtures.',
            fldWQQilfNfKNBNxfRRcocEF: 'Install new light fixtures in bedroom',
            fldWq1298CFTGLlaWyRrGwPF: '1',
            fldYRgGqUo7YzvyFR7cat64o: '2024-05-07 00:05',
            fldjbXuZwbWKqIR3jKfhhv2d: ['Daniel Parker'],
            fldox1nmGmR7Qxs61IiO2roc: ['Olivia Monroe'],
          },
        },
        {
          templateId: 'recWt5br6KUs1VLup8MLjQp3',
          data: {
            fld1DOMyVkXQg5M36sWqC9w3: null,
            fld1LxkShWTapdf26DRdPSv7: 45,
            fld8illlrbxWFUYBTre7ku7y: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fld9F51yWXBHMIrW0zTWYCa2: '10.0',
            fldSMG48Ch9UGJl6K8z7YsbC: 'Two coats required.',
            fldWQQilfNfKNBNxfRRcocEF: 'Paint living room walls',
            fldWq1298CFTGLlaWyRrGwPF: true,
            fldYRgGqUo7YzvyFR7cat64o: '2024-05-07T00:05:00.000Z',
            fldjbXuZwbWKqIR3jKfhhv2d: ['recxgNdFt9Y3aleKX1Y0ej4Z'],
            fldox1nmGmR7Qxs61IiO2roc: ['recnYNS42ysbvJv2A6b6vKK7'],
          },
          values: {
            fld1DOMyVkXQg5M36sWqC9w3: '450',
            fld1LxkShWTapdf26DRdPSv7: '$45',
            fld8illlrbxWFUYBTre7ku7y: ['Sample.pdf'],
            fld9F51yWXBHMIrW0zTWYCa2: '10.0',
            fldSMG48Ch9UGJl6K8z7YsbC: 'Two coats required.',
            fldWQQilfNfKNBNxfRRcocEF: 'Paint living room walls',
            fldWq1298CFTGLlaWyRrGwPF: '1',
            fldYRgGqUo7YzvyFR7cat64o: '2024-05-07 00:05',
            fldjbXuZwbWKqIR3jKfhhv2d: ['Amelia Rose'],
            fldox1nmGmR7Qxs61IiO2roc: ['James Thompson'],
          },
        },
        {
          templateId: 'rechiDxcWhsAxt5Y7CoSVMLG',
          data: {
            fld1DOMyVkXQg5M36sWqC9w3: null,
            fld1LxkShWTapdf26DRdPSv7: 35,
            fld8illlrbxWFUYBTre7ku7y: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fld9F51yWXBHMIrW0zTWYCa2: 3,
            fldSMG48Ch9UGJl6K8z7YsbC: 'Standard replacement.',
            fldWQQilfNfKNBNxfRRcocEF: 'Replace kitchen faucet',
            fldWq1298CFTGLlaWyRrGwPF: true,
            fldYRgGqUo7YzvyFR7cat64o: '2025-01-09T04:31:00.000Z',
            fldjbXuZwbWKqIR3jKfhhv2d: ['recochr1L3YQRSaYRE9Xrs7y'],
            fldox1nmGmR7Qxs61IiO2roc: ['rec1Z91DoKdNVW4vZZJt4yla'],
          },
          values: {
            fld1DOMyVkXQg5M36sWqC9w3: '105',
            fld1LxkShWTapdf26DRdPSv7: '$35',
            fld8illlrbxWFUYBTre7ku7y: ['Sample.pdf'],
            fld9F51yWXBHMIrW0zTWYCa2: '3.0',
            fldSMG48Ch9UGJl6K8z7YsbC: 'Standard replacement.',
            fldWQQilfNfKNBNxfRRcocEF: 'Replace kitchen faucet',
            fldWq1298CFTGLlaWyRrGwPF: '1',
            fldYRgGqUo7YzvyFR7cat64o: '2025-01-09 04:31',
            fldjbXuZwbWKqIR3jKfhhv2d: ['Eva Zhang'],
            fldox1nmGmR7Qxs61IiO2roc: ['Elizabeth Davis'],
          },
        },
        {
          templateId: 'recohOlEsq0uay5nzl3OE58H',
          data: {
            fld1LxkShWTapdf26DRdPSv7: 45,
            fld8illlrbxWFUYBTre7ku7y: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fld9F51yWXBHMIrW0zTWYCa2: 2,
            fldSMG48Ch9UGJl6K8z7YsbC: 'Needed new control panel.',
            fldWQQilfNfKNBNxfRRcocEF: 'Repaired Power Supply',
            fldWq1298CFTGLlaWyRrGwPF: '',
            fldYRgGqUo7YzvyFR7cat64o: '2025-05-07T00:05:00.000Z',
            fldjbXuZwbWKqIR3jKfhhv2d: ['rec8JWKxGhmPSfnFajjCjSHM'],
            fldox1nmGmR7Qxs61IiO2roc: ['recQiCHcLYmVOqbDkbUUontX'],
          },
          values: {
            fld1DOMyVkXQg5M36sWqC9w3: '90',
            fld1LxkShWTapdf26DRdPSv7: '$45',
            fld8illlrbxWFUYBTre7ku7y: ['Sample.pdf'],
            fld9F51yWXBHMIrW0zTWYCa2: '2.0',
            fldSMG48Ch9UGJl6K8z7YsbC: 'Needed new control panel.',
            fldWQQilfNfKNBNxfRRcocEF: 'Repaired Power Supply',
            fldWq1298CFTGLlaWyRrGwPF: '0',
            fldYRgGqUo7YzvyFR7cat64o: '2025-05-07 00:05',
            fldjbXuZwbWKqIR3jKfhhv2d: ['Ethan Gray'],
            fldox1nmGmR7Qxs61IiO2roc: ['Alexander Ross'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'dattMDMI5jdGUumof383iHI0',
      name: {
        en: 'Clients',
        'zh-CN': '客户',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwbHwktHxtY2NDV4aqEknLO',
          name: {
            en: 'Clients',
            'zh-CN': '客户',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldjOBaDVQWrBR6zhtx9ocCi',
              hidden: false,
            },
            {
              templateId: 'fldHZ5DJd68gFDrqiWAzkcEp',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldjOBaDVQWrBR6zhtx9ocCi',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Name',
            'zh-CN': '姓名',
          },
          primary: true,
        },
        {
          type: 'LINK',
          templateId: 'fldHZ5DJd68gFDrqiWAzkcEp',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Work Items',
            'zh-CN': '工作事项',
          },
          property: {
            foreignDatabaseTemplateId: 'datmIG43x4jODfgnC9T3E8UI',
            brotherFieldTemplateId: 'fldox1nmGmR7Qxs61IiO2roc',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recQiCHcLYmVOqbDkbUUontX',
          data: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['recohOlEsq0uay5nzl3OE58H'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'Alexander Ross',
          },
          values: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['Repaired Power Supply'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'Alexander Ross',
          },
        },
        {
          templateId: 'recO0ky7J73rkFQz0pPirzqN',
          data: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['rec8XHT5J230e9hMtohiAC6L'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'Sebastian Clark',
          },
          values: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['Clean air ducts'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'Sebastian Clark',
          },
        },
        {
          templateId: 'recXQXr6jR2KUjGy9namY8li',
          data: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['rec8pFhol2bY4Q8FXfT6dx2m'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'Olivia Monroe',
          },
          values: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['Install new light fixtures in bedroom'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'Olivia Monroe',
          },
        },
        {
          templateId: 'recnYNS42ysbvJv2A6b6vKK7',
          data: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['recWt5br6KUs1VLup8MLjQp3'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'James Thompson',
          },
          values: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['Paint living room walls'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'James Thompson',
          },
        },
        {
          templateId: 'rec1Z91DoKdNVW4vZZJt4yla',
          data: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['rechiDxcWhsAxt5Y7CoSVMLG'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'Elizabeth Davis',
          },
          values: {
            fldHZ5DJd68gFDrqiWAzkcEp: ['Replace kitchen faucet'],
            fldjOBaDVQWrBR6zhtx9ocCi: 'Elizabeth Davis',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datJZkfs9xC7LkgjO6xPfqLM',
      name: {
        en: 'Workers',
        'zh-CN': '工作者',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwTK8BlD1BtDlO3EddNJKAu',
          name: {
            en: 'Worker',
            'zh-CN': '工作者',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldClUcZWmzJzwamgjhHPaPh',
              hidden: false,
            },
            {
              templateId: 'fldnSTVL1eRCtSmFeC2t4rtv',
              hidden: false,
            },
            {
              templateId: 'fldL39SSXO0BfQwTfXvRrsp6',
              hidden: false,
            },
            {
              templateId: 'fldt7xL1MdjafKQo73tu7F6t',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldClUcZWmzJzwamgjhHPaPh',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Name',
            'zh-CN': '姓名',
          },
          primary: true,
        },
        {
          type: 'EMAIL',
          templateId: 'fldnSTVL1eRCtSmFeC2t4rtv',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Email',
            'zh-CN': '电子邮件',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldL39SSXO0BfQwTfXvRrsp6',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Phone',
            'zh-CN': '电话',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldt7xL1MdjafKQo73tu7F6t',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Work Items',
            'zh-CN': '工作事项',
          },
          property: {
            foreignDatabaseTemplateId: 'datmIG43x4jODfgnC9T3E8UI',
            brotherFieldTemplateId: 'fldjbXuZwbWKqIR3jKfhhv2d',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'rec8JWKxGhmPSfnFajjCjSHM',
          data: {
            fldClUcZWmzJzwamgjhHPaPh: 'Ethan Gray',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['recohOlEsq0uay5nzl3OE58H'],
          },
          values: {
            fldClUcZWmzJzwamgjhHPaPh: 'Ethan Gray',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['Repaired Power Supply'],
          },
        },
        {
          templateId: 'recjl8wjTwWJ9l2wAVQ2EjmU',
          data: {
            fldClUcZWmzJzwamgjhHPaPh: 'Charlotte Lake',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['rec8XHT5J230e9hMtohiAC6L'],
          },
          values: {
            fldClUcZWmzJzwamgjhHPaPh: 'Charlotte Lake',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['Clean air ducts'],
          },
        },
        {
          templateId: 'recdAgXJjScmtYEqVVIVwUKA',
          data: {
            fldClUcZWmzJzwamgjhHPaPh: 'Daniel Parker',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['rec8pFhol2bY4Q8FXfT6dx2m'],
          },
          values: {
            fldClUcZWmzJzwamgjhHPaPh: 'Daniel Parker',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['Install new light fixtures in bedroom'],
          },
        },
        {
          templateId: 'recxgNdFt9Y3aleKX1Y0ej4Z',
          data: {
            fldClUcZWmzJzwamgjhHPaPh: 'Amelia Rose',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['recWt5br6KUs1VLup8MLjQp3'],
          },
          values: {
            fldClUcZWmzJzwamgjhHPaPh: 'Amelia Rose',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['Paint living room walls'],
          },
        },
        {
          templateId: 'recochr1L3YQRSaYRE9Xrs7y',
          data: {
            fldClUcZWmzJzwamgjhHPaPh: 'Eva Zhang',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['rechiDxcWhsAxt5Y7CoSVMLG'],
          },
          values: {
            fldClUcZWmzJzwamgjhHPaPh: 'Eva Zhang',
            fldL39SSXO0BfQwTfXvRrsp6: '123-4567890',
            fldnSTVL1eRCtSmFeC2t4rtv: '<EMAIL>',
            fldt7xL1MdjafKQo73tu7F6t: ['Replace kitchen faucet'],
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
