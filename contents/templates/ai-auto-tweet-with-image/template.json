{"templateId": "ai-auto-tweet-with-image", "name": {"en": "AI Auto-Tweet with Image", "ja": "AIによる画像付きX投稿自動公開", "zh-CN": "AI自动发布带图片的 X 推文", "zh-TW": "AI自動發佈帶圖片的X推文"}, "description": {"en": "You can use this template to achieve AI automated X(Twitter) tweets, read the prepared tweet content in the database, and automatically post tweets to help you increase the exposure of social media and increase fan interaction.", "ja": "このテンプレートを使用すれば、AIが自動的にX（Twitter）に投稿を行います。データテーブルに準備された投稿内容を読み取り、自動的にツイートを公開するため、ソーシャルメディアの露出を向上させ、フォロワーとのエンゲージメントを増やすのに役立ちます。", "zh-CN": "您可以使用该模板，实现 AI 自动发布 X(Twitter) 推文，读取数据表中准备好的推文资料，自动发布推文，帮助您提高社交媒体的曝光度，增加粉丝互动。", "zh-TW": "您可以使用該模板，實現 AI 自動發佈 X(Twitter) 推文，讀取數據表中準備好的推文資料，自動發佈推文，幫助您提高社交媒體的曝光度，增加粉絲互動。"}, "cover": {"type": "URL", "url": "/assets/template/template-cover-x-ai-automated-tweets.png"}, "author": "pengjin <<EMAIL>>", "category": ["project"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.7", "resources": [{"resourceType": "AUTOMATION", "templateId": "auto_schedule_tweets", "name": {"en": "AI Automatically Posts X Tweets", "ja": "AI自動X投稿", "zh-CN": "AI 自动发布 X 推文", "zh-TW": "AI 自動發佈 X 推文"}, "description": {"en": "Generate tweets through OpenAI and automatically post tweets", "ja": "OpenAIでツイートを生成し、自動的に投稿を公開", "zh-CN": "通过 OpenAI 生成推文，并自动发布推文", "zh-TW": "透過 OpenAI 生成推文，並自動發佈推文"}, "triggers": [{"triggerType": "RECORD_MATCH", "templateId": "scheduler", "description": {"en": "Trigger when status change to Publish", "ja": "ステータスが「公開」に変更されたときにトリガーされる", "zh-CN": "当状态变更为“发布”时触发", "zh-TW": "當狀態變更為「發佈」時觸發"}, "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldfSyDRqIRVJiznnBV7NeRu", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optaeJPuKqscUbuSmzV3hojq"}}]}, "databaseTemplateId": "db_tweets"}}], "actions": [{"templateId": "act0htgOv9e5AwC4HV29mJzV", "description": {"en": "Generate copy based on the image title", "ja": "画像タイトルに基づいて文案を生成", "zh-CN": "根据图片标题生成文案", "zh-TW": "根據圖片標題生成文案"}, "actionType": "OPENAI_GENERATE_TEXT", "input": {"urlType": "INTEGRATION", "type": "OPENAI_GENERATE_TEXT", "integrationId": "", "prompt": "Please write a Twitter post based on the image title <%= _triggers.scheduler.record.cells.field_tweet_content.value %>, with a character limit of 280.    \nContent requirements: Three-line copywriting, with tags placed in the last line.\nThere should be two line breaks between each line.", "model": "gpt-4o"}}, {"templateId": "actEI5Q0KY7GyjPTV3F37gvJ", "description": {"en": "Upload Tweet Image", "ja": "ツイート画像をアップロード", "zh-CN": "上传推文图片", "zh-TW": "上傳推文圖片"}, "actionType": "TWITTER_UPLOAD_MEDIA", "input": {"urlType": "INTEGRATION", "type": "TWITTER_UPLOAD_MEDIA", "integrationId": "", "data": {"mediaUrls": "<%= JSON.stringify(_triggers.scheduler.record.cells.field_media.data) %>"}}}, {"templateId": "actET9eFe5kPwM5DwRXVq36h", "description": {"en": "Create a tweet with the authorizer account", "ja": "認証済みアカウントでツイートを作成", "zh-CN": "使用授权帐户创建推文", "zh-TW": "使用授權帳戶建立推文"}, "actionType": "X_CREATE_TWEET", "input": {"urlType": "INTEGRATION", "type": "X_CREATE_TWEET", "authMethod": "OAUTH1", "integrationId": "", "data": {"text": "<%= _actions.act0htgOv9e5AwC4HV29mJzV.body.choices[0].message.content %>", "mediaIds": "<%= JSON.stringify(_actions.actEI5Q0KY7GyjPTV3F37gvJ.mediaIds) %>"}}}, {"templateId": "actYDsvvrQakgSJM4dBsy36q", "description": {"en": "Update Twitter URL", "ja": "Twitter URLを更新", "zh-CN": "更新Twitter URL", "zh-TW": "更新Twitter URL"}, "actionType": "UPDATE_RECORD", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _triggers.scheduler.record.id %>", "fieldKeyType": "ID", "data": {"fldnmcqXZmOHWXFfgWSjEUwY": "<%= _actions.act0htgOv9e5AwC4HV29mJzV.body.choices[0].message.content %>", "fldqqS69QyZIZWfQBJEONVY1": "https://x.com/bika_ai/status/<%= _actions.actET9eFe5kPwM5DwRXVq36h.id %>"}, "databaseTemplateId": "db_tweets"}}]}, {"resourceType": "DATABASE", "templateId": "db_tweets", "name": {"en": "X Tweet Content ", "ja": "X投稿内容", "zh-CN": "X 推文内容", "zh-TW": "X 推文內容"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all", "name": {"en": "All Tweets", "ja": "すべてのツイート", "zh-CN": "所有推文", "zh-TW": "所有推文"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "field_tweet_content", "hidden": false, "width": 447}, {"templateId": "field_media", "hidden": false, "width": 138}, {"templateId": "fldfSyDRqIRVJiznnBV7NeRu", "hidden": false, "width": 137}, {"templateId": "fldnmcqXZmOHWXFfgWSjEUwY", "hidden": false}, {"templateId": "fldqqS69QyZIZWfQBJEONVY1", "hidden": false}, {"templateId": "fldvgsqGg73sX1S064BWyKA1", "hidden": false}]}, {"type": "TABLE", "templateId": "view_today", "name": {"en": "Today's Tweets", "ja": "本日のツイート", "zh-CN": "今日推文", "zh-TW": "今日推文"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldvgsqGg73sX1S064BWyKA1", "fieldType": "MODIFIED_TIME", "clause": {"operator": "Is", "value": ["Today"]}}]}, "sorts": [], "fields": [{"templateId": "field_tweet_content", "hidden": false}, {"templateId": "field_media", "hidden": false}, {"templateId": "fldfSyDRqIRVJiznnBV7NeRu", "hidden": false}, {"templateId": "fldnmcqXZmOHWXFfgWSjEUwY", "hidden": false}, {"templateId": "fldqqS69QyZIZWfQBJEONVY1", "hidden": false}, {"templateId": "fldvgsqGg73sX1S064BWyKA1", "hidden": false}], "extra": {"isHideAllItems": false}}], "fields": [{"type": "SINGLE_TEXT", "templateId": "field_tweet_content", "privilege": "TYPE_EDIT", "name": {"en": "Media Title", "ja": "メディアタイトル", "zh-CN": "媒体标题", "zh-TW": "媒體標題"}, "required": true, "primary": true}, {"type": "ATTACHMENT", "templateId": "field_media", "privilege": "NAME_EDIT", "name": {"en": "Media", "ja": "メディアファイル", "zh-CN": "媒体文件", "zh-TW": "媒體檔案"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldfSyDRqIRVJiznnBV7NeRu", "privilege": "NAME_EDIT", "name": {"en": "Status", "ja": "ステータス", "zh-CN": "状态", "zh-TW": "狀態"}, "property": {"options": [{"id": "optaeJPuKqscUbuSmzV3hojq", "name": "Publish", "color": "deepPurple"}, {"id": "optzWTVeiF0KTco8Ieb6GwDf", "name": "In progress", "color": "indigo"}], "defaultValue": ""}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldnmcqXZmOHWXFfgWSjEUwY", "privilege": "NAME_EDIT", "name": {"en": "Tweet Content", "ja": "ツイート内容", "zh-CN": "推文内容", "zh-TW": "推文內容"}, "primary": false}, {"type": "URL", "templateId": "fldqqS69QyZIZWfQBJEONVY1", "privilege": "NAME_EDIT", "name": {"en": "Tweet url", "ja": "ツイートリンク", "zh-CN": "推文链接", "zh-TW": "推文連結"}, "primary": false}, {"type": "MODIFIED_TIME", "templateId": "fldvgsqGg73sX1S064BWyKA1", "privilege": "NAME_EDIT", "name": "修改日期", "required": false, "property": {"dateFormat": "YYYY-MM-DD", "includeTime": true}, "primary": false}], "records": [{"templateId": "recwTUDUX6AGJ13XWzSiZvAx", "data": {"field_media": [{"id": "tplattJu6xAPx120AhWB8tadjqN", "name": "Image 4 Million-Row Database Scalability and Open API Integration.gif", "path": "template/tplattJu6xAPx120AhWB8tadjqN.gif", "bucket": "bika-staging", "mimeType": "image/gif", "size": 718730}], "field_tweet_content": "Million-Row Database Scalability   Open API Integration", "fldfSyDRqIRVJiznnBV7NeRu": ["optaeJPuKqscUbuSmzV3hojq"], "fldnmcqXZmOHWXFfgWSjEUwY": "Unlock the potential of massive data with our million-row database solutions and seamless Open API integration. 🚀📊\n\nBoost efficiency and scale like never before with innovative data strategies.\n\n#DatabaseSolutions #OpenAPI #Scalability #TechInnovation", "fldqqS69QyZIZWfQBJEONVY1": "https://x.com/bika_ai/status/1930221838082421156"}, "values": {"field_media": ["Image 4 Million-Row Database Scalability and Open API Integration.gif"], "field_tweet_content": "Million-Row Database Scalability   Open API Integration", "fldfSyDRqIRVJiznnBV7NeRu": ["Publish"], "fldnmcqXZmOHWXFfgWSjEUwY": "Unlock the potential of massive data with our million-row database solutions and seamless Open API integration. 🚀📊\n\nBoost efficiency and scale like never before with innovative data strategies.\n\n#DatabaseSolutions #OpenAPI #Scalability #TechInnovation", "fldqqS69QyZIZWfQBJEONVY1": "https://x.com/bika_ai/status/1930221838082421156"}}, {"templateId": "rec6QM9wnnZVmCOXCdJOJIiV", "data": {"field_media": [{"id": "tplattqhoGVI7hLliykpT3U8TuJ", "name": "Image 3 Project Issue & Ticket template Auto-generates Tasks, Reminders, and AI reports.gif", "path": "template/tplattqhoGVI7hLliykpT3U8TuJ.gif", "bucket": "bika-staging", "mimeType": "image/gif", "size": 924830}], "field_tweet_content": "Project Issue & Ticket template Auto-generates Tasks, Reminders, and AI reports", "fldfSyDRqIRVJiznnBV7NeRu": ["optaeJPuKqscUbuSmzV3hojq"], "fldnmcqXZmOHWXFfgWSjEUwY": "Streamline your workflow effortlessly! 🚀 With our Project Issue & Ticket template, auto-generate tasks, set reminders, and receive insightful AI reports to keep your projects on track. 📈  \n\nTransform your productivity today!  \n\n#ProjectManagement #Productivity #AI", "fldqqS69QyZIZWfQBJEONVY1": "https://x.com/bika_ai/status/1914982891697877018"}, "values": {"field_media": ["Image 3 Project Issue & Ticket template Auto-generates Tasks, Reminders, and AI reports.gif"], "field_tweet_content": "Project Issue & Ticket template Auto-generates Tasks, Reminders, and AI reports", "fldfSyDRqIRVJiznnBV7NeRu": ["Publish"], "fldnmcqXZmOHWXFfgWSjEUwY": "Streamline your workflow effortlessly! 🚀 With our Project Issue & Ticket template, auto-generate tasks, set reminders, and receive insightful AI reports to keep your projects on track. 📈  \n\nTransform your productivity today!  \n\n#ProjectManagement #Productivity #AI", "fldqqS69QyZIZWfQBJEONVY1": "https://x.com/bika_ai/status/1914982891697877018"}}, {"templateId": "recu4SvaRYU05PDhYdTOI9Yq", "data": {"field_media": [{"id": "tplattEIssNqHRAr14JGxukFCK3", "name": "Image 2 50+ Triggers, Actions, and Integrations to Build Various Process Automations.gif", "path": "template/tplattEIssNqHRAr14JGxukFCK3.gif", "bucket": "bika-staging", "mimeType": "image/gif", "size": 447332}], "field_tweet_content": "50+ Triggers, Actions, and Integrations to Build Various Process Automations", "fldfSyDRqIRVJiznnBV7NeRu": ["optaeJPuKqscUbuSmzV3hojq"], "fldnmcqXZmOHWXFfgWSjEUwY": "Unleash the power of automation and streamline your workflows with over 50+ triggers, actions, and integrations. 🚀✨  \n\n\nBoost productivity and simplify tasks with ease. 📈🛠️  \n\n\n#Automation #Efficiency #ProductivityBoost", "fldqqS69QyZIZWfQBJEONVY1": "https://x.com/bika_ai/status/1914939041222681066"}, "values": {"field_media": ["Image 2 50+ Triggers, Actions, and Integrations to Build Various Process Automations.gif"], "field_tweet_content": "50+ Triggers, Actions, and Integrations to Build Various Process Automations", "fldfSyDRqIRVJiznnBV7NeRu": ["Publish"], "fldnmcqXZmOHWXFfgWSjEUwY": "Unleash the power of automation and streamline your workflows with over 50+ triggers, actions, and integrations. 🚀✨  \n\n\nBoost productivity and simplify tasks with ease. 📈🛠️  \n\n\n#Automation #Efficiency #ProductivityBoost", "fldqqS69QyZIZWfQBJEONVY1": "https://x.com/bika_ai/status/1914939041222681066"}}, {"templateId": "recZCb96HWbv5nBFlBeLCga4", "data": {"field_media": [{"id": "tplatt1YB0izfD1tUytNgcTe1jM", "name": "Image 1 Send Emails in Bulk (Tag Triggered) Automation.gif", "path": "template/tplatt1YB0izfD1tUytNgcTe1jM.gif", "bucket": "bika-staging", "mimeType": "image/gif", "size": 423821}], "field_tweet_content": "Send Emails in Bulk (Tag Triggered) Automation", "fldfSyDRqIRVJiznnBV7NeRu": ["optaeJPuKqscUbuSmzV3hojq"], "fldnmcqXZmOHWXFfgWSjEUwY": "Streamline your outreach with automation and send personalized emails at scale with ease.  \n\nTrigger-based functionality ensures perfect timing and efficient communication.  \n\n#EmailMarketing #Automation #ProductivityBoost", "fldqqS69QyZIZWfQBJEONVY1": "https://x.com/bika_ai/status/1914939034197233861"}, "values": {"field_media": ["Image 1 Send Emails in Bulk (Tag Triggered) Automation.gif"], "field_tweet_content": "Send Emails in Bulk (Tag Triggered) Automation", "fldfSyDRqIRVJiznnBV7NeRu": ["Publish"], "fldnmcqXZmOHWXFfgWSjEUwY": "Streamline your outreach with automation and send personalized emails at scale with ease.  \n\nTrigger-based functionality ensures perfect timing and efficient communication.  \n\n#EmailMarketing #Automation #ProductivityBoost", "fldqqS69QyZIZWfQBJEONVY1": "https://x.com/bika_ai/status/1914939034197233861"}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}