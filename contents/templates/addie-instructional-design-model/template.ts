import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'addie-instructional-design-model',
  name: 'ADDIE Instructional Design Model',
  description:
    'The ADDIE model is a simple process used by instructional designers and training developers to create all types of learning content, e-learning or instructor-led, for any organization',
  cover: '/assets/template/template-cover-addie-instructional-design-model.jpeg',
  author: '<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>',
  category: ['operation'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.4',
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'datG5HmzzFhq0LNx0lFie2IF',
      name: 'To-Do<PERSON>',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwimnvIZqPnCbjPwcVh726S',
          name: 'By Project ',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldwZJoY4TLT5KElk88FB6ym',
            },
            {
              templateId: 'fldFyXfeibBr6m7g3CTGBop5',
            },
            {
              templateId: 'fldsgOmjuXvNaOlrfRDanEUu',
            },
            {
              templateId: 'fldOYX5NtEBB9j5ilo7R0PwW',
            },
            {
              templateId: 'fldFev2WWHAo4T1YYbwydv5R',
              width: 150,
            },
            {
              templateId: 'fldu9ZyxJf8fA0l49uXp5AEk',
            },
            {
              templateId: 'fldULOa6UpDNUdgJSdDJOs6U',
            },
            {
              templateId: 'fldm2enct0W6RHgV2w3ek7gm',
            },
          ],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fldwZJoY4TLT5KElk88FB6ym',
          privilege: 'TYPE_EDIT',
          name: 'Task Name',
          primary: true,
        },
        {
          type: 'RATING',
          templateId: 'fldFyXfeibBr6m7g3CTGBop5',
          privilege: 'NAME_EDIT',
          name: 'Urgency',
          property: {
            icon: {
              type: 'EMOJI',
              emoji: '⭐️',
            },
            max: 5,
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldsgOmjuXvNaOlrfRDanEUu',
          privilege: 'NAME_EDIT',
          name: 'Docs',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldOYX5NtEBB9j5ilo7R0PwW',
          privilege: 'NAME_EDIT',
          name: 'Status',
          property: {
            options: [
              {
                id: 'optFtuSTNue5PT3T32emjhGs',
                name: 'In progress ',
                color: 'red',
              },
              {
                id: 'optrh5OqkcSQ1gPZdPfiqClq',
                name: 'Not Started',
                color: 'tangerine5',
              },
              {
                id: 'optrYaYa6WSZxz9qAypli5EE',
                name: 'Complete',
                color: 'orange5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'PERCENT',
          templateId: 'fldFev2WWHAo4T1YYbwydv5R',
          privilege: 'NAME_EDIT',
          name: 'Percent Complete',
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '%',
            symbolAlign: 'right',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldu9ZyxJf8fA0l49uXp5AEk',
          privilege: 'NAME_EDIT',
          name: 'Due Date',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldULOa6UpDNUdgJSdDJOs6U',
          privilege: 'NAME_EDIT',
          name: 'Notes',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldm2enct0W6RHgV2w3ek7gm',
          privilege: 'NAME_EDIT',
          name: 'Stage',
          property: {
            options: [
              {
                id: 'optIDBhBj4Bgx0Cfn88HA9cm',
                name: 'Design',
                color: 'red',
              },
              {
                id: 'opt9jM3znmEFWbUhrlrb2QUY',
                name: 'Evaluation',
                color: 'teal4',
              },
              {
                id: 'optJpchHvyY5GQmiW0t3mgPF',
                name: 'Analysis',
                color: 'indigo5',
              },
              {
                id: 'opt4hn0rjuUcA7sx8sCOVF1c',
                name: 'Implementation',
                color: 'tangerine4',
              },
              {
                id: 'opty3XJBHADT2iJI75YMsa8J',
                name: 'Development',
                color: 'red',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recx60e14KQxkGOTzqAsnyZl',
          data: {
            fldFev2WWHAo4T1YYbwydv5R: 0.8,
            fldOYX5NtEBB9j5ilo7R0PwW: ['optFtuSTNue5PT3T32emjhGs'],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['optIDBhBj4Bgx0Cfn88HA9cm'],
            fldsgOmjuXvNaOlrfRDanEUu: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-12T00:00:00.000Z',
            fldwZJoY4TLT5KElk88FB6ym: 'Outline content',
          },
          values: {
            fldFev2WWHAo4T1YYbwydv5R: '80.00%',
            fldOYX5NtEBB9j5ilo7R0PwW: ['In progress '],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['Design'],
            fldsgOmjuXvNaOlrfRDanEUu: ['Sample.pdf'],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-12',
            fldwZJoY4TLT5KElk88FB6ym: 'Outline content',
          },
        },
        {
          templateId: 'recFZiM8LGo7ufBb5nKjU9bw',
          data: {
            fldFev2WWHAo4T1YYbwydv5R: 0.35,
            fldFyXfeibBr6m7g3CTGBop5: 2,
            fldOYX5NtEBB9j5ilo7R0PwW: ['optFtuSTNue5PT3T32emjhGs'],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['optIDBhBj4Bgx0Cfn88HA9cm'],
            fldsgOmjuXvNaOlrfRDanEUu: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-09-30T00:00:00.000Z',
            fldwZJoY4TLT5KElk88FB6ym: 'Complete needs analysis exercise',
          },
          values: {
            fldFev2WWHAo4T1YYbwydv5R: '35.00%',
            fldFyXfeibBr6m7g3CTGBop5: '2',
            fldOYX5NtEBB9j5ilo7R0PwW: ['In progress '],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['Design'],
            fldsgOmjuXvNaOlrfRDanEUu: ['Sample.pdf'],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-09-30',
            fldwZJoY4TLT5KElk88FB6ym: 'Complete needs analysis exercise',
          },
        },
        {
          templateId: 'rec6E6Gc9fYL0AcRkQikFMoZ',
          data: {
            fldFev2WWHAo4T1YYbwydv5R: 0.1,
            fldOYX5NtEBB9j5ilo7R0PwW: ['optrh5OqkcSQ1gPZdPfiqClq'],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['opt9jM3znmEFWbUhrlrb2QUY'],
            fldsgOmjuXvNaOlrfRDanEUu: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-12T00:00:00.000Z',
            fldwZJoY4TLT5KElk88FB6ym: 'Evaluate current learning environment',
          },
          values: {
            fldFev2WWHAo4T1YYbwydv5R: '10.00%',
            fldOYX5NtEBB9j5ilo7R0PwW: ['Not Started'],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['Evaluation'],
            fldsgOmjuXvNaOlrfRDanEUu: ['Sample.pdf'],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-12',
            fldwZJoY4TLT5KElk88FB6ym: 'Evaluate current learning environment',
          },
        },
        {
          templateId: 'recZdNjyDFHCnr6xpHi6qbKm',
          data: {
            fldFev2WWHAo4T1YYbwydv5R: 0.1,
            fldFyXfeibBr6m7g3CTGBop5: 1,
            fldOYX5NtEBB9j5ilo7R0PwW: ['optrh5OqkcSQ1gPZdPfiqClq'],
            fldULOa6UpDNUdgJSdDJOs6U:
              'Who are the learners and what are their characteristics?\n' +
              'What is the desired new behavior?\n' +
              'What types of learning constraints exist?\n' +
              'What are the delivery options?\n',
            fldm2enct0W6RHgV2w3ek7gm: ['optJpchHvyY5GQmiW0t3mgPF'],
            fldsgOmjuXvNaOlrfRDanEUu: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-12T00:00:00.000Z',
            fldwZJoY4TLT5KElk88FB6ym: 'Create content assets',
          },
          values: {
            fldFev2WWHAo4T1YYbwydv5R: '10.00%',
            fldFyXfeibBr6m7g3CTGBop5: '1',
            fldOYX5NtEBB9j5ilo7R0PwW: ['Not Started'],
            fldULOa6UpDNUdgJSdDJOs6U:
              'Who are the learners and what are their characteristics?\n' +
              'What is the desired new behavior?\n' +
              'What types of learning constraints exist?\n' +
              'What are the delivery options?\n',
            fldm2enct0W6RHgV2w3ek7gm: ['Analysis'],
            fldsgOmjuXvNaOlrfRDanEUu: ['Sample.pdf'],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-12',
            fldwZJoY4TLT5KElk88FB6ym: 'Create content assets',
          },
        },
        {
          templateId: 'reclSZhbf13mSkUI32qRp1O5',
          data: {
            fldFev2WWHAo4T1YYbwydv5R: 0.7,
            fldFyXfeibBr6m7g3CTGBop5: 2,
            fldOYX5NtEBB9j5ilo7R0PwW: ['optFtuSTNue5PT3T32emjhGs'],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['opt4hn0rjuUcA7sx8sCOVF1c'],
            fldsgOmjuXvNaOlrfRDanEUu: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-07T00:00:00.000Z',
            fldwZJoY4TLT5KElk88FB6ym: 'Create storyboards',
          },
          values: {
            fldFev2WWHAo4T1YYbwydv5R: '70.00%',
            fldFyXfeibBr6m7g3CTGBop5: '2',
            fldOYX5NtEBB9j5ilo7R0PwW: ['In progress '],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['Implementation'],
            fldsgOmjuXvNaOlrfRDanEUu: ['Sample.pdf'],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-07',
            fldwZJoY4TLT5KElk88FB6ym: 'Create storyboards',
          },
        },
        {
          templateId: 'rec73rVUT8owMqOulma96aJo',
          data: {
            fldFev2WWHAo4T1YYbwydv5R: 1,
            fldOYX5NtEBB9j5ilo7R0PwW: ['optrYaYa6WSZxz9qAypli5EE'],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['opty3XJBHADT2iJI75YMsa8J'],
            fldsgOmjuXvNaOlrfRDanEUu: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-12T00:00:00.000Z',
            fldwZJoY4TLT5KElk88FB6ym: 'Write design document',
          },
          values: {
            fldFev2WWHAo4T1YYbwydv5R: '100.00%',
            fldOYX5NtEBB9j5ilo7R0PwW: ['Complete'],
            fldULOa6UpDNUdgJSdDJOs6U: '',
            fldm2enct0W6RHgV2w3ek7gm: ['Development'],
            fldsgOmjuXvNaOlrfRDanEUu: ['Sample.pdf'],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-12',
            fldwZJoY4TLT5KElk88FB6ym: 'Write design document',
          },
        },
        {
          templateId: 'rec7jWG9sqSgn1Gss7lgGd10',
          data: {
            fldFev2WWHAo4T1YYbwydv5R: 0.5,
            fldFyXfeibBr6m7g3CTGBop5: 3,
            fldOYX5NtEBB9j5ilo7R0PwW: ['optFtuSTNue5PT3T32emjhGs'],
            fldULOa6UpDNUdgJSdDJOs6U:
              'What are the pedagogical considerations?\n' +
              'What adult learning theory considerations apply?\n' +
              'What is the timeline for project completion?',
            fldm2enct0W6RHgV2w3ek7gm: ['optJpchHvyY5GQmiW0t3mgPF'],
            fldsgOmjuXvNaOlrfRDanEUu: [
              {
                name: 'Sample.pdf',
                id: 'tplattgqNezQYLVVA2jSk6pO4ap',
                path: 'template/tplattgqNezQYLVVA2jSk6pO4ap.pdf',
                bucket: 'bika-staging',
                mimeType: 'application/pdf',
                size: 16737,
              },
            ],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-07T00:00:00.000Z',
            fldwZJoY4TLT5KElk88FB6ym: 'Identify key problems',
          },
          values: {
            fldFev2WWHAo4T1YYbwydv5R: '50.00%',
            fldFyXfeibBr6m7g3CTGBop5: '3',
            fldOYX5NtEBB9j5ilo7R0PwW: ['In progress '],
            fldULOa6UpDNUdgJSdDJOs6U:
              'What are the pedagogical considerations?\n' +
              'What adult learning theory considerations apply?\n' +
              'What is the timeline for project completion?',
            fldm2enct0W6RHgV2w3ek7gm: ['Analysis'],
            fldsgOmjuXvNaOlrfRDanEUu: ['Sample.pdf'],
            fldu9ZyxJf8fA0l49uXp5AEk: '2024-10-07',
            fldwZJoY4TLT5KElk88FB6ym: 'Identify key problems',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
