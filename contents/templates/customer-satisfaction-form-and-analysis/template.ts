import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'customer-satisfaction-form-and-analysis',
  name: {
    'zh-CN': '顾客满意表及分析',
    en: 'Customer Satisfaction Form and Analysis',
  },
  description: {
    en: 'The Customer Satisfaction Form and Analysis Template simplifies collecting, organizing, and analyzing customer feedback. With structured forms, Automated email reminder for followers to categorize insights, and a detailed dashboard, this template helps teams measure satisfaction, identify trends, and take action to improve products and services.',
    'zh-CN':
      '客户满意度表格与分析模板简化了收集、组织和分析客户反馈的过程。通过结构化表格、自动化的跟进者电子邮件提醒以分类见解以及详细的仪表板，该模板帮助团队衡量满意度、识别趋势，并采取行动改进产品和服务。',
  },
  cover: '/assets/template/customer-satisfaction-form-and-analysis/cover.png',
  author: '<PERSON><PERSON> <<EMAIL>>',
  category: ['sales'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.4',
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'dat92XtwJgNXnXgURsat7UYv',
      name: {
        en: 'Feedback',
        'zh-CN': '反馈',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwWkuxWYHMjdbBQwUovJkVe',
          name: {
            en: 'All Responses',
            'zh-CN': '所有回复',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldOCXsYbGcdmlRHv8ovtvrK',
              hidden: false,
            },
            {
              templateId: 'fld0qQ2GDbJwmUwFxdl2pfrq',
              hidden: false,
            },
            {
              templateId: 'fldlukhcAFRheSMZ0O5pax77',
              hidden: false,
            },
            {
              templateId: 'fldsQZrQ8dxnuPQhUuum2uoH',
              hidden: false,
            },
            {
              templateId: 'fldt4srXCeWdZKFQLadN4o7u',
              hidden: false,
            },
            {
              templateId: 'fld2x05Zec9vmsrb9saCnaQ9',
              hidden: false,
            },
            {
              templateId: 'fldV6Vk2DPZ8wnF0lA34HHiY',
              hidden: false,
            },
            {
              templateId: 'fld59t3V6EP0nGBGeD5REEmm',
              hidden: false,
            },
            {
              templateId: 'fldsGEvZzd8WMQUoT8aV6api',
              hidden: false,
            },
            {
              templateId: 'fldfSPbvnvCTGGg5ApZtijvQ',
              hidden: false,
              width: 322,
            },
            {
              templateId: 'fldpIMkbJqM8Y6JIbA5sl3rB',
              hidden: false,
              width: 216,
            },
            {
              templateId: 'fldyKmaxA4nClOp7VT8ES89l',
              hidden: false,
            },
            {
              templateId: 'fldzQHQ0piBUKswIa1Gz3SYq',
              hidden: false,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwDXyEjLHbc369nQisEm0yl',
          name: {
            en: 'Promoters Feedback',
            'zh-CN': '促进者反馈',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldzQHQ0piBUKswIa1Gz3SYq',
                fieldType: 'FORMULA',
                clause: {
                  operator: 'Is',
                  value: 'Promoter',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldOCXsYbGcdmlRHv8ovtvrK',
              hidden: false,
            },
            {
              templateId: 'fld0qQ2GDbJwmUwFxdl2pfrq',
              hidden: false,
            },
            {
              templateId: 'fldlukhcAFRheSMZ0O5pax77',
              hidden: true,
            },
            {
              templateId: 'fldsQZrQ8dxnuPQhUuum2uoH',
              hidden: true,
            },
            {
              templateId: 'fldt4srXCeWdZKFQLadN4o7u',
              hidden: true,
            },
            {
              templateId: 'fld2x05Zec9vmsrb9saCnaQ9',
              hidden: false,
            },
            {
              templateId: 'fldV6Vk2DPZ8wnF0lA34HHiY',
              hidden: false,
            },
            {
              templateId: 'fld59t3V6EP0nGBGeD5REEmm',
              hidden: false,
            },
            {
              templateId: 'fldsGEvZzd8WMQUoT8aV6api',
              hidden: false,
            },
            {
              templateId: 'fldfSPbvnvCTGGg5ApZtijvQ',
              hidden: false,
            },
            {
              templateId: 'fldpIMkbJqM8Y6JIbA5sl3rB',
              hidden: false,
            },
            {
              templateId: 'fldyKmaxA4nClOp7VT8ES89l',
              hidden: false,
            },
            {
              templateId: 'fldzQHQ0piBUKswIa1Gz3SYq',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwh9qiBHY8fcbhvYbF5iyLn',
          name: {
            en: 'Detractors Feedback',
            'zh-CN': '贬低者反馈',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldzQHQ0piBUKswIa1Gz3SYq',
                fieldType: 'FORMULA',
                clause: {
                  operator: 'Is',
                  value: 'Detractor',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldOCXsYbGcdmlRHv8ovtvrK',
              hidden: false,
            },
            {
              templateId: 'fld0qQ2GDbJwmUwFxdl2pfrq',
              hidden: false,
            },
            {
              templateId: 'fldlukhcAFRheSMZ0O5pax77',
              hidden: true,
            },
            {
              templateId: 'fldsQZrQ8dxnuPQhUuum2uoH',
              hidden: true,
            },
            {
              templateId: 'fldt4srXCeWdZKFQLadN4o7u',
              hidden: true,
            },
            {
              templateId: 'fld2x05Zec9vmsrb9saCnaQ9',
              hidden: false,
            },
            {
              templateId: 'fldV6Vk2DPZ8wnF0lA34HHiY',
              hidden: false,
            },
            {
              templateId: 'fld59t3V6EP0nGBGeD5REEmm',
              hidden: false,
            },
            {
              templateId: 'fldsGEvZzd8WMQUoT8aV6api',
              hidden: false,
            },
            {
              templateId: 'fldfSPbvnvCTGGg5ApZtijvQ',
              hidden: false,
            },
            {
              templateId: 'fldpIMkbJqM8Y6JIbA5sl3rB',
              hidden: false,
            },
            {
              templateId: 'fldyKmaxA4nClOp7VT8ES89l',
              hidden: false,
            },
            {
              templateId: 'fldzQHQ0piBUKswIa1Gz3SYq',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwC7Rm6xIyqNvcUUj6NFJE5',
          name: {
            en: 'Highlighted Feedback',
            'zh-CN': '重点反馈',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldyKmaxA4nClOp7VT8ES89l',
                fieldType: 'CHECKBOX',
                clause: {
                  operator: 'Is',
                  value: true,
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldOCXsYbGcdmlRHv8ovtvrK',
              hidden: false,
            },
            {
              templateId: 'fld0qQ2GDbJwmUwFxdl2pfrq',
              hidden: false,
            },
            {
              templateId: 'fldlukhcAFRheSMZ0O5pax77',
              hidden: false,
            },
            {
              templateId: 'fldsQZrQ8dxnuPQhUuum2uoH',
              hidden: false,
            },
            {
              templateId: 'fldt4srXCeWdZKFQLadN4o7u',
              hidden: false,
            },
            {
              templateId: 'fld2x05Zec9vmsrb9saCnaQ9',
              hidden: false,
            },
            {
              templateId: 'fldV6Vk2DPZ8wnF0lA34HHiY',
              hidden: false,
            },
            {
              templateId: 'fld59t3V6EP0nGBGeD5REEmm',
              hidden: false,
            },
            {
              templateId: 'fldsGEvZzd8WMQUoT8aV6api',
              hidden: false,
            },
            {
              templateId: 'fldfSPbvnvCTGGg5ApZtijvQ',
              hidden: false,
            },
            {
              templateId: 'fldpIMkbJqM8Y6JIbA5sl3rB',
              hidden: false,
            },
            {
              templateId: 'fldyKmaxA4nClOp7VT8ES89l',
              hidden: false,
            },
            {
              templateId: 'fldzQHQ0piBUKswIa1Gz3SYq',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwOe0myRKlWOYWDrCCifavg',
          name: {
            en: 'Feedback Form',
            'zh-CN': '反馈收集表单',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldOCXsYbGcdmlRHv8ovtvrK',
              hidden: false,
            },
            {
              templateId: 'fld0qQ2GDbJwmUwFxdl2pfrq',
              hidden: true,
            },
            {
              templateId: 'fldlukhcAFRheSMZ0O5pax77',
              hidden: false,
            },
            {
              templateId: 'fldsQZrQ8dxnuPQhUuum2uoH',
              hidden: false,
            },
            {
              templateId: 'fldt4srXCeWdZKFQLadN4o7u',
              hidden: false,
            },
            {
              templateId: 'fld2x05Zec9vmsrb9saCnaQ9',
              hidden: false,
            },
            {
              templateId: 'fldV6Vk2DPZ8wnF0lA34HHiY',
              hidden: false,
            },
            {
              templateId: 'fld59t3V6EP0nGBGeD5REEmm',
              hidden: false,
            },
            {
              templateId: 'fldsGEvZzd8WMQUoT8aV6api',
              hidden: false,
            },
            {
              templateId: 'fldfSPbvnvCTGGg5ApZtijvQ',
              hidden: false,
            },
            {
              templateId: 'fldpIMkbJqM8Y6JIbA5sl3rB',
              hidden: false,
            },
            {
              templateId: 'fldyKmaxA4nClOp7VT8ES89l',
              hidden: true,
            },
            {
              templateId: 'fldzQHQ0piBUKswIa1Gz3SYq',
              hidden: true,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldOCXsYbGcdmlRHv8ovtvrK',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Customer Name',
            'zh-CN': '客户名称',
          },
          description: {
            en: 'Enter your full name as the person providing feedback',
            'zh-CN': '请输入您的全名',
          },
          required: true,
          primary: true,
        },
        {
          type: 'LINK',
          templateId: 'fld0qQ2GDbJwmUwFxdl2pfrq',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Insights',
            'zh-CN': '见解',
          },
          property: {
            foreignDatabaseTemplateId: 'dat05M0XLEbmTUbAGasJxXf3',
            brotherFieldTemplateId: 'fldUTciG0o7UupyRPusyGs91',
          },
          primary: false,
        },
        {
          type: 'CREATED_TIME',
          templateId: 'fldlukhcAFRheSMZ0O5pax77',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Submission Time',
            'zh-CN': '提交时间',
          },
          description: 'No input needed.',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'EMAIL',
          templateId: 'fldsQZrQ8dxnuPQhUuum2uoH',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Email',
            'zh-CN': '电子邮件',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldt4srXCeWdZKFQLadN4o7u',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Company Name',
            'zh-CN': '公司名称',
          },
          primary: false,
        },
        {
          type: 'RATING',
          templateId: 'fld2x05Zec9vmsrb9saCnaQ9',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Product Satisfaction',
            'zh-CN': '产品满意度',
          },
          description: {
            en: 'On a scale from 1-5, how satisfied are you with our product?',
            'zh-CN': '在1到5的评分范围内，您对我们的产品满意吗？',
          },
          required: true,
          property: {
            icon: {
              type: 'EMOJI',
              emoji: '⭐️',
            },
            max: 5,
          },
          primary: false,
        },
        {
          type: 'RATING',
          templateId: 'fldV6Vk2DPZ8wnF0lA34HHiY',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Service Satisfaction',
            'zh-CN': '服务满意度',
          },
          description: {
            en: 'Rate your satisfaction with our service on a scale from 1 to 5',
            'zh-CN': '请在1到5的评分范围内评定您对我们服务的满意度',
          },
          required: true,
          property: {
            icon: {
              type: 'EMOJI',
              emoji: '⭐️',
            },
            max: 5,
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fld59t3V6EP0nGBGeD5REEmm',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Product Comparison',
            'zh-CN': '产品比较',
          },
          description: {
            en: 'How would you compare our product to the other solutions on the market?',
            'zh-CN': '您如何将我们的产品与市场上的其他解决方案进行比较？',
          },
          required: true,
          property: {
            options: [
              {
                id: 'optwBI1TNwV4dP1mpsZ150mv',
                name: 'Same as others',
                color: 'deepPurple',
              },
              {
                id: 'optbdCTqYewijlNeQp3ds3X6',
                name: 'Better than others',
                color: 'indigo',
              },
              {
                id: 'optPtw0071REhkEyf2iUEMuN',
                name: 'Worse than others',
                color: 'blue',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldsGEvZzd8WMQUoT8aV6api',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Service Comparison',
            'zh-CN': '服务比较',
          },
          description: {
            en: "How would you compare our team's service to the other solutions on the market?",
            'zh-CN': '您如何将我们团队的服务与市场上的其他解决方案进行比较？',
          },
          required: true,
          property: {
            options: [
              {
                id: 'optJsfHwzDnsuasAWmEwN80n',
                name: 'Same as others',
                color: 'deepPurple',
              },
              {
                id: 'optB5u7BWDC5gLWOJK9saRPZ',
                name: 'Better than others',
                color: 'indigo',
              },
              {
                id: 'opt7LtxDQ98wzlINoiJUmol5',
                name: 'Worse than others',
                color: 'blue',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldfSPbvnvCTGGg5ApZtijvQ',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Reasons',
            'zh-CN': '原因',
          },
          description: {
            en: 'Briefly explain the reasons for your ratings. This helps us understand what we’re doing well or where we can improve.',
            'zh-CN': '简要说明您评分的原因。这有助于我们了解我们做得好的地方或可以改进的地方。',
          },

          primary: false,
        },
        {
          type: 'RATING',
          templateId: 'fldpIMkbJqM8Y6JIbA5sl3rB',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Rating',
            'zh-CN': '评分',
          },
          description: {
            en: 'On a scale from 1-10, how likely are you to recommend our product to a friend or colleague?',
            'zh-CN': '在1到10的评分范围内，您有多大可能将我们的产品推荐给朋友或同事？',
          },
          required: true,
          property: {
            icon: {
              type: 'EMOJI',
              emoji: '⭐️',
            },
            max: 10,
          },
          primary: false,
        },
        {
          type: 'CHECKBOX',
          templateId: 'fldyKmaxA4nClOp7VT8ES89l',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Highlight',
            'zh-CN': '突出',
          },

          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldzQHQ0piBUKswIa1Gz3SYq',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Promoter/Detractor',
            'zh-CN': '促进者/贬低者',
          },
          property: {
            expressionTemplate:
              'IF(Value({fldpIMkbJqM8Y6JIbA5sl3rB})<7,"Detractor",IF(And(Value({fldpIMkbJqM8Y6JIbA5sl3rB})>=7,VALUE({fldpIMkbJqM8Y6JIbA5sl3rB})<9),"Passive","Promoter")) ',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recQwTc2o0riCgg9utYIqDNe',
          data: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['reci1Omh4cOnrem6TQSgs3Uv'],
            fld2x05Zec9vmsrb9saCnaQ9: 4,
            fld59t3V6EP0nGBGeD5REEmm: ['optwBI1TNwV4dP1mpsZ150mv'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'Evelyn Parker',
            fldV6Vk2DPZ8wnF0lA34HHiY: 3,
            fldfSPbvnvCTGGg5ApZtijvQ: 'Complex onboarding',
            fldpIMkbJqM8Y6JIbA5sl3rB: 8,
            fldsGEvZzd8WMQUoT8aV6api: ['optJsfHwzDnsuasAWmEwN80n'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'EduTech Global',
            fldyKmaxA4nClOp7VT8ES89l: true,
            fldzQHQ0piBUKswIa1Gz3SYq: 'Passive',
          },
          values: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['Long Wait Times'],
            fld2x05Zec9vmsrb9saCnaQ9: '4',
            fld59t3V6EP0nGBGeD5REEmm: ['Same as others'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'Evelyn Parker',
            fldV6Vk2DPZ8wnF0lA34HHiY: '3',
            fldfSPbvnvCTGGg5ApZtijvQ: 'Complex onboarding',
            fldpIMkbJqM8Y6JIbA5sl3rB: '8',
            fldsGEvZzd8WMQUoT8aV6api: ['Same as others'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'EduTech Global',
            fldyKmaxA4nClOp7VT8ES89l: '1',
            fldzQHQ0piBUKswIa1Gz3SYq: 'Passive',
          },
        },
        {
          templateId: 'recXs4wJkWZvwkLa7xNvuX38',
          data: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['rec07amf11jLromLJ916IvEk'],
            fld2x05Zec9vmsrb9saCnaQ9: 5,
            fld59t3V6EP0nGBGeD5REEmm: ['optbdCTqYewijlNeQp3ds3X6'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'David Martinez',
            fldV6Vk2DPZ8wnF0lA34HHiY: 5,
            fldfSPbvnvCTGGg5ApZtijvQ: 'Fast resolution of issues',
            fldpIMkbJqM8Y6JIbA5sl3rB: 10,
            fldsGEvZzd8WMQUoT8aV6api: ['optB5u7BWDC5gLWOJK9saRPZ'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'Healthcare Systems Co.',
            fldzQHQ0piBUKswIa1Gz3SYq: 'Promoter',
          },
          values: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['Quick Resolution'],
            fld2x05Zec9vmsrb9saCnaQ9: '5',
            fld59t3V6EP0nGBGeD5REEmm: ['Better than others'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'David Martinez',
            fldV6Vk2DPZ8wnF0lA34HHiY: '5',
            fldfSPbvnvCTGGg5ApZtijvQ: 'Fast resolution of issues',
            fldpIMkbJqM8Y6JIbA5sl3rB: '10',
            fldsGEvZzd8WMQUoT8aV6api: ['Better than others'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'Healthcare Systems Co.',
            fldzQHQ0piBUKswIa1Gz3SYq: 'Promoter',
          },
        },
        {
          templateId: 'rec7iOho9fCRcp7VU6nqbePV',
          data: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['recTasYO5a7oA4j9D9UobivB'],
            fld2x05Zec9vmsrb9saCnaQ9: 1,
            fld59t3V6EP0nGBGeD5REEmm: ['optPtw0071REhkEyf2iUEMuN'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'Catherine Lee',
            fldV6Vk2DPZ8wnF0lA34HHiY: 2,
            fldfSPbvnvCTGGg5ApZtijvQ: 'Missing advanced features',
            fldpIMkbJqM8Y6JIbA5sl3rB: 3,
            fldsGEvZzd8WMQUoT8aV6api: ['opt7LtxDQ98wzlINoiJUmol5'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'Cloud Nexus Corp.',
            fldyKmaxA4nClOp7VT8ES89l: true,
            fldzQHQ0piBUKswIa1Gz3SYq: 'Detractor',
          },
          values: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['Limited Features'],
            fld2x05Zec9vmsrb9saCnaQ9: '1',
            fld59t3V6EP0nGBGeD5REEmm: ['Worse than others'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'Catherine Lee',
            fldV6Vk2DPZ8wnF0lA34HHiY: '2',
            fldfSPbvnvCTGGg5ApZtijvQ: 'Missing advanced features',
            fldpIMkbJqM8Y6JIbA5sl3rB: '3',
            fldsGEvZzd8WMQUoT8aV6api: ['Worse than others'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'Cloud Nexus Corp.',
            fldyKmaxA4nClOp7VT8ES89l: '1',
            fldzQHQ0piBUKswIa1Gz3SYq: 'Detractor',
          },
        },
        {
          templateId: 'rec11ztl89aZIFWK30sUfWVS',
          data: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['recB5gWTVjYNr0AZ3L9QPkTA'],
            fld2x05Zec9vmsrb9saCnaQ9: 5,
            fld59t3V6EP0nGBGeD5REEmm: ['optwBI1TNwV4dP1mpsZ150mv'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'Bob Smith',
            fldV6Vk2DPZ8wnF0lA34HHiY: 3,
            fldfSPbvnvCTGGg5ApZtijvQ: 'Long wait times for support',
            fldlukhcAFRheSMZ0O5pax77: '2024-12-24T07:57:25.304Z',
            fldpIMkbJqM8Y6JIbA5sl3rB: 7,
            fldsGEvZzd8WMQUoT8aV6api: ['optB5u7BWDC5gLWOJK9saRPZ'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'Retail Innovators Ltd.',
            fldzQHQ0piBUKswIa1Gz3SYq: 'Passive',
          },
          values: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['Helpful Support Team'],
            fld2x05Zec9vmsrb9saCnaQ9: '5',
            fld59t3V6EP0nGBGeD5REEmm: ['Same as others'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'Bob Smith',
            fldV6Vk2DPZ8wnF0lA34HHiY: '3',
            fldfSPbvnvCTGGg5ApZtijvQ: 'Long wait times for support',
            fldlukhcAFRheSMZ0O5pax77: '2024-12-24',
            fldpIMkbJqM8Y6JIbA5sl3rB: '7',
            fldsGEvZzd8WMQUoT8aV6api: ['Better than others'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'Retail Innovators Ltd.',
            fldyKmaxA4nClOp7VT8ES89l: '0',
            fldzQHQ0piBUKswIa1Gz3SYq: 'Passive',
          },
        },
        {
          templateId: 'rec9cAYVzSRiPQeV6YL9i7Pa',
          data: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['recdzL0IEZu1HL4Z3piR3c3R'],
            fld2x05Zec9vmsrb9saCnaQ9: 4,
            fld59t3V6EP0nGBGeD5REEmm: ['optbdCTqYewijlNeQp3ds3X6'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'Alice Johnson',
            fldV6Vk2DPZ8wnF0lA34HHiY: 4,
            fldfSPbvnvCTGGg5ApZtijvQ: 'Intuitive design',
            fldlukhcAFRheSMZ0O5pax77: '2024-12-24T07:57:28.201Z',
            fldpIMkbJqM8Y6JIbA5sl3rB: 10,
            fldsGEvZzd8WMQUoT8aV6api: ['optJsfHwzDnsuasAWmEwN80n'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'Tech Solutions Inc.',
            fldzQHQ0piBUKswIa1Gz3SYq: 'Promoter',
          },
          values: {
            fld0qQ2GDbJwmUwFxdl2pfrq: ['Ease of Use'],
            fld2x05Zec9vmsrb9saCnaQ9: '4',
            fld59t3V6EP0nGBGeD5REEmm: ['Better than others'],
            fldOCXsYbGcdmlRHv8ovtvrK: 'Alice Johnson',
            fldV6Vk2DPZ8wnF0lA34HHiY: '4',
            fldfSPbvnvCTGGg5ApZtijvQ: 'Intuitive design',
            fldlukhcAFRheSMZ0O5pax77: '2024-12-24',
            fldpIMkbJqM8Y6JIbA5sl3rB: '10',
            fldsGEvZzd8WMQUoT8aV6api: ['Same as others'],
            fldsQZrQ8dxnuPQhUuum2uoH: '<EMAIL>',
            fldt4srXCeWdZKFQLadN4o7u: 'Tech Solutions Inc.',
            fldyKmaxA4nClOp7VT8ES89l: '0',
            fldzQHQ0piBUKswIa1Gz3SYq: 'Promoter',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'dat05M0XLEbmTUbAGasJxXf3',
      name: {
        en: 'Themes / Insights',
        'zh-CN': '主题 / 见解',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwowPM2wxMvO7pyB3LN84Tm',
          name: {
            en: 'Default',
            'zh-CN': '默认',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldjN4yagJ4yDZpNEPfLHXlp',
              hidden: false,
            },
            {
              templateId: 'fldGbmGNP5CzNwqTNaxoqwac',
              hidden: false,
            },
            {
              templateId: 'fldTzLqbN5dvgKMNyvHhYG2m',
              hidden: false,
              width: 264,
            },
            {
              templateId: 'fldUTciG0o7UupyRPusyGs91',
              hidden: false,
            },
            {
              templateId: 'fldIeBOS2wyYWRK8R5n2X0u8',
              hidden: false,
              width: 259,
            },
            {
              templateId: 'fld5mpKdryOlIy4SPqUuLFGn',
              hidden: false,
              width: 150,
            },
            {
              templateId: 'fldyGMorayrNmXyCJ91Rlbka',
              hidden: false,
              width: 219,
            },
          ],
        },
        {
          type: 'KANBAN',
          templateId: 'viwTLMWmLrhS9gybPojE1wB9',
          name: {
            en: 'Kanban',
            'zh-CN': '看板',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldjN4yagJ4yDZpNEPfLHXlp',
              hidden: false,
            },
            {
              templateId: 'fldGbmGNP5CzNwqTNaxoqwac',
              hidden: false,
            },
            {
              templateId: 'fldTzLqbN5dvgKMNyvHhYG2m',
              hidden: false,
            },
            {
              templateId: 'fldUTciG0o7UupyRPusyGs91',
              hidden: false,
            },
            {
              templateId: 'fldIeBOS2wyYWRK8R5n2X0u8',
              hidden: false,
            },
            {
              templateId: 'fld5mpKdryOlIy4SPqUuLFGn',
              hidden: false,
            },
            {
              templateId: 'fldyGMorayrNmXyCJ91Rlbka',
              hidden: false,
            },
          ],
          groups: [],
          extra: {
            kanbanGroupingFieldId: 'fldyGMorayrNmXyCJ91Rlbka',
            displayFieldName: true,
          },
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldjN4yagJ4yDZpNEPfLHXlp',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Short Name',
            'zh-CN': '简短概括',
          },
          primary: true,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldGbmGNP5CzNwqTNaxoqwac',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Positive/Negative',
            'zh-CN': '正面/负面',
          },
          property: {
            options: [
              {
                id: 'optPbIqECG4z6muyPWpi3T7J',
                name: 'Positive',
                color: 'deepPurple',
              },
              {
                id: 'optr0ItjDPE0OuMUL5BfUyzV',
                name: 'Negative',
                color: 'indigo',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldTzLqbN5dvgKMNyvHhYG2m',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Description',
            'zh-CN': '描述',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldUTciG0o7UupyRPusyGs91',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Relevant Customers',
            'zh-CN': '相关客户',
          },
          property: {
            foreignDatabaseTemplateId: 'dat92XtwJgNXnXgURsat7UYv',
            brotherFieldTemplateId: 'fld0qQ2GDbJwmUwFxdl2pfrq',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldIeBOS2wyYWRK8R5n2X0u8',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Action to Resolve',
            'zh-CN': '如何解决',
          },
          primary: false,
        },
        {
          type: 'MEMBER',
          templateId: 'fld5mpKdryOlIy4SPqUuLFGn',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Follower',
            'zh-CN': '跟进者',
          },
          property: {},
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldyGMorayrNmXyCJ91Rlbka',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Resolution Status',
            'zh-CN': '解决状态',
          },
          property: {
            options: [
              {
                id: 'optEWlO9NyHaDDH9SnOfeDqD',
                name: 'Resolved',
                color: 'deepPurple',
              },
              {
                id: 'optP54wNRkgdGw8M8DjkyKAt',
                name: 'In Progress',
                color: 'indigo',
              },
              {
                id: 'opt1gIO5ToOcYnhe4f65LGQW',
                name: 'Pending',
                color: 'blue',
              },
              {
                id: 'optWqaB2VPKx9yyfw9waZu2e',
                name: 'Ongoing',
                color: 'teal',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'rec07amf11jLromLJ916IvEk',
          data: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Positive',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Maintain quick response systems and encourage feedback.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Customers appreciate fast resolutions to their issues.',
            fldUTciG0o7UupyRPusyGs91: ['recXs4wJkWZvwkLa7xNvuX38'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Quick Resolution',
            fldyGMorayrNmXyCJ91Rlbka: ['opt1gIO5ToOcYnhe4f65LGQW'],
          },
          values: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Positive',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Maintain quick response systems and encourage feedback.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Customers appreciate fast resolutions to their issues.',
            fldUTciG0o7UupyRPusyGs91: ['David Martinez'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Quick Resolution',
            fldyGMorayrNmXyCJ91Rlbka: ['Pending'],
          },
        },
        {
          templateId: 'recTasYO5a7oA4j9D9UobivB',
          data: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Negative',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Conduct a survey to identify missing features.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Some users feel the platform lacks key features for advanced use.',
            fldUTciG0o7UupyRPusyGs91: ['rec7iOho9fCRcp7VU6nqbePV'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Limited Features',
            fldyGMorayrNmXyCJ91Rlbka: ['optWqaB2VPKx9yyfw9waZu2e'],
          },
          values: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Negative',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Conduct a survey to identify missing features.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Some users feel the platform lacks key features for advanced use.',
            fldUTciG0o7UupyRPusyGs91: ['Catherine Lee'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Limited Features',
            fldyGMorayrNmXyCJ91Rlbka: ['Ongoing'],
          },
        },
        {
          templateId: 'recB5gWTVjYNr0AZ3L9QPkTA',
          data: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Positive',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Recognize and reward the support team for their efforts.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Support team receives praise for helpful and friendly service.',
            fldUTciG0o7UupyRPusyGs91: ['rec11ztl89aZIFWK30sUfWVS', 'rec7iOho9fCRcp7VU6nqbePV'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Helpful Support Team',
            fldyGMorayrNmXyCJ91Rlbka: ['opt1gIO5ToOcYnhe4f65LGQW'],
          },
          values: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Positive',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Recognize and reward the support team for their efforts.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Support team receives praise for helpful and friendly service.',
            fldUTciG0o7UupyRPusyGs91: ['Bob Smith', 'Catherine Lee'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Helpful Support Team',
            fldyGMorayrNmXyCJ91Rlbka: ['Pending'],
          },
        },
        {
          templateId: 'reci1Omh4cOnrem6TQSgs3Uv',
          data: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Negative',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Hire additional support staff to reduce wait times.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Customers report long wait times for support responses.',
            fldUTciG0o7UupyRPusyGs91: ['recQwTc2o0riCgg9utYIqDNe'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Long Wait Times',
            fldyGMorayrNmXyCJ91Rlbka: ['optEWlO9NyHaDDH9SnOfeDqD'],
          },
          values: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Negative',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Hire additional support staff to reduce wait times.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Customers report long wait times for support responses.',
            fldUTciG0o7UupyRPusyGs91: ['Evelyn Parker'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Long Wait Times',
            fldyGMorayrNmXyCJ91Rlbka: ['Resolved'],
          },
        },
        {
          templateId: 'recdzL0IEZu1HL4Z3piR3c3R',
          data: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Positive',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Continue investing in UX improvements.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Users find the platform intuitive and easy to navigate.',
            fldUTciG0o7UupyRPusyGs91: ['rec9cAYVzSRiPQeV6YL9i7Pa'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Ease of Use',
            fldyGMorayrNmXyCJ91Rlbka: ['optWqaB2VPKx9yyfw9waZu2e'],
          },
          values: {
            fld5mpKdryOlIy4SPqUuLFGn: [],
            fldGbmGNP5CzNwqTNaxoqwac: 'Positive',
            fldIeBOS2wyYWRK8R5n2X0u8: 'Continue investing in UX improvements.',
            fldTzLqbN5dvgKMNyvHhYG2m: 'Users find the platform intuitive and easy to navigate.',
            fldUTciG0o7UupyRPusyGs91: ['Alice Johnson'],
            fldjN4yagJ4yDZpNEPfLHXlp: 'Ease of Use',
            fldyGMorayrNmXyCJ91Rlbka: ['Ongoing'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datf7ElG1e0cPeYeteo66Qco',
      name: {
        en: 'Follower',
        'zh-CN': '跟进者',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwBDxVsYiy4quFUrGg4AJwo',
          name: {
            en: 'Default',
            'zh-CN': '默认',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldW9fDCfzejwBuauw35KKCX',
              hidden: false,
            },
            {
              templateId: 'fld2PZqjcBkMc7gGk6vxNMQP',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'MEMBER',
          templateId: 'fldW9fDCfzejwBuauw35KKCX',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Members',
            'zh-CN': '成员',
          },
          property: {},
          primary: true,
        },
        {
          type: 'EMAIL',
          templateId: 'fld2PZqjcBkMc7gGk6vxNMQP',
          privilege: 'FULL_EDIT',
          name: {
            en: 'Email',
            'zh-CN': '电子邮件',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recHky9fjUmbrogbrCNTdlB3',
          data: {
            fld2PZqjcBkMc7gGk6vxNMQP: '<EMAIL>',
            fldW9fDCfzejwBuauw35KKCX: [],
          },
          values: {
            fld2PZqjcBkMc7gGk6vxNMQP: '<EMAIL>',
            fldW9fDCfzejwBuauw35KKCX: [],
          },
        },
        {
          templateId: 'recFvmVvtB4oK1WYCo1UqxKI',
          data: {
            fld2PZqjcBkMc7gGk6vxNMQP: '<EMAIL>',
            fldW9fDCfzejwBuauw35KKCX: [],
          },
          values: {
            fld2PZqjcBkMc7gGk6vxNMQP: '<EMAIL>',
            fldW9fDCfzejwBuauw35KKCX: [],
          },
        },
        {
          templateId: 'recPlAaCky1GYWXU8ckrfa7M',
          data: {
            fld2PZqjcBkMc7gGk6vxNMQP: '<EMAIL>',
            fldW9fDCfzejwBuauw35KKCX: [],
          },
          values: {
            fld2PZqjcBkMc7gGk6vxNMQP: '<EMAIL>',
            fldW9fDCfzejwBuauw35KKCX: [],
          },
        },
      ],
    },
    {
      resourceType: 'FORM',
      templateId: 'fomaBDAdkOQ3k4ql5yvjQYrP',
      name: {
        en: 'Feedback Form',
        'zh-CN': '反馈收集表',
      },
      formType: 'DATABASE',
      databaseTemplateId: 'dat92XtwJgNXnXgURsat7UYv',
      metadata: {
        type: 'VIEW',
        viewTemplateId: 'viwOe0myRKlWOYWDrCCifavg',
      },
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'auto_retrieve_email',
      name: {
        en: 'Assign followers when there is new feedback',
        'zh-CN': '当有新反馈时分配跟进者',
      },
      triggers: [
        {
          triggerType: 'FORM_SUBMITTED',
          templateId: 'received_email',
          description: {
            en: 'When there is new feedback',
            'zh-CN': '当有新反馈时',
          },
          input: {
            type: 'FORM',
            formTemplateId: 'fomaBDAdkOQ3k4ql5yvjQYrP',
          },
        },
      ],
      actions: [
        {
          templateId: 'take_follower',
          description: {
            en: 'Rotate follow-up member',
            'zh-CN': '轮换跟进成员',
          },
          actionType: 'ROUND_ROBIN',
          input: {
            type: 'DATABASE',
            databaseTemplateId: 'datf7ElG1e0cPeYeteo66Qco',
          },
        },
        {
          templateId: 'acthRkLYS4VVgVsWNMN1hVCr',
          description: {
            en: 'Assign followers',
            'zh-CN': '分配跟进者',
          },
          actionType: 'CREATE_RECORD',
          input: {
            type: 'RECORD_BODY',
            fieldKeyType: 'TEMPLATE_ID',
            data: {
              fld5mpKdryOlIy4SPqUuLFGn:
                '<%= JSON.stringify(_actions.take_follower.record.cells.fldW9fDCfzejwBuauw35KKCX.data) %>',
              fldUTciG0o7UupyRPusyGs91: '<%= _triggers.received_email.record.id %>',
            },
            databaseTemplateId: 'dat05M0XLEbmTUbAGasJxXf3',
          },
        },
        {
          templateId: 'actVxXR9x2rolFMGtmXtefmX',
          description: {
            en: 'Send an email to remind followers',
            'zh-CN': '发送邮件提醒跟进者',
          },
          actionType: 'SEND_EMAIL',
          input: {
            subject: 'New insights to be analyzed',
            body: {
              markdown:
                'There is a new user feedback that you need to follow up and analyze\n' +
                '\n' +
                'Customer Name: <%= _triggers.received_email.record.cells.fldOCXsYbGcdmlRHv8ovtvrK.value %>\n' +
                'Product Satisfaction: <%= _triggers.received_email.record.cells.fld2x05Zec9vmsrb9saCnaQ9.value %>\n' +
                'Service Satisfaction: <%= _triggers.received_email.record.cells.fldV6Vk2DPZ8wnF0lA34HHiY.value %>\n' +
                'Product Comparison:<%= JSON.stringify(_triggers.received_email.record.cells.fld59t3V6EP0nGBGeD5REEmm.value) %>\n' +
                'Service Comparison: <%= JSON.stringify(_triggers.received_email.record.cells.fldsGEvZzd8WMQUoT8aV6api.value) %>\n' +
                'Reasons: <%= _triggers.received_email.record.cells.fldfSPbvnvCTGGg5ApZtijvQ.value %>\n' +
                'Rating: <%= _triggers.received_email.record.cells.fldpIMkbJqM8Y6JIbA5sl3rB.value %>\n' +
                '\n' +
                "Please analyze the user's feedback ratings and insights, and follow up on subsequent resolve action. Click the link below to follow up:  \n" +
                '<%= _actions.acthRkLYS4VVgVsWNMN1hVCr.record.url %>',
              json: {
                type: 'doc',
                content: [
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'There is a new user feedback that you need to follow up and analyze',
                        type: 'text',
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Customer Name: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: ['_triggers', 'received_email', 'record', 'cells', 'fldOCXsYbGcdmlRHv8ovtvrK', 'value'],
                          tips: '',
                          names: ['触发器', '有新的表单提交时', '记录', '单元格', 'Customer Name', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Product Satisfaction: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: ['_triggers', 'received_email', 'record', 'cells', 'fld2x05Zec9vmsrb9saCnaQ9', 'value'],
                          tips: '',
                          names: ['触发器', '有新的表单提交时', '记录', '单元格', 'Product Satisfaction', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Service Satisfaction: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: ['_triggers', 'received_email', 'record', 'cells', 'fldV6Vk2DPZ8wnF0lA34HHiY', 'value'],
                          tips: '',
                          names: ['触发器', '有新的表单提交时', '记录', '单元格', 'Service Satisfaction', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Product Comparison:',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: 'JSON.stringify(_triggers.received_email.record.cells.fld59t3V6EP0nGBGeD5REEmm.value)',
                          tips: '',
                          names: ['触发器', '有新的表单提交时', '记录', '单元格', 'Product Comparison', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Service Comparison: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: 'JSON.stringify(_triggers.received_email.record.cells.fldsGEvZzd8WMQUoT8aV6api.value)',
                          tips: '',
                          names: ['触发器', '有新的表单提交时', '记录', '单元格', 'Service Comparison', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Reasons: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: ['_triggers', 'received_email', 'record', 'cells', 'fldfSPbvnvCTGGg5ApZtijvQ', 'value'],
                          tips: '',
                          names: ['触发器', '有新的表单提交时', '记录', '单元格', 'Reasons', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: 'Rating: ',
                        type: 'text',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: ['_triggers', 'received_email', 'record', 'cells', 'fldpIMkbJqM8Y6JIbA5sl3rB', 'value'],
                          tips: '',
                          names: ['触发器', '有新的表单提交时', '记录', '单元格', 'Rating', 'value'],
                        },
                      },
                    ],
                  },
                  {
                    type: 'paragraph',
                  },
                  {
                    type: 'paragraph',
                    content: [
                      {
                        text: "Please analyze the user's feedback ratings and insights, and follow up on subsequent resolve action. Click the link below to follow up:",
                        type: 'text',
                      },
                      {
                        type: 'hardBreak',
                      },
                      {
                        type: 'variable',
                        attrs: {
                          ids: ['_actions', 'acthRkLYS4VVgVsWNMN1hVCr', 'record', 'url'],
                          tips: '',
                          names: ['执行器', '创建记录', '记录', 'url'],
                        },
                      },
                      {
                        type: 'hardBreak',
                      },
                    ],
                  },
                ],
              },
            },
            to: [
              {
                type: 'EMAIL_STRING',
                email: '<%= _actions.take_follower.record.cells.fld2PZqjcBkMc7gGk6vxNMQP.value %>',
              },
            ],
            senderName: '',
            cc: [],
            bcc: [],
            replyTo: [],
            type: 'SERVICE',
          },
        },
      ],
    },
    {
      resourceType: 'DASHBOARD',
      templateId: 'dsbJLOS2cggSJhYPNeYcPyDO',
      name: {
        en: 'Feedback and satisfaction statistics',
        'zh-CN': '反馈和满意度统计',
      },
      widgets: [
        {
          templateId: 'wdtmGRFLqLAiqoAKfpYvxA3v',
          type: 'NUMBER',
          name: {
            en: 'Total number of responses',
            'zh-CN': '总回复数',
          },
          summaryDescription: {
            en: 'Total number of feedback records',
            'zh-CN': '总反馈记录数',
          },
          datasource: {
            databaseTemplateId: 'dat92XtwJgNXnXgURsat7UYv',
            viewTemplateId: 'viwWkuxWYHMjdbBQwUovJkVe',
            type: 'DATABASE',
            metricsType: 'COUNT_RECORDS',
          },
        },
        {
          templateId: 'wdtEBJ8eBLTBF9K6b2gWQxQH',
          type: 'CHART',
          name: {
            en: 'Total Rating Number of Feedbacks by Score',
            'zh-CN': '总评分不同级别评分的反馈数量',
          },
          datasource: {
            databaseTemplateId: 'dat92XtwJgNXnXgURsat7UYv',
            viewTemplateId: 'viwWkuxWYHMjdbBQwUovJkVe',
            type: 'DATABASE',
            chartType: 'bar',
            metricsType: 'COUNT_RECORDS',
            dimensionTemplateId: 'fldpIMkbJqM8Y6JIbA5sl3rB',
          },
          settings: {
            showEmptyValues: true,
            showDataTips: true,
            sortByAxis: 'Y',
          },
        },
        {
          templateId: 'wdt622gXdxrihNWTx4pNjBBF',
          type: 'CHART',
          name: {
            en: 'Product satisfaction Number of Feedbacks by Score',
            'zh-CN': '产品满意度不同级别评分的反馈数量',
          },
          datasource: {
            databaseTemplateId: 'dat92XtwJgNXnXgURsat7UYv',
            viewTemplateId: 'viwWkuxWYHMjdbBQwUovJkVe',
            type: 'DATABASE',
            chartType: 'bar',
            metricsType: 'COUNT_RECORDS',
            dimensionTemplateId: 'fld2x05Zec9vmsrb9saCnaQ9',
          },
          settings: {
            showEmptyValues: true,
            showDataTips: true,
          },
        },
        {
          templateId: 'wdt9qhk91B78kvwwRZ27imTv',
          type: 'CHART',
          name: {
            en: 'Service Satisfaction Number of Feedbacks by Score',
            'zh-CN': '服务满意度不同级别评分的反馈数量',
          },
          datasource: {
            databaseTemplateId: 'dat92XtwJgNXnXgURsat7UYv',
            viewTemplateId: 'viwWkuxWYHMjdbBQwUovJkVe',
            type: 'DATABASE',
            chartType: 'bar',
            metricsType: 'COUNT_RECORDS',
            dimensionTemplateId: 'fldV6Vk2DPZ8wnF0lA34HHiY',
          },
          settings: {
            showEmptyValues: true,
            showDataTips: true,
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
