import { PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import type { Locale, ExtendLocale } from '@bika/types/i18n/bo';

export interface TemplatePhotoStrategy {
  // 输入模板名字和应用场景，替换模板里的变量, Title_1,  USE_CASES_{0-23}, PERSONAS_{0-3}
  strategy: 'TEMPLATE_PHOTO';
  output: string;
  // 循环模式，template只循环一次，其它的根据填了多少就循环多少，被进行变量赋值
  loop?: 'TEMPLATE' | 'USE_CASE' | 'PERSONA' | string[];
  photoFile: string;
  locales: Locale[];
}

export interface TemplateAIStrategy {
  // 将模板文件里的变量替换成输入的prompt
  strategy: 'TEMPLATE_AI';
  promptFile: string;
  // 输出的路径
  output: string;

  // 循环模式，template只循环一次，其它的根据填了多少就循环多少，被进行变量赋值
  loopKeywords?: string[];
  loopPersonas?: boolean;
  loopUseCases?: boolean;

  // 是否动态内容生成，会在文件名后边加个随机英文单词，避开文件名重复检测
  dynamic?: boolean;

  // 模板筛选，包含以下字符串的模板才会被使用
  includeFilters?: string[];

  // 模板筛选，id包含以下字符串的模板不会被使用
  excludeFilters?: string[];

  // 包含哪些语言？ 不设置则包含所有
  locales?: ExtendLocale[];

  model: PresetLanguageAIModelDef;
}

/**
 * 导出React Flow图片
 */
export interface TemplateReactFlowStrategy {
  // 输入模板名字和应用场景，替换模板里的变量, Title_1,  USE_CASES_{0-23}, PERSONAS_{0-3}
  strategy: 'TEMPLATE_REACT_FLOW';
  output: string;
  locales: Locale[];
}

export type IAigcStrategy =
  | {
      // Bika公司主PPT，公司介绍+罗列各种解决方案，内容丰富，每个语言只有1份
      strategy: 'BIKA_PPT';
      pptxFile: string;
    }
  | {
      // 模板PPT，供用户在浏览模板的时候，进行下载，然后拿到公司内部进行汇报，相对简短一点，是Bika PPT的缩水版，每个模板都会生成
      strategy: 'TEMPLATE_PPT';
      pptxFile: string;
    }
  | {
      // 模板PPT，抓取“use Case”，然后生成一个有生命力的主题，前面几页吹牛，后面几页依然是解决方案和产品
      strategy: 'TEMPLATE_AI_USE_CASE_PPT';
      pptxFile: string;
    }
  | TemplateReactFlowStrategy
  | TemplateAIStrategy
  | TemplatePhotoStrategy;
