import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'customer-after-sales-service-management',
  name: {
    en: 'Customer After-Sales Service Management',
    'zh-CN': '客户售后服务管理',
  },
  description: {
    en: 'This template integrates customer case tracking, after-sales service, employee training, and customer information management, providing comprehensive support for delivering a high-quality customer experience. And can be used in scenarios such as retail and e-commerce, residential property maintenance, home appliance installation and repair, and domestic services.',
    'zh-CN':
      '客户售后服务管理：自动分配工单和超时提醒，可快速响应客户需求，让您的企业更专业、高效地管理客户服务，提升客户体验。此模板可用于零售和电子商务、小区物业维修，家电安装维修，家政服务等工单分配和跟踪场景。',
  },
  cover: '/assets/template/customer-after-sales-service-management/customer-after-sales-service-management.png',
  author: 'Tian<PERSON> <<EMAIL>>',
  category: ['project', 'sales'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.1.2',
  resources: [
    {
      resourceType: 'FOLDER',
      templateId: 'foldH9Ty6LlbKCsSe9yEQWVTT',
      name: {
        en: 'Automation',
        'zh-CN': '自动化',
      },
      description: {
        en: 'Business Workflow is here！\n',
        'zh-CN': '自动分配工单和提醒超时，快速响应客户需求。\n',
      },
      scope: 'SPACE',
      children: [
        {
          resourceType: 'AUTOMATION',
          templateId: 'atoM2scAMCiVssgJQ8zeO1kS',
          name: {
            en: 'Automatic Assignment of Service Ticket Handlers',
            'zh-CN': '售后工单自动分配处理人',
          },
          description: {
            en: 'Automatically Assign After-sales Members and Create Follow-up Tasks',
            'zh-CN': '自动分配售后成员并创建跟进任务',
          },
          triggers: [
            {
              triggerType: 'RECORD_CREATED',
              templateId: 'trgcuwyhjZGtz4rQeUKjZXi2',
              description: {
                en: 'When a new after-sales service ticket is submitted',
                'zh-CN': '有新的售后工单提交时',
              },
              input: {
                type: 'DATABASE',
                databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
              },
            },
          ],
          actions: [
            {
              templateId: 'actvWxjaztu8TgIgzTYfICdN',
              description: {
                en: 'Assign after-sales duty personnel on a rotating basis',
                'zh-CN': '轮流分配售后值班人员',
              },
              actionType: 'ROUND_ROBIN',
              input: {
                type: 'DATABASE',
                databaseTemplateId: 'datGiErEs6O6eVWkRsdapJhP',
              },
            },
            {
              templateId: 'actotpuMnIr0lgb7hv2OYwCO',
              description: {
                en: "Assign the responsible person to the 'After-sales Handler' column in the after-sales service table.",
                'zh-CN': '将分配的责任人填到售后服务表的「售后处理人」列',
              },
              actionType: 'UPDATE_RECORD',
              input: {
                type: 'SPECIFY_RECORD_BODY',
                recordId: '<%= _triggers.trgcuwyhjZGtz4rQeUKjZXi2.record.id %>',
                fieldKeyType: 'TEMPLATE_ID',
                data: {
                  fldtsE8ORBbVKv0nBuJi50Km: ['optD4p3BE0Naz358NjVjGgy5'],
                  fldxYBqk8NeW60dAMGJDQfIK:
                    '<%= JSON.stringify(_actions.actvWxjaztu8TgIgzTYfICdN.record.cells.fldiuYimyqBCvLvxx7RbQIoL.data) %>',
                },
                databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
              },
            },
            {
              templateId: 'actmtzG6AznWBJhIJhSI0H60',
              description: {
                en: 'Create a task for the assigned after-sales person to follow up on the new service ticket.',
                'zh-CN': '给被分配的售后负责人创建一个任务以跟进新工单',
              },
              actionType: 'CREATE_MISSION',
              input: {
                type: 'MISSION_BODY',
                mission: {
                  type: 'UPDATE_RECORD',
                  name: 'Hi, you have an after-sales service ticket that needs follow-up.',
                  description:
                    '### Task Details        \n' +
                    '- Customer:  <%= JSON.stringify(_triggers.trgcuwyhjZGtz4rQeUKjZXi2.record.cells.fldPOFMl69aIbsHCS84AmIQh.data) %>    \n' +
                    '- Type: <%= _joinArrayAsText(_triggers.trgcuwyhjZGtz4rQeUKjZXi2.record.cells.fldXSCoRZ8OFj7HwGtA5VJns.value) %>        \n' +
                    '- Reason: <%= _triggers.trgcuwyhjZGtz4rQeUKjZXi2.record.cells.fldLQ08DQnZ5QriSLT8KFgXe.data %>        \n' +
                    '### Task Objectives        \n' +
                    '- 💃🏻 Check the service ticket details;        \n' +
                    '- 🍀 Follow up and provide feedback to the customer;        \n' +
                    '- 🏁 Update the status and follow-up time;        \n' +
                    '- 🎉 Change the status to completed after finishing the task.',
                  canReject: true,
                  canCompleteManually: true,
                  canTransfer: true,
                  forcePopup: false,
                  assignType: 'DEDICATED',
                  to: [
                    {
                      type: 'SPECIFY_UNITS',
                      unitIds: [
                        '<%= JSON.stringify(_actions.actvWxjaztu8TgIgzTYfICdN.record.cells.fldiuYimyqBCvLvxx7RbQIoL.data) %>',
                      ],
                    },
                  ],
                  reminders: [],
                  buttonText: {
                    en: 'Follow up',
                    'zh-CN': '去跟进',
                  },
                  recordId: '<%= _triggers.trgcuwyhjZGtz4rQeUKjZXi2.record.id %>',
                  databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
                },
              },
            },
          ],
        },
        {
          resourceType: 'AUTOMATION',
          templateId: 'atoUDlU19RvJueVxB3dUOS9B',
          name: {
            en: 'Service Ticket Timeout Reminder',
            'zh-CN': '工单超时提醒',
          },
          triggers: [
            {
              triggerType: 'SCHEDULER',
              templateId: 'trgdPsMj6qGATVEdoCRQKGsl',
              description: {
                en: 'Every day at 9:00 AM',
                'zh-CN': '每天上午9:00',
              },
              input: {
                type: 'SCHEDULER',
                scheduler: {
                  repeat: {
                    every: {
                      type: 'DAY',
                      interval: 1,
                    },
                  },
                  datetime: '2025-01-23T01:00:17.100Z',
                },
              },
            },
          ],
          actions: [
            {
              templateId: 'actnOWSdBPL9PWFY8zmmDHjc',
              description: {
                en: 'Find Timeout service ticket records',
                'zh-CN': '查找超时的工单记录',
              },
              actionType: 'FIND_RECORDS',
              input: {
                interruptIfNoRecord: true,
                type: 'DATABASE_WITH_FILTER',
                filters: {
                  conjunction: 'And',
                  conditions: [],
                  conds: [
                    {
                      fieldTemplateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
                      fieldType: 'FORMULA',
                      clause: {
                        operator: 'Contains',
                        value: 'Timeout',
                      },
                    },
                  ],
                },
                databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
              },
            },
            {
              templateId: 'actXZtK1KJUZkiEkyoxr40V0',
              description: {
                en: 'Send a message reminder to Slack',
                'zh-CN': '发送超时汇总记录到slack',
              },
              actionType: 'SLACK_WEBHOOK',
              input: {
                type: 'SLACK_WEBHOOK',
                data: {
                  msgtype: 'text',
                  text:
                    'Today is <%= new Date().toLocaleDateString() %>                    \n' +
                    'These service tickets have reached the preset timeout limit. Please handle this service ticket as soon as possible to ensure it is completed within a reasonable time frame.  👇🏻👇🏻                               \n' +
                    '                                          \n' +
                    "<%= _renderRecordsAsGrid(_actions.actnOWSdBPL9PWFY8zmmDHjc.records, ['fldXSCoRZ8OFj7HwGtA5VJns','fldcdqoNZ2nkyYRV3QZnRNfs']) %>  \n" +
                    '                          \n' +
                    '🗣️If you are unable to handle it in time, the system will automatically escalate this service ticket to your superior leader for processing.',
                },
                urlType: 'URL',
                url: '',
              },
            },
          ],
        },
      ],
      cover: {
        type: 'UNSPLASH',
        url: 'https://images.unsplash.com/photo-1497005367839-6e852de72767?q=80&w=2067&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
      readme: {
        en:
          '\n' +
          '### 📍 How to Use?\n' +
          'Included automations:\n' +
          '- Automatic Assignment of Service Ticket Handlers: When a new ticket is submitted, the system automatically assigns a handler based on the rotation table. This significantly reduces the workload for managers.\n' +
          '- Service Ticket Timeout Reminder: Automatically remind the assigned handler when a ticket is in an overdue status. Timely detection and alerting of overdue tickets help prevent the negative impact of delayed tickets.',
        'zh-CN':
          '### 📍 如何使用？\n' +
          '包含的自动化:\n' +
          '- 自动分配工单处理人:有新的工单提交，根据轮值表自动分配处理人，并且AI自动将工单拆分为结构化的任务。大大减轻了管理者的工作。\n' +
          '- 工单超时提醒：当工单为超时状态时，自动提醒处理人。及时发现和预警超时工单，避免了工单滞后带来的负面影响。',
      },
    },
    {
      resourceType: 'FOLDER',
      templateId: 'foldN2dal8w0H5CnFPDB9fdVe',
      name: {
        en: 'Personal Workstation',
        'zh-CN': '个人工作台',
      },
      description: {
        en: 'Employees can quickly find the entry point to handle tasks in the files they use.',
        'zh-CN': '个性化工作台，帮助员工高效管理任务。',
      },
      scope: 'SPACE',
      children: [
        {
          resourceType: 'DASHBOARD',
          templateId: 'dsb3nQyZROr4mFWJMnIPqxeZ',
          name: {
            en: 'Personal Task Statistics',
            'zh-CN': '个人任务统计',
          },
          widgets: [
            {
              templateId: 'wdtl79JXcsrYMaozxX1nILDg',
              type: 'NUMBER',
              name: {
                en: 'Processed by me',
                'zh-CN': '我处理的',
              },
              layout: {
                x: 0,
                y: 0,
                w: 6,
                h: 2,
              },
              summaryDescription: {
                en: 'Tickets I Handle',
                'zh-CN': '我处理的',
              },
              datasource: {
                databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
                viewTemplateId: 'viwuWOrfSJ3cG5jXIbbG16R5',
                type: 'DATABASE',
                metricsType: 'COUNT_RECORDS',
              },
            },
            {
              templateId: 'wdtCGtk6fZAhUfWjqwWsHIQF',
              type: 'NUMBER',
              name: {
                en: 'Approved by me',
                'zh-CN': '我审批的',
              },
              layout: {
                x: 6,
                y: 0,
                w: 6,
                h: 2,
              },
              summaryDescription: {
                en: 'Tickets I Approve',
                'zh-CN': '我审批的',
              },
              datasource: {
                databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
                viewTemplateId: 'viwVC6EwWr7L2Fxl6ox7yL9k',
                type: 'DATABASE',
                metricsType: 'COUNT_RECORDS',
              },
            },
            {
              templateId: 'wdt221CuqYKQnnUfZR8pYOfx',
              type: 'NUMBER',
              name: {
                en: 'Pending processing by me',
                'zh-CN': '我的待处理',
              },
              layout: {
                x: 0,
                y: 2,
                w: 6,
                h: 2,
              },
              summaryDescription: {
                en: 'My Pending Tickets',
                'zh-CN': '我的待处理',
              },
              datasource: {
                databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
                viewTemplateId: 'viw53orgZ40TXvSdrCRZ60Gl',
                type: 'DATABASE',
                metricsType: 'COUNT_RECORDS',
              },
            },
            {
              templateId: 'wdtpMxnPgyzkEe0CXC1gd7Zc',
              type: 'NUMBER',
              name: {
                en: 'Pending approval by me',
                'zh-CN': '我的待审批',
              },
              layout: {
                x: 6,
                y: 2,
                w: 6,
                h: 2,
              },
              summaryDescription: {
                en: 'My Pending Approvals',
                'zh-CN': '我的待审批',
              },
              datasource: {
                databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
                viewTemplateId: 'viwfLCCgWyh3M9uV8gGeGLcB',
                type: 'DATABASE',
                metricsType: 'COUNT_RECORDS',
              },
            },
            {
              templateId: 'wdtbqsZDXRRbfhSoRyzfFvYi',
              type: 'CHART',
              name: {
                en: 'After-sales types',
                'zh-CN': '售后类型',
              },
              layout: {
                x: 0,
                y: 4,
                w: 6,
                h: 3,
              },
              datasource: {
                databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
                viewTemplateId: 'viwKKuXegRTUHRVZ0UINAZSn',
                type: 'DATABASE',
                chartType: 'pie',
                metricsType: 'COUNT_RECORDS',
                dimensionTemplateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
              },
              settings: {
                showEmptyValues: true,
                showDataTips: true,
                excludeZeroPoint: false,
                theme: 'theme_color_1',
                sortByAxis: 'X',
                sortRule: 'ASC',
              },
            },
            {
              templateId: 'wdt5s48NA3asa56WTlourCt9',
              type: 'CHART',
              name: {
                en: 'Timeout status',
                'zh-CN': '超时状态',
              },
              layout: {
                x: 6,
                y: 3,
                w: 6,
                h: 3,
              },
              datasource: {
                databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
                viewTemplateId: 'viwKKuXegRTUHRVZ0UINAZSn',
                type: 'DATABASE',
                chartType: 'bar',
                metricsType: 'COUNT_RECORDS',
                dimensionTemplateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
              },
              settings: {
                showEmptyValues: true,
                showDataTips: true,
                excludeZeroPoint: false,
                theme: 'theme_color_4',
                sortByAxis: 'X',
                sortRule: 'ASC',
              },
            },
          ],
        },
        {
          resourceType: 'MIRROR',
          templateId: 'mirquF3BAfIjQveo9zQGerAQ',
          name: {
            en: 'Service Tickets I Approve',
            'zh-CN': '我负责审批的工单',
          },
          mirrorType: 'DATABASE_VIEW',
          databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
          viewTemplateId: 'viwVC6EwWr7L2Fxl6ox7yL9k',
        },
        {
          resourceType: 'MIRROR',
          templateId: 'mirHVLeO6XCQxfF1UyZOdUiO',
          name: {
            en: 'Service Tickets I Handle',
            'zh-CN': '我负责处理的工单',
          },
          mirrorType: 'DATABASE_VIEW',
          databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
          viewTemplateId: 'viwuWOrfSJ3cG5jXIbbG16R5',
        },
        {
          resourceType: 'FORM',
          templateId: 'fom1V4G4rCdYRBIJRj6ZMqpJ',
          name: {
            en: 'After-Sales Service Ticket Entry',
            'zh-CN': '售后工单录入',
          },
          brandLogo: {
            type: 'EMOJI',
            backgroundColor: '',
            emoji: '😎',
          },
          formType: 'DATABASE',
          databaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
          metadata: {
            type: 'VIEW',
            viewTemplateId: 'viwM1B6jqta4Gm3hFduHmIQt',
          },
        },
      ],
      cover: {
        type: 'UNSPLASH',
        url: 'https://images.unsplash.com/photo-1526657782461-9fe13402a841?q=80&w=1984&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
      readme: {
        en:
          '### 📍 How to Use?\n' +
          'Included Nodes:\n' +
          '- Personal Task Statistics: A dashboard for various task statistics of "my tasks".\n' +
          '- Service Tickets I Approve: Quickly find Service Tickets that I am responsible for approving here.\n' +
          '- Service Tickets I Handle: Service Tickets assigned to "me", quickly find the Service Tickets I am responsible for here.\n' +
          '- After-sales Service Ticket Entry: A form for quick entry of Service Tickets.\n',
        'zh-CN':
          '### 📍如何使用？\n' +
          '包含的节点：\n' +
          '- 个人任务统计：“我”的各项任务统计仪表盘。\n' +
          '- 我负责审批的工单：在这里可快速找到自己负责审批的工单。 \n' +
          '- 我负责处理的工单：分配给“我”的工单，在这里可快速找到自己负责的工单。    \n' +
          '- 售后工单录入：表单，实现工单快速录入.  \n',
      },
    },
    {
      resourceType: 'FOLDER',
      templateId: 'foldIGMre2Tne39YYlQOZAZvS',
      name: {
        en: 'Backend Management',
        'zh-CN': '后台管理',
      },
      description: {
        en: 'Tables for administrator backend management, facilitating unified permission settings.',
        'zh-CN': '管理员后台管理的数据表，方便统一权限设置。',
      },
      scope: 'SPACE',
      children: [
        {
          resourceType: 'DATABASE',
          templateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
          name: {
            en: 'After-Sales Service',
            'zh-CN': '售后服务',
          },
          databaseType: 'DATUM',
          views: [
            {
              type: 'TABLE',
              templateId: 'viwKKuXegRTUHRVZ0UINAZSn',
              name: {
                en: 'All',
                'zh-CN': '所有',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fldhBIK7j01ZzQyXnK9OFYRm',
                  width: 175,
                },
                {
                  templateId: 'fldPOFMl69aIbsHCS84AmIQh',
                  hidden: false,
                  width: 150,
                },
                {
                  templateId: 'fldkdIS5JUYjLweREzM0h6ID',
                  hidden: false,
                  width: 181,
                },
                {
                  templateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
                  width: 160,
                },
                {
                  templateId: 'fldPIM8SlNUBi3D3PscnXPMv',
                  width: 150,
                },
                {
                  templateId: 'fldZRslfymlbGdiKQwLf4r8E',
                  width: 164,
                },
                {
                  templateId: 'fldLQ08DQnZ5QriSLT8KFgXe',
                  width: 153,
                },
                {
                  templateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                  width: 150,
                },
                {
                  templateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                  width: 224,
                },
                {
                  templateId: 'fldZLUWsuiGwty4YmnAWADch',
                  width: 168,
                },
                {
                  templateId: 'fldKOMikSA5v7Rclzv7wMtPu',
                },
                {
                  templateId: 'fldQByn4MtGVyEkCEi5wcOHb',
                  width: 170,
                },
                {
                  templateId: 'fldsbQ5tHOcPJYPpI2EhNNSk',
                  width: 255,
                },
                {
                  templateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                },
                {
                  templateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
                  hidden: false,
                  width: 239,
                },
              ],
            },
            {
              type: 'TABLE',
              templateId: 'viwC7m9wOh7WydUMavyvj76b',
              name: {
                en: 'Pending',
                'zh-CN': '待处理',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
                conds: [
                  {
                    fieldTemplateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                    fieldType: 'SINGLE_SELECT',
                    clause: {
                      operator: 'Contains',
                      value: ['optDKP1WEO52Z6dxcJR4w1oR', 'optD4p3BE0Naz358NjVjGgy5'],
                    },
                  },
                ],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fldhBIK7j01ZzQyXnK9OFYRm',
                  hidden: false,
                },
                {
                  templateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
                  hidden: false,
                },
                {
                  templateId: 'fldPIM8SlNUBi3D3PscnXPMv',
                  hidden: false,
                },
                {
                  templateId: 'fldZRslfymlbGdiKQwLf4r8E',
                  hidden: false,
                },
                {
                  templateId: 'fldLQ08DQnZ5QriSLT8KFgXe',
                  hidden: false,
                },
                {
                  templateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                  hidden: false,
                },
                {
                  templateId: 'fldZLUWsuiGwty4YmnAWADch',
                  hidden: false,
                },
                {
                  templateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                  hidden: false,
                },
                {
                  templateId: 'fldKOMikSA5v7Rclzv7wMtPu',
                  hidden: false,
                },
                {
                  templateId: 'fldQByn4MtGVyEkCEi5wcOHb',
                  hidden: false,
                },
                {
                  templateId: 'fldsbQ5tHOcPJYPpI2EhNNSk',
                  hidden: false,
                },
                {
                  templateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                  hidden: false,
                },
                {
                  templateId: 'fldPOFMl69aIbsHCS84AmIQh',
                  hidden: false,
                },
                {
                  templateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
                  hidden: false,
                },
                {
                  templateId: 'fldkdIS5JUYjLweREzM0h6ID',
                  hidden: false,
                },
              ],
            },
            {
              type: 'TABLE',
              templateId: 'viwuWOrfSJ3cG5jXIbbG16R5',
              name: {
                en: 'Tickets I Handle',
                'zh-CN': '我负责的',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
                conds: [
                  {
                    fieldTemplateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                    fieldType: 'MEMBER',
                    clause: {
                      operator: 'Is',
                      value: ['Self'],
                    },
                  },
                ],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fldhBIK7j01ZzQyXnK9OFYRm',
                },
                {
                  templateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
                },
                {
                  templateId: 'fldPIM8SlNUBi3D3PscnXPMv',
                },
                {
                  templateId: 'fldZRslfymlbGdiKQwLf4r8E',
                },
                {
                  templateId: 'fldLQ08DQnZ5QriSLT8KFgXe',
                },
                {
                  templateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                },
                {
                  templateId: 'fldZLUWsuiGwty4YmnAWADch',
                  width: 150,
                },
                {
                  templateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                },
                {
                  templateId: 'fldKOMikSA5v7Rclzv7wMtPu',
                },
                {
                  templateId: 'fldQByn4MtGVyEkCEi5wcOHb',
                },
                {
                  templateId: 'fldsbQ5tHOcPJYPpI2EhNNSk',
                },
                {
                  templateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                },
                {
                  templateId: 'fldPOFMl69aIbsHCS84AmIQh',
                  hidden: false,
                },
                {
                  templateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
                  hidden: false,
                },
                {
                  templateId: 'fldkdIS5JUYjLweREzM0h6ID',
                  hidden: false,
                },
              ],
            },
            {
              type: 'TABLE',
              templateId: 'viwVC6EwWr7L2Fxl6ox7yL9k',
              name: {
                en: 'Tickets I Approve',
                'zh-CN': '我审批的',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
                conds: [
                  {
                    fieldTemplateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                    fieldType: 'MEMBER',
                    clause: {
                      operator: 'Contains',
                      value: ['Self'],
                    },
                  },
                ],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fldhBIK7j01ZzQyXnK9OFYRm',
                  hidden: false,
                },
                {
                  templateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
                  hidden: false,
                },
                {
                  templateId: 'fldPIM8SlNUBi3D3PscnXPMv',
                  hidden: false,
                },
                {
                  templateId: 'fldZRslfymlbGdiKQwLf4r8E',
                  hidden: false,
                },
                {
                  templateId: 'fldLQ08DQnZ5QriSLT8KFgXe',
                  hidden: false,
                },
                {
                  templateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                  hidden: false,
                },
                {
                  templateId: 'fldZLUWsuiGwty4YmnAWADch',
                  hidden: false,
                },
                {
                  templateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                  hidden: true,
                },
                {
                  templateId: 'fldKOMikSA5v7Rclzv7wMtPu',
                  hidden: true,
                },
                {
                  templateId: 'fldQByn4MtGVyEkCEi5wcOHb',
                  hidden: true,
                },
                {
                  templateId: 'fldsbQ5tHOcPJYPpI2EhNNSk',
                  hidden: false,
                },
                {
                  templateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                  hidden: false,
                },
                {
                  templateId: 'fldPOFMl69aIbsHCS84AmIQh',
                  hidden: false,
                },
                {
                  templateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
                  hidden: false,
                },
                {
                  templateId: 'fldkdIS5JUYjLweREzM0h6ID',
                  hidden: false,
                },
              ],
              groups: [
                {
                  fieldTemplateId: 'fldZLUWsuiGwty4YmnAWADch',
                  asc: true,
                },
              ],
            },
            {
              type: 'KANBAN',
              templateId: 'viwfokmwr1zGH9eUmt1RVJbg',
              name: {
                en: 'Status Kanban',
                'zh-CN': '状态看板',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fldhBIK7j01ZzQyXnK9OFYRm',
                  hidden: false,
                },
                {
                  templateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
                  hidden: false,
                },
                {
                  templateId: 'fldPIM8SlNUBi3D3PscnXPMv',
                  hidden: false,
                },
                {
                  templateId: 'fldZRslfymlbGdiKQwLf4r8E',
                  hidden: false,
                },
                {
                  templateId: 'fldLQ08DQnZ5QriSLT8KFgXe',
                  hidden: false,
                },
                {
                  templateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                  hidden: false,
                },
                {
                  templateId: 'fldZLUWsuiGwty4YmnAWADch',
                  hidden: false,
                },
                {
                  templateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                  hidden: false,
                },
                {
                  templateId: 'fldKOMikSA5v7Rclzv7wMtPu',
                  hidden: false,
                },
                {
                  templateId: 'fldQByn4MtGVyEkCEi5wcOHb',
                  hidden: false,
                },
                {
                  templateId: 'fldsbQ5tHOcPJYPpI2EhNNSk',
                  hidden: false,
                },
                {
                  templateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                  hidden: false,
                },
                {
                  templateId: 'fldPOFMl69aIbsHCS84AmIQh',
                  hidden: false,
                },
                {
                  templateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
                  hidden: true,
                },
                {
                  templateId: 'fldkdIS5JUYjLweREzM0h6ID',
                  hidden: false,
                },
              ],
              groups: [],
              extra: {
                kanbanGroupingFieldTemplateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                displayFieldName: true,
              },
            },
            {
              type: 'TABLE',
              templateId: 'viw53orgZ40TXvSdrCRZ60Gl',
              name: {
                en: 'My Pending Tickets',
                'zh-CN': '我的待处理',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
                conds: [
                  {
                    fieldTemplateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                    fieldType: 'MEMBER',
                    clause: {
                      operator: 'Is',
                      value: ['Self'],
                    },
                  },
                  {
                    fieldTemplateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                    fieldType: 'SINGLE_SELECT',
                    clause: {
                      operator: 'Contains',
                      value: ['optDKP1WEO52Z6dxcJR4w1oR', 'optD4p3BE0Naz358NjVjGgy5'],
                    },
                  },
                ],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fldhBIK7j01ZzQyXnK9OFYRm',
                  hidden: false,
                },
                {
                  templateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
                  hidden: false,
                },
                {
                  templateId: 'fldPIM8SlNUBi3D3PscnXPMv',
                  hidden: false,
                },
                {
                  templateId: 'fldZRslfymlbGdiKQwLf4r8E',
                  hidden: false,
                },
                {
                  templateId: 'fldLQ08DQnZ5QriSLT8KFgXe',
                  hidden: false,
                },
                {
                  templateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                  hidden: false,
                },
                {
                  templateId: 'fldZLUWsuiGwty4YmnAWADch',
                  hidden: false,
                },
                {
                  templateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                  hidden: false,
                },
                {
                  templateId: 'fldKOMikSA5v7Rclzv7wMtPu',
                  hidden: false,
                },
                {
                  templateId: 'fldQByn4MtGVyEkCEi5wcOHb',
                  hidden: false,
                },
                {
                  templateId: 'fldsbQ5tHOcPJYPpI2EhNNSk',
                  hidden: false,
                },
                {
                  templateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                  hidden: false,
                },
                {
                  templateId: 'fldPOFMl69aIbsHCS84AmIQh',
                  hidden: false,
                },
                {
                  templateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
                  hidden: false,
                },
                {
                  templateId: 'fldkdIS5JUYjLweREzM0h6ID',
                  hidden: false,
                },
              ],
              groups: [],
            },
            {
              type: 'TABLE',
              templateId: 'viwfLCCgWyh3M9uV8gGeGLcB',
              name: {
                en: 'My Pending Approvals',
                'zh-CN': '我的待审批',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
                conds: [
                  {
                    fieldTemplateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                    fieldType: 'MEMBER',
                    clause: {
                      operator: 'Is',
                      value: ['Self'],
                    },
                  },
                  {
                    fieldTemplateId: 'fldZLUWsuiGwty4YmnAWADch',
                    fieldType: 'SINGLE_SELECT',
                    clause: {
                      operator: 'IsEmpty',
                    },
                  },
                ],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fldhBIK7j01ZzQyXnK9OFYRm',
                  hidden: false,
                },
                {
                  templateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
                  hidden: false,
                },
                {
                  templateId: 'fldPIM8SlNUBi3D3PscnXPMv',
                  hidden: false,
                },
                {
                  templateId: 'fldZRslfymlbGdiKQwLf4r8E',
                  hidden: false,
                },
                {
                  templateId: 'fldLQ08DQnZ5QriSLT8KFgXe',
                  hidden: false,
                },
                {
                  templateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                  hidden: false,
                },
                {
                  templateId: 'fldZLUWsuiGwty4YmnAWADch',
                  hidden: false,
                },
                {
                  templateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                  hidden: false,
                },
                {
                  templateId: 'fldKOMikSA5v7Rclzv7wMtPu',
                  hidden: false,
                },
                {
                  templateId: 'fldQByn4MtGVyEkCEi5wcOHb',
                  hidden: false,
                },
                {
                  templateId: 'fldsbQ5tHOcPJYPpI2EhNNSk',
                  hidden: false,
                },
                {
                  templateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                  hidden: false,
                },
                {
                  templateId: 'fldPOFMl69aIbsHCS84AmIQh',
                  hidden: false,
                },
                {
                  templateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
                  hidden: false,
                },
                {
                  templateId: 'fldkdIS5JUYjLweREzM0h6ID',
                  hidden: false,
                },
              ],
              groups: [],
            },
            {
              type: 'TABLE',
              templateId: 'viwM1B6jqta4Gm3hFduHmIQt',
              name: {
                en: 'Form View',
                'zh-CN': '表单视图',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fldhBIK7j01ZzQyXnK9OFYRm',
                  hidden: false,
                },
                {
                  templateId: 'fldPOFMl69aIbsHCS84AmIQh',
                  hidden: false,
                },
                {
                  templateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
                  hidden: false,
                },
                {
                  templateId: 'fldPIM8SlNUBi3D3PscnXPMv',
                  hidden: false,
                },
                {
                  templateId: 'fldZRslfymlbGdiKQwLf4r8E',
                  hidden: false,
                },
                {
                  templateId: 'fldLQ08DQnZ5QriSLT8KFgXe',
                  hidden: false,
                },
                {
                  templateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
                  hidden: false,
                },
                {
                  templateId: 'fldxYBqk8NeW60dAMGJDQfIK',
                  hidden: true,
                },
                {
                  templateId: 'fldKOMikSA5v7Rclzv7wMtPu',
                  hidden: true,
                },
                {
                  templateId: 'fldQByn4MtGVyEkCEi5wcOHb',
                  hidden: true,
                },
                {
                  templateId: 'fldsbQ5tHOcPJYPpI2EhNNSk',
                  hidden: true,
                },
                {
                  templateId: 'fldtsE8ORBbVKv0nBuJi50Km',
                  hidden: true,
                },
                {
                  templateId: 'fldZLUWsuiGwty4YmnAWADch',
                  hidden: true,
                },
                {
                  templateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
                  hidden: true,
                },
                {
                  templateId: 'fldkdIS5JUYjLweREzM0h6ID',
                  hidden: true,
                },
              ],
              groups: [],
            },
          ],
          fields: [
            {
              type: 'DATETIME',
              templateId: 'fldhBIK7j01ZzQyXnK9OFYRm',
              privilege: 'TYPE_EDIT',
              name: {
                en: 'Submission Time',
                'zh-CN': '申请时间',
              },
              required: false,
              property: {
                dateFormat: 'YYYY-MM-DD',
                includeTime: false,
              },
              primary: true,
            },
            {
              type: 'SINGLE_SELECT',
              templateId: 'fldXSCoRZ8OFj7HwGtA5VJns',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Type',
                'zh-CN': '售后类型',
              },
              property: {
                options: [
                  {
                    id: 'opt2buqLSmfEG',
                    name: 'Return and Refund',
                    color: 'red5',
                  },
                  {
                    id: 'optSOZk78lc4H',
                    name: 'Exchange',
                    color: 'teal',
                  },
                  {
                    id: 'optW1DCByPh8G',
                    name: 'Cash Back',
                    color: 'green',
                  },
                  {
                    id: 'optLTOgnaOsLS',
                    name: 'Replenishment',
                    color: 'pink5',
                  },
                  {
                    id: 'optU8Gk15cfgm',
                    name: 'Refund',
                    color: 'deepPurple',
                  },
                ],
                defaultValue: '',
              },
              primary: false,
            },
            {
              type: 'CURRENCY',
              templateId: 'fldPIM8SlNUBi3D3PscnXPMv',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Amount',
                'zh-CN': '金额',
              },
              property: {
                precision: 2,
                commaStyle: 'thousand',
                symbol: '$',
                symbolAlign: 'left',
              },
              primary: false,
            },
            {
              type: 'SINGLE_SELECT',
              templateId: 'fldZRslfymlbGdiKQwLf4r8E',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Responsible Party',
                'zh-CN': '售后责任方',
              },
              property: {
                options: [
                  {
                    id: 'optJIYN97FopLkI1LKvFnF7F',
                    name: 'Warehouse',
                    color: 'deepPurple',
                  },
                  {
                    id: 'optTOIXme3DKFHgHPGyFgnAj',
                    name: 'Customer',
                    color: 'indigo',
                  },
                  {
                    id: 'opt9p7bWetS5UylTglYaiR3O',
                    name: 'Supplier',
                    color: 'blue',
                  },
                  {
                    id: 'optz7cx8LFNWaswbeQU6phxZ',
                    name: 'Logistics',
                    color: 'teal',
                  },
                  {
                    id: 'opt6FKHvi60Ph5xhUin5lhxO',
                    name: 'Merchant',
                    color: 'green',
                  },
                ],
                defaultValue: '',
              },
              primary: false,
            },
            {
              type: 'LONG_TEXT',
              templateId: 'fldLQ08DQnZ5QriSLT8KFgXe',
              privilege: 'NAME_EDIT',
              name: {
                en: 'After-sales Reason',
                'zh-CN': '售后原因',
              },
              primary: false,
            },
            {
              type: 'MEMBER',
              templateId: 'fldcdqoNZ2nkyYRV3QZnRNfs',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Supervisor',
                'zh-CN': '主管',
              },
              property: {},
              primary: false,
            },
            {
              type: 'SINGLE_SELECT',
              templateId: 'fldZLUWsuiGwty4YmnAWADch',
              privilege: 'NAME_EDIT',
              name: {
                en: "Supervisor's Approval Comments",
                'zh-CN': '主管审批意见',
              },
              property: {
                options: [
                  {
                    id: 'optB1hePJPrtbUOOYQiVj6Ss',
                    name: 'Approve',
                    color: 'green5',
                  },
                  {
                    id: 'opty7TCKDWa3a6WkxOMZ2VkP',
                    name: 'Reject',
                    color: 'red',
                  },
                ],
                defaultValue: '',
              },
              primary: false,
            },
            {
              type: 'MEMBER',
              templateId: 'fldxYBqk8NeW60dAMGJDQfIK',
              privilege: 'NAME_EDIT',
              name: {
                en: 'After-sales Handler',
                'zh-CN': '售后处理人',
              },
              property: {},
              primary: false,
            },
            {
              type: 'DATETIME',
              templateId: 'fldKOMikSA5v7Rclzv7wMtPu',
              privilege: 'NAME_EDIT',
              name: {
                en: 'After-sales Start Time',
                'zh-CN': '售后开始时间',
              },
              property: {
                dateFormat: 'YYYY-MM-DD',
                includeTime: true,
                autofill: true,
              },
              primary: false,
            },
            {
              type: 'DATETIME',
              templateId: 'fldQByn4MtGVyEkCEi5wcOHb',
              privilege: 'NAME_EDIT',
              name: {
                en: 'After-sales Completion Time',
                'zh-CN': '售后完成时间',
              },
              property: {
                dateFormat: 'YYYY-MM-DD',
                includeTime: true,
              },
              primary: false,
            },
            {
              type: 'FORMULA',
              templateId: 'fldsbQ5tHOcPJYPpI2EhNNSk',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Processing Duration (Hours)',
                'zh-CN': '售后处理时长(小时)',
              },
              required: false,
              property: {
                expressionTemplate:
                  'round(DATETIME_DIFF({fldQByn4MtGVyEkCEi5wcOHb},{fldKOMikSA5v7Rclzv7wMtPu},"hours"))',
              },
              primary: false,
            },
            {
              type: 'SINGLE_SELECT',
              templateId: 'fldtsE8ORBbVKv0nBuJi50Km',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Status',
                'zh-CN': '处理状态',
              },
              property: {
                options: [
                  {
                    id: 'optRHUfYngfBMz1jHyWARV81',
                    name: 'Completed',
                    color: 'green5',
                  },
                  {
                    id: 'optDKP1WEO52Z6dxcJR4w1oR',
                    name: 'In Progress',
                    color: 'red',
                  },
                  {
                    id: 'optD4p3BE0Naz358NjVjGgy5',
                    name: 'Unprocessed',
                    color: 'deepPurple5',
                  },
                ],
                defaultValue: '',
              },
              primary: false,
            },
            {
              type: 'LINK',
              templateId: 'fldPOFMl69aIbsHCS84AmIQh',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Associated Customer',
                'zh-CN': '关联客户',
              },
              property: {
                foreignDatabaseTemplateId: 'datEt10lYn6LcwekUu8V1H5w',
                brotherFieldTemplateId: 'fldT88GpOPy883QwZg2mHEdL',
              },
              primary: false,
            },
            {
              type: 'FORMULA',
              templateId: 'fldj6MAxIwWUMWMdA3r6CU4E',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Timeout Determination',
                'zh-CN': '超时判断',
              },
              required: false,
              property: {
                expressionTemplate:
                  'if(and({fldtsE8ORBbVKv0nBuJi50Km}!="Completed",{fldsbQ5tHOcPJYPpI2EhNNSk}>24),"🔴Timeout",blank())',
              },
              primary: false,
            },
            {
              type: 'LOOKUP',
              templateId: 'fldkdIS5JUYjLweREzM0h6ID',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Contact Information',
                'zh-CN': '客户联系方式',
              },
              required: false,
              property: {
                relatedLinkFieldTemplateId: 'fldPOFMl69aIbsHCS84AmIQh',
                lookupTargetFieldTemplateId: 'fldsMyEB1bgPZON9rSTM20u4',
                lookupTargetFieldType: 'PHONE',
                dataType: 'STRING',
                lookUpLimit: 'ALL',
                rollUpType: 'VALUES',
              },
              primary: false,
            },
          ],
          records: [
            {
              templateId: 'recHxTuU99RGwJSHY8soW2ts',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-26T00:00:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe:
                  'The shipment was too late, the customer no longer needs it, and the system automatically refunds.',
                fldPIM8SlNUBi3D3PscnXPMv: 103,
                fldPOFMl69aIbsHCS84AmIQh: ['rec16mkcXisYFbVoLSJRrpdL'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['optW1DCByPh8G'],
                fldZLUWsuiGwty4YmnAWADch: ['optB1hePJPrtbUOOYQiVj6Ss'],
                fldZRslfymlbGdiKQwLf4r8E: ['optJIYN97FopLkI1LKvFnF7F'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['meb44LqJEPtK8dj7rPWs2H5r'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-25T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 144,
                fldtsE8ORBbVKv0nBuJi50Km: ['optD4p3BE0Naz358NjVjGgy5'],
                fldxYBqk8NeW60dAMGJDQfIK: ['mebOu4584YOsijJUE0rBFGyq'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-26 00:00',
                fldLQ08DQnZ5QriSLT8KFgXe:
                  'The shipment was too late, the customer no longer needs it, and the system automatically refunds.',
                fldPIM8SlNUBi3D3PscnXPMv: '$103',
                fldPOFMl69aIbsHCS84AmIQh: ['Fiona Moon'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Cash Back'],
                fldZLUWsuiGwty4YmnAWADch: ['通过'],
                fldZRslfymlbGdiKQwLf4r8E: ['Warehouse'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['Allen'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-25',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '144',
                fldtsE8ORBbVKv0nBuJi50Km: ['Unprocessed'],
                fldxYBqk8NeW60dAMGJDQfIK: ['Doris'],
              },
            },
            {
              templateId: 'recsauc3tmEGSI5kZuZXCuAU',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-27T00:00:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe: "The customer doesn't want it anymore, return and refund.",
                fldPIM8SlNUBi3D3PscnXPMv: 137,
                fldPOFMl69aIbsHCS84AmIQh: ['recixrU8s6Kn6NGAcLr7XYT0'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['opt2buqLSmfEG'],
                fldZLUWsuiGwty4YmnAWADch: ['opty7TCKDWa3a6WkxOMZ2VkP'],
                fldZRslfymlbGdiKQwLf4r8E: ['optTOIXme3DKFHgHPGyFgnAj'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['meb44LqJEPtK8dj7rPWs2H5r'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-26T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: null,
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 120,
                fldtsE8ORBbVKv0nBuJi50Km: ['optRHUfYngfBMz1jHyWARV81'],
                fldxYBqk8NeW60dAMGJDQfIK: ['mebOu4584YOsijJUE0rBFGyq'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-27 00:00',
                fldLQ08DQnZ5QriSLT8KFgXe: "The customer doesn't want it anymore, return and refund.",
                fldPIM8SlNUBi3D3PscnXPMv: '$137',
                fldPOFMl69aIbsHCS84AmIQh: ['Bella'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Return and Refund'],
                fldZLUWsuiGwty4YmnAWADch: ['驳回'],
                fldZRslfymlbGdiKQwLf4r8E: ['Customer'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['Allen'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-26',
                fldj6MAxIwWUMWMdA3r6CU4E: 'null',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '120',
                fldtsE8ORBbVKv0nBuJi50Km: ['Completed'],
                fldxYBqk8NeW60dAMGJDQfIK: ['Doris'],
              },
            },
            {
              templateId: 'recQnynTI2hoWZhmqn7seYCh',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-27T21:00:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe: 'A bag of beef jerky has expired.',
                fldPIM8SlNUBi3D3PscnXPMv: 67,
                fldPOFMl69aIbsHCS84AmIQh: ['recixrU8s6Kn6NGAcLr7XYT0'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['optSOZk78lc4H'],
                fldZLUWsuiGwty4YmnAWADch: ['optB1hePJPrtbUOOYQiVj6Ss'],
                fldZRslfymlbGdiKQwLf4r8E: ['opt9p7bWetS5UylTglYaiR3O'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['meb44LqJEPtK8dj7rPWs2H5r'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-26T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: null,
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 99,
                fldtsE8ORBbVKv0nBuJi50Km: ['optRHUfYngfBMz1jHyWARV81'],
                fldxYBqk8NeW60dAMGJDQfIK: ['meb2L8Nt9VTdy53bd0b9hhsg'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-27 21:00',
                fldLQ08DQnZ5QriSLT8KFgXe: 'A bag of beef jerky has expired.',
                fldPIM8SlNUBi3D3PscnXPMv: '$67',
                fldPOFMl69aIbsHCS84AmIQh: ['Bella'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Exchange'],
                fldZLUWsuiGwty4YmnAWADch: ['通过'],
                fldZRslfymlbGdiKQwLf4r8E: ['Supplier'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['Allen'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-26',
                fldj6MAxIwWUMWMdA3r6CU4E: 'null',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '99',
                fldtsE8ORBbVKv0nBuJi50Km: ['Completed'],
                fldxYBqk8NeW60dAMGJDQfIK: ['Kelvin'],
              },
            },
            {
              templateId: 'recSfROtp5dcjVUOHmds79a0',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-28T22:20:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Unable to query waybill information',
                fldPIM8SlNUBi3D3PscnXPMv: 33,
                fldPOFMl69aIbsHCS84AmIQh: ['reco5i4fLP8XTBviGC3un4hV'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['optLTOgnaOsLS'],
                fldZLUWsuiGwty4YmnAWADch: ['optB1hePJPrtbUOOYQiVj6Ss'],
                fldZRslfymlbGdiKQwLf4r8E: ['optz7cx8LFNWaswbeQU6phxZ'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['meb44LqJEPtK8dj7rPWs2H5r'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-28T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 74,
                fldtsE8ORBbVKv0nBuJi50Km: ['optDKP1WEO52Z6dxcJR4w1oR'],
                fldxYBqk8NeW60dAMGJDQfIK: ['meb2L8Nt9VTdy53bd0b9hhsg'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-28 22:20',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Unable to query waybill information',
                fldPIM8SlNUBi3D3PscnXPMv: '$33',
                fldPOFMl69aIbsHCS84AmIQh: ['DJ Wang'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Replenishment'],
                fldZLUWsuiGwty4YmnAWADch: ['通过'],
                fldZRslfymlbGdiKQwLf4r8E: ['Logistics'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['Allen'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-28',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '74',
                fldtsE8ORBbVKv0nBuJi50Km: ['In Progress'],
                fldxYBqk8NeW60dAMGJDQfIK: ['Kelvin'],
              },
            },
            {
              templateId: 'recL169KY42HBKMITuC4IPAE',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-29T22:00:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Customer refuses to receive',
                fldPIM8SlNUBi3D3PscnXPMv: 86,
                fldPOFMl69aIbsHCS84AmIQh: ['recStxYoSz3wBNQsdOy3XHWU'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['opt2buqLSmfEG'],
                fldZLUWsuiGwty4YmnAWADch: ['optB1hePJPrtbUOOYQiVj6Ss'],
                fldZRslfymlbGdiKQwLf4r8E: ['optTOIXme3DKFHgHPGyFgnAj'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['meb44LqJEPtK8dj7rPWs2H5r'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-28T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 50,
                fldtsE8ORBbVKv0nBuJi50Km: ['optDKP1WEO52Z6dxcJR4w1oR'],
                fldxYBqk8NeW60dAMGJDQfIK: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-29 22:00',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Customer refuses to receive',
                fldPIM8SlNUBi3D3PscnXPMv: '$86',
                fldPOFMl69aIbsHCS84AmIQh: ['Gabe River'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Return and Refund'],
                fldZLUWsuiGwty4YmnAWADch: ['通过'],
                fldZRslfymlbGdiKQwLf4r8E: ['Customer'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['Allen'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-28',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '50',
                fldtsE8ORBbVKv0nBuJi50Km: ['In Progress'],
                fldxYBqk8NeW60dAMGJDQfIK: ['tianlu'],
              },
            },
            {
              templateId: 'recHI1cp0AHqQgLGzJsRl4OZ',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-30T23:10:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Customers participate in the 100 off promotion for orders over 500.',
                fldPIM8SlNUBi3D3PscnXPMv: 100,
                fldPOFMl69aIbsHCS84AmIQh: ['reckwaxOonkIEqHu9GmLGfWj'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['optW1DCByPh8G'],
                fldZLUWsuiGwty4YmnAWADch: ['opty7TCKDWa3a6WkxOMZ2VkP'],
                fldZRslfymlbGdiKQwLf4r8E: ['opt6FKHvi60Ph5xhUin5lhxO'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['meb44LqJEPtK8dj7rPWs2H5r'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-30T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 25,
                fldtsE8ORBbVKv0nBuJi50Km: ['optDKP1WEO52Z6dxcJR4w1oR'],
                fldxYBqk8NeW60dAMGJDQfIK: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-30 23:10',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Customers participate in the 100 off promotion for orders over 500.',
                fldPIM8SlNUBi3D3PscnXPMv: '$100',
                fldPOFMl69aIbsHCS84AmIQh: ['Dana Snow'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Cash Back'],
                fldZLUWsuiGwty4YmnAWADch: ['驳回'],
                fldZRslfymlbGdiKQwLf4r8E: ['Merchant'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['Allen'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-30',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '25',
                fldtsE8ORBbVKv0nBuJi50Km: ['In Progress'],
                fldxYBqk8NeW60dAMGJDQfIK: ['tianlu'],
              },
            },
            {
              templateId: 'recfu5ZkJQBR2pomyUgtOfvv',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-31T23:00:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Warehouse sent the wrong goods',
                fldPIM8SlNUBi3D3PscnXPMv: 40,
                fldPOFMl69aIbsHCS84AmIQh: ['recTHT0ypvVj8x8ptDIXkHhO'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['opt2buqLSmfEG'],
                fldZLUWsuiGwty4YmnAWADch: ['optB1hePJPrtbUOOYQiVj6Ss'],
                fldZRslfymlbGdiKQwLf4r8E: ['optJIYN97FopLkI1LKvFnF7F'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['mebLEjuvm9b3itUY2DFh8P18'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-31T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: null,
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 1,
                fldtsE8ORBbVKv0nBuJi50Km: ['optRHUfYngfBMz1jHyWARV81'],
                fldxYBqk8NeW60dAMGJDQfIK: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-31 23:00',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Warehouse sent the wrong goods',
                fldPIM8SlNUBi3D3PscnXPMv: '$40',
                fldPOFMl69aIbsHCS84AmIQh: ['Beautiful Wang'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Return and Refund'],
                fldZLUWsuiGwty4YmnAWADch: ['通过'],
                fldZRslfymlbGdiKQwLf4r8E: ['Warehouse'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['Liam'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-31',
                fldj6MAxIwWUMWMdA3r6CU4E: 'null',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '1',
                fldtsE8ORBbVKv0nBuJi50Km: ['Completed'],
                fldxYBqk8NeW60dAMGJDQfIK: ['tianlu'],
              },
            },
            {
              templateId: 'recNcrvXIKhBKsVwwA34ExdU',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-31T23:00:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Customers participate in the 50 off promotion for orders over 300',
                fldPIM8SlNUBi3D3PscnXPMv: 50,
                fldPOFMl69aIbsHCS84AmIQh: ['recSDzKbTx7mZQOQMeYJNY8z'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['optW1DCByPh8G'],
                fldZLUWsuiGwty4YmnAWADch: ['optB1hePJPrtbUOOYQiVj6Ss'],
                fldZRslfymlbGdiKQwLf4r8E: ['opt6FKHvi60Ph5xhUin5lhxO'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['mebOu4584YOsijJUE0rBFGyq'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-31T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: null,
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 1,
                fldtsE8ORBbVKv0nBuJi50Km: ['optDKP1WEO52Z6dxcJR4w1oR'],
                fldxYBqk8NeW60dAMGJDQfIK: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-03-31 23:00',
                fldLQ08DQnZ5QriSLT8KFgXe: 'Customers participate in the 50 off promotion for orders over 300',
                fldPIM8SlNUBi3D3PscnXPMv: '$50',
                fldPOFMl69aIbsHCS84AmIQh: ['Alex Mercer'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-01 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Cash Back'],
                fldZLUWsuiGwty4YmnAWADch: ['通过'],
                fldZRslfymlbGdiKQwLf4r8E: ['Merchant'],
                fldcdqoNZ2nkyYRV3QZnRNfs: [' 杨婷婷'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-03-31',
                fldj6MAxIwWUMWMdA3r6CU4E: 'null',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '1',
                fldtsE8ORBbVKv0nBuJi50Km: ['In Progress'],
                fldxYBqk8NeW60dAMGJDQfIK: ['tianlu'],
              },
            },
            {
              templateId: 'recUIiayXpKf4glb2nxVYPNl',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-04-01T23:00:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe:
                  'There is a problem with the product quality, and the customer requests a refund.',
                fldPIM8SlNUBi3D3PscnXPMv: 96,
                fldPOFMl69aIbsHCS84AmIQh: ['rec7Jwkn97tLikvGHxN12z9G'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-02T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['optU8Gk15cfgm'],
                fldZLUWsuiGwty4YmnAWADch: ['optB1hePJPrtbUOOYQiVj6Ss'],
                fldZRslfymlbGdiKQwLf4r8E: ['opt9p7bWetS5UylTglYaiR3O'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['meb44LqJEPtK8dj7rPWs2H5r'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-04-02T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: null,
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 1,
                fldtsE8ORBbVKv0nBuJi50Km: ['optD4p3BE0Naz358NjVjGgy5'],
                fldxYBqk8NeW60dAMGJDQfIK: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2021-04-01 23:00',
                fldLQ08DQnZ5QriSLT8KFgXe:
                  'There is a problem with the product quality, and the customer requests a refund.',
                fldPIM8SlNUBi3D3PscnXPMv: '$96',
                fldPOFMl69aIbsHCS84AmIQh: ['Edward Grigg'],
                fldQByn4MtGVyEkCEi5wcOHb: '2021-04-02 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Refund'],
                fldZLUWsuiGwty4YmnAWADch: ['通过'],
                fldZRslfymlbGdiKQwLf4r8E: ['Supplier'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['全正和'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-04-02',
                fldj6MAxIwWUMWMdA3r6CU4E: 'null',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '1',
                fldtsE8ORBbVKv0nBuJi50Km: ['Unprocessed'],
                fldxYBqk8NeW60dAMGJDQfIK: ['tianlu'],
              },
            },
            {
              templateId: 'recdt9k8Tndd4emBujTUMZRK',
              data: {
                fldKOMikSA5v7Rclzv7wMtPu: '2024-04-29T18:00:00.000Z',
                fldLQ08DQnZ5QriSLT8KFgXe: 'One item was missed and the customer requested a refund',
                fldPIM8SlNUBi3D3PscnXPMv: 56,
                fldPOFMl69aIbsHCS84AmIQh: ['recUav1Q07mNwPACU2vw2O9c'],
                fldQByn4MtGVyEkCEi5wcOHb: '2024-05-01T00:00:00.000Z',
                fldXSCoRZ8OFj7HwGtA5VJns: ['optU8Gk15cfgm'],
                fldZLUWsuiGwty4YmnAWADch: ['optB1hePJPrtbUOOYQiVj6Ss'],
                fldZRslfymlbGdiKQwLf4r8E: ['optJIYN97FopLkI1LKvFnF7F'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['meb2L8Nt9VTdy53bd0b9hhsg'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-04-02T16:00:00.000Z',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: '+86-123456',
                fldsbQ5tHOcPJYPpI2EhNNSk: 30,
                fldtsE8ORBbVKv0nBuJi50Km: ['optD4p3BE0Naz358NjVjGgy5'],
                fldxYBqk8NeW60dAMGJDQfIK: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
              },
              values: {
                fldKOMikSA5v7Rclzv7wMtPu: '2024-04-29 18:00',
                fldLQ08DQnZ5QriSLT8KFgXe: 'One item was missed and the customer requested a refund',
                fldPIM8SlNUBi3D3PscnXPMv: '$56',
                fldPOFMl69aIbsHCS84AmIQh: ['Sunshine brat'],
                fldQByn4MtGVyEkCEi5wcOHb: '2024-05-01 00:00',
                fldXSCoRZ8OFj7HwGtA5VJns: ['Refund'],
                fldZLUWsuiGwty4YmnAWADch: ['通过'],
                fldZRslfymlbGdiKQwLf4r8E: ['Warehouse'],
                fldcdqoNZ2nkyYRV3QZnRNfs: ['Kelvin'],
                fldhBIK7j01ZzQyXnK9OFYRm: '2021-04-02',
                fldj6MAxIwWUMWMdA3r6CU4E: '🔴Timeout',
                fldkdIS5JUYjLweREzM0h6ID: ['+86-123456'],
                fldsbQ5tHOcPJYPpI2EhNNSk: '30',
                fldtsE8ORBbVKv0nBuJi50Km: ['Unprocessed'],
                fldxYBqk8NeW60dAMGJDQfIK: ['tianlu'],
              },
            },
          ],
        },
        {
          resourceType: 'DATABASE',
          templateId: 'datEt10lYn6LcwekUu8V1H5w',
          name: {
            en: 'Customer Information',
            'zh-CN': '客户信息',
          },
          databaseType: 'DATUM',
          views: [
            {
              type: 'TABLE',
              templateId: 'viwf2NgpqhQQSskyp8zXrUsW',
              name: {
                en: 'ALL',
                'zh-CN': '所有客户',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fld4ZmBuVXxwOaUf5b49zdPh',
                  hidden: false,
                  width: 169,
                },
                {
                  templateId: 'fldwWKZvSEAXTm56ix0w3amy',
                  hidden: false,
                  width: 150,
                },
                {
                  templateId: 'fldsMyEB1bgPZON9rSTM20u4',
                  hidden: false,
                  width: 198,
                },
                {
                  templateId: 'fldI9FoukC06OFQnoR8btlfV',
                  hidden: false,
                },
                {
                  templateId: 'fldQe027GyFj2nJMfPEeifWa',
                  hidden: false,
                  width: 177,
                },
                {
                  templateId: 'fld9JHrcDWsF0ckfBZ2R8PuZ',
                  hidden: false,
                  width: 175,
                },
                {
                  templateId: 'fldT88GpOPy883QwZg2mHEdL',
                  hidden: false,
                },
                {
                  templateId: 'fld8EJBt4TL25gfprRLyN0Wt',
                  hidden: false,
                },
                {
                  templateId: 'fldIJVPE5FLk7KL2ap18IGbE',
                  hidden: false,
                  width: 177,
                },
              ],
            },
            {
              type: 'TABLE',
              templateId: 'viwquyZEjVUJDeXr4OmqQOv7',
              name: {
                en: 'Customer Type',
                'zh-CN': '客户性质分组',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fld4ZmBuVXxwOaUf5b49zdPh',
                  hidden: false,
                },
                {
                  templateId: 'fldwWKZvSEAXTm56ix0w3amy',
                  hidden: false,
                },
                {
                  templateId: 'fldsMyEB1bgPZON9rSTM20u4',
                  hidden: false,
                },
                {
                  templateId: 'fldI9FoukC06OFQnoR8btlfV',
                  hidden: false,
                },
                {
                  templateId: 'fldQe027GyFj2nJMfPEeifWa',
                  hidden: false,
                },
                {
                  templateId: 'fld9JHrcDWsF0ckfBZ2R8PuZ',
                  hidden: false,
                },
                {
                  templateId: 'fldT88GpOPy883QwZg2mHEdL',
                  hidden: false,
                },
                {
                  templateId: 'fld8EJBt4TL25gfprRLyN0Wt',
                  hidden: false,
                },
                {
                  templateId: 'fldIJVPE5FLk7KL2ap18IGbE',
                  hidden: false,
                },
              ],
              groups: [
                {
                  fieldTemplateId: 'fldI9FoukC06OFQnoR8btlfV',
                  asc: true,
                },
              ],
            },
          ],
          fields: [
            {
              type: 'LONG_TEXT',
              templateId: 'fld4ZmBuVXxwOaUf5b49zdPh',
              privilege: 'TYPE_EDIT',
              name: {
                en: 'Buyer Name',
                'zh-CN': '买家会员名',
              },
              primary: true,
            },
            {
              type: 'SINGLE_SELECT',
              templateId: 'fldwWKZvSEAXTm56ix0w3amy',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Gender',
                'zh-CN': '性别',
              },
              property: {
                options: [
                  {
                    id: 'optH9DeOr6huatV1QO2P21lk',
                    name: 'Female',
                    color: 'deepPurple',
                  },
                  {
                    id: 'optckUjrHGg6VqEOYXSORvzq',
                    name: 'Male',
                    color: 'indigo',
                  },
                ],
                defaultValue: '',
              },
              primary: false,
            },
            {
              type: 'PHONE',
              templateId: 'fldsMyEB1bgPZON9rSTM20u4',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Phone',
                'zh-CN': '手机号',
              },
              primary: false,
            },
            {
              type: 'SINGLE_SELECT',
              templateId: 'fldI9FoukC06OFQnoR8btlfV',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Customer Type',
                'zh-CN': '客户性质',
              },
              property: {
                options: [
                  {
                    id: 'optaCYYz4bThfBcjzxwm38lB',
                    name: 'Repeat Customer',
                    color: 'deepPurple',
                  },
                  {
                    id: 'opt1DUVODmjdcvbaGxTbXqSS',
                    name: 'First Time',
                    color: 'indigo',
                  },
                ],
                defaultValue: '',
              },
              primary: false,
            },
            {
              type: 'DATETIME',
              templateId: 'fldQe027GyFj2nJMfPEeifWa',
              privilege: 'NAME_EDIT',
              name: {
                en: 'First Purchase',
                'zh-CN': '首次下单时间',
              },
              property: {
                dateFormat: 'YYYY-MM-DD',
                includeTime: false,
              },
              primary: false,
            },
            {
              type: 'DATETIME',
              templateId: 'fld9JHrcDWsF0ckfBZ2R8PuZ',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Last Purchase',
                'zh-CN': '最近下单时间',
              },
              property: {
                dateFormat: 'YYYY-MM-DD',
                includeTime: false,
              },
              primary: false,
            },
            {
              type: 'LINK',
              templateId: 'fldT88GpOPy883QwZg2mHEdL',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Service Record',
                'zh-CN': '售后记录',
              },
              property: {
                foreignDatabaseTemplateId: 'dat1Zw6dpnlYgfg0ElEPYBwx',
                brotherFieldTemplateId: 'fldPOFMl69aIbsHCS84AmIQh',
              },
              primary: false,
            },
            {
              type: 'FORMULA',
              templateId: 'fld8EJBt4TL25gfprRLyN0Wt',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Returns Count',
                'zh-CN': '退货次数',
              },
              required: false,
              property: {
                expressionTemplate: 'COUNTA({fldT88GpOPy883QwZg2mHEdL})',
              },
              primary: false,
            },
            {
              type: 'LOOKUP',
              templateId: 'fldIJVPE5FLk7KL2ap18IGbE',
              privilege: 'NAME_EDIT',
              name: {
                en: 'Total Amount',
                'zh-CN': '消费总金额',
              },
              required: false,
              property: {
                relatedLinkFieldTemplateId: 'fldT88GpOPy883QwZg2mHEdL',
                lookupTargetFieldTemplateId: 'fldPIM8SlNUBi3D3PscnXPMv',
                lookupTargetFieldType: 'CURRENCY',
                dataType: 'NUMBER',
                lookUpLimit: 'ALL',
                rollUpType: 'SUM',
                formatting: {
                  type: 'CURRENCY',
                  property: {},
                },
              },
              primary: false,
            },
          ],
          records: [
            {
              templateId: 'rec16mkcXisYFbVoLSJRrpdL',
              data: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Fiona Moon',
                fld8EJBt4TL25gfprRLyN0Wt: 1,
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2021-02-24T00:00:00.000Z',
                fldI9FoukC06OFQnoR8btlfV: ['optaCYYz4bThfBcjzxwm38lB'],
                fldIJVPE5FLk7KL2ap18IGbE: 103,
                fldQe027GyFj2nJMfPEeifWa: '2020-06-01T00:00:00.000Z',
                fldT88GpOPy883QwZg2mHEdL: ['recHxTuU99RGwJSHY8soW2ts'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['optH9DeOr6huatV1QO2P21lk'],
              },
              values: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Fiona Moon',
                fld8EJBt4TL25gfprRLyN0Wt: '1',
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2021-02-24',
                fldI9FoukC06OFQnoR8btlfV: ['Repeat Customer'],
                fldIJVPE5FLk7KL2ap18IGbE: ['103'],
                fldQe027GyFj2nJMfPEeifWa: '2020-06-01',
                fldT88GpOPy883QwZg2mHEdL: ['2021-03-25'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['Female'],
              },
            },
            {
              templateId: 'recixrU8s6Kn6NGAcLr7XYT0',
              data: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Bella',
                fld8EJBt4TL25gfprRLyN0Wt: 2,
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-11-11T00:00:00.000Z',
                fldI9FoukC06OFQnoR8btlfV: ['optaCYYz4bThfBcjzxwm38lB'],
                fldIJVPE5FLk7KL2ap18IGbE: 204,
                fldQe027GyFj2nJMfPEeifWa: '2020-05-11T00:00:00.000Z',
                fldT88GpOPy883QwZg2mHEdL: ['recsauc3tmEGSI5kZuZXCuAU', 'recQnynTI2hoWZhmqn7seYCh'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['optH9DeOr6huatV1QO2P21lk'],
              },
              values: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Bella',
                fld8EJBt4TL25gfprRLyN0Wt: '2',
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-11-11',
                fldI9FoukC06OFQnoR8btlfV: ['Repeat Customer'],
                fldIJVPE5FLk7KL2ap18IGbE: ['204'],
                fldQe027GyFj2nJMfPEeifWa: '2020-05-11',
                fldT88GpOPy883QwZg2mHEdL: ['2021-03-26', '2021-03-26'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['Female'],
              },
            },
            {
              templateId: 'reco5i4fLP8XTBviGC3un4hV',
              data: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'DJ Wang',
                fld8EJBt4TL25gfprRLyN0Wt: 1,
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-12-15T00:00:00.000Z',
                fldI9FoukC06OFQnoR8btlfV: ['opt1DUVODmjdcvbaGxTbXqSS'],
                fldIJVPE5FLk7KL2ap18IGbE: 33,
                fldQe027GyFj2nJMfPEeifWa: '2020-12-15T00:00:00.000Z',
                fldT88GpOPy883QwZg2mHEdL: ['recSfROtp5dcjVUOHmds79a0'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['optckUjrHGg6VqEOYXSORvzq'],
              },
              values: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'DJ Wang',
                fld8EJBt4TL25gfprRLyN0Wt: '1',
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-12-15',
                fldI9FoukC06OFQnoR8btlfV: ['First Time'],
                fldIJVPE5FLk7KL2ap18IGbE: ['33'],
                fldQe027GyFj2nJMfPEeifWa: '2020-12-15',
                fldT88GpOPy883QwZg2mHEdL: ['2021-03-28'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['Male'],
              },
            },
            {
              templateId: 'recStxYoSz3wBNQsdOy3XHWU',
              data: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Gabe River',
                fld8EJBt4TL25gfprRLyN0Wt: 1,
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-11-17T00:00:00.000Z',
                fldI9FoukC06OFQnoR8btlfV: ['opt1DUVODmjdcvbaGxTbXqSS'],
                fldIJVPE5FLk7KL2ap18IGbE: 86,
                fldQe027GyFj2nJMfPEeifWa: '2020-11-17T00:00:00.000Z',
                fldT88GpOPy883QwZg2mHEdL: ['recL169KY42HBKMITuC4IPAE'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['optckUjrHGg6VqEOYXSORvzq'],
              },
              values: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Gabe River',
                fld8EJBt4TL25gfprRLyN0Wt: '1',
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-11-17',
                fldI9FoukC06OFQnoR8btlfV: ['First Time'],
                fldIJVPE5FLk7KL2ap18IGbE: ['86'],
                fldQe027GyFj2nJMfPEeifWa: '2020-11-17',
                fldT88GpOPy883QwZg2mHEdL: ['2021-03-28'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['Male'],
              },
            },
            {
              templateId: 'reckwaxOonkIEqHu9GmLGfWj',
              data: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Dana Snow',
                fld8EJBt4TL25gfprRLyN0Wt: 1,
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2021-01-15T00:00:00.000Z',
                fldI9FoukC06OFQnoR8btlfV: ['optaCYYz4bThfBcjzxwm38lB'],
                fldIJVPE5FLk7KL2ap18IGbE: 100,
                fldQe027GyFj2nJMfPEeifWa: '2020-04-18T00:00:00.000Z',
                fldT88GpOPy883QwZg2mHEdL: ['recHI1cp0AHqQgLGzJsRl4OZ'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['optH9DeOr6huatV1QO2P21lk'],
              },
              values: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Dana Snow',
                fld8EJBt4TL25gfprRLyN0Wt: '1',
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2021-01-15',
                fldI9FoukC06OFQnoR8btlfV: ['Repeat Customer'],
                fldIJVPE5FLk7KL2ap18IGbE: ['100'],
                fldQe027GyFj2nJMfPEeifWa: '2020-04-18',
                fldT88GpOPy883QwZg2mHEdL: ['2021-03-30'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['Female'],
              },
            },
            {
              templateId: 'recTHT0ypvVj8x8ptDIXkHhO',
              data: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Beautiful Wang',
                fld8EJBt4TL25gfprRLyN0Wt: 1,
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2021-02-18T00:00:00.000Z',
                fldI9FoukC06OFQnoR8btlfV: ['optaCYYz4bThfBcjzxwm38lB'],
                fldIJVPE5FLk7KL2ap18IGbE: 40,
                fldQe027GyFj2nJMfPEeifWa: '2020-02-18T00:00:00.000Z',
                fldT88GpOPy883QwZg2mHEdL: ['recfu5ZkJQBR2pomyUgtOfvv'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['optH9DeOr6huatV1QO2P21lk'],
              },
              values: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Beautiful Wang',
                fld8EJBt4TL25gfprRLyN0Wt: '1',
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2021-02-18',
                fldI9FoukC06OFQnoR8btlfV: ['Repeat Customer'],
                fldIJVPE5FLk7KL2ap18IGbE: ['40'],
                fldQe027GyFj2nJMfPEeifWa: '2020-02-18',
                fldT88GpOPy883QwZg2mHEdL: ['2021-03-31'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['Female'],
              },
            },
            {
              templateId: 'recSDzKbTx7mZQOQMeYJNY8z',
              data: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Alex Mercer',
                fld8EJBt4TL25gfprRLyN0Wt: 1,
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2021-04-06T00:00:00.000Z',
                fldI9FoukC06OFQnoR8btlfV: ['optaCYYz4bThfBcjzxwm38lB'],
                fldIJVPE5FLk7KL2ap18IGbE: 50,
                fldQe027GyFj2nJMfPEeifWa: '2020-02-09T00:00:00.000Z',
                fldT88GpOPy883QwZg2mHEdL: ['recNcrvXIKhBKsVwwA34ExdU'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['optH9DeOr6huatV1QO2P21lk'],
              },
              values: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Alex Mercer',
                fld8EJBt4TL25gfprRLyN0Wt: '1',
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2021-04-06',
                fldI9FoukC06OFQnoR8btlfV: ['Repeat Customer'],
                fldIJVPE5FLk7KL2ap18IGbE: ['50'],
                fldQe027GyFj2nJMfPEeifWa: '2020-02-09',
                fldT88GpOPy883QwZg2mHEdL: ['2021-03-31'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['Female'],
              },
            },
            {
              templateId: 'rec7Jwkn97tLikvGHxN12z9G',
              data: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Edward Grigg',
                fld8EJBt4TL25gfprRLyN0Wt: 1,
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-12-15T00:00:00.000Z',
                fldI9FoukC06OFQnoR8btlfV: ['opt1DUVODmjdcvbaGxTbXqSS'],
                fldIJVPE5FLk7KL2ap18IGbE: 96,
                fldQe027GyFj2nJMfPEeifWa: '2020-12-15T00:00:00.000Z',
                fldT88GpOPy883QwZg2mHEdL: ['recUIiayXpKf4glb2nxVYPNl'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['optckUjrHGg6VqEOYXSORvzq'],
              },
              values: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Edward Grigg',
                fld8EJBt4TL25gfprRLyN0Wt: '1',
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-12-15',
                fldI9FoukC06OFQnoR8btlfV: ['First Time'],
                fldIJVPE5FLk7KL2ap18IGbE: ['96'],
                fldQe027GyFj2nJMfPEeifWa: '2020-12-15',
                fldT88GpOPy883QwZg2mHEdL: ['2021-04-02'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['Male'],
              },
            },
            {
              templateId: 'recUav1Q07mNwPACU2vw2O9c',
              data: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Sunshine brat',
                fld8EJBt4TL25gfprRLyN0Wt: 1,
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-11-10T00:00:00.000Z',
                fldI9FoukC06OFQnoR8btlfV: ['opt1DUVODmjdcvbaGxTbXqSS'],
                fldIJVPE5FLk7KL2ap18IGbE: 56,
                fldQe027GyFj2nJMfPEeifWa: '2020-11-10T00:00:00.000Z',
                fldT88GpOPy883QwZg2mHEdL: ['recdt9k8Tndd4emBujTUMZRK'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['optckUjrHGg6VqEOYXSORvzq'],
              },
              values: {
                fld4ZmBuVXxwOaUf5b49zdPh: 'Sunshine brat',
                fld8EJBt4TL25gfprRLyN0Wt: '1',
                fld9JHrcDWsF0ckfBZ2R8PuZ: '2020-11-10',
                fldI9FoukC06OFQnoR8btlfV: ['First Time'],
                fldIJVPE5FLk7KL2ap18IGbE: ['56'],
                fldQe027GyFj2nJMfPEeifWa: '2020-11-10',
                fldT88GpOPy883QwZg2mHEdL: ['2021-04-02'],
                fldsMyEB1bgPZON9rSTM20u4: '+86-123456',
                fldwWKZvSEAXTm56ix0w3amy: ['Male'],
              },
            },
          ],
        },
        {
          resourceType: 'DATABASE',
          templateId: 'datGiErEs6O6eVWkRsdapJhP',
          name: {
            en: 'After-Sales Duty Personnel',
            'zh-CN': '售后值班人员',
          },
          databaseType: 'DATUM',
          views: [
            {
              type: 'TABLE',
              templateId: 'viwgSl6YmvY9RCqDA46YHs0j',
              name: {
                en: 'All',
                'zh-CN': '所有',
              },
              filters: {
                conjunction: 'And',
                conditions: [],
              },
              sorts: [],
              fields: [
                {
                  templateId: 'fldiuYimyqBCvLvxx7RbQIoL',
                  hidden: false,
                  width: 126,
                },
                {
                  templateId: 'fldsxil2SxdTnVkbCz3iPXJ3',
                  hidden: false,
                },
              ],
            },
          ],
          fields: [
            {
              type: 'AUTO_NUMBER',
              templateId: 'fldiuYimyqBCvLvxx7RbQIoL',
              privilege: 'TYPE_EDIT',
              name: {
                en: 'ID',
                'zh-CN': '编号',
              },
              required: false,
              property: {
                nextId: 6,
              },
              primary: true,
            },
            {
              type: 'MEMBER',
              templateId: 'fldsxil2SxdTnVkbCz3iPXJ3',
              privilege: 'FULL_EDIT',
              name: '值班人员',
              property: {},
              primary: false,
            },
          ],
          records: [
            {
              templateId: 'recHVC4vanLAZsUfTRsLirVa',
              data: {
                fldiuYimyqBCvLvxx7RbQIoL: 2,
                fldsxil2SxdTnVkbCz3iPXJ3: ['mebKbYM1QqrLrM5y4R5HgPMJ'],
              },
              values: {
                fldiuYimyqBCvLvxx7RbQIoL: '2',
                fldsxil2SxdTnVkbCz3iPXJ3: ['tianlu'],
              },
            },
            {
              templateId: 'recJVtTDheYeLYIw72sloIw2',
              data: {
                fldiuYimyqBCvLvxx7RbQIoL: 3,
                fldsxil2SxdTnVkbCz3iPXJ3: ['meb2L8Nt9VTdy53bd0b9hhsg'],
              },
              values: {
                fldiuYimyqBCvLvxx7RbQIoL: '3',
                fldsxil2SxdTnVkbCz3iPXJ3: ['Kelvin'],
              },
            },
            {
              templateId: 'recqhDJ3i8Xsad1lcE6uj85J',
              data: {
                fldiuYimyqBCvLvxx7RbQIoL: 4,
                fldsxil2SxdTnVkbCz3iPXJ3: ['mebYTyD52sAMtTtcdxum1ixz'],
              },
              values: {
                fldiuYimyqBCvLvxx7RbQIoL: '4',
                fldsxil2SxdTnVkbCz3iPXJ3: ['Leonchow'],
              },
            },
            {
              templateId: 'recN0M1pWSYI5vbQuVYeiL1V',
              data: {
                fldiuYimyqBCvLvxx7RbQIoL: 5,
                fldsxil2SxdTnVkbCz3iPXJ3: ['mebWu72dzRLjATy3RLr3YJes'],
              },
              values: {
                fldiuYimyqBCvLvxx7RbQIoL: '5',
                fldsxil2SxdTnVkbCz3iPXJ3: ['casper'],
              },
            },
            {
              templateId: 'recCP4tXsQWEraDy7cENuCh0',
              data: {
                fldiuYimyqBCvLvxx7RbQIoL: 6,
                fldsxil2SxdTnVkbCz3iPXJ3: ['mebLEjuvm9b3itUY2DFh8P18'],
              },
              values: {
                fldiuYimyqBCvLvxx7RbQIoL: '6',
                fldsxil2SxdTnVkbCz3iPXJ3: ['Liam'],
              },
            },
          ],
        },
      ],
      cover: {
        type: 'UNSPLASH',
        url: 'https://images.unsplash.com/photo-*************-ce68d2c6f44d?q=80&w=2072&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      },
      readme: {
        en:
          '### 📍 How to Use?\n' +
          'The table includes:\n' +
          '- After-sales Service: Records customer ticket information to ensure traceability of after-sales services.\n' +
          '- Customer Information: Manages customer accounts, contact details, and other information.\n' +
          '- After-sales Duty Personnel: Personnel arranged to handle work orders.\n' +
          '\n' +
          '**Tips**: Backend files can be set to be managed only by relevant administrators. ',
        'zh-CN':
          '### 📍如何使用？\n' +
          '数据表包含：\n' +
          '- 售后服务：记录客户工单信息，确保售后有迹可循。\n' +
          '- 客户信息：管理客户账号、联系方式等信息。\n' +
          '- 售后值班人员：安排工单处理的人员。 \n' +
          '\n' +
          '后台文件可设置为仅相关管理员可管理。\n',
      },
    },
  ],
  initMissions: [],
};

export default template;
