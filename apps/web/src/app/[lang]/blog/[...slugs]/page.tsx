import { getServerDictionary } from '@bika/contents/i18n/server';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { ArticleContent } from '@bika/domains/website/client/article';
import { AIGCSO } from '@bika/domains/website/server/aigc/aigc-so';
import { generateTocAndModifyHtml } from '@bika/domains/website/server/utils';
import { LocalContentLoader } from '@bika/server-orm';
import type { Locale } from '@bika/types/i18n/bo';
import type { TemplateCardInfoVO } from '@bika/types/template/vo';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import React from 'react';
import style from './index.module.css';

interface RouteParams {
  slugs: string[];
  lang: string;
}

export interface PageProps {
  params: Promise<RouteParams>;
}

/**
 * /en/blog/template这个域下的，都是读取bika-content工程的
 *
 * @param params
 * @returns
 */
// async function isAigcBlog(lang: string, slugs: string[]) {
//   const slug1 = slugs[0];
//   if (slug1 === 'template') {
//     return true;
//   }
//   return false;
// }

async function getBlog(lang: string, slugs: string[]) {
  try {
    const result = await LocalContentLoader.blog.autoBlog(lang, slugs);
    if (!result) {
      throw new Error('Blog not found');
    }
    return result;
  } catch (e) {
    const result = await AIGCSO.getAigcBlog(lang, decodeURIComponent(slugs.join('/')));
    return result;
  }
}

export default async function Detail(params: PageProps) {
  try {
    const { slugs: slug, lang } = await params.params;

    // if (isAigcBlog(params)) {
    //   const aigcBlog = await AIGCSO.getAigcBlog(lang, slug.join('/'));
    //   return <>{aigcBlog}</>;
    // }

    let frontMatter: Record<string, string>;
    let content: string;

    if (slug[0] === 'news') {
      const result = await AIGCSO.getNewsFromPath(lang, slug[1]);
      frontMatter = result.meta;
      content = result.content;
    } else {
      const result = await getBlog(lang, slug);
      frontMatter = result.meta;
      content = result.content;
    }

    const { tree, modifiedHtml } = generateTocAndModifyHtml(content);

    const randomTemplatesSOs = await StoreTemplateSO.getRandomTemplates();
    const templateVOs: TemplateCardInfoVO[] = [];
    for (const tplSO of randomTemplatesSOs) {
      const tplVO = await tplSO.toInfoVO(lang as Locale);
      templateVOs.push(tplVO);
    }
    const relatedLinks = await AIGCSO.getRandomBlogsLinks(lang as Locale);

    return (
      <div className={style.blog}>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'Bika.ai',
              url: process.env.APP_HOSTNAME,
              logo: `${process.env.APP_HOSTNAME}/assets/icons/logo/bika-logo-icon.png`,
            }),
          }}
        />
        <ArticleContent
          relatedLinks={relatedLinks}
          locale={lang as Locale}
          meta={{
            title: frontMatter.title,
            date: frontMatter.date as unknown as Date,
            author: frontMatter.author,
            cover: frontMatter.cover,
          }}
          html={modifiedHtml}
          toc={tree}
          templateInfos={templateVOs}
        />
      </div>
    );
  } catch (e) {
    return notFound();
  }
}

export async function generateMetadata(params: PageProps): Promise<Metadata> {
  try {
    const { slugs: slug, lang } = await params.params;
    const dict = getServerDictionary(lang as Locale);
    const { meta: frontMatter } = await getBlog(lang, slug);
    return {
      title: frontMatter.title,
      description: frontMatter.description || dict.slogan.slogan_prd_xl,
      keywords: frontMatter.keywords || dict.slogan.keywords,
      openGraph: {
        title: frontMatter.title,
        type: 'article',
        description: frontMatter.description,
        images: [{ url: frontMatter.cover }],
      },
      twitter: {
        card: 'summary_large_image',
        site: '@bika_ai',
        title: frontMatter.title,
        description: frontMatter.description,
        images: frontMatter.cover,
        creator: '@bika_ai',
      },
    };

    // const { meta: frontMatter } =
    //   slug[0] === 'news'
    //     ? await AIGCSO.getNewsFromPath(lang, slug[1])
    //     : await LocalContentLoader.blog.autoBlog(lang, slug);
    // return {
    //   title: frontMatter.title,
    //   description: frontMatter.description,
    //   keywords: frontMatter.keywords,
    //   openGraph: {
    //     title: frontMatter.title,
    //     type: 'article',
    //     description: frontMatter.description,
    //     images: [{ url: frontMatter.cover }],
    //   },
    //   twitter: {
    //     card: 'summary_large_image',
    //     site: '@bika_ai',
    //     title: frontMatter.title,
    //     description: frontMatter.description,
    //     images: frontMatter.cover,
    //     creator: '@bika_ai',
    //   },
    //   alternates: {
    //     languages: await generateLanguageUrls(`blog/${slug.join('/')}`, 'en', async (locale) => {
    //       try {
    //         const ret =
    //           slug[0] === 'news'
    //             ? await AIGCSO.getNewsFromPath(locale, slug[1])
    //             : await LocalContentLoader.blog.autoBlog(locale, slug);
    //         return true;
    //       } catch (e) {
    //         return false;
    //       }
    //     }),
    //   },
    // };
  } catch (e) {
    return notFound();
  }
}
