{"templateId": "ai-automated-ticket-system", "name": {"en": "AI Project Issues and Tickets", "ja": "AI自動化チケットリクエストレポート", "zh-CN": "AI 自动化工单和BUG管理", "zh-TW": "AI 自動化工單和BUG管理"}, "description": {"en": "Use AI automation to manage your product and project work orders, requirements, and bugs. By automatically collecting, summarizing, and prompting actions, you can manage project progress more efficiently and provide timely feedback to users on development progress", "ja": "AI 自動化を使用してプロジェクトのチケット、要件、バグを管理します。自動収集、集計、および促進を通じて、プロジェクトの進行をより効果的に管理し、ユーザーに開発の進捗状況をタイムリーにフィードバックできます", "zh-CN": "使用 AI 自动化来管理您的产品和项目工单、需求和 BUG。通过自动收集、汇总和催促处理，您可以更有效地管理项目进度，并及时向用户反馈开发进展。", "zh-TW": "使用 AI 自動化管理您的項目工單、需求和 BUG。通過自動收集、彙總和催促處理，您可以更有效地管理項目進度，並及時向用戶反饋開發進展"}, "keywords": {"en": "AI automation, Ticket management, Bug tracking, Requirement handling, Project progress, Feedback system", "ja": "AI自動化, チケット管理, バグ追跡, 要件処理, プロジェクト進捗, フィードバックシステム", "zh-TW": "AI自動化, 工單管理, BUG追踪, 需求處理, 專案進度, 反饋系統", "zh-CN": "AI自动化, 工单管理, BUG追踪, 需求处理, 项目进度, 反馈系统"}, "personas": {"en": "Project Manager, <PERSON><PERSON><PERSON>, QA Engineer, Product Owner, Customer Support, Business Analyst", "zh-CN": "项目经理, 开发人员, 测试工程师, 产品负责人, 客户支持, 业务分析师", "zh-TW": "項目經理, 開發人員, 測試工程師, 產品負責人, 客戶支持, 業務分析師", "ja": "プロジェクトマネージャー, 開発者, QAエンジニア, プロダクトオーナー, カスタマーサポート, ビジネスアナリスト"}, "useCases": {"en": "Submitting bug reports, Requesting new features, Tracking ticket status, Assigning tickets, Sending progress updates, Automated reminders, Creating test cases, Verifying bug fixes, Conducting regression tests, Providing QA feedback, Ensuring product quality, Developer-QA collaboration, Collecting requirements, Prioritizing features, Managing backlog, Integrating customer feedback, Planning sprints, Monitoring development, Logging customer issues, Escalating critical tickets, Analyzing feedback, Communicating status updates, Tracking user satisfaction, Generating reports, Monitoring metrics, Prioritizing tasks, Allocating resources, Tracking milestones, Reporting progress, Preparing documentation, Identifying improvements, Facilitating collaboration, Tracking project progress, Resolving issues", "zh-CN": "提交Bug报告, 请求新功能, 跟踪工单状态, 分配工单, 发送进度更新, 自动提醒, 创建测试用例, 验证Bug修复, 进行回归测试, 提供QA反馈, 确保产品质量, 开发与QA协作, 收集需求, 优先处理功能, 管理待办事项, 整合客户反馈, 规划冲刺, 监控开发, 记录客户问题, 升级关键工单, 分析反馈, 沟通状态更新, 跟踪用户满意度, 生成报告, 监控指标, 优先处理任务, 分配资源, 跟踪里程碑, 报告进度, 准备文档, 识别改进, 促进协作, 跟踪项目进度, 解决问题", "zh-TW": "提交Bug報告, 請求新功能, 跟蹤工單狀態, 分配工單, 發送進度更新, 自動提醒, 創建測試用例, 驗證Bug修復, 進行回歸測試, 提供QA反饋, 確保產品質量, 開發與QA協作, 收集需求, 優先處理功能, 管理待辦事項, 整合客戶反饋, 規劃衝刺, 監控開發, 記錄客戶問題, 升級關鍵工單, 分析反饋, 溝通狀態更新, 跟蹤用戶滿意度, 生成報告, 監控指標, 優先處理任務, 分配資源, 跟蹤里程碑, 報告進度, 準備文檔, 識別改進, 促進協作, 跟蹤項目進度, 解決問題", "ja": "バグ報告の提出, 新機能のリクエスト, チケットのステータス追跡, チケットの割り当て, 進捗状況の更新送信, 自動リマインダー, テストケースの作成, バグ修正の確認, リグレッションテストの実施, QAフィードバックの提供, 製品品質の確保, 開発とQAの協力, 要件の収集, 機能の優先順位付け, バックログの管理, 顧客フィードバックの統合, スプリントの計画, 開発の監視, 顧客問題の記録, 重要チケットのエスカレーション, フィードバックの分析, ステータス更新のコミュニケーション, ユーザー満足度の追跡, レポートの生成, 指標の監視, タスクの優先順位付け, リソースの割り当て, マイルストーンの追跡, 進捗状況の報告, 文書の準備, 改善点の特定, 協力の促進, プロジェクト進捗の追跡, 問題の解決"}, "cover": "/assets/template/template-cover-ai-automated-ticket-system.png", "author": "pengjin <<EMAIL>>", "category": ["project"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "0.2.11", "resources": [{"resourceType": "FORM", "templateId": "fomGXQXnT0B9x8uelV8eZOwF", "name": {"en": "🌟Bug Report & Feature Request ticket", "ja": "🌟エラーレポート＆機能リクエスト", "zh-CN": "🌟 错误报告 & 功能请求", "zh-TW": "🌟錯誤報告 & 功能請求"}, "description": {"en": "For feature 🌟 suggestions or error 🐞 reports, please fill out this form. We will prioritize your request, resolve it promptly, and provide timely updates at each stage 💪", "zh-CN": "对于功能🌟建议或错误🐞报告，请填写此表格。我们将优先处理您的请求，迅速解决，并在每个阶段及时向您更新进展💪"}, "brandLogo": {"type": "ATTACHMENT", "attachmentId": "tplattd389Bsc56iNVPWlWpF1x5", "relativePath": "template/tplattd389Bsc56iNVPWlWpF1x5.png"}, "formType": "DATABASE", "databaseTemplateId": "datrGBZV5xQlu3jaucLbsbW0", "metadata": {"type": "VIEW", "viewTemplateId": "viwHexOqsrHFlvQf3FQQhK2U"}}, {"resourceType": "DATABASE", "templateId": "datrGBZV5xQlu3jaucLbsbW0", "name": {"en": "Requirement and Bug <PERSON>", "ja": "要件とBUGフィードバック表", "zh-CN": "需求与BUG反馈表", "zh-TW": "需求與BUG反饋表"}, "description": {"en": "All submitted tickets will be displayed in the \"Request and Bug Feedback\" table. The \"All Tickets\" view shows all ticket data, the \"Complaints View\" is used for the creation of \"Error Report & Feature Request\" forms, the \"Total Number of Tickets Submitted This Week\" is used to count all tickets submitted this week, the \"Request View\" is used to count all request-type tickets, and the \"Bug Tickets\" view is used to count all bug-type tickets", "ja": "The submitted tickets will be displayed in the \"Requirements and BUG Feedback\" table. Among them, the \"All Tickets\" view shows all ticket data, the \"Complaints View\" is used for creating \"Error Report & Feature Request\" forms, the \"Total Number of Tickets Created This Week\" is used to count all tickets submitted this week, the \"Requirement View\" is used to count all requirement-related tickets, and the \"BUG Tickets\" view is used to count all BUG-related tickets", "zh-CN": "提交的工单都会在\"需求与BUG反馈\"表中显示出来，其中\"所有工单\"视图显示所有工单数据，\"吐槽视图\"用于\"错误报告 & 功能请求\"表单的制作，\"本周所创工单总数\"用于统计本周所有提交的工单，\"需求视图\"用于统计所有需求类工单，\"BUG类工单\"用于统计所有BUG类的工单", "zh-TW": "提交的工單都會在\"需求與BUG反饋\"表中顯示出來，其中\"所有工單\"視圖顯示所有工單數據，\"吐槽視圖\"用於\"錯誤報告 & 功能請求\"表單的製作，\"本週所創工單總數\"用於統計本週所有提交的工單，\"需求視圖\"用於統計所有需求類工單，\"BUG類工單\"用於統計所有BUG類的工單"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwkaGQcA07dKca02gWTl6Io", "name": {"en": "All Tickets", "ja": "すべてのチケット", "zh-CN": "所有的工单", "zh-TW": "所有的工單"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [{"fieldTemplateId": "fldC6g70ibHoXuRot5NlcVYt", "asc": true}], "fields": [{"templateId": "fldDX3543NuPkC1WGJVUHor9", "width": 205}, {"templateId": "fldUHui7lKIhkRr6XcfyZCdd", "width": 163}, {"templateId": "fldRhnjab903Jef5jBGudl0C", "hidden": false, "width": 412}, {"templateId": "fldeDaxie2KzrjuKMxx9vMQg"}, {"templateId": "fld7zqK8uQsV4LmvSOF9hQfV", "width": 173}, {"templateId": "fldUU1weQVydeguK1RrOkRxU", "width": 300}, {"templateId": "fldZal15VVh6DcwhbiXecFV7"}, {"templateId": "fldC6g70ibHoXuRot5NlcVYt", "hidden": true, "width": 150}, {"templateId": "fldXKMbrrgRvhqs7TB2Xvy0r"}, {"templateId": "fldrc7EfvMpg5LUYVL1yKMWv", "width": 213}, {"templateId": "fldKihtT2gIxZWANAXIrz0L2", "hidden": true}, {"templateId": "fldg0RtdEtHgTNaA6mzTu3iA", "width": 160}, {"templateId": "fldxQPq3QuRf4HeT7EqGBsJ7", "width": 380}, {"templateId": "fldq7Mysjw0LxmbRfRTTKVi3", "width": 237}, {"templateId": "fld0WYg3wDZUH4cgq884ma1I", "hidden": true}, {"templateId": "fldgGXEW9LJyZZB2DSXchzfO", "hidden": true}]}, {"type": "TABLE", "templateId": "viwHexOqsrHFlvQf3FQQhK2U", "name": {"en": "Feedback View", "ja": "フィードバックビュー", "zh-CN": "吐槽视图", "zh-TW": "吐槽視圖"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldDX3543NuPkC1WGJVUHor9", "hidden": false, "width": 347}, {"templateId": "fldUHui7lKIhkRr6XcfyZCdd", "hidden": false}, {"templateId": "fldeDaxie2KzrjuKMxx9vMQg", "hidden": false}, {"templateId": "fld0WYg3wDZUH4cgq884ma1I", "hidden": true}, {"templateId": "fldgGXEW9LJyZZB2DSXchzfO", "hidden": true}, {"templateId": "fld7zqK8uQsV4LmvSOF9hQfV", "hidden": false}, {"templateId": "fldUU1weQVydeguK1RrOkRxU", "hidden": true}, {"templateId": "fldZal15VVh6DcwhbiXecFV7", "hidden": true}, {"templateId": "fldC6g70ibHoXuRot5NlcVYt", "hidden": true}, {"templateId": "fldXKMbrrgRvhqs7TB2Xvy0r", "hidden": true}, {"templateId": "fldrc7EfvMpg5LUYVL1yKMWv", "hidden": true}, {"templateId": "fldKihtT2gIxZWANAXIrz0L2", "hidden": true}, {"templateId": "fldRhnjab903Jef5jBGudl0C", "hidden": true}, {"templateId": "fldg0RtdEtHgTNaA6mzTu3iA", "hidden": true}, {"templateId": "fldxQPq3QuRf4HeT7EqGBsJ7", "hidden": true}, {"templateId": "fldq7Mysjw0LxmbRfRTTKVi3", "hidden": true}]}, {"type": "TABLE", "templateId": "viwcbyhtCTLz8EYZxj5WEwb3", "name": {"en": "Total Number of Tickets Created This Week", "ja": "Total Number of Tickets Created This Week", "zh-CN": "本周所创工单总数", "zh-TW": "本週創建的工單總數"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldrc7EfvMpg5LUYVL1yKMWv", "fieldType": "CREATED_TIME", "clause": {"operator": "Is", "value": ["ThisWeek"]}}]}, "sorts": [], "fields": [{"templateId": "fldDX3543NuPkC1WGJVUHor9", "hidden": false}, {"templateId": "fldUHui7lKIhkRr6XcfyZCdd", "hidden": false}, {"templateId": "fldeDaxie2KzrjuKMxx9vMQg", "hidden": false}, {"templateId": "fld0WYg3wDZUH4cgq884ma1I", "hidden": true}, {"templateId": "fldgGXEW9LJyZZB2DSXchzfO", "hidden": true}, {"templateId": "fld7zqK8uQsV4LmvSOF9hQfV", "hidden": false}, {"templateId": "fldUU1weQVydeguK1RrOkRxU", "hidden": false}, {"templateId": "fldZal15VVh6DcwhbiXecFV7", "hidden": false}, {"templateId": "fldC6g70ibHoXuRot5NlcVYt", "hidden": true}, {"templateId": "fldXKMbrrgRvhqs7TB2Xvy0r", "hidden": false}, {"templateId": "fldrc7EfvMpg5LUYVL1yKMWv", "hidden": false}, {"templateId": "fldKihtT2gIxZWANAXIrz0L2", "hidden": true}, {"templateId": "fldRhnjab903Jef5jBGudl0C", "hidden": false}, {"templateId": "fldg0RtdEtHgTNaA6mzTu3iA", "hidden": true}, {"templateId": "fldxQPq3QuRf4HeT7EqGBsJ7", "hidden": false}, {"templateId": "fldq7Mysjw0LxmbRfRTTKVi3", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwNChVtDEa9yZ1vvE7SbIQs", "name": {"en": "Requirement", "ja": "要求", "zh-CN": "需求", "zh-TW": "需求"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldUHui7lKIhkRr6XcfyZCdd", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optJeEdpokLk2"}}]}, "sorts": [], "fields": [{"templateId": "fldDX3543NuPkC1WGJVUHor9", "width": 379}, {"templateId": "fldUHui7lKIhkRr6XcfyZCdd"}, {"templateId": "fldeDaxie2KzrjuKMxx9vMQg"}, {"templateId": "fld0WYg3wDZUH4cgq884ma1I"}, {"templateId": "fldgGXEW9LJyZZB2DSXchzfO"}, {"templateId": "fld7zqK8uQsV4LmvSOF9hQfV"}, {"templateId": "fldUU1weQVydeguK1RrOkRxU"}, {"templateId": "fldZal15VVh6DcwhbiXecFV7"}, {"templateId": "fldC6g70ibHoXuRot5NlcVYt"}, {"templateId": "fldXKMbrrgRvhqs7TB2Xvy0r"}, {"templateId": "fldrc7EfvMpg5LUYVL1yKMWv"}, {"templateId": "fldKihtT2gIxZWANAXIrz0L2"}, {"templateId": "fldRhnjab903Jef5jBGudl0C"}, {"templateId": "fldxQPq3QuRf4HeT7EqGBsJ7"}, {"templateId": "fldq7Mysjw0LxmbRfRTTKVi3"}, {"templateId": "fldg0RtdEtHgTNaA6mzTu3iA"}]}, {"type": "TABLE", "templateId": "viwhougrmsLPUYoCvEgYCe0D", "name": {"en": "BUG-type Ticket", "ja": "バグタイプのチケット", "zh-CN": "BUG类工单", "zh-TW": "BUG類工單"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldUU1weQVydeguK1RrOkRxU", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Contains", "value": ["opt1LGMYOZ9p5d164BE1n829", "optLyY92gy342I7r6HENbMiX", "optgD8hHXw7aRQ7KCWbGwLlu"]}}]}, "sorts": [], "fields": [{"templateId": "fldDX3543NuPkC1WGJVUHor9", "hidden": false}, {"templateId": "fldUHui7lKIhkRr6XcfyZCdd", "hidden": false}, {"templateId": "fldeDaxie2KzrjuKMxx9vMQg", "hidden": true}, {"templateId": "fld0WYg3wDZUH4cgq884ma1I", "hidden": true}, {"templateId": "fldgGXEW9LJyZZB2DSXchzfO", "hidden": true}, {"templateId": "fld7zqK8uQsV4LmvSOF9hQfV", "hidden": true}, {"templateId": "fldUU1weQVydeguK1RrOkRxU", "hidden": false}, {"templateId": "fldZal15VVh6DcwhbiXecFV7", "hidden": false}, {"templateId": "fldC6g70ibHoXuRot5NlcVYt", "hidden": true}, {"templateId": "fldXKMbrrgRvhqs7TB2Xvy0r", "hidden": true}, {"templateId": "fldrc7EfvMpg5LUYVL1yKMWv", "hidden": true}, {"templateId": "fldKihtT2gIxZWANAXIrz0L2", "hidden": true}, {"templateId": "fldRhnjab903Jef5jBGudl0C", "hidden": true}, {"templateId": "fldg0RtdEtHgTNaA6mzTu3iA", "hidden": true}, {"templateId": "fldxQPq3QuRf4HeT7EqGBsJ7", "hidden": true}, {"templateId": "fldq7Mysjw0LxmbRfRTTKVi3", "hidden": true}], "groups": []}], "fields": [{"type": "LONG_TEXT", "templateId": "fldDX3543NuPkC1WGJVUHor9", "privilege": "TYPE_EDIT", "name": {"en": "Problem Description", "ja": "問題説明", "zh-CN": "问题描述", "zh-TW": "問題描述"}, "description": {"en": "Clearly and concisely describing the encountered issue or bug helps us quickly understand and resolve the problem", "ja": "遭遇した問題やバグを明確かつ簡潔に説明することで、迅速に理解し解決することができます", "zh-CN": "清晰简明地描述遇到的问题或BUG，帮助我们快速理解并解决问题", "zh-TW": "清晰簡明地描述遇到的問題或BUG，幫助我們快速理解並解決問題"}, "required": true, "primary": true}, {"type": "SINGLE_SELECT", "templateId": "fldUHui7lKIhkRr6XcfyZCdd", "privilege": "FULL_EDIT", "name": {"en": "Problem Type", "ja": "問題タイプ", "zh-CN": "问题类型", "zh-TW": "問題類型"}, "description": {"en": "Please select the type of problem so that we can better understand the problem", "ja": "問題の種類を選択してください。そうすることで、問題をよりよく理解できます", "zh-CN": "请选择问题类型，以便我们更好地理解问题", "zh-TW": "請選擇問題類型，以便我們更好地理解問題"}, "required": true, "property": {"options": [{"templateId": "bug", "id": "opt8eaFX66qHu", "name": "BUG", "color": "red"}, {"templateId": "feat", "id": "optJeEdpokLk2", "name": "Feat", "color": "blue"}]}, "primary": false}, {"type": "ATTACHMENT", "templateId": "fldeDaxie2KzrjuKMxx9vMQg", "privilege": "FULL_EDIT", "name": {"en": "Related Attachments", "ja": "関連添付ファイル", "zh-CN": "相关附件", "zh-TW": "相關附件"}, "description": {"en": "You can upload related attachments such as screenshots, which helps us solve the problem faster", "ja": "関連する添付ファイル（スクリーンショットなど）をアップロードできます。これにより、問題をより速く解決できます", "zh-CN": "您可以上传相关附件，如截图，这有助于我们更快地解决问题", "zh-TW": "您可以上傳相關附件，如截圖，這有助於我們更快地解決問題"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fld0WYg3wDZUH4cgq884ma1I", "privilege": "FULL_EDIT", "name": {"en": "Device Information (Computer Version)", "ja": "デバイス情報（コンピューター版）", "zh-CN": "设备信息（电脑版）", "zh-TW": "設備信息（電腦版）"}, "description": {"en": "Please fill in your device information so that we can better understand your problem", "ja": "デバイス情報を入力して、問題をより良く理解できるようにしてください", "zh-CN": "请填写您的设备信息，以便我们更好地了解您的问题", "zh-TW": "請填寫您的設備信息，以便我們更好地了解您的問題"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldgGXEW9LJyZZB2DSXchzfO", "privilege": "FULL_EDIT", "name": {"en": "Device Information (Mobile Version)", "ja": "デバイス情報（モバイル版）", "zh-CN": "设备信息（手机版）", "zh-TW": "設備信息（手機版）"}, "description": {"en": "Please fill in your device information so that we can better understand your problem", "ja": "デバイス情報を入力して、問題をより良く理解できるようにしてください", "zh-CN": "请填写您的设备信息，以便我们更好地了解您的问题", "zh-TW": "請填寫您的設備信息，以便我們更好地了解您的問題"}, "primary": false}, {"type": "EMAIL", "templateId": "fld7zqK8uQsV4LmvSOF9hQfV", "privilege": "FULL_EDIT", "name": {"en": "Email Address", "ja": "メールアドレス", "zh-CN": "邮件地址", "zh-TW": "電子郵件地址"}, "description": {"en": "Please provide your email address, for example: <EMAIL>", "ja": "お手数ですが、より良い連絡のためにメールアドレス（例：<EMAIL>）をご記入いただけますか？", "zh-CN": "请填写您的邮箱地址  (例如：<EMAIL>)，以便后续向您及时反馈最新进展。", "zh-TW": "麻煩您可以填寫您的郵箱地址（例如： <EMAIL> ），以便後續向您及時回饋最新進展"}, "required": true, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldUU1weQVydeguK1RrOkRxU", "privilege": "FULL_EDIT", "name": {"en": "Requirement/BUG Status", "ja": "要求/バグの状態", "zh-CN": "需求/BUG 状态", "zh-TW": "需求/BUG 狀態"}, "description": {"en": "Please select your type of ticket", "ja": "支払タイプを選択してください", "zh-CN": "请选择您的需求/BUG状态", "zh-TW": "請選擇您的申請類型"}, "property": {"options": [{"templateId": "processing", "id": "optNsjRpULW7r", "name": "processing", "color": "deepPurple"}, {"templateId": "solved", "id": "optnNtZQzLo6I", "name": "solved", "color": "green"}, {"id": "opthpIp1FqRBja9oi3FiJuAx", "name": "reject", "color": "deepPurple"}, {"id": "optLyY92gy342I7r6HENbMiX", "name": "added to the requirement pool", "color": "tangerine5"}, {"id": "optKv5rutR8Dd6YNN48e78Uo", "name": "close", "color": "orange5"}, {"id": "optgD8hHXw7aRQ7KCWbGwLlu", "name": "Entered the iteration version", "color": "deepPurple"}, {"id": "opt1LGMYOZ9p5d164BE1n829", "name": "Open", "color": "red5"}, {"id": "optsUHX46bmBaJFPlDr8JqVT", "name": "Pending", "color": "deepPurple"}, {"id": "optyh3v22QTR5DtfzgXaRqml", "name": "Launched", "color": "deepPurple"}]}, "primary": false}, {"type": "MEMBER", "templateId": "fldZal15VVh6DcwhbiXecFV7", "privilege": "FULL_EDIT", "name": {"en": "<PERSON><PERSON><PERSON>", "ja": "担当者", "zh-CN": "处理人", "zh-TW": "處理人"}, "description": {"en": "Please select your assignee", "ja": "担当者を選択してください", "zh-CN": "请选择您的处理人", "zh-TW": "請選擇您的處理人"}, "property": {"many": true}, "primary": false}, {"type": "DATETIME", "templateId": "fldC6g70ibHoXuRot5NlcVYt", "privilege": "FULL_EDIT", "name": {"en": "Expected Online Time", "ja": "リリース予定時間", "zh-CN": "预计上线时间", "zh-TW": "預計完成日期"}, "description": {"en": "Please fill in your expected completion date", "ja": "予定完了日を入力してください", "zh-CN": "预计上线时间", "zh-TW": "請填寫您的預計完成日期"}, "property": {"timeZone": "Asia/Shanghai", "dateFormat": "YYYY/MM/DD", "includeTime": false}, "primary": false}, {"type": "CREATED_BY", "templateId": "fldXKMbrrgRvhqs7TB2Xvy0r", "privilege": "FULL_EDIT", "name": {"en": "Submitter", "ja": "提出者", "zh-CN": "提交人", "zh-TW": "提交人"}, "primary": false}, {"type": "CREATED_TIME", "templateId": "fldrc7EfvMpg5LUYVL1yKMWv", "privilege": "FULL_EDIT", "name": {"en": "Submission Time", "ja": "提出時間", "zh-CN": "提交时间", "zh-TW": "提交時間"}, "property": {"timeZone": "Asia/Shanghai", "dateFormat": "YYYY/MM/DD", "includeTime": true}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldKihtT2gIxZWANAXIrz0L2", "privilege": "FULL_EDIT", "name": {"en": "Rejection Reason", "ja": "拒否理由", "zh-CN": "拒绝原因", "zh-TW": "拒绝原因"}, "primary": false}, {"type": "URL", "templateId": "fldRhnjab903Jef5jBGudl0C", "privilege": "FULL_EDIT", "name": {"en": "Issue Link", "ja": "問題リンク", "zh-CN": "问题链接", "zh-TW": "問題鏈接"}, "primary": false}, {"type": "MODIFIED_BY", "templateId": "fldg0RtdEtHgTNaA6mzTu3iA", "privilege": "FULL_EDIT", "name": {"en": "Ticket Editor", "ja": "チケット修正者", "zh-CN": "工单修改人"}, "required": false, "primary": false}, {"type": "FORMULA", "templateId": "fldxQPq3QuRf4HeT7EqGBsJ7", "privilege": "FULL_EDIT", "name": {"en": "BUG Status Formula (English)", "ja": "バグ状態式（英語）", "zh-CN": "BUG 状态公式（英文)", "zh-TW": "BUG 狀態公式（英文）"}, "required": false, "property": {"expressionTemplate": "IF({fldUU1weQVydeguK1RrOkRxU}=\"Open\",\"Open (remark: Your ticket is currently being processed)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"added to the requirement pool\",\"added to the requirement pool (remark: This feature has been added to the requirement resource pool)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"Launched\",\"launched (remark: The function has been launched)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"Entered the iteration version\",\"Entered the iteration version (remark: The function has entered the iteration planning stage)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"close\",\"close (remark: Your tikcet has been closed)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"solved\",\"solved (Remark: Your ticket has been resolved. If you still have any questions, please reply 🙋🏻‍♂️ directly to this email !!)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"Pending\",\"Pending (remark: needs additional information from the requester or customer to continue solving the ticket)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"reject\",\"reject (remark: Your ticket has been rejected)\"，“”))))))))\n"}, "primary": false}, {"type": "FORMULA", "templateId": "fldq7Mysjw0LxmbRfRTTKVi3", "privilege": "FULL_EDIT", "name": {"en": "BUG Status Formula (Chinese)", "ja": "バグ状態式（中国語）", "zh-CN": "BUG 状态公式(中文)", "zh-TW": "BUG 狀態公式（中文）"}, "required": false, "property": {"expressionTemplate": "IF({fldUU1weQVydeguK1RrOkRxU}=\"Open\",\"Open (备注：您的工单目前正在处理中)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"added to the requirement pool\",\"added to the requirement pool (备注: 该功能已经添加到需求资源池中)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"Launched\",\"launched (备注：该功能已上线)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"Entered the iteration version\",\"Entered the iteration version (备注：指功能已经进入迭代规划)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"close\",\"close (备注: 您的工单已关闭)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"solved\",\"solved (备注：您所提的问题已修复，如还有问题，请直接回复🙋🏻‍♂️该邮件)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"Pending\",\"Pending (备注: 需要您提供额外信息以继续解决问题)\",IF({fldUU1weQVydeguK1RrOkRxU}=\"reject\",\"reject(备注：您的工单已被拒绝)\"，\"\"))))))))"}, "primary": false}], "records": [{"templateId": "recAYn8PKLhyES8XIvUuRo8J", "data": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "When the table already has data rows, adding an auto-incrementing number does not display", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/001", "fldUHui7lKIhkRr6XcfyZCdd": ["optJeEdpokLk2"], "fldUU1weQVydeguK1RrOkRxU": ["optKv5rutR8Dd6YNN48e78Uo"], "fldXKMbrrgRvhqs7TB2Xvy0r": "usrcffIpPctI8JIiRSZ7xUDC", "fldZal15VVh6DcwhbiXecFV7": ["mebOao2Jhx7coXmOEunhpC4n"], "fldeDaxie2KzrjuKMxx9vMQg": [{"id": "tplattyJHJzTqnsB6VQM9WA6K5e", "name": "image.png", "path": "template/tplattyJHJzTqnsB6VQM9WA6K5e.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 29675}], "fldg0RtdEtHgTNaA6mzTu3iA": null, "fldq7Mysjw0LxmbRfRTTKVi3": "close (备注: 您的工单已关闭)", "fldrc7EfvMpg5LUYVL1yKMWv": "2025-01-14T06:03:26.832Z", "fldxQPq3QuRf4HeT7EqGBsJ7": "close (remark: Your tikcet has been closed)"}, "values": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "When the table already has data rows, adding an auto-incrementing number does not display", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/001", "fldUHui7lKIhkRr6XcfyZCdd": ["Feat"], "fldUU1weQVydeguK1RrOkRxU": ["close"], "fldXKMbrrgRvhqs7TB2Xvy0r": "李嘉立", "fldZal15VVh6DcwhbiXecFV7": ["pengjin"], "fldeDaxie2KzrjuKMxx9vMQg": ["image.png"], "fldg0RtdEtHgTNaA6mzTu3iA": "pengjin", "fldq7Mysjw0LxmbRfRTTKVi3": "close (备注: 您的工单已关闭)", "fldrc7EfvMpg5LUYVL1yKMWv": "2025/01/14 11:44", "fldxQPq3QuRf4HeT7EqGBsJ7": "close (remark: Your tikcet has been closed)"}}, {"templateId": "rec1K1ucCHwlrqmEMVlUohQp", "data": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "\nAfter Table A is linked to Table B, when the associated value in Table A is cleared, Table B will not automatically clear the associated item. Instead, it will display as NULL", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/002", "fldUHui7lKIhkRr6XcfyZCdd": ["opt8eaFX66qHu"], "fldUU1weQVydeguK1RrOkRxU": ["optsUHX46bmBaJFPlDr8JqVT"], "fldXKMbrrgRvhqs7TB2Xvy0r": "usrT2HY6vIdVPClL6dTjacGr", "fldZal15VVh6DcwhbiXecFV7": ["mebOao2Jhx7coXmOEunhpC4n"], "fldeDaxie2KzrjuKMxx9vMQg": [{"id": "tplattLgZoQ4eLBHxP8gwgfk2nQ", "name": "image.png", "path": "template/tplattLgZoQ4eLBHxP8gwgfk2nQ.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 213708}], "fldg0RtdEtHgTNaA6mzTu3iA": null, "fldq7Mysjw0LxmbRfRTTKVi3": "Pending (备注: 需要您提供额外信息以继续解决问题)", "fldrc7EfvMpg5LUYVL1yKMWv": "2025-01-14T06:03:26.832Z", "fldxQPq3QuRf4HeT7EqGBsJ7": "Pending (remark: needs additional information from the requester or customer to continue solving the ticket)"}, "values": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "\nAfter Table A is linked to Table B, when the associated value in Table A is cleared, Table B will not automatically clear the associated item. Instead, it will display as NULL", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/002", "fldUHui7lKIhkRr6XcfyZCdd": ["BUG"], "fldUU1weQVydeguK1RrOkRxU": ["Pending"], "fldXKMbrrgRvhqs7TB2Xvy0r": "<PERSON>", "fldZal15VVh6DcwhbiXecFV7": ["pengjin"], "fldeDaxie2KzrjuKMxx9vMQg": ["image.png"], "fldg0RtdEtHgTNaA6mzTu3iA": "pengjin", "fldq7Mysjw0LxmbRfRTTKVi3": "Pending (备注: 需要您提供额外信息以继续解决问题)", "fldrc7EfvMpg5LUYVL1yKMWv": "2025/01/14 12:05", "fldxQPq3QuRf4HeT7EqGBsJ7": "Pending (remark: needs additional information from the requester or customer to continue solving the ticket)"}}, {"templateId": "recXrbveAXoDeJz3laNeqTdA", "data": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "Percentage input displays as null after entering 0", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/003", "fldUHui7lKIhkRr6XcfyZCdd": ["opt8eaFX66qHu"], "fldUU1weQVydeguK1RrOkRxU": ["opt1LGMYOZ9p5d164BE1n829"], "fldXKMbrrgRvhqs7TB2Xvy0r": "usrQuCFxsWOYK0q9wTICSR9k", "fldZal15VVh6DcwhbiXecFV7": ["mebOao2Jhx7coXmOEunhpC4n"], "fldeDaxie2KzrjuKMxx9vMQg": [{"id": "tplatt6WEkaEerKYRrVqSzFY9ay", "name": "image.png", "path": "template/tplatt6WEkaEerKYRrVqSzFY9ay.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 60752}], "fldg0RtdEtHgTNaA6mzTu3iA": null, "fldrc7EfvMpg5LUYVL1yKMWv": "2025-01-14T06:03:26.832Z"}, "values": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "Percentage input displays as null after entering 0", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/003", "fldUHui7lKIhkRr6XcfyZCdd": ["BUG"], "fldUU1weQVydeguK1RrOkRxU": ["Open"], "fldXKMbrrgRvhqs7TB2Xvy0r": "钟潇", "fldZal15VVh6DcwhbiXecFV7": ["pengjin"], "fldeDaxie2KzrjuKMxx9vMQg": ["image.png"], "fldg0RtdEtHgTNaA6mzTu3iA": "pengjin", "fldq7Mysjw0LxmbRfRTTKVi3": "Open (备注：您的工单目前正在处理中)", "fldrc7EfvMpg5LUYVL1yKMWv": "2025/01/14 12:14", "fldxQPq3QuRf4HeT7EqGBsJ7": "Open (remark: Your ticket is currently being processed)"}}, {"templateId": "recoB9orDIYh51rt38iZtyXy", "data": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "Automation - Fields that cannot be modified in the Create Record Action/Update Record Action should be grayed out or hidden", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/004", "fldUHui7lKIhkRr6XcfyZCdd": ["opt8eaFX66qHu"], "fldUU1weQVydeguK1RrOkRxU": ["opt1LGMYOZ9p5d164BE1n829"], "fldXKMbrrgRvhqs7TB2Xvy0r": "usrQuCFxsWOYK0q9wTICSR9k", "fldZal15VVh6DcwhbiXecFV7": ["mebOao2Jhx7coXmOEunhpC4n"], "fldeDaxie2KzrjuKMxx9vMQg": [{"id": "tplatt8vfEtZipkNiSpVX8m6Ihz", "name": "image.png", "path": "template/tplatt8vfEtZipkNiSpVX8m6Ihz.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 38585}], "fldg0RtdEtHgTNaA6mzTu3iA": null, "fldrc7EfvMpg5LUYVL1yKMWv": "2025-01-14T06:03:26.832Z"}, "values": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "Automation - Fields that cannot be modified in the Create Record Action/Update Record Action should be grayed out or hidden", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/004", "fldUHui7lKIhkRr6XcfyZCdd": ["BUG"], "fldUU1weQVydeguK1RrOkRxU": ["Open"], "fldXKMbrrgRvhqs7TB2Xvy0r": "钟潇", "fldZal15VVh6DcwhbiXecFV7": ["pengjin"], "fldeDaxie2KzrjuKMxx9vMQg": ["image.png"], "fldg0RtdEtHgTNaA6mzTu3iA": "pengjin", "fldq7Mysjw0LxmbRfRTTKVi3": "Open (备注：您的工单目前正在处理中)", "fldrc7EfvMpg5LUYVL1yKMWv": "2025/01/14 13:54", "fldxQPq3QuRf4HeT7EqGBsJ7": "Open (remark: Your ticket is currently being processed)"}}, {"templateId": "recNzyCAnQdL6hS9qpOxzgDt", "data": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "Member avatar display error", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/005", "fldUHui7lKIhkRr6XcfyZCdd": ["opt8eaFX66qHu"], "fldUU1weQVydeguK1RrOkRxU": ["opt1LGMYOZ9p5d164BE1n829"], "fldXKMbrrgRvhqs7TB2Xvy0r": "usr04CQqKlWHlOkII5agWnLL", "fldZal15VVh6DcwhbiXecFV7": ["mebOao2Jhx7coXmOEunhpC4n"], "fldeDaxie2KzrjuKMxx9vMQg": [{"id": "tpltplatt81BI7IbiZWI9EeXvyWWOC", "name": "编组 56.png", "path": "template/tpltplatt81BI7IbiZWI9EeXvyWWOC.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 45300}], "fldg0RtdEtHgTNaA6mzTu3iA": null, "fldq7Mysjw0LxmbRfRTTKVi3": "Open (备注：您的工单目前正在处理中)", "fldrc7EfvMpg5LUYVL1yKMWv": "2025-01-15T14:47:01.937Z", "fldxQPq3QuRf4HeT7EqGBsJ7": "Open (remark: Your ticket is currently being processed)"}, "values": {"fld7zqK8uQsV4LmvSOF9hQfV": "<EMAIL>", "fldDX3543NuPkC1WGJVUHor9": "Member avatar display error", "fldRhnjab903Jef5jBGudl0C": "https://github.com/vikadata/bika/005", "fldUHui7lKIhkRr6XcfyZCdd": ["BUG"], "fldUU1weQVydeguK1RrOkRxU": ["Open"], "fldXKMbrrgRvhqs7TB2Xvy0r": "pengjin", "fldZal15VVh6DcwhbiXecFV7": ["pengjin"], "fldeDaxie2KzrjuKMxx9vMQg": ["编组 56.png"], "fldg0RtdEtHgTNaA6mzTu3iA": "pengjin", "fldq7Mysjw0LxmbRfRTTKVi3": "Open (备注：您的工单目前正在处理中)", "fldrc7EfvMpg5LUYVL1yKMWv": "2025/01/15 22:47", "fldxQPq3QuRf4HeT7EqGBsJ7": "Open (remark: Your ticket is currently being processed)"}}]}, {"resourceType": "AUTOMATION", "templateId": "atofBr65dFuMUcVpGWEK6iOp", "name": {"en": "Ticket Notification Automation", "ja": "チケットプッシュ自動化", "zh-CN": "工单推送自动化", "zh-TW": "工單推送自動化"}, "triggers": [{"triggerType": "RECORD_CREATED", "templateId": "trgca5zAmW8nwUK9iZ5pH21j", "description": {"en": "A new feedback ticket has been submitted", "ja": "新しいフィードバックチケットが提出されました", "zh-CN": "有新的吐槽工单提交", "zh-TW": "有新的吐槽工單提交"}, "input": {"type": "DATABASE", "databaseTemplateId": "datrGBZV5xQlu3jaucLbsbW0"}}], "actions": [{"templateId": "act2I2YOBFcATBL5ItrFuGkO", "description": {"en": "There is a new feedback ticket pending processing", "ja": "新しいフィードバックチケットが処理待ちです", "zh-CN": "有新的吐槽工单待处理", "zh-TW": "有新的吐槽工單待處理"}, "actionType": "WECOM_WEBHOOK", "input": {"type": "WECOM_WEBHOOK", "data": {"msgtype": "markdown", "markdown": {"content": "📫 There are new complaint tickets pending             \n                      \nProblem Description：<%= _triggers.trgca5zAmW8nwUK9iZ5pH21j.record.cells.fldDX3543NuPkC1WGJVUHor9.value %>                          \nContact Information：<%= _triggers.trgca5zAmW8nwUK9iZ5pH21j.record.cells.fld7zqK8uQsV4LmvSOF9hQfV.value %>                            \n🐲 [Continue Complaining](https://staging.bika.ai/space/spcMsybxuWea53UJQIe85zx3/node/fomGXQXnT0B9x8uelV8eZOwF) 🐛[Continue Asking Questions](https://staging.bika.ai/space/spcMsybxuWea53UJQIe85zx3/node/fomGXQXnT0B9x8uelV8eZOwF)                      \n📝 [Ticket Details](https://staging.bika.ai/space/spcMsybxuWea53UJQIe85zx3/node/datrGBZV5xQlu3jaucLbsbW0)"}}, "urlType": "URL", "url": ""}}, {"templateId": "actdV0o5eBnr8tTdCu5XnUPb", "description": {"en": "Notify the other party the submmission has been received", "ja": "相手に提出が受け取られたことを通知する", "zh-CN": "通知对方已收到提交", "zh-TW": "通知對方已收到提交"}, "actionType": "SEND_EMAIL", "input": {"subject": "<%= _triggers.trgca5zAmW8nwUK9iZ5pH21j.record.cells.fld7zqK8uQsV4LmvSOF9hQfV.value %>, feedback submitted successfully", "body": {"html": "<p>&lt;!DOCTYPE html&gt; &lt;html lang=\"en\"&gt; &lt;head&gt; &lt;meta charset=\"UTF-8\"&gt; &lt;meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"&gt; &lt;title&gt;Image and Divider&lt;/title&gt; &lt;style&gt; .container { text-align: left; overflow: hidden; } .divider { border: none; border-top: 1px solid #e0e0e0; margin: 20px 0; } &lt;/style&gt; &lt;/head&gt; &lt;body&gt; &lt;!----&gt; &lt;div class=\"container\"&gt; &lt;img src=\"https://apitabledotcom.wpcomstaging.com/wp-content/uploads/2024/09/bikaai.png\" alt=\"bikaai\" width=\"150px\" height=\"auto\"&gt; &lt;/div&gt; &lt;!-- --&gt; &lt;hr class=\"divider\"&gt; &lt;/body&gt; &lt;/html&gt;<br></p><p>Dear Bika.ai User,<br><br>Thank you for your ticket submission. We have received it and will respond promptly. Below is the information you provided:<br><br>&lt;b&gt;Problem description:&lt;/b&gt;<span names=\"触发器,有新的记录创建时,记录,单元格,Problem Description,value\" tips=\"\" ids=\"_triggers,trgPKxsHC4T25V8KkPnMwq8P,record,cells,fld6Qxfwpuhzxs6zihZayLx7,value\" data-variable=\"\" class=\"variable-tag\"><%= _triggers.trgca5zAmW8nwUK9iZ5pH21j.record.cells.fldDX3543NuPkC1WGJVUHor9.value %></span><br><br>Attachments: Currently not displayed<br><br>We will notify you of any status updates. If you have questions or need further assistance, please contact us. We appreciate your feedback.<br><br>Best regards,<br>The Bika.ai Team<br></p><p>&lt;!DOCTYPE html&gt; &lt;html lang=\"en\"&gt; &lt;head&gt; &lt;meta charset=\"UTF-8\"&gt; &lt;meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"&gt; &lt;title&gt;Image and Divider&lt;/title&gt; &lt;style&gt; .container { text-align: left; overflow: hidden; } .divider { border: none; border-top: 1px solid #e0e0e0; margin: 20px 0; } &lt;/style&gt; &lt;/head&gt; &lt;body&gt; &lt;!----&gt; &lt;div class=\"container\"&gt; &lt;img src=\"https://apitabledotcom.wpcomstaging.com/wp-content/uploads/2024/09/bikaai.png\" alt=\"bikaai\" width=\"150px\" height=\"auto\"&gt; &lt;/div&gt; &lt;!-- --&gt; &lt;hr class=\"divider\"&gt; &lt;/body&gt; &lt;/html&gt;<br><br>尊敬的用户,<br><br>您好！感谢您提交的工单，我们已经收到了，并将在第一时间进行跟进。以下是你提供的工单内容：<br><br>&lt;b&gt;问题描述:&lt;/b&gt;<span names=\"触发器,有新的记录创建时,记录,单元格,Problem Description,value\" tips=\"\" ids=\"_triggers,trgPKxsHC4T25V8KkPnMwq8P,record,cells,fld6Qxfwpuhzxs6zihZayLx7,value\" data-variable=\"\" class=\"variable-tag\"><%= _triggers.trgca5zAmW8nwUK9iZ5pH21j.record.cells.fldDX3543NuPkC1WGJVUHor9.value %></span><br><br>附件：当前暂不显示<br><br>如工单状态有任何更新，我们将第一时间通知您。如有疑问或需要进一步帮助，请随时联系我们,感谢你的反馈 !<br></p><p>祝好，</p><p>Bika.ai团队<br><br><br><br></p>", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "<!DOCTYPE html> <html lang=\"en\"> <head> <meta charset=\"UTF-8\"> <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"> <title>Image and Divider</title> <style> .container { text-align: left; overflow: hidden; } .divider { border: none; border-top: 1px solid #e0e0e0; margin: 20px 0; } </style> </head> <body> <!----> <div class=\"container\"> <img src=\"https://apitabledotcom.wpcomstaging.com/wp-content/uploads/2024/09/bikaai.png\" alt=\"bikaai\" width=\"150px\" height=\"auto\"> </div> <!-- --> <hr class=\"divider\"> </body> </html>", "type": "text"}, {"type": "hardBreak"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "Dear Bika.ai User,", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "Thank you for your ticket submission. We have received it and will respond promptly. Below is the information you provided:", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "<b>Problem description:</b>", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trgPKxsHC4T25V8KkPnMwq8P", "record", "cells", "fld6Qxfwpuhzxs6zihZayLx7", "value"], "tips": "", "names": ["触发器", "有新的记录创建时", "记录", "单元格", "Problem Description", "value"]}}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "Attachments: Currently not displayed", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "We will notify you of any status updates. If you have questions or need further assistance, please contact us. We appreciate your feedback.", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "Best regards,", "type": "text"}, {"type": "hardBreak"}, {"text": "The Bika.ai Team", "type": "text"}, {"type": "hardBreak"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "<!DOCTYPE html> <html lang=\"en\"> <head> <meta charset=\"UTF-8\"> <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"> <title>Image and Divider</title> <style> .container { text-align: left; overflow: hidden; } .divider { border: none; border-top: 1px solid #e0e0e0; margin: 20px 0; } </style> </head> <body> <!----> <div class=\"container\"> <img src=\"https://apitabledotcom.wpcomstaging.com/wp-content/uploads/2024/09/bikaai.png\" alt=\"bikaai\" width=\"150px\" height=\"auto\"> </div> <!-- --> <hr class=\"divider\"> </body> </html>", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "尊敬的用户,", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "您好！感谢您提交的工单，我们已经收到了，并将在第一时间进行跟进。以下是你提供的工单内容：", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "<b>问题描述:</b>", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trgPKxsHC4T25V8KkPnMwq8P", "record", "cells", "fld6Qxfwpuhzxs6zihZayLx7", "value"], "tips": "", "names": ["触发器", "有新的记录创建时", "记录", "单元格", "Problem Description", "value"]}}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "附件：当前暂不显示", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "如工单状态有任何更新，我们将第一时间通知您。如有疑问或需要进一步帮助，请随时联系我们,感谢你的反馈 !", "type": "text"}, {"type": "hardBreak"}]}, {"type": "paragraph", "content": [{"text": "祝好，", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "Bika.ai团队", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"type": "hardBreak"}]}]}}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trgca5zAmW8nwUK9iZ5pH21j.record.cells.fld7zqK8uQsV4LmvSOF9hQfV.value %>"}], "senderName": "Bika.ai Customer Service Team", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}, {"templateId": "actDiJpMjw6slycgyB3pNY8A", "description": {"en": "Message sent to Slack", "ja": "メッセージをSlackに送信", "zh-CN": "消息发送到 Slack", "zh-TW": "消息發送到 Slack"}, "actionType": "SLACK_WEBHOOK", "input": {"type": "SLACK_WEBHOOK", "data": {"msgtype": "text", "text": "📫 There are new complaint tickets pending              \n          \nProblem Description：<%= _triggers.trgca5zAmW8nwUK9iZ5pH21j.record.cells.fldDX3543NuPkC1WGJVUHor9.value %>              \nContact Information：<%= _triggers.trgca5zAmW8nwUK9iZ5pH21j.record.cells.fld7zqK8uQsV4LmvSOF9hQfV.value %>                \n🐲 <https://staging.bika.ai/space/spcMsybxuWea53UJQIe85zx3/node/fomGXQXnT0B9x8uelV8eZOwF|Continue Complaining>    \n🐛 <https://staging.bika.ai/space/spcMsybxuWea53UJQIe85zx3/node/fomGXQXnT0B9x8uelV8eZOwF|Continue Asking Questions>    \n📝 <https://staging.bika.ai/space/spcMsybxuWea53UJQIe85zx3/node/datrGBZV5xQlu3jaucLbsbW0|Ticket Details>"}, "urlType": "URL", "url": ""}}]}, {"resourceType": "AUTOMATION", "templateId": "ato6McMu4lUGXVjCPl8MZnEr", "name": {"en": "Ticket Status Change Notification Automation", "ja": "チケット状態変更通知の自動化", "zh-CN": "工单状态变更提醒自动化", "zh-TW": "工單狀態變更提醒自動化"}, "triggers": [{"triggerType": "RECORD_MATCH", "templateId": "trglq4YtXgGoORnpmjACDBCh", "description": {"en": "The ticket status has changed", "ja": "チケットの状態が変更されました", "zh-CN": "工单状态发生变更", "zh-TW": "工單狀態發生變更"}, "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldUU1weQVydeguK1RrOkRxU", "fieldType": "SINGLE_SELECT", "clause": {"operator": "IsNotEmpty"}}]}, "databaseTemplateId": "datrGBZV5xQlu3jaucLbsbW0"}}], "actions": [{"templateId": "actn5B3FfCWHJdqNGYvLWACC", "description": {"en": "The ticket feedback person has sent an email reminder", "ja": "チケットのフィードバック担当者がメール通知を送信しました", "zh-CN": "工单反馈人发送邮件提醒", "zh-TW": "工單反饋人發送郵件提醒"}, "actionType": "SEND_EMAIL", "input": {"subject": "Your ticket status has been updated to: <%= JSON.stringify(_triggers.trglq4YtXgGoORnpmjACDBCh.record.cells.fldUU1weQVydeguK1RrOkRxU.value) %>", "body": {"html": "<p>&lt;div style=\"text-align: left; overflow: hidden;\"&gt;</p><p>  &lt;img src=\"https://apitabledotcom.wpcomstaging.com/wp-content/uploads/2024/09/bikaai.png\" alt=\"bikaai\" width=\"150px\" height=\"auto\"&gt;</p><p>&lt;/div&gt;</p><p>&lt;hr style=\"border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;\"&gt;</p><p>Dear Bika.ai User,&lt;br&gt;&lt;br&gt;</p><p>Hello! Thank you for submitting your ticket. We have followed up on your issue, and here is the latest update on your ticket:&lt;br&gt;&lt;br&gt;</p><p>&lt;b&gt;Problem&nbsp;Description: &lt;/b&gt;<span names=\"Triggers,Record Match,Record,Cells,Problem Description,value\" tips=\"\" ids=\"_triggers,trgkxrLepuffxpL0qf9oaW9y,record,cells,fld6Qxfwpuhzxs6zihZayLx7,value\" data-variable=\"\" class=\"variable-tag\"><%= _triggers.trglq4YtXgGoORnpmjACDBCh.record.cells.fldDX3543NuPkC1WGJVUHor9.value %></span>&lt;br&gt;&lt;br&gt;</p><p>&lt;b&gt;Current&nbsp;Status:&nbsp;&lt;/b&gt;<span names=\"Triggers,Record Match,Record,Cells,BUG状态公式英文,value\" tips=\"\" ids=\"_triggers,trgkxrLepuffxpL0qf9oaW9y,record,cells,fld3zWT7pGHm20QZJp3Ejt9x,value\" data-variable=\"\" class=\"variable-tag\"><%= _triggers.trglq4YtXgGoORnpmjACDBCh.record.cells.fldxQPq3QuRf4HeT7EqGBsJ7.value %></span>&lt;br&gt;&lt;br&gt;</p><p>&lt;b&gt;Issue&nbsp;Record&nbsp;Link: &lt;/b&gt;<span names=\"Triggers,Record Match,Record,Cells,issue链接,value\" tips=\"\" ids=\"_triggers,trgkxrLepuffxpL0qf9oaW9y,record,cells,fldu4qBTQlvud5pn91NBf6JS,value\" data-variable=\"\" class=\"variable-tag\"><%= _triggers.trglq4YtXgGoORnpmjACDBCh.record.cells.fldRhnjab903Jef5jBGudl0C.value %></span>&lt;br&gt;&lt;br&gt;</p><p>If there are any updates regarding the status of your ticket, we will notify you immediately. If you have any questions or require further assistance, &lt;b&gt;please directly reply to this email <NAME_EMAIL>.&lt;/b&gt;&lt;br&gt;&lt;br&gt;</p><p>Best regards,&lt;br&gt;</p><p>The Bika.ai Team</p><p>&lt;br&gt;&lt;br&gt;</p><p>&lt;div style=\"text-align: left; overflow: hidden;\"&gt;</p><p>  &lt;img src=\"https://apitabledotcom.wpcomstaging.com/wp-content/uploads/2024/09/bikaai.png\" alt=\"bikaai\" width=\"150px\" height=\"auto\"&gt;</p><p>&lt;/div&gt;</p><p>&lt;hr style=\"border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;\"&gt;</p><p>尊敬的用户,&lt;br&gt;&lt;br&gt;</p><p>您好！感谢您提交的工单&nbsp;。我们对您的问题进行了跟进，以下是您工单的最新情况：&lt;br&gt;&lt;br&gt;</p><p>&lt;b&gt;问题描述: &lt;/b&gt;<span names=\"Triggers,Record Match,Record,Cells,Problem Description,value\" tips=\"\" ids=\"_triggers,trgkxrLepuffxpL0qf9oaW9y,record,cells,fld6Qxfwpuhzxs6zihZayLx7,value\" data-variable=\"\" class=\"variable-tag\"><%= _triggers.trglq4YtXgGoORnpmjACDBCh.record.cells.fldDX3543NuPkC1WGJVUHor9.value %></span>&lt;br&gt;&lt;br&gt;</p><p>&lt;b&gt;工单当前状态: &lt;/b&gt;<span names=\"Triggers,Record Match,Record,Cells,BUG状态公式中文,value\" tips=\"\" ids=\"_triggers,trgkxrLepuffxpL0qf9oaW9y,record,cells,fldGZVXTS6Hvmo3jIQBkLCGP,value\" data-variable=\"\" class=\"variable-tag\"><%= _triggers.trglq4YtXgGoORnpmjACDBCh.record.cells.fldq7Mysjw0LxmbRfRTTKVi3.value %></span>&lt;br&gt;&lt;br&gt;</p><p>&lt;b&gt;问题记录issue链接: &lt;/b&gt;<span names=\"Triggers,Record Match,Record,Cells,issue链接,value\" tips=\"\" ids=\"_triggers,trgkxrLepuffxpL0qf9oaW9y,record,cells,fldu4qBTQlvud5pn91NBf6JS,value\" data-variable=\"\" class=\"variable-tag\"><%= _triggers.trglq4YtXgGoORnpmjACDBCh.record.cells.fldRhnjab903Jef5jBGudl0C.value %></span>&lt;br&gt;&lt;br&gt;</p><p>如工单状态有任何更新，我们将第一时间通知您。如有疑问或需要进一步帮助，&lt;b&gt;请直接回复本邮件，并Cc <EMAIL>。&lt;/b&gt;&lt;br&gt;&lt;br&gt;</p><p>祝好，&lt;br&gt;</p><p>Bika.ai团队</p>", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "<div style=\"text-align: left; overflow: hidden;\">", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "  <img src=\"https://apitabledotcom.wpcomstaging.com/wp-content/uploads/2024/09/bikaai.png\" alt=\"bikaai\" width=\"150px\" height=\"auto\">", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "</div>", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "<hr style=\"border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;\">", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "Dear <PERSON><PERSON>.ai User,<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "Hello! Thank you for submitting your ticket. We have followed up on your issue, and here is the latest update on your ticket:<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "<b>Problem Description: </b>", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trgkxrLepuffxpL0qf9oaW9y", "record", "cells", "fld6Qxfwpuhzxs6zihZayLx7", "value"], "tips": "", "names": ["Triggers", "Record Match", "Record", "Cells", "Problem Description", "value"]}}, {"text": "<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "<b>Current Status: </b>", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trgkxrLepuffxpL0qf9oaW9y", "record", "cells", "fld3zWT7pGHm20QZJp3Ejt9x", "value"], "tips": "", "names": ["Triggers", "Record Match", "Record", "Cells", "BUG状态公式英文", "value"]}}, {"text": "<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "<b>Issue Record Link: </b>", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trgkxrLepuffxpL0qf9oaW9y", "record", "cells", "fldu4qBTQlvud5pn91NBf6JS", "value"], "tips": "", "names": ["Triggers", "Record Match", "Record", "Cells", "issue链接", "value"]}}, {"text": "<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "If there are any updates regarding the status of your ticket, we will notify you immediately. If you have any questions or require further assistance, <b>please directly reply to this email <NAME_EMAIL>.</b><br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "Best regards,<br>", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "The Bika.ai Team", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "<div style=\"text-align: left; overflow: hidden;\">", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "  <img src=\"https://apitabledotcom.wpcomstaging.com/wp-content/uploads/2024/09/bikaai.png\" alt=\"bikaai\" width=\"150px\" height=\"auto\">", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "</div>", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "<hr style=\"border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;\">", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "尊敬的用户,<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "您好！感谢您提交的工单 。我们对您的问题进行了跟进，以下是您工单的最新情况：<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "<b>问题描述: </b>", "type": "text"}, {"type": "variable", "attrs": {"ids": "_triggers,trgkxrLepuffxpL0qf9oaW9y,record,cells,fld6Qxfwpuhzxs6zihZayLx7,value", "tips": "", "names": "Triggers,Record Match,Record,Cells,Problem Description,value"}}, {"text": "<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "<b>工单当前状态: </b>", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_triggers", "trgkxrLepuffxpL0qf9oaW9y", "record", "cells", "fldGZVXTS6Hvmo3jIQBkLCGP", "value"], "tips": "", "names": ["Triggers", "Record Match", "Record", "Cells", "BUG状态公式中文", "value"]}}, {"text": "<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "<b>问题记录issue链接: </b>", "type": "text"}, {"type": "variable", "attrs": {"ids": "_triggers,trgkxrLepuffxpL0qf9oaW9y,record,cells,fldu4qBTQlvud5pn91NBf6JS,value", "tips": "", "names": "Triggers,Record Match,Record,Cells,issue链接,value"}}, {"text": "<br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "如工单状态有任何更新，我们将第一时间通知您。如有疑问或需要进一步帮助，<b>请直接回复本邮件，并Cc <EMAIL>。</b><br><br>", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "祝好，<br>", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "Bika.ai团队", "type": "text"}]}]}}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trglq4YtXgGoORnpmjACDBCh.record.cells.fld7zqK8uQsV4LmvSOF9hQfV.value %>"}], "senderName": "Bika.ai Customer Service Team", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}, {"resourceType": "AUTOMATION", "templateId": "ato7rMfQSXELYzA4ejKtZK5m", "name": {"en": "AI Automated Reporting", "ja": "AI自動集計報告", "zh-CN": "AI自动汇总报告", "zh-TW": "AI自動彙總報告"}, "description": {"en": "Weekly AI Aggregation Reminder", "ja": "毎週のAI集計リマインダー", "zh-CN": "每周AI汇总提醒", "zh-TW": "每週 AI 彙總提醒"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trgYQbccyoDfznlFvexQ8fXL", "description": {"en": "Weekly Automatic Trigger", "ja": "毎週自動トリガー", "zh-CN": "每周自动触发", "zh-TW": "每週自動觸發"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["FRI"]}}, "timezone": "AUTO", "datetime": "2024-09-20T09:00:00.000Z"}}}], "actions": [{"templateId": "actZrZLMcIQRkrcGeA8F4bAA", "description": {"en": "Find all tickets collected this week", "ja": "今週に収集されたすべてのチケットを検索", "zh-CN": "查找本周收集的所有工单", "zh-TW": "查找本周收集的所有工單"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldrc7EfvMpg5LUYVL1yKMWv", "fieldType": "CREATED_TIME", "clause": {"operator": "Is", "value": ["ThisWeek"]}}, {"fieldTemplateId": "fldUHui7lKIhkRr6XcfyZCdd", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opt8eaFX66qHu"}}]}, "databaseTemplateId": "datrGBZV5xQlu3jaucLbsbW0"}}, {"templateId": "actbnBmFct6BtdUU3n1cJeEj", "description": {"en": "Query request-type ticket records", "ja": "依頼タイプのチケット記録を確認", "zh-CN": "查询需求类工单记录"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "viwNChVtDEa9yZ1vvE7SbIQs", "databaseTemplateId": "datrGBZV5xQlu3jaucLbsbW0"}}, {"templateId": "actwoBJXqkjv0RcVCxPsUuU6", "description": {"en": "Query bug-type ticket records", "ja": "バグタイプのチケット記録を確認", "zh-CN": "查询BUG类工单记录", "zh-TW": "查詢BUG類工單記錄"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "viwhougrmsLPUYoCvEgYCe0D", "databaseTemplateId": "datrGBZV5xQlu3jaucLbsbW0"}}, {"templateId": "actj8HUqHslhwjveiofVQXMa", "description": {"en": "Send AI Aggregated Report", "ja": "AI 集計レポートを送信", "zh-CN": "发送 AI 汇总报告", "zh-TW": "發送 AI 彙總報告"}, "actionType": "SEND_REPORT", "input": {"to": [], "markdown": "## 1. Overview  \nwe have received a total of<%= _actions.actZrZLMcIQRkrcGeA8F4bAA.records.length %> tickets  \n <br>\n  \n## 2. Requirement Ticket  \n  \nA total of <%= _actions.actbnBmFct6BtdUU3n1cJeEj.records.length %>Requirement tickets have been received  \n  \n  \n## 3.Preview of Pending Bug  \n<%= _renderRecordsAsList(_actions.actwoBJXqkjv0RcVCxPsUuU6.records, ['fldDX3543NuPkC1WGJVUHor9','fldUHui7lKIhkRr6XcfyZCdd','fld7zqK8uQsV4LmvSOF9hQfV','fldUU1weQVydeguK1RrOkRxU','fldZal15VVh6DcwhbiXecFV7']) %>", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "##  1. Overview", "type": "text"}, {"type": "hardBreak"}, {"text": "we have received a total of", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_actions", "actZrZLMcIQRkrcGeA8F4bAA", "records", "length"], "tips": "", "names": ["执行器", "查找记录", "records", "length(1)"]}}, {"text": " tickets", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "##  2. Requirement Ticket", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "A total of ", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_actions", "actbnBmFct6BtdUU3n1cJeEj", "records", "length"], "tips": "", "names": ["执行器", "查找记录", "records", "length(1)"]}}, {"text": "Requirement tickets have been received", "type": "text"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"type": "hardBreak"}, {"text": "## 3.Preview of Pending Bugst", "type": "text"}, {"type": "hardBreak"}, {"type": "variable", "attrs": {"ids": "_renderRecordsAsList(_actions.actwoBJXqkjv0RcVCxPsUuU6.records, ['fldDX3543NuPkC1WGJVUHor9','fldUHui7lKIhkRr6XcfyZCdd','fld7zqK8uQsV4LmvSOF9hQfV','fldUU1weQVydeguK1RrOkRxU','fldZal15VVh6DcwhbiXecFV7'])", "tips": "选中的字段: 问题描述, 问题类型, 邮件地址, 需求/BUG 状态, 处理人", "names": ["执行器", "查找记录", "记录列表"]}}, {"type": "hardBreak"}]}, {"type": "paragraph", "content": [{"type": "hardBreak"}, {"type": "hardBreak"}, {"type": "hardBreak"}]}]}, "subject": "Weekly Ticket Report - <%= new Date().toLocaleDateString() %>", "type": "MARKDOWN"}}]}], "initMissions": []}