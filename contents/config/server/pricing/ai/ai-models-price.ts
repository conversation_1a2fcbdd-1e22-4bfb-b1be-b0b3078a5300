import { z } from 'zod';
import { PresetLanguageAIModelDef } from '@bika/types/ai/bo';
import { AIModelPrice, AIModelPriceSchema } from '@bika/types/pricing/bo';
import aiModelPricesJSON from './ai-models-price.json';

let _cacheAIModelPrices: Record<PresetLanguageAIModelDef | 'default', AIModelPrice> | undefined;

/**
 * lazy load sku configs
 * @returns
 */
const getAIModelPricesConfigs = () => {
  if (_cacheAIModelPrices) return _cacheAIModelPrices!;
  _cacheAIModelPrices = z.record(AIModelPriceSchema).parse(aiModelPricesJSON);
  return _cacheAIModelPrices!;
};

export const getAIModelPricesConfig = (model: PresetLanguageAIModelDef | 'default') => {
  const configs = getAIModelPricesConfigs();
  const config = configs[model];
  if (!config) {
    console.warn(`AI model ${model} not found, using default config, plese set AI models price table!`);
    return configs.default;
  }
  return config;
};
