{"templateId": "14-day-automated-cold-email-outreach-campaign", "name": "14-Day Automated Cold-Email Outreach Campaign", "description": "This template is designed for cold outreach to influencers, peers, or potential partners. It automates a 14-day email sequence that gradually introduces Bika.ai to your target audience and nurtures engagement. The workflow includes a series of five emails sent on Day 1, Day 2, Day 4, Day 7, and Day 14, with an unsubscribe option in each email. The sequence pauses if the recipient replies or unsubscribes, ensuring that you’re not over-communicating with uninterested prospects.\n\nWith this template, you can:\n\n- Automate cold outreach without the need for manual follow-ups.\n- Track outreach progress and engagement.\n- Customize emails to reflect your brand’s voice and offerings.\n- Reduce manual work by automatically updating the status of email outreach.\n- Easily manage unsubscribes to maintain a compliant outreach process.\n\n🔖Tip: Please replace the unsubscribe link in your email with the share link to the \"Unsubscribe Form\" you’ve created.", "cover": "/assets/template/14-day-automated-cold-email-outreach-campaign/14-day-automated-cold-email-outreach-campaign.png", "author": "Thea <<EMAIL>>", "category": ["project", "marketing", "sales", "email"], "keywords": "cold email, outreach, email campaign", "useCases": "Cold email outreach, Email campaign, Sales outreach, Marketing outreach, Automated email sequence, Email automation", "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.3", "resources": [{"resourceType": "DATABASE", "templateId": "dat75WYXko2I5VFbV09P78No", "name": "Outreach Contact List", "description": "The Outreach Contact List tracks all of your outreach targets, including key details such as the contact's name, email, company, and website. It also records the status of emails sent on Day 1, Day 2, Day 4, Day 7, and Day 14, as well as whether the recipient replied or unsubscribed. This table helps you manage your outreach progress and adjust follow-ups based on engagement.", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwXIUy8Hd75qLYzGysdvLB8", "name": "All", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldm56dudVh37vClEcKCAFMi", "width": 183}, {"templateId": "fldqGYNRBlqI5j8V2myNJemk", "width": 291}, {"templateId": "fldDwyzXY5RRo5za2pzao1b4"}, {"templateId": "fldwqSzOd7Jbl35qcCUkgwBb"}, {"templateId": "fldwZ5iyY7zRJhLTqSj3TMIH", "width": 209}, {"templateId": "fld4nlWtEUWkBmpqmgKkq41V"}, {"templateId": "fldHmoMDQe7ZFSfilsZV3pDU", "width": 210}, {"templateId": "fldvK8YCif8iDbqXmWETzATJ"}]}, {"type": "TABLE", "templateId": "viwHH5ubZFUyuD5qJjkSahis", "name": "D1 Sent", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld4nlWtEUWkBmpqmgKkq41V", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opttIrI6Pm4cTx0kIQpyHQXe"}}, {"fieldTemplateId": "fldHmoMDQe7ZFSfilsZV3pDU", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optFMpmOaMtohDMkNWEpf9yK"}}]}, "sorts": [], "fields": [{"templateId": "fldm56dudVh37vClEcKCAFMi"}, {"templateId": "fldDwyzXY5RRo5za2pzao1b4"}, {"templateId": "fldwZ5iyY7zRJhLTqSj3TMIH"}, {"templateId": "fldwqSzOd7Jbl35qcCUkgwBb"}, {"templateId": "fldqGYNRBlqI5j8V2myNJemk"}, {"templateId": "fld4nlWtEUWkBmpqmgKkq41V"}, {"templateId": "fldHmoMDQe7ZFSfilsZV3pDU", "width": 150}, {"templateId": "fldvK8YCif8iDbqXmWETzATJ"}], "groups": []}, {"type": "TABLE", "templateId": "viwbYK93ZohRBoi5S4SLwOIL", "name": "D2 Sent", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld4nlWtEUWkBmpqmgKkq41V", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opttIrI6Pm4cTx0kIQpyHQXe"}}, {"fieldTemplateId": "fldHmoMDQe7ZFSfilsZV3pDU", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optNhyjOmfbd6mRohI7URDN4"}}]}, "sorts": [], "fields": [{"templateId": "fldm56dudVh37vClEcKCAFMi", "hidden": false}, {"templateId": "fldDwyzXY5RRo5za2pzao1b4", "hidden": false}, {"templateId": "fldwZ5iyY7zRJhLTqSj3TMIH", "hidden": false}, {"templateId": "fldwqSzOd7Jbl35qcCUkgwBb", "hidden": false}, {"templateId": "fldqGYNRBlqI5j8V2myNJemk", "hidden": false}, {"templateId": "fld4nlWtEUWkBmpqmgKkq41V", "hidden": false}, {"templateId": "fldHmoMDQe7ZFSfilsZV3pDU", "hidden": false, "width": 150}, {"templateId": "fldvK8YCif8iDbqXmWETzATJ", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwEY5xnj0s2OaqLLYsrfelJ", "name": "D4 Sent", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld4nlWtEUWkBmpqmgKkq41V", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opttIrI6Pm4cTx0kIQpyHQXe"}}, {"fieldTemplateId": "fldHmoMDQe7ZFSfilsZV3pDU", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opts6aRhE6apDpEyoqFpq5YC"}}]}, "sorts": [], "fields": [{"templateId": "fldm56dudVh37vClEcKCAFMi", "hidden": false}, {"templateId": "fldDwyzXY5RRo5za2pzao1b4", "hidden": false}, {"templateId": "fldwZ5iyY7zRJhLTqSj3TMIH", "hidden": false}, {"templateId": "fldwqSzOd7Jbl35qcCUkgwBb", "hidden": false}, {"templateId": "fldqGYNRBlqI5j8V2myNJemk", "hidden": false}, {"templateId": "fld4nlWtEUWkBmpqmgKkq41V", "hidden": false}, {"templateId": "fldHmoMDQe7ZFSfilsZV3pDU", "hidden": false, "width": 150}, {"templateId": "fldvK8YCif8iDbqXmWETzATJ", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwFg757t8zomkFH8YsB06ji", "name": "D7 Sent", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld4nlWtEUWkBmpqmgKkq41V", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opttIrI6Pm4cTx0kIQpyHQXe"}}, {"fieldTemplateId": "fldHmoMDQe7ZFSfilsZV3pDU", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optwmYZ2ilbKedytVaFm9O06"}}]}, "sorts": [], "fields": [{"templateId": "fldm56dudVh37vClEcKCAFMi", "hidden": false}, {"templateId": "fldDwyzXY5RRo5za2pzao1b4", "hidden": false}, {"templateId": "fldwZ5iyY7zRJhLTqSj3TMIH", "hidden": false}, {"templateId": "fldwqSzOd7Jbl35qcCUkgwBb", "hidden": false}, {"templateId": "fldqGYNRBlqI5j8V2myNJemk", "hidden": false}, {"templateId": "fld4nlWtEUWkBmpqmgKkq41V", "hidden": false}, {"templateId": "fldHmoMDQe7ZFSfilsZV3pDU", "hidden": false}, {"templateId": "fldvK8YCif8iDbqXmWETzATJ", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwzlnnnXICYCJ6EEzKrEJI8", "name": "D14 Sent", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld4nlWtEUWkBmpqmgKkq41V", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opttIrI6Pm4cTx0kIQpyHQXe"}}, {"fieldTemplateId": "fldHmoMDQe7ZFSfilsZV3pDU", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optcnLYIKk9pXtkxrYs1DhQh"}}]}, "sorts": [], "fields": [{"templateId": "fldm56dudVh37vClEcKCAFMi", "hidden": false}, {"templateId": "fldDwyzXY5RRo5za2pzao1b4", "hidden": false}, {"templateId": "fldwZ5iyY7zRJhLTqSj3TMIH", "hidden": false}, {"templateId": "fldwqSzOd7Jbl35qcCUkgwBb", "hidden": false}, {"templateId": "fldqGYNRBlqI5j8V2myNJemk", "hidden": false}, {"templateId": "fld4nlWtEUWkBmpqmgKkq41V", "hidden": false}, {"templateId": "fldHmoMDQe7ZFSfilsZV3pDU", "hidden": false}, {"templateId": "fldvK8YCif8iDbqXmWETzATJ", "hidden": false}], "groups": []}, {"type": "TABLE", "templateId": "viwIshqpvZ6NQZPyeMNrbz8y", "name": "Round1 Done", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld4nlWtEUWkBmpqmgKkq41V", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opt74RGPxiogNRwqte4fJX6H"}}]}, "sorts": [], "fields": [{"templateId": "fldm56dudVh37vClEcKCAFMi"}, {"templateId": "fldDwyzXY5RRo5za2pzao1b4"}, {"templateId": "fldwZ5iyY7zRJhLTqSj3TMIH"}, {"templateId": "fldwqSzOd7Jbl35qcCUkgwBb"}, {"templateId": "fldqGYNRBlqI5j8V2myNJemk"}, {"templateId": "fld4nlWtEUWkBmpqmgKkq41V"}, {"templateId": "fldHmoMDQe7ZFSfilsZV3pDU", "width": 150}, {"templateId": "fldvK8YCif8iDbqXmWETzATJ"}], "groups": []}], "fields": [{"type": "FORMULA", "templateId": "fldm56dudVh37vClEcKCAFMi", "privilege": "TYPE_EDIT", "name": "Summary", "property": {"expressionTemplate": "{fld4nlWtEUWkBmpqmgKkq41V}&'-'& {fldDwyzXY5RRo5za2pzao1b4}&'-'& {fldHmoMDQe7ZFSfilsZV3pDU}"}, "primary": true}, {"type": "SINGLE_TEXT", "templateId": "fldDwyzXY5RRo5za2pzao1b4", "privilege": "FULL_EDIT", "name": "Name", "required": false, "primary": false}, {"type": "URL", "templateId": "fldwZ5iyY7zRJhLTqSj3TMIH", "privilege": "FULL_EDIT", "name": "Website", "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldwqSzOd7Jbl35qcCUkgwBb", "privilege": "FULL_EDIT", "name": "Company", "primary": false}, {"type": "EMAIL", "templateId": "fldqGYNRBlqI5j8V2myNJemk", "privilege": "FULL_EDIT", "name": "Email", "required": false, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fld4nlWtEUWkBmpqmgKkq41V", "privilege": "FULL_EDIT", "name": "Status", "property": {"options": [{"id": "opttIrI6Pm4cTx0kIQpyHQXe", "name": "Subscribed", "color": "green5"}, {"id": "opt0ettGQvaMltPaB9ahX15I", "name": "Unsubscribed", "color": "red5"}, {"id": "optX8Ju2sWtMHOZruXpYX4TU", "name": "Cleaned", "color": "orange"}, {"id": "opt74RGPxiogNRwqte4fJX6H", "name": "No Response", "color": "blue"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldHmoMDQe7ZFSfilsZV3pDU", "privilege": "FULL_EDIT", "name": "<PERSON><PERSON>", "property": {"options": [{"id": "optFMpmOaMtohDMkNWEpf9yK", "name": "Day1", "color": "tangerine1"}, {"id": "optNhyjOmfbd6mRohI7URDN4", "name": "Day2", "color": "tangerine2"}, {"id": "opts6aRhE6apDpEyoqFpq5YC", "name": "Day4", "color": "tangerine3"}, {"id": "optwmYZ2ilbKedytVaFm9O06", "name": "Day7", "color": "tangerine4"}, {"id": "optcnLYIKk9pXtkxrYs1DhQh", "name": "Day14", "color": "tangerine5"}], "defaultValue": ""}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldvK8YCif8iDbqXmWETzATJ", "privilege": "FULL_EDIT", "name": "Note", "primary": false}], "records": [{"templateId": "rectlZ7lrLJXKgg3uUv6A3DM", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opt0ettGQvaMltPaB9ahX15I"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["optFMpmOaMtohDMkNWEpf9yK"], "fldm56dudVh37vClEcKCAFMi": "Unsubscribed-<PERSON>-Day1", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://ideaspark.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "IdeaSpark\t"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Unsubscribed"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day1"], "fldm56dudVh37vClEcKCAFMi": "Unsubscribed-<PERSON>-Day1", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://ideaspark.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "IdeaSpark\t"}}, {"templateId": "recsJj07dMIRsiLs3rNPVS2Q", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opttIrI6Pm4cTx0kIQpyHQXe"], "fldDwyzXY5RRo5za2pzao1b4": "Bella Sun", "fldHmoMDQe7ZFSfilsZV3pDU": ["optNhyjOmfbd6mRohI7URDN4"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Bella Sun-Day2", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://softco.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "SoftCo"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Subscribed"], "fldDwyzXY5RRo5za2pzao1b4": "Bella Sun", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day2"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Bella Sun-Day2", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://softco.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "SoftCo"}}, {"templateId": "rec6YdM1sSoLapy8sbmRmCqI", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opttIrI6Pm4cTx0kIQpyHQXe"], "fldDwyzXY5RRo5za2pzao1b4": "Alex <PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["optFMpmOaMtohDMkNWEpf9yK"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Alex Star-Day1", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://widgetz.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "Widgetz Inc."}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Subscribed"], "fldDwyzXY5RRo5za2pzao1b4": "Alex <PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day1"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Alex Star-Day1", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://widgetz.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "Widgetz Inc."}}, {"templateId": "recBqLvEKRO5t2V8neqIdbl2", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opttIrI6Pm4cTx0kIQpyHQXe"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["opts6aRhE6apDpEyoqFpq5YC"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-<PERSON>-Day4", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://datafirm.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "DataFirm"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Subscribed"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day4"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-<PERSON>-Day4", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://datafirm.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "DataFirm"}}, {"templateId": "recfaHweA7rNYgQxG46AVu3Z", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opt0ettGQvaMltPaB9ahX15I"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["optwmYZ2ilbKedytVaFm9O06"], "fldm56dudVh37vClEcKCAFMi": "Unsubscribed-<PERSON>-Day7", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://adstream.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "AdStream"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Unsubscribed"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day7"], "fldm56dudVh37vClEcKCAFMi": "Unsubscribed-<PERSON>-Day7", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://adstream.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "AdStream"}}, {"templateId": "recU7u2WuCz9kta3RcSZvSjr", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opttIrI6Pm4cTx0kIQpyHQXe"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["optFMpmOaMtohDMkNWEpf9yK"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Ethan Wave-Day1", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://logictree.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "LogicTree"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Subscribed"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day1"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Ethan Wave-Day1", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://logictree.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "LogicTree"}}, {"templateId": "recLzL3h8Ayd9vXMAsmX1uah", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opttIrI6Pm4cTx0kIQpyHQXe"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["optwmYZ2ilbKedytVaFm9O06"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-<PERSON>-Day7", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://creativehq.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "CreativeHQ\t"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Subscribed"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day7"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-<PERSON>-Day7", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://creativehq.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "CreativeHQ\t"}}, {"templateId": "recfqWAn39m9JvyknHagYGt5", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opttIrI6Pm4cTx0kIQpyHQXe"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["opts6aRhE6apDpEyoqFpq5YC"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-<PERSON>-Day4", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "Positive interest in Bika.ai.", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://newtech.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "NewTech"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Subscribed"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day4"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-<PERSON>-Day4", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "Positive interest in Bika.ai.", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://newtech.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "NewTech"}}, {"templateId": "reckRikWA6FwHKIlTUGkEcDu", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opttIrI6Pm4cTx0kIQpyHQXe"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["optNhyjOmfbd6mRohI7URDN4"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-<PERSON>-Day2", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://brightly.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "<PERSON>ly"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Subscribed"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day2"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-<PERSON>-Day2", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://brightly.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "<PERSON>ly"}}, {"templateId": "recioA67FcDwYqZrdqzZ30Bo", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opttIrI6Pm4cTx0kIQpyHQXe"], "fldDwyzXY5RRo5za2pzao1b4": "Ivan <PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["optcnLYIKk9pXtkxrYs1DhQh"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Ivan Stream-Day14", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://bluebay.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "BlueBay"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Subscribed"], "fldDwyzXY5RRo5za2pzao1b4": "Ivan <PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day14"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Ivan Stream-Day14", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldvK8YCif8iDbqXmWETzATJ": "", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://bluebay.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "BlueBay"}}, {"templateId": "recD2ogEyzMg0R7bFi4UWfhH", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opt0ettGQvaMltPaB9ahX15I"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["opts6aRhE6apDpEyoqFpq5YC"], "fldm56dudVh37vClEcKCAFMi": "Unsubscribed-<PERSON>-Day4", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>\t", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://digitwave.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "DigitWave"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Unsubscribed"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day4"], "fldm56dudVh37vClEcKCAFMi": "Unsubscribed-<PERSON>-Day4", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>\t", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://digitwave.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "DigitWave"}}, {"templateId": "rec6RB9FzDCPz21RebMQyFOl", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opttIrI6Pm4cTx0kIQpyHQXe"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["optNhyjOmfbd6mRohI7URDN4"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Olivia Horizon-Day2", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://trendmap.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "TrendMap"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["Subscribed"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day2"], "fldm56dudVh37vClEcKCAFMi": "Subscribed-Olivia Horizon-Day2", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://trendmap.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "TrendMap"}}, {"templateId": "recS1vrWaTp26rXOzUEyIfH6", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opt74RGPxiogNRwqte4fJX6H"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["optcnLYIKk9pXtkxrYs1DhQh"], "fldm56dudVh37vClEcKCAFMi": "No Response-<PERSON>-Day14", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://techspark.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "TechSpark"}, "values": {"fld4nlWtEUWkBmpqmgKkq41V": ["No Response"], "fldDwyzXY5RRo5za2pzao1b4": "<PERSON>", "fldHmoMDQe7ZFSfilsZV3pDU": ["Day14"], "fldm56dudVh37vClEcKCAFMi": "No Response-<PERSON>-Day14", "fldqGYNRBlqI5j8V2myNJemk": "<EMAIL>", "fldwZ5iyY7zRJhLTqSj3TMIH": "https://techspark.fake", "fldwqSzOd7Jbl35qcCUkgwBb": "TechSpark"}}]}, {"resourceType": "AUTOMATION", "templateId": "atoTDPVrROMSHV96ILO3A43L", "name": "Email Automation Workflow", "description": "The Email Automation Workflow sends emails on Day 1, Day 2, Day 4, Day 7, and Day 14, with customizable content for each step. It tracks the progress of each email sent, ensuring timely follow-ups without manual effort.\n\n*Tip: Please replace the unsubscribe link in your email with the share link to the \"Unsubscribe Form\" you’ve created.", "triggers": [{"triggerType": "RECORD_CREATED", "templateId": "trgXDgMw85HZFXPfWtat4Ukj", "description": "When a new audience is added (new record created).", "input": {"type": "DATABASE", "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}], "actions": [{"templateId": "actEEddUsY6sny2nIFL5iFOT", "description": "Send the first email (Day 1).", "actionType": "SEND_EMAIL", "input": {"subject": "Hi <%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>, I’m <PERSON> from Bika.ai!", "body": {"markdown": "Hi <%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>,\n\nI hope you're doing well! My name is <PERSON>, and I’m with [Bika.ai](https://bika.ai/). We help businesses automate routine tasks, streamline workflows, and scale operations efficiently.\n\nI’m reaching out because I think you might find our platform helpful if you’re looking to save time and improve productivity.\n\nWould you be open to a quick chat about how Bika.ai could fit into your current operations? Let me know if you’re interested, I’d be happy to show you how it works!\n\nBest regards,\n\n<PERSON>er Success at Bika.ai\n\n---\n\n> Want to change how you receive these emails?You can [unsubscribe from this list](https://bika.ai/template).\n\n> Please do not reply to this email. For any inquiries, contact [<EMAIL>](mailto:<EMAIL>)."}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldqGYNRBlqI5j8V2myNJemk.value %>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}, {"templateId": "actHGnJxhTtmWJAE5Z1PSwpQ", "description": "Identify recipients who have received the email", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld4nlWtEUWkBmpqmgKkq41V", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opttIrI6Pm4cTx0kIQpyHQXe"}}]}, "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actMayCukju8bGCmvUXNFKh0", "description": "Update email sent status to \"Day 1\" and move them to the Day 1 view.", "actionType": "UPDATE_RECORD", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _renderRecordIdsAsList(_actions.actHGnJxhTtmWJAE5Z1PSwpQ.records) %>", "fieldKeyType": "TEMPLATE_ID", "data": {"fldHmoMDQe7ZFSfilsZV3pDU": ["optFMpmOaMtohDMkNWEpf9yK"]}, "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actTHPAjohhZYsqfp7ePGhkS", "description": "Wait for 1 day.", "actionType": "DELAY", "input": {"type": "DELAY", "unit": "MINUTE", "value": 1}}, {"templateId": "act06eE2FZHa5NSymYJiwB1r", "description": "Find subscribers who are still subscribed in the Day 1 view.", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "viwHH5ubZFUyuD5qJjkSahis", "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actTo6Q6tSb68aXhMK8z6Epb", "description": "Enter the sending loop.", "actionType": "LOOP", "input": {"type": "PREV_ACTION", "actionTemplateId": "act06eE2FZHa5NSymYJiwB1r", "path": "records"}, "actions": [{"description": "Send the second email (Day 2).", "actionType": "SEND_EMAIL", "templateId": "actrOMWALcHapi3TKtDZWwBQ", "input": {"subject": "<%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>, did you see my message about Bika.ai?", "body": {"markdown": "Hi <%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>,\n\nI wanted to quickly follow up on my previous email. I understand you're busy, but I believe [Bika.ai](https://bika.ai/) could really help streamline some of your processes.\n\nWe specialize in automating repetitive tasks, allowing you to focus more on strategic work that drives growth.\n\nIf you’d like, I can walk you through how Bika.ai could help you save time. Let me know what you think!\n\nBest,\n\n<PERSON> Success at Bika.ai\n\n---\n\n> Want to change how you receive these emails?You can [unsubscribe from this list](https://bika.ai/template).\n\n> Please do not reply to this email. For any inquiries, contact [<EMAIL>](mailto:<EMAIL>)."}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldqGYNRBlqI5j8V2myNJemk.value %>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}, {"templateId": "actgej14xVxxl5tKL6t9UqrC", "description": "Update email status to \"Day 2\" and move them to the Day 2 view.", "actionType": "UPDATE_RECORD", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _renderRecordIdsAsList(_actions.act06eE2FZHa5NSymYJiwB1r.records) %>", "fieldKeyType": "TEMPLATE_ID", "data": {"fldHmoMDQe7ZFSfilsZV3pDU": ["optNhyjOmfbd6mRohI7URDN4"]}, "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actPttbELIPrZxWwMN40LF9z", "description": "Wait for 2 days", "actionType": "DELAY", "input": {"type": "DELAY", "unit": "MINUTE", "value": 2}}, {"templateId": "act44F2l7lFjMGIoh3wNjyq9", "description": "Find subscribers who are still subscribed in the Day 2 view", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "viwbYK93ZohRBoi5S4SLwOIL", "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actb1Sc912YgVAK0MDBJpuRg", "actionType": "LOOP", "input": {"type": "PREV_ACTION", "actionTemplateId": "act44F2l7lFjMGIoh3wNjyq9", "path": "records"}, "actions": [{"description": "Send the third email (Day 4)", "actionType": "SEND_EMAIL", "templateId": "actlCH0AZq1KpeuZFBUkqzcG", "input": {"subject": "<%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>, do you want to save time with automation?", "body": {"markdown": "Hi <%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>,\n\nI know you’re probably juggling a lot of things, so I’ll keep this brief.\n\nAt [Bika.ai](https://bika.ai/), we help businesses automate tasks like email outreach, project management, and data tracking. Our platform is designed to help you reduce manual work and focus on the things that matter most.\n\nI’d love to show you how we could help. Let me know if you’re open to a quick demo.\n\nLooking forward to hearing from you!\n\nLinda\n\n<PERSON>er Success at Bika.ai\n\n---\n\n> Want to change how you receive these emails?You can [unsubscribe from this list](https://bika.ai/en/template).\n\n> Please do not reply to this email. For any inquiries, contact [<EMAIL>](mailto:<EMAIL>)."}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldqGYNRBlqI5j8V2myNJemk.value %>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}, {"templateId": "act6A5QsmKRwK8Dpz4mh1rHf", "description": "Update email status to \"Day 4\" and move them to the Day 4 view", "actionType": "UPDATE_RECORD", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _renderRecordIdsAsList(_actions.act44F2l7lFjMGIoh3wNjyq9.records) %>", "fieldKeyType": "TEMPLATE_ID", "data": {"fldHmoMDQe7ZFSfilsZV3pDU": ["opts6aRhE6apDpEyoqFpq5YC"]}, "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actOBjWAI6K5RuCLEo5JBcJU", "description": "Wait for 3 days", "actionType": "DELAY", "input": {"type": "DELAY", "unit": "MINUTE", "value": 3}}, {"templateId": "actcqTyVf700Wp0TylKiJ2Js", "description": "Find subscribers who are still subscribed in the Day 4 view", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "viwEY5xnj0s2OaqLLYsrfelJ", "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actIEdHglc1YgKzfUhDodksN", "actionType": "LOOP", "input": {"type": "PREV_ACTION", "actionTemplateId": "actcqTyVf700Wp0TylKiJ2Js", "path": "records"}, "actions": [{"description": "Send the fourth email (Day 7)", "actionType": "SEND_EMAIL", "templateId": "actc4urVcS0c8sGAlLhvD0k0", "input": {"subject": "<%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>, still curious about how automation can help?", "body": {"markdown": "Hi <%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>,\n\nI just wanted to check in again.\n\nIf you’re still thinking about ways to improve productivity and streamline your daily processes, [Bika.ai](https://bika.ai/) could be a great fit. We’re all about making automation simple and effective, so you can save time and increase efficiency.\n\nWould you be interested in a quick conversation to explore this further?\n\nBest regards,\n\n<PERSON> at Bika.ai\n\n---\n\n> Want to change how you receive these emails?You can [unsubscribe from this list](https://bika.ai/en/template).\n\n> Please do not reply to this email. For any inquiries, contact [<EMAIL>](mailto:<EMAIL>)."}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldqGYNRBlqI5j8V2myNJemk.value %>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}, {"templateId": "actcNBrUWpbesi4ZrcMzyX1G", "description": "Update email status to \"Day 7\" and move them to the Day 7 view", "actionType": "UPDATE_RECORD", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _renderRecordIdsAsList(_actions.actcqTyVf700Wp0TylKiJ2Js.records) %>", "fieldKeyType": "TEMPLATE_ID", "data": {"fldHmoMDQe7ZFSfilsZV3pDU": ["optwmYZ2ilbKedytVaFm9O06"]}, "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actTRAEiKzZMAl6oa3ZWRU7D", "description": "Wait for 7 days", "actionType": "DELAY", "input": {"type": "DELAY", "unit": "MINUTE", "value": 7}}, {"templateId": "acteg7HjzmLZt4ZGmOUtcQnC", "description": "Find subscribers who are still subscribed in the Day 7 view", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "viwFg757t8zomkFH8YsB06ji", "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actELqbEKXE45CxlAGCxnuj8", "actionType": "LOOP", "input": {"type": "PREV_ACTION", "actionTemplateId": "acteg7HjzmLZt4ZGmOUtcQnC", "path": "records"}, "actions": [{"description": "Send the fifth email (Day 14)", "actionType": "SEND_EMAIL", "templateId": "actO6cvWj5dW2UaulFoXU6YO", "input": {"subject": "<%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>, let’s talk about how Bika.ai can help you scale", "body": {"markdown": "Hi <%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldDwyzXY5RRo5za2pzao1b4.value %>,\n\nI wanted to follow up one last time.\n\nIf you’ve been considering how to improve workflows and automate your processes, [Bika.ai](https://bika.ai/) could really help. We’ve helped many businesses in your space save time and improve efficiency with our automation tools.\n\nIf you’re open to it, I’d be happy to show you exactly how it works. Let me know if you're interested!\n\nBest,\n\n<PERSON> Success at Bika.ai\n\n---\n\n> Want to change how you receive these emails?You can [unsubscribe from this list](https://bika.ai/en/template).\n\n> Please do not reply to this email. For any inquiries, contact [<EMAIL>](mailto:<EMAIL>)."}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trgXDgMw85HZFXPfWtat4Ukj.record.cells.fldqGYNRBlqI5j8V2myNJemk.value %>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}, {"templateId": "actWLMl0YcjykTpQSj9qXR1y", "description": "Update email status to \"Day 14\" and move them to the Day 14 view", "actionType": "UPDATE_RECORD", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _renderRecordIdsAsList(_actions.acteg7HjzmLZt4ZGmOUtcQnC.records) %>", "fieldKeyType": "TEMPLATE_ID", "data": {"fldHmoMDQe7ZFSfilsZV3pDU": ["optcnLYIKk9pXtkxrYs1DhQh"]}, "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "acteMb82vAkgLUfy4USnyqen", "description": "Wait for 2 days", "actionType": "DELAY", "input": {"type": "DELAY", "unit": "MINUTE", "value": 2}}, {"templateId": "actXu04B9lJcCfiavkBwJSwW", "description": "Find subscribers who are still subscribed in the Day 14 view", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_VIEW", "viewTemplateId": "viwzlnnnXICYCJ6EEzKrEJI8", "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actWWWT3STA0fGtZxVeNopFm", "description": "Mark these contacts as \"No Response\"", "actionType": "UPDATE_RECORD", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _renderRecordIdsAsList(_actions.actXu04B9lJcCfiavkBwJSwW.records) %>", "fieldKeyType": "TEMPLATE_ID", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opt74RGPxiogNRwqte4fJX6H"]}, "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}]}, {"resourceType": "FORM", "templateId": "fomuWWaZRLHA6zwCPzHhPEO0", "name": "Unsubscribe Form", "description": "We’re sorry to see you go!😢 If you still want to unsubscribe from our emails, simply submit this form, and we’ll make sure you no longer receive updates from us. But hey, if you change your mind, we’ll always be here with fresh updates when you’re ready to reconnect!\n\n*Tip: Please replace the unsubscribe link in your email with the share link to the \"Unsubscribe Form\" you’ve created.", "brandLogo": {"type": "EMOJI", "backgroundColor": "", "emoji": "😖"}, "formType": "DATABASE", "databaseTemplateId": "datns84vuOLM4Q33JMBUI5eG", "metadata": {"type": "VIEW", "viewTemplateId": "viwQo8UNdPvwep4XTN5HjGb7"}}, {"resourceType": "AUTOMATION", "templateId": "atouNa7oISoOhDNpeJmwdkkK", "name": "Unsubscribed Automation", "description": "An automated process that manages users who unsubscribe from emails.", "triggers": [{"triggerType": "FORM_SUBMITTED", "templateId": "trgqjEavaiWzzooYFXC3kem7", "description": "New User Email Unsubscribe.", "input": {"type": "FORM", "formTemplateId": "fomuWWaZRLHA6zwCPzHhPEO0"}}], "actions": [{"templateId": "act6UQ0gLdEd7x51dNu9b8RE", "description": "Retrieve the Matching Record", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldqGYNRBlqI5j8V2myNJemk", "fieldType": "EMAIL", "clause": {"operator": "Is", "value": "<%= _triggers.trgqjEavaiWzzooYFXC3kem7.record.cells.fldk3sRoYnOjpbtzrmDVA1vC.value %>"}}, {"fieldTemplateId": "fld4nlWtEUWkBmpqmgKkq41V", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opttIrI6Pm4cTx0kIQpyHQXe"}}]}, "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}, {"templateId": "actLN4AQ9HHAWsL1WNI9oOTA", "description": "<PERSON> the Email Status as \"Unsubscribed\"", "actionType": "UPDATE_RECORD", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _renderRecordIdsAsList(_actions.act6UQ0gLdEd7x51dNu9b8RE.records) %>", "fieldKeyType": "TEMPLATE_ID", "data": {"fld4nlWtEUWkBmpqmgKkq41V": ["opt0ettGQvaMltPaB9ahX15I"]}, "databaseTemplateId": "dat75WYXko2I5VFbV09P78No"}}]}, {"resourceType": "DATABASE", "templateId": "datns84vuOLM4Q33JMBUI5eG", "name": "Unsubscribed Contacts", "description": "The Unsubscribed Contacts table records the contacts who have opted out of your outreach campaign. Each entry includes their email, unsubscribe date, and the reason (if available). This ensures that your email automation process complies with opt-out requests, and no further communication is sent to these contacts.", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwQo8UNdPvwep4XTN5HjGb7", "name": "Form", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldk3sRoYnOjpbtzrmDVA1vC", "hidden": false, "width": 219}, {"templateId": "fld4BkYH5jE3KbFrHyaQvVgA", "hidden": false, "width": 233}, {"templateId": "fldtnsqghvFLiWlxzMO9TaD9", "hidden": true}]}, {"type": "TABLE", "templateId": "viwBOni0tKNj2p1CrCKnkYeC", "name": "All", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldk3sRoYnOjpbtzrmDVA1vC", "hidden": false}, {"templateId": "fld4BkYH5jE3KbFrHyaQvVgA", "hidden": false, "width": 285}, {"templateId": "fldtnsqghvFLiWlxzMO9TaD9", "hidden": false}], "groups": []}], "fields": [{"type": "EMAIL", "templateId": "fldk3sRoYnOjpbtzrmDVA1vC", "privilege": "TYPE_EDIT", "name": "Email", "required": true, "primary": true}, {"type": "LONG_TEXT", "templateId": "fld4BkYH5jE3KbFrHyaQvVgA", "privilege": "FULL_EDIT", "name": "May we ask why you’ve unsubscribed? ", "description": "Your feedback helps us improve.", "primary": false}, {"type": "CREATED_TIME", "templateId": "fldtnsqghvFLiWlxzMO9TaD9", "privilege": "FULL_EDIT", "name": "Unsubscribed Date", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": true}, "primary": false}], "records": [{"templateId": "receA2hpOacZCVAcHyxIvvhH", "data": {"fld4BkYH5jE3KbFrHyaQvVgA": "Needs more tailored offerings.", "fldk3sRoYnOjpbtzrmDVA1vC": "<EMAIL>", "fldtnsqghvFLiWlxzMO9TaD9": null}, "values": {"fld4BkYH5jE3KbFrHyaQvVgA": "Needs more tailored offerings.", "fldk3sRoYnOjpbtzrmDVA1vC": "<EMAIL>", "fldtnsqghvFLiWlxzMO9TaD9": "2024-11-15 07:31"}}, {"templateId": "recgFpdqAKNyXj1oImopUI8R", "data": {"fld4BkYH5jE3KbFrHyaQvVgA": "Found another solution.", "fldk3sRoYnOjpbtzrmDVA1vC": "<EMAIL>", "fldtnsqghvFLiWlxzMO9TaD9": null}, "values": {"fld4BkYH5jE3KbFrHyaQvVgA": "Found another solution.", "fldk3sRoYnOjpbtzrmDVA1vC": "<EMAIL>", "fldtnsqghvFLiWlxzMO9TaD9": "2024-11-15 07:31"}}, {"templateId": "recO0hqWcGtlbKBO5gRFg47v", "data": {"fld4BkYH5jE3KbFrHyaQvVgA": "Exploring different vendors.", "fldk3sRoYnOjpbtzrmDVA1vC": "<EMAIL>", "fldtnsqghvFLiWlxzMO9TaD9": null}, "values": {"fld4BkYH5jE3KbFrHyaQvVgA": "Exploring different vendors.", "fldk3sRoYnOjpbtzrmDVA1vC": "<EMAIL>", "fldtnsqghvFLiWlxzMO9TaD9": "2024-11-15 07:31"}}]}], "initMissions": []}