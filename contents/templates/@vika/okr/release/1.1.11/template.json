{"templateId": "@vika/okr", "name": {"en": "<PERSON><PERSON>", "ja": "<PERSON><PERSON>", "zh-TW": "維格 OKR", "zh-CN": "维格 OKR "}, "description": {"en": "This is a template for managing team quarterly OKRs in Vika, helping teams better set and track goals.", "ja": "これはVikaでチームの四半期のOKRを管理するためのテンプレートで、チームが目標をよりよく設定し、追跡するのを支援します。", "zh-TW": "這是一個用於維格管理團隊季度 OKR 的模板，幫助團隊更好地制定和追踪目標。", "zh-CN": "这是一个用于维格管理团队季度 OKR 的模板，帮助团队更好地制定和追踪目标。"}, "cover": "/assets/template/template-cover-vika-okr.png", "author": "Na<PERSON>a<PERSON>on <<EMAIL>>", "category": "project", "keywords": {"zh-CN": "轮值, 值班, 提醒, 企微, 目标管理, 团队管理, 自动化", "zh-TW": "輪值, 值班, 提醒, 企微, 目標管理, 團隊管理, 自動化", "en": "rotation, duty, reminder, wecom, goal management, team management, automation", "ja": "ローテーション, 当番, リマインダー, 企業微信, ゴール管理, チーム管理, 自動化"}, "personas": {"en": "admin, manager, project manager, HR", "ja": "管理者, マネージャー, プロジェクトマネージャー, 人事", "zh-TW": "管理員, 管理者, 项目经理, 人力资源", "zh-CN": "管理员, 经理, 项目经理, 人力资源"}, "useCases": {"zh-CN": "企业微信自动化提醒, 团队目标管理, OKR制定和评审, 提高团队一致性, 时间节省工具, 团队成员参与度提升, 流程自动化, 团队绩效管理, 目标追踪工具, 高效团队管理, 企业目标管理, 项目目标管理, 团队沟通与协作, 自动提醒和报告, 目标进度跟踪, 管理者监督工具, 任务分配和跟进, 团队绩效分析, 目标达成度评估, 周期性目标设定, 个人和团队目标对齐, 绩效考核和奖励, 企业战略规划, 团队协同效能提升", "en": "WeCom automation reminder, team goal management, OKR formulation and review, improve team consistency, time-saving tools, increase team member participation, process automation, team performance management, goal tracking tools, efficient team management, enterprise goal management, project goal management, team communication and collaboration, automatic reminders and reports, goal progress tracking, manager supervision tools, task assignment and follow-up, team performance analysis, goal achievement evaluation, periodic goal setting, personal and team goal alignment, performance appraisal and rewards, enterprise strategic planning, team collaborative efficiency improvement", "ja": "企業微信の自動化リマインダー, チームの目標管理, OKRの策定とレビュー, チームの一貫性向上, 時間節約ツール, チームメンバーの参加度向上, プロセスの自動化, チームのパフォーマンス管理, 目標追跡ツール, 効率的なチーム管理, 企業の目標管理, プロジェクトの目標管理, チームのコミュニケーションと協力, 自動リマインダーとレポート, 目標の進捗状況の追跡, マネージャーの監督ツール, タスクの割り当てとフォローアップ, チームのパフォーマンス分析, 目標の達成度評価, 周期的な目標設定, 個人とチームの目標の整合, パフォーマンス評価と報酬, 企業の戦略的計画, チームの協力効率向上", "zh-TW": "企業微信自動提醒, 團隊目標管理, OKR制定和審核, 提高團隊一致性, 節省時間工具, 提高團隊成員參與度, 流程自動化, 團隊績效管理, 目標追蹤工具, 高效團隊管理, 企業目標管理, 項目目標管理, 團隊溝通與協作, 自動提醒和報告, 目標進度追蹤, 管理者監督工具, 任務分配和跟進, 團隊績效分析, 目標達成度評估, 週期性目標設定, 個人和團隊目標對齊, 績效考核和獎勵, 企業戰略規劃, 團隊協同效能提升"}, "visibility": "PRIVATE", "schemaVersion": "v1", "version": "1.1.11", "resources": [{"resourceType": "DATABASE", "templateId": "okr_database", "name": {"en": "OKR Database", "zh-CN": "OKR 数据库", "zh-TW": "OKR 數據庫", "ja": "OKR データベース"}, "description": {"en": "Team members' OKRs", "zh-CN": "存储团队成员的季度 OKR。", "zh-TW": "存儲團隊成員的季度 OKR。", "ja": "チームメンバーの四半期OKRを保存します。"}, "permissions": [], "type": "DATUM", "views": [{"templateId": "all_okr_view", "name": {"zh-CN": "所有 OKR", "zh-TW": "所有 OKR", "en": "All OKR", "ja": "すべてのOKR"}, "type": "TABLE"}, {"templateId": "my_okr_view", "name": {"zh-CN": "我的 OKR", "zh-TW": "我的 OKR", "en": "My OKR", "ja": "私のOKR"}, "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "okr_creator", "operator": "Is", "fieldType": "CREATED_BY", "value": ["Self"]}]}, "type": "TABLE"}, {"templateId": "okr_review_view", "name": {"zh-CN": "我负责评审的 OKR", "zh-TW": "我負責評審的 OKR", "en": "OKR I am responsible for reviewing", "ja": "私が責任を持ってレビューするOKR"}, "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "okr_align_superior", "operator": "Is", "fieldType": "MEMBER", "value": ["Self"]}]}, "type": "TABLE"}, {"description": {"zh-CN": "用来控制填写OKR任务的结构", "en": "Used to control the structure of the task of filling in OKR", "zh-TW": "用來控制填寫OKR任務的結構", "ja": "OKRの記入タスクの構造を制御するために使用されます"}, "templateId": "submit_okr_view", "name": {"zh-CN": "提交OKR视图", "zh-TW": "提交OKR視圖", "en": "Submit OKR View", "ja": "OKR提出ビュー"}, "type": "TABLE", "fields": [{"templateId": "okr_quarter"}, {"templateId": "okr_creator"}, {"templateId": "okr_review"}, {"templateId": "okr_details"}, {"templateId": "okr_align_superior"}, {"templateId": "okr_self_score"}, {"templateId": "okr_superior_score", "hidden": true}, {"templateId": "okr_attachment"}, {"templateId": "okr_submit_time"}]}], "fields": [{"templateId": "okr_quarter", "name": {"zh-CN": "季度", "zh-TW": "季度", "en": "Quarter", "ja": "四半期"}, "description": {"zh-CN": "OKR 所属的季度", "zh-TW": "OKR 所屬的季度", "en": "The quarter to which the OKR belongs", "ja": "OKRが属する四半期"}, "required": true, "type": "SINGLE_SELECT", "property": {"options": [{"templateId": "2024q1", "name": "2024 Q1", "color": "blue"}, {"templateId": "2024q2", "name": "2024 Q2", "color": "green"}, {"templateId": "2024q3", "name": "2024 Q3", "color": "deepPurple"}, {"templateId": "2024q4", "name": "2024 Q4", "color": "red"}]}}, {"templateId": "okr_creator", "name": {"zh-CN": "提交人", "zh-TW": "提交人", "en": "Submitter", "ja": "提出者"}, "type": "CREATED_BY"}, {"templateId": "okr_review", "name": {"en": "What is your previous quarter progress? Review it.", "zh-CN": "上季度的 OKR 进展如何？请进行回顾。", "zh-TW": "上季度的 OKR 進展如何？請進行回顧。", "ja": "前四半期の進捗はどうですか？レビューしてください。"}, "description": {"zh-CN": "\n请列出本季度的新OKR和Milestone，示例：\n\n研发工程师R&D Engineer的OKR示例：\n- O: sprint: 确保AI服务AIServer等开发和每周迭代有Github Contribution\n  - KR: 完成数据分析师、QA机器人、GPT-4 Chat机器人、Form机器人、考试机器人相关Issue > 3\n  - KR: 每周Github contribution > 5 & develop分支PR > 1 & commit > 2 & 每周Github Issues close > 2\n  - KR: 日常关注和解答开源社区和客户用户提出的问题 相关 Issue >= 3\n- O: feat: AI Prompting和功能实现\n  - KR: AIServer的AIForm、Chatbot、QABot、AnalystBot的功能实现\n  - KR: 基于DataBus对URL、Attachment、Refiner进行数据训练\n  - KR: 实现PDF、TableBundle、TextBundle多模态数据的Embedding和产品需求实现\n\nProduct Manager、Marketing、Sales的OKR示例：\n\n- O1: 提升产品的市场份额和用户满意度\n    - KR1: 增加产品的市场份额至行业前三名\n    - KR2: 提升用户满意度调查得分至90分以上\n- O1: 实现销售目标并增加客户满意度\n    - KR1: 达到季度销售额目标，增长率不低于20%\n    - KR2: 提高客户满意度调查得分至85分以上\n- O2: 拓展新客户和市场份额\n    - KR1: 获得10个新客户并完成首次交易\n    - KR2: 将产品销售到2个新市场，并建立长期合作关系\n- O1: 提升品牌知名度和市场影响力\n    - KR1: 增加社交媒体关注者数至10万人\n    - KR2: 提高品牌在行业内的知名度，获得5次媒体报道\n- O2: 改善市场营销效果和转化率\n    - KR1: 提高营销活动的转化率至5%\n    - KR2: 优化网站流量和用户体验，将跳出率降低至50%以下\n\nMilestone:\n* 10月30日: xxxx\n* 11月30日: yyyy\n\n实际OKR根据组织的具体情况和目标进行定制。\n          ", "zh-TW": "\n請列出本季度的新OKR和Milestone，示例：\n\n研發工程師R&D Engineer的OKR示例：\n- O: sprint: 確保AI服務AIServer等開發和每週迭代有Github Contribution\n  - KR: 完成數據分析師、QA機器人、GPT-4 Chat機器人、Form機器人、考試機器人相關Issue > 3\n  - KR: 每週Github contribution > 5 & develop分支PR > 1 & commit > 2 & 每週Github Issues close > 2\n  - KR: 日常關注和解答開源社區和客戶用戶提出的問題 相關 Issue >= 3\n- O: feat: AI Prompting和功能實現\n  - KR: AIServer的AIForm、Chatbot、QABot、AnalystBot的功能實現\n  - KR: 基於DataBus對URL、Attachment、Refiner進行數據訓練\n  - KR: 實現PDF、TableBundle、TextBundle多模態數據的Embedding和產品需求實現\n\nProduct Manager、Marketing、Sales的OKR示例：\n\n- O1: 提升產品的市場份額和用戶滿意度\n    - KR1: 增加產品的市場份額至行業前三名\n    - KR2: 提升用戶滿意度調查得分至90分以上\n- O1: 實現銷售目標並增加客戶滿意度\n    - KR1: 達到季度銷售額目標，增長率不低於20%\n    - KR2: 提高客戶滿意度調查得分至85分以上\n- O2: 拓展新客戶和市場份額\n    - KR1: 獲得10個新客戶並完成首次交易\n    - KR2: 將產品銷售到2個新市場，並建立長期合作關係\n- O1: 提升品牌知名度和市場影響力\n    - KR1: 增加社交媒體關注者數至10萬人\n    - KR2: 提高品牌在行業內的知名度，獲得5次媒體報道\n- O2: 改善市場營銷效果和轉化率\n    - KR1: 提高營銷活動的轉化率至5%\n    - KR2: 優化網站流量和用戶體驗，將跳出率降低至50%以下\n\nMilestone:\n* 10月30日: xxxx\n* 11月30日: yyyy\n\n實際OKR根據組織的具體情況和目標進行定制。", "en": "\nPlease list the new OKRs and Milestones for this quarter, example:\n\nR&D Engineer's OKR example:\n- O: sprint: Ensure development and weekly iteration of AI services such as AIServer have Github Contribution\n  - KR: Complete > 3 issues related to Data Analyst, QA Robot, GPT-4 Chat Robot, Form Robot, Exam Robot\n  - KR: Weekly Github contribution > 5 & develop branch PR > 1 & commit > 2 & weekly Github Issues close > 2\n  - KR: Regularly pay attention to and answer questions raised by the open-source community and customer users, related Issue >= 3\n- O: feat: AI Prompting and feature implementation\n  - KR: Implementation of AIServer's AIForm, Chatbot, QABot, AnalystBot features\n  - KR: Data training based on DataBus for URL, Attachment, Refiner\n  - KR: Implementation of PDF, TableBundle, TextBundle multimodal data Embedding and product requirements\n\nProduct Manager, Marketing, Sales OKR example:\n\n- O1: Increase the market share and customer satisfaction of the product\n    - KR1: Increase the market share of the product to the top three in the industry\n    - KR2: Increase customer satisfaction survey score to above 90\n- O1: Achieve sales targets and increase customer satisfaction\n    - KR1: Achieve quarterly sales target, with a growth rate of not less than 20%\n    - KR2: Increase customer satisfaction survey score to above 85\n- O2: Expand new customers and market share\n    - KR1: Acquire 10 new customers and complete the first transaction\n    - KR2: Sell the product to 2 new markets and establish long-term cooperation\n- O1: Enhance brand awareness and market influence\n    - KR1: Increase the number of social media followers to 100,000\n    - KR2: Improve brand awareness in the industry, receive 5 media reports\n- O2: Improve marketing effectiveness and conversion rate\n    - KR1: Increase the conversion rate of marketing activities to 5%\n    - KR2: Optimize website traffic and user experience, reduce bounce rate to below 50%\n\nMilestone:\n* October 30: xxxx\n* November 30: yyyy\n\nActual OKRs are customized according to the specific situation and goals of the organization.", "ja": "\n本四半期の新しいOKRとマイルストーンをリストしてください、例:\n\nR&DエンジニアのOKR例:\n- O: sprint: AIServerなどのAIサービスの開発と週次イテレーションがGithub Contributionを持つことを確保する\n  - KR: データアナリスト、QAロボット、GPT-4チャットロボット、フォームロボット、試験ロボットに関連するIssue > 3を完了する\n  - KR: 週次Github contribution > 5 & developブランチPR > 1 & commit > 2 & 週次Github Issues close > 2\n  - KR: オープンソースコミュニティと顧客ユーザーから提出された質問に定期的に注意を払い、回答する、関連Issue >= 3\n- O: feat: AI Promptingと機能実装\n  - KR: AIServerのAIForm、Chatbot、QABot、AnalystBotの機能実装\n  - KR: DataBusを基にしたURL、Attachment、Refinerのデータトレーニング\n  - KR: PDF、TableBundle、TextBundleのマルチモーダルデータのEmbeddingと製品要件の実装\n\nプロダクトマネージャー、マーケティング、セールスのOKR例:\n\n- O1: 製品の市場シェアと顧客満足度を向上させる\n    - KR1: 業界トップ3内に製品の市場シェアを増加させる\n    - KR2: 顧客満足度調査のスコアを90以上に向上させる\n- O1: 販売目標を達成し、顧客満足度を向上させる\n    - KR1: 季節販売目標を達成し、成長率を20%以上にする\n    - KR2: 顧客満足度調査のスコアを85以上に向上させる\n- O2: 新規顧客と市場シェアを拡大する\n    - KR1: 10の新規顧客を獲得し、初回取引を完了する\n    - KR2: 2つの新市場に製品を販売し、長期的な協力を確立する\n- O1: ブランド認知度と市場影響力を向上させる\n    - KR1: ソーシャルメディアのフォロワー数を10万人に増加させる\n    - KR2: 業界内でのブランド認知度を向上させ、5回のメディア報道を受ける\n- O2: マーケティングの効果と転換率を改善する\n    - KR1: マーケティング活動の転換率を5%に向上させる\n    - KR2: ウェブサイトのトラフィックとユーザー体験を最適化し、直帰率を50%以下に減少させる\n\nマイルストーン:\n* 10月30日: xxxx\n* 11月30日: yyyy\n\n実際のOKRは、組織の具体的な状況と目標に応じてカスタマイズされます。"}, "required": true, "type": "LONG_TEXT"}, {"templateId": "okr_details", "name": {"zh-CN": "What is your this new quarter OKRs and Milestones?", "zh-TW": "What is your this new quarter OKRs and Milestones?", "en": "What is your this new quarter OKRs and Milestones?", "ja": "What is your this new quarter OKRs and Milestones?"}, "description": {"zh-CN": "\n请列出本季度的新OKR和Milestone，示例：\n\n研发工程师R&D Engineer的OKR示例：\n- O: sprint: 确保AI服务AIServer等开发和每周迭代有Github Contribution\n  - KR: 完成数据分析师、QA机器人、GPT-4 Chat机器人、Form机器人、考试机器人相关Issue > 3\n  - KR: 每周Github contribution > 5 & develop分支PR > 1 & commit > 2 & 每周Github Issues close > 2\n  - KR: 日常关注和解答开源社区和客户用户提出的问题 相关 Issue >= 3\n- O: feat: AI Prompting和功能实现\n  - KR: AIServer的AIForm、Chatbot、QABot、AnalystBot的功能实现\n  - KR: 基于DataBus对URL、Attachment、Refiner进行数据训练\n  - KR: 实现PDF、TableBundle、TextBundle多模态数据的Embedding和产品需求实现\n\nProduct Manager、Marketing、Sales的OKR示例：\n\n- O1: 提升产品的市场份额和用户满意度\n    - KR1: 增加产品的市场份额至行业前三名\n    - KR2: 提升用户满意度调查得分至90分以上\n- O1: 实现销售目标并增加客户满意度\n    - KR1: 达到季度销售额目标，增长率不低于20%\n    - KR2: 提高客户满意度调查得分至85分以上\n- O2: 拓展新客户和市场份额\n    - KR1: 获得10个新客户并完成首次交易\n    - KR2: 将产品销售到2个新市场，并建立长期合作关系\n- O1: 提升品牌知名度和市场影响力\n    - KR1: 增加社交媒体关注者数至10万人\n    - KR2: 提高品牌在行业内的知名度，获得5次媒体报道\n- O2: 改善市场营销效果和转化率\n    - KR1: 提高营销活动的转化率至5%\n    - KR2: 优化网站流量和用户体验，将跳出率降低至50%以下\n\nMilestone:\n* 10月30日: xxxx\n* 11月30日: yyyy\n\n实际OKR根据组织的具体情况和目标进行定制。\n          ", "zh-TW": "\n請列出本季度的新OKR和Milestone，示例：\n\n研發工程師R&D Engineer的OKR示例：\n- O: sprint: 確保AI服務AIServer等開發和每週迭代有Github Contribution\n  - KR: 完成數據分析師、QA機器人、GPT-4 Chat機器人、Form機器人、考試機器人相關Issue > 3\n  - KR: 每週Github contribution > 5 & develop分支PR > 1 & commit > 2 & 每週Github Issues close > 2\n  - KR: 日常關注和解答開源社區和客戶用戶提出的問題 相關 Issue >= 3\n- O: feat: AI Prompting和功能實現\n  - KR: AIServer的AIForm、Chatbot、QABot、AnalystBot的功能實現\n  - KR: 基於DataBus對URL、Attachment、Refiner進行數據訓練\n  - KR: 實現PDF、TableBundle、TextBundle多模態數據的Embedding和產品需求實現\n\nProduct Manager、Marketing、Sales的OKR示例：\n\n- O1: 提升產品的市場份額和用戶滿意度\n    - KR1: 增加產品的市場份額至行業前三名\n    - KR2: 提升用戶滿意度調查得分至90分以上\n- O1: 實現銷售目標並增加客戶滿意度\n    - KR1: 達到季度銷售額目標，增長率不低於20%\n    - KR2: 提高客戶滿意度調查得分至85分以上\n- O2: 拓展新客戶和市場份額\n    - KR1: 獲得10個新客戶並完成首次交易\n    - KR2: 將產品銷售到2個新市場，並建立長期合作關係\n- O1: 提升品牌知名度和市場影響力\n    - KR1: 增加社交媒體關注者數至10萬人\n    - KR2: 提高品牌在行業內的知名度，獲得5次媒體報道\n- O2: 改善市場營銷效果和轉化率\n    - KR1: 提高營銷活動的轉化率至5%\n    - KR2: 優化網站流量和用戶體驗，將跳出率降低至50%以下\n\nMilestone:\n* 10月30日: xxxx\n* 11月30日: yyyy\n\n實際OKR根據組織的具體情況和目標進行定制。", "en": "\nPlease list the new OKRs and Milestones for this quarter, example:\n\nR&D Engineer's OKR example:\n- O: sprint: Ensure development and weekly iteration of AI services such as AIServer have Github Contribution\n  - KR: Complete > 3 issues related to Data Analyst, QA Robot, GPT-4 Chat Robot, Form Robot, Exam Robot\n  - KR: Weekly Github contribution > 5 & develop branch PR > 1 & commit > 2 & weekly Github Issues close > 2\n  - KR: Regularly pay attention to and answer questions raised by the open-source community and customer users, related Issue >= 3\n- O: feat: AI Prompting and feature implementation\n  - KR: Implementation of AIServer's AIForm, Chatbot, QABot, AnalystBot features\n  - KR: Data training based on DataBus for URL, Attachment, Refiner\n  - KR: Implementation of PDF, TableBundle, TextBundle multimodal data Embedding and product requirements\n\nProduct Manager, Marketing, Sales OKR example:\n\n- O1: Increase the market share and customer satisfaction of the product\n    - KR1: Increase the market share of the product to the top three in the industry\n    - KR2: Increase customer satisfaction survey score to above 90\n- O1: Achieve sales targets and increase customer satisfaction\n    - KR1: Achieve quarterly sales target, with a growth rate of not less than 20%\n    - KR2: Increase customer satisfaction survey score to above 85\n- O2: Expand new customers and market share\n    - KR1: Acquire 10 new customers and complete the first transaction\n    - KR2: Sell the product to 2 new markets and establish long-term cooperation\n- O1: Enhance brand awareness and market influence\n    - KR1: Increase the number of social media followers to 100,000\n    - KR2: Improve brand awareness in the industry, receive 5 media reports\n- O2: Improve marketing effectiveness and conversion rate\n    - KR1: Increase the conversion rate of marketing activities to 5%\n    - KR2: Optimize website traffic and user experience, reduce bounce rate to below 50%\n\nMilestone:\n* October 30: xxxx\n* November 30: yyyy\n\nActual OKRs are customized according to the specific situation and goals of the organization.", "ja": "\n本四半期の新しいOKRとマイルストーンをリストしてください、例:\n\nR&DエンジニアのOKR例:\n- O: sprint: AIServerなどのAIサービスの開発と週次イテレーションがGithub Contributionを持つことを確保する\n  - KR: データアナリスト、QAロボット、GPT-4チャットロボット、フォームロボット、試験ロボットに関連するIssue > 3を完了する\n  - KR: 週次Github contribution > 5 & developブランチPR > 1 & commit > 2 & 週次Github Issues close > 2\n  - KR: オープンソースコミュニティと顧客ユーザーから提出された質問に定期的に注意を払い、回答する、関連Issue >= 3\n- O: feat: AI Promptingと機能実装\n  - KR: AIServerのAIForm、Chatbot、QABot、AnalystBotの機能実装\n  - KR: DataBusを基にしたURL、Attachment、Refinerのデータトレーニング\n  - KR: PDF、TableBundle、TextBundleのマルチモーダルデータのEmbeddingと製品要件の実装\n\nプロダクトマネージャー、マーケティング、セールスのOKR例:\n\n- O1: 製品の市場シェアと顧客満足度を向上させる\n    - KR1: 業界トップ3内に製品の市場シェアを増加させる\n    - KR2: 顧客満足度調査のスコアを90以上に向上させる\n- O1: 販売目標を達成し、顧客満足度を向上させる\n    - KR1: 季節販売目標を達成し、成長率を20%以上にする\n    - KR2: 顧客満足度調査のスコアを85以上に向上させる\n- O2: 新規顧客と市場シェアを拡大する\n    - KR1: 10の新規顧客を獲得し、初回取引を完了する\n    - KR2: 2つの新市場に製品を販売し、長期的な協力を確立する\n- O1: ブランド認知度と市場影響力を向上させる\n    - KR1: ソーシャルメディアのフォロワー数を10万人に増加させる\n    - KR2: 業界内でのブランド認知度を向上させ、5回のメディア報道を受ける\n- O2: マーケティングの効果と転換率を改善する\n    - KR1: マーケティング活動の転換率を5%に向上させる\n    - KR2: ウェブサイトのトラフィックとユーザー体験を最適化し、直帰率を50%以下に減少させる\n\nマイルストーン:\n* 10月30日: xxxx\n* 11月30日: yyyy\n\n実際のOKRは、組織の具体的な状況と目標に応じてカスタマイズされます。"}, "required": true, "validators": [{"timing": "ON_SUBMIT", "level": "WARNING", "type": "AI_PROMPT", "prompt": "Your task is to evaluate the OKR text in the text. All that is required is that the objectives and key results are outlined. Key results should be related to the objective.\nIMPORTANT: Specificity and measurability are not require.\nHere are four examples of OKR evaluations:\n\nExample 1:\nObjective: 提高公司产品在市场中的竞争力\nKey Result 1: 在下个季度末，产品的市场份额增加 5%\nKey Result 2: 客户满意度评分从 80% 提高到 90%\n\nResponse:\n{\n  \"success\": true\n}\n\nExample 2:\nObjective: 扩大品牌影响力\nKey Result 1: 增加广告投放\nKey Result 2: 提升社交媒体互动次数\n\nResponse:\n{\n  \"success\": false,\n    \"message\": \"目标虽然方向正确，但不够具体。关键结果 '增加广告投放' 缺乏可量化的指标，例如没有指定增加的比例或具体数值。'提升社交媒体互动次数' 需要明确提升的目标百分比或具体数值才能量化。此外，两个关键结果都需要更明确地说明如何支持品牌影响力的扩大。\"\n}\n\nExample 3:\nObjective: Increase the efficiency of our customer service department\nKey Result 1: Reduce average call resolution time by 2 minutes\nKey Result 2: Achieve a customer satisfaction score of 95%\n\nResponse:\n{\n  \"success\": true\n}\n\nExample 4:\nObjective: Enhance online presence\nKey Result 1: Launch a new marketing campaign\nKey Result 2: Increase followers on social media platforms\n\nResponse:\n{\n  \"success\": false,\n  \"message\": \"The objective 'Enhance online presence' is directionally sound but lacks specificity. The key result 'Launch a new marketing campaign' is not quantifiable without metrics to define success. 'Increase followers on social media platforms' needs a specific target for the increase to be measurable. Additionally, both key results require clearer linkage to how they will enhance the online presence.\"\n}"}], "type": "LONG_TEXT"}, {"templateId": "okr_align_superior", "name": {"zh-CN": "上级领导", "zh-TW": "上級領導", "en": "Superior", "ja": "上司"}, "description": {"zh-CN": "该成员的上级领导，用于提醒上级 Review OKR", "zh-TW": "該成員的上級領導，用於提醒上級 Review OKR", "en": "The member's superior, used to remind the superior to review OKR", "ja": "そのメンバーの上司、上司にOKRのレビューをリマインドするために使用"}, "required": true, "type": "MEMBER", "property": {"many": false}}, {"templateId": "okr_self_score", "name": {"zh-CN": "自评分", "zh-TW": "自評分", "en": "Self Score", "ja": "自己評価"}, "description": {"zh-CN": "对自己本季度 OKR 的评分", "zh-TW": "對自己本季度 OKR 的評分", "en": "Self-assessment of your OKRs for this quarter", "ja": "今四半期のOKRの自己評価"}, "required": true, "type": "SINGLE_SELECT", "property": {"options": [{"templateId": "0", "name": "0分：未达成目标，态度有问题", "color": "red"}, {"templateId": "0.1", "name": "0.1分：未达成目标", "color": "teal"}, {"templateId": "0.2", "name": "0.2分：目标达成效果较差，未及时调整", "color": "green"}, {"templateId": "0.3", "name": "0.3分：目标达成效果较差", "color": "yellow"}, {"templateId": "0.4", "name": "0.4分：目标达成效果一般，还需努力", "color": "orange"}, {"templateId": "0.5", "name": "0.5分：目标达成效果一般", "color": "blue"}, {"templateId": "0.6", "name": "0.6分：目标基本达成", "color": "indigo"}, {"templateId": "0.7", "name": "0.7分：目标达成效果不错", "color": "pink"}, {"templateId": "0.8", "name": "0.8分：100%达成目标", "color": "tangerine"}, {"templateId": "0.9", "name": "0.9分：110%达成目标", "color": "deepPurple"}, {"templateId": "1", "name": "1分：超出预期", "color": "deepPurple"}]}}, {"templateId": "okr_superior_score", "name": {"zh-CN": "上级评分", "zh-TW": "上級評分", "en": "Superior Score", "ja": "上司評価"}, "description": {"zh-CN": "上级对你本季度 OKR 的评分", "zh-TW": "上級對你本季度 OKR 的評分", "en": "Superior's assessment of your OKRs for this quarter", "ja": "上司が今四半期のOKRを評価"}, "type": "SINGLE_SELECT", "property": {"options": [{"templateId": "0", "name": "0分：未达成目标，态度有问题", "color": "red"}, {"templateId": "0.1", "name": "0.1分：未达成目标", "color": "teal"}, {"templateId": "0.2", "name": "0.2分：目标达成效果较差，未及时调整", "color": "green"}, {"templateId": "0.3", "name": "0.3分：目标达成效果较差", "color": "yellow"}, {"templateId": "0.4", "name": "0.4分：目标达成效果一般，还需努力", "color": "orange"}, {"templateId": "0.5", "name": "0.5分：目标达成效果一般", "color": "blue"}, {"templateId": "0.6", "name": "0.6分：目标基本达成", "color": "indigo"}, {"templateId": "0.7", "name": "0.7分：目标达成效果不错", "color": "pink"}, {"templateId": "0.8", "name": "0.8分：100%达成目标", "color": "tangerine"}, {"templateId": "0.9", "name": "0.9分：110%达成目标", "color": "deepPurple"}, {"templateId": "1", "name": "1分：超出预期", "color": "deepPurple"}]}}, {"templateId": "okr_attachment", "name": {"zh-CN": "附件", "zh-TW": "附件", "en": "Attachment", "ja": "添付ファイル"}, "type": "ATTACHMENT"}, {"templateId": "okr_submit_time", "name": {"zh-CN": "提交时间", "zh-TW": "提交時間", "en": "Submit Time", "ja": "提出時間"}, "type": "CREATED_TIME", "property": {"dateFormat": "YYYY-MM-DD", "timeFormat": "HH:mm", "includeTime": false}}]}, {"resourceType": "AUTOMATION", "templateId": "okr_submit_automation", "name": {"zh-CN": "OKR Review", "zh-TW": "OKR Review", "en": "OKR Review", "ja": "OKR レビュー"}, "description": {"zh-CN": "当成员提交 OKR 时提醒上级 Review OKR", "zh-TW": "當成員提交 OKR 時提醒上級 Review OKR", "en": "Remind the superior to review OKR when the member submits OKR", "ja": "メンバーがOKRを提出すると、上司にOKRのレビューを促す"}, "permissions": [], "status": "ACTIVE", "triggers": [{"triggerType": "RECORD_CREATED", "templateId": "okr_created", "description": {"zh-CN": "当成员提交 OKR 时触发", "zh-TW": "當成員提交 OKR 時觸發", "en": "Triggered when a member submits OKR", "ja": "メンバーがOKRを提出するとトリガーされます"}, "input": {"type": "DATABASE", "databaseTemplateId": "okr_database"}}], "actions": [{"templateId": "review_okr_mission", "description": {"zh-CN": "提醒上级 Review OKR，并打上评分", "zh-TW": "提醒上級 Review OKR，並打上評分", "en": "Remind the superior to review OKR and score it", "ja": "上司にOKRのレビューと評価を促す"}, "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "UPDATE_RECORD", "name": {"zh-CN": "<%= _triggers.okr_created.record.cells.okr_creator.value[0] %>的 OKR 已经提交了，需要你审阅", "zh-TW": "<%= _triggers.okr_created.record.cells.okr_creator.value[0] %>的 OKR 已經提交了，需要你審閱", "en": "<%= _triggers.okr_created.record.cells.okr_creator.value[0] %>'s OKR has been submitted and needs your review", "ja": "<%= _triggers.okr_created.record.cells.okr_creator.value[0] %>のOKRが提出され、レビューが必要です"}, "description": {"zh-CN": "请点击「审核 OKR」 按钮，并在「上级评分」字段中进行打分，如果过程中有问题可以在记录详情评论时 @ 相关成员", "zh-TW": "請點擊「審核 OKR」 按鈕，並在「上級評分」欄位中進行打分，如果過程中有問題可以在記錄詳情評論時 @ 相關成員", "en": "Please click the \"Review OKR\" button and score in the \"Superior Score\" field. If there are any issues during the process, you can @ relevant members in the record details comments", "ja": "「OKRのレビュー」ボタンをクリックし、「上司評価」フィールドで評価してください。プロセス中に問題がある場合は、レコードの詳細コメントで関連メンバーに@してください"}, "canReject": false, "canCompleteManually": false, "canTransfer": false, "afterActions": [{"templateId": "okr_review_success_notify", "description": {"zh-CN": "通知提交人查看上级评分", "zh-TW": "通知提交人查看上級評分", "en": "Notify the submitter to view the superior's score", "ja": "提出者に上司の評価を表示するよう通知"}, "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "REVIEW_RECORD", "name": {"zh-CN": "OKR 评分结果通知", "zh-TW": "OKR 評分結果通知", "en": "OKR Score Notification", "ja": "OKRの評価結果通知"}, "description": {"zh-CN": "<%= record.cells.okr_creator.value[0] %>，你的 OKR 已经被审阅，点击查看评分结果", "zh-TW": "<%= record.cells.okr_creator.value[0] %>，你的 OKR 已經被審閱，點擊查看評分結果", "en": "<%= record.cells.okr_creator.value[0] %>, your OKR has been reviewed, click to view the score", "ja": "<%= record.cells.okr_creator.value[0] %>、あなたのOKRがレビューされました、クリックしてスコアを表示"}, "to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= record.cells.okr_creator.data[0] %>"]}], "buttonText": {"zh-CN": "确认评分结果", "zh-TW": "確認評分結果", "en": "Confirm Score", "ja": "評価結果を確認"}, "databaseTemplateId": "okr_database", "recordId": "<%= record.id %>"}}}], "assignType": "DEDICATED", "to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= _triggers.okr_created.record.cells.okr_align_superior.data[0] %>"]}], "reminders": [{"name": {"zh-CN": "Review OKR", "zh-TW": "Review OKR", "en": "Review OKR", "ja": "Review OKR"}, "description": {"zh-CN": "请检查 <%= _triggers.okr_created.record.cells.okr_creator.value[0] %> 的 OKR，并完成评分", "zh-TW": "請檢查 <%= _triggers.okr_created.record.cells.okr_creator.value[0] %> 的 OKR，並完成評分", "en": "Please review <%= _triggers.okr_created.record.cells.okr_creator.value[0] %>'s OKR and complete the score", "ja": "<%= _triggers.okr_created.record.cells.okr_creator.value[0] %>のOKRを確認し、評価を完了してください"}, "scheduler": {"repeat": {"every": {"type": "DAY", "interval": 1}}, "timezone": "AUTO", "datetime": {"hour": 10, "minute": 30, "type": "TODAY"}}, "to": []}], "buttonText": {"zh-CN": "Review OKR", "zh-TW": "Review OKR", "en": "Review OKR", "ja": "OKRのレビュー"}, "databaseTemplateId": "okr_database", "recordId": "<%= _triggers.okr_created.record.id %>"}}}]}, {"resourceType": "AUTOMATION", "templateId": "okr_review_automation", "name": {"zh-CN": "每周 Review OKR", "zh-TW": "每周 Review OKR", "en": "Weekly Review OKR", "ja": "毎週のOKRレビュー"}, "description": {"zh-CN": "提醒团队成员 Review OKR", "zh-TW": "提醒團隊成員 Review OKR", "en": "Remind team members to review OKR", "ja": "チームメンバーにOKRのレビューを促す"}, "permissions": [], "status": "ACTIVE", "triggers": [{"triggerType": "SCHEDULER", "templateId": "okr_review_scheduler", "description": {"zh-CN": "每周一 00:00 触发", "zh-TW": "每周一 00:00 觸發", "en": "Triggered at 00:00 every Monday", "ja": "毎週月曜日 00:00 にトリガー"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["MON"]}}, "timezone": "AUTO", "datetime": {"hour": 0, "minute": 0, "type": "TODAY"}}}}], "actions": [{"templateId": "find_quarter_okr_for_weekly_review", "description": {"zh-CN": "向所有本季度创建过 OKR 的成员发送 OKR Review 任务", "zh-TW": "向所有本季度創建過 OKR 的成員發送 OKR Review 任務", "en": "Send OKR Review tasks to all members who have created OKRs this quarter", "ja": "今四半期にOKRを作成したすべてのメンバーにOKRレビュータスクを送信"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "databaseTemplateId": "okr_database", "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "okr_submit_time", "operator": "Is", "fieldType": "CREATED_TIME", "value": ["ThisQuarter"]}]}}, "actions": [{"templateId": "review_okr_mission", "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "COMMENT_RECORD", "name": {"zh-CN": "每周 OKR Review", "zh-TW": "每周 OKR Review", "en": "Weekly OKR Review", "ja": "毎週のOKRレビュー"}, "description": {"zh-CN": "请点击 「评论 OKR」跳转到你填写的 OKR 后评论你上周的 OKR 进度", "zh-TW": "請點擊 「評論 OKR」跳轉到你填寫的 OKR 後評論你上週的 OKR 進度", "en": "Please click \"Comment OKR\" to jump to the OKR you filled out and comment on your progress last week", "ja": "「OKRをコメント」をクリックして、記入したOKRに移動し、先週の進捗にコメントしてください"}, "assignType": "DEDICATED", "to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= _item.record.cells.okr_creator.data[0] %>"]}], "buttonText": {"zh-CN": "评论 OKR", "zh-TW": "評論 OKR", "en": "Comment OKR", "ja": "OKRをコメント"}, "databaseTemplateId": "okr_database", "recordId": "<%= _item.record.id %>"}}}]}]}, {"resourceType": "AUTOMATION", "templateId": "okr_write_automation", "name": {"zh-CN": "季度末提醒填写下个季度 OKR", "zh-TW": "季度末提醒填寫下個季度 OKR", "en": "End of quarter reminder to fill in next quarter's OK<PERSON>", "ja": "四半期末に次の四半期のOKRを記入するようにリマインダー"}, "description": {"zh-CN": "在每个季度的最后一个月25号提醒团队成员填写下个季度的 OKR", "zh-TW": "在每個季度的最後一個月25號提醒團隊成員填寫下個季度的 OKR", "en": "Remind team members to fill in next quarter's OK<PERSON> on the 25th of the last month of each quarter", "ja": "各四半期の最後の月の25日にチームメンバーに次の四半期のOKRを記入するようにリマインダー"}, "permissions": [], "status": "ACTIVE", "triggers": [{"triggerType": "SCHEDULER", "templateId": "okr_write_scheduler", "description": {"zh-CN": "季度末月 25 日 10:00 开始触发", "zh-TW": "季度末月 25 日 10:00 開始觸發", "en": "Triggered at 10:00 on the 25th of the last month of each quarter", "ja": "各四半期の最後の月の25日10:00にトリガー"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "MONTH", "interval": 3, "monthDays": [25]}}, "timezone": "AUTO", "datetime": {"hour": 10, "type": "QUARTER", "quarter": 0, "monthOfQuarter": 3, "dayOfMonth": 25}}}}], "actions": [{"templateId": "update_next_quarter_okr_mission", "description": {"zh-CN": "创建填写下个季度 OKR 的任务", "zh-TW": "創建填寫下個季度 OKR 的任務", "en": "Create a task to fill in next quarter's OKR", "ja": "次の四半期のOKRを記入するタスクを作成"}, "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "CREATE_RECORD", "name": {"zh-CN": "<%= _to.name %>, 请填写下个季度的 OKR", "zh-TW": "<%= _to.name %>, 請填寫下個季度的 OKR", "en": "<%= _to.name %>, please fill in the OKR for next quarter", "ja": "<%= _to.name %>, 次の四半期のOKRを記入してください"}, "description": {"zh-CN": "请尽快点击「填写 OKR」 按钮进行 OKR 的录入和提交，如有问题可以联系 HR 或相关管理员", "zh-TW": "請盡快點擊「填寫 OKR」 按鈕進行 OKR 的錄入和提交，如有問題可以聯繫 HR 或相關管理員", "en": "Please click the \"Fill in OKR\" button to enter and submit OKR as soon as possible. If you have any questions, please contact HR or relevant administrators", "ja": "できるだけ早く「OKRの記入」ボタンをクリックしてOKRを入力して送信してください。質問がある場合は、HRまたは関連する管理者に連絡してください"}, "canReject": false, "canCompleteManually": false, "canTransfer": false, "assignType": "DEDICATED", "to": [{"type": "UNIT_ROLE", "roleTemplateId": "role_okr_member"}, {"type": "CURRENT_OPERATOR"}], "buttonText": {"zh-CN": "填写 OKR", "zh-TW": "填寫 OKR", "en": "Fill in OKR", "ja": "OKRを記入"}, "databaseTemplateId": "okr_database", "viewTemplateId": "submit_okr_view"}}}]}, {"resourceType": "AUTOMATION", "templateId": "okr_report_automation", "name": {"zh-CN": "季度末生成 OKR 报告", "zh-TW": "季度末生成 OKR 報告", "en": "Generate OKR Report at the end of the quarter", "ja": "四半期末にOKRレポートを生成"}, "description": {"zh-CN": "在每个季度结束时生成 OKR 报告，以便团队回顾和展望", "zh-TW": "在每個季度結束時生成 OKR 報告，以便團隊回顧和展望", "en": "Generate OKR report at the end of each quarter for team review and outlook", "ja": "各四半期の終了時にOKRレポートを生成し、チームのレビューと展望を行います"}, "permissions": [], "status": "ACTIVE", "triggers": [{"triggerType": "SCHEDULER", "templateId": "okr_report_scheduler", "description": {"zh-CN": "季度末最后一天 10:00触发", "zh-TW": "季度末最後一天 10:00觸發", "en": "Triggered at 10:00 on the last day of each quarter", "ja": "各四半期の最終日の10:00にトリガー"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "MONTH", "interval": 3, "monthDays": ["LAST_DAY"]}}, "timezone": "AUTO", "datetime": {"hour": 10, "type": "QUARTER", "quarter": 0, "monthOfQuarter": 3, "dayOfMonth": "LAST_DAY"}}}}], "actions": [{"templateId": "find_quarter_okr_review", "description": {"zh-CN": "查找所有在本季度创建的 OKR", "zh-TW": "查找所有在本季度創建的 OKR", "en": "Find all OKRs created this quarter", "ja": "今四半期に作成されたすべてのOKRを検索"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "databaseTemplateId": "okr_database", "filters": {"conjunction": "And", "conditions": [{"fieldTemplateId": "okr_submit_time", "operator": "Is", "fieldType": "CREATED_TIME", "value": ["ThisQuarter"]}]}}}, {"templateId": "send_quarter_okr_report", "description": {"zh-CN": "整理并生成 OKR 报告", "zh-TW": "整理並生成 OKR 報告", "en": "Organize and generate OKR report", "ja": "整理してOKRレポートを生成"}, "actionType": "SEND_REPORT", "input": {"to": [{"type": "UNIT_ROLE", "roleTemplateId": "role_okr_member"}, {"type": "CURRENT_OPERATOR"}], "type": "MARKDOWN", "subject": "⭐️ <%\n                const date = new Date();\n                const month = date.getMonth();\n                let quarter = '';\n                if (month >= 0 && month <= 2) {\n                  quarter = 'Q1';\n                } else if (month >= 3 && month <= 5) {\n                  quarter = 'Q2';\n                } else if (month >= 6 && month <= 8) {\n                  quarter = 'Q3';\n                } else if (month >= 9 && month <= 11) {\n                  quarter = 'Q4';\n                }\n                print(date.getFullYear() + quarter);\n              %> OKR Review", "markdown": "\n<%\n  const date = new Date();\n  const month = date.getMonth();\n  let quarter = '';\n  if (month >= 0 && month <= 2) {\n    quarter = 'Q1';\n  } else if (month >= 3 && month <= 5) {\n    quarter = 'Q2';\n  } else if (month >= 6 && month <= 8) {\n    quarter = 'Q3';\n  } else if (month >= 9 && month <= 11) {\n    quarter = 'Q4';\n  }\n  print(date.getFullYear() + quarter);\n%> 季度大家的 OKR 完成情况如下：\n\n<% _actions.find_quarter_okr_review.records.forEach(function(okr_item) { %>\n  <h1><%= okr_item.cells.okr_creator.value[0] %></h1>\n  <h2>上季度回顾</h2>\n  <%= okr_item.cells.okr_review.value %>\n  <h2>本季度 OKR</h2>\n  <%= okr_item.cells.okr_details.value %>\n  <br/>\n<% }); %>          \n\n希望大家在下个季度继续努力，也欢迎大家对 OKR 进行反馈和建议。\n"}}]}], "initMissions": [{"type": "READ_TEMPLATE_README", "templateId": "@vika/okr", "name": {"zh-CN": "💡OKR 模板使用须知", "zh-TW": "💡OKR 模板使用須知", "en": "💡OKR Template Usage Instructions", "ja": "💡OKR テンプレートの使用方法"}, "forcePopup": true, "assignType": "DEDICATED", "to": [{"type": "CURRENT_OPERATOR"}], "time": 5, "beforeText": {"title": {"zh-CN": "恭喜您，模板已经安装完毕", "zh-TW": "恭喜您，模板已經安裝完畢", "ja": "おめでとうございます、テンプレートのインストールが完了しました", "en": "Congratulations, the template has been installed"}, "description": {"zh-CN": "下一步请您花几分钟阅读模板的的使用教程。", "zh-TW": "下一步請您花幾分鐘閱讀模板的使用教程。", "en": "Next, please take a few minutes to read the tutorial on how to use the template.", "ja": "次に、テンプレートの使用方法についてのチュートリアルを数分間お読みください。"}}, "redirect": {"type": "MY_MISSIONS"}, "wizardGuideId": "COMMON_MY_TODO_TUTORIAL"}, {"type": "INVITE_MEMBER", "name": {"zh-CN": "💡OKR 系统初始化任务 1：请邀请需要填写 OKR 的成员加入空间站", "zh-TW": "💡OKR 系統初始化任務 1：請邀請需要填寫 OKR 的成員加入空間站", "en": "💡OKR System Initialization Task 1: Invite members who need to fill in OKRs to join the space station", "ja": "💡OKR システム初期化タスク 1：OKRを記入する必要があるメンバーを招待してスペースステーションに参加してください"}, "description": {"zh-CN": "你需要邀请需要填写 OKR 的成员加入空间站，可以设置角色为「OKR 参与者」然后创建邀请链接", "zh-TW": "您需要邀請需要填寫 OKR 的成員加入空間站，可以設置角色為「OKR 參與者」然後創建邀請鏈接", "en": "You need to invite members who need to fill in OKRs to join the space station, set the role to \"OKR Participant\" and then create an invitation link", "ja": "OKRを記入する必要があるメンバーを招待してスペースステーションに参加してください。「OKR参加者」の役割を設定し、招待リンクを作成してください"}, "canCompleteManually": true, "assignType": "SHARE", "to": [{"type": "ADMIN"}], "roleTemplateId": "role_okr_member"}, {"type": "REDIRECT_SPACE_NODE", "name": {"zh-CN": "💡OKR 系统初始化任务 2：试试手动发起 OKR 收集", "zh-TW": "💡OKR 系統初始化任務 2：試試手動發起 OKR 收集", "en": "💡OKR System Initialization Task 2: Try to manually initiate OKR collection", "ja": "💡OKR システム初期化タスク 2：手動でOKR収集を試してみてください"}, "description": {"zh-CN": "每季度最后一个月的 25 号上午10点，BIKA 会自动创建一个收集 OKR 的任务。你也可以尝试一下手动发起 OKR 收集任务~提示：在自动化界面底部点击“手动触发”可以预览自动化的效果，点击“启动/停用”可以启用或停用自动化。", "zh-TW": "每季度最後一個月的 25 號上午10點，BIKA 會自動創建一個收集 OKR 的任務。您也可以嘗試一下手動發起 OKR 收集任務~提示：在自動化界面底部點擊“手動觸發”可以預覽自動化的效果，點擊“啟用/停用”可以啟用或停用自動化。", "en": "At 10:00 am on the 25th of the last month of each quarter, BIKA will automatically create a task to collect OKRs. You can also try to manually initiate the OKR collection task~Tip: Click \"Manual Trigger\" at the bottom of the automation interface to preview the effect of the automation. Click \"Enable/Disable\" to enable or disable the automation.", "ja": "毎四半期の最終月の25日の午前10時に、BIKAは自動的にOKRを収集するタスクを作成します。手動でOKR収集タスクを開始することもできます~ヒント：自動化インターフェースの下部で「手動トリガー」をクリックして自動化の効果をプレビューし、自動化を有効または無効にするには「有効/無効」をクリックしてください。"}, "canCompleteManually": true, "assignType": "SHARE", "to": [{"type": "ADMIN"}], "nodeTemplateId": "okr_write_automation", "wizardGuideId": "OKR_INIT_MISSION_MANUAL_TRIGGER"}], "newMemberJoinMissions": [{"type": "REDIRECT_SPACE_NODE", "name": {"zh-CN": "新手任务：欢迎加入空间站！请查看 OKR 系统的介绍，了解如何使用 OKR 系统，这将对后续的使用很有帮助", "zh-TW": "新手任務：歡迎加入空間站！請查看 OKR 系統的介紹，了解如何使用 OKR 系統，這將對後續的使用很有幫助", "en": "Newbie Mission: Welcome to the space station! Please check the introduction of the OKR system to learn how to use the OKR system, which will be very helpful for subsequent use", "ja": "新人ミッション：スペースステーションへようこそ！OKRシステムの紹介をチェックして、OKRシステムの使用方法を学んでください。これは後続の使用に非常に役立ちます"}, "description": {"zh-CN": "## OKR 系统介绍\n\nVika OKR 管理模板旨在协助团队高效运作，支持 OKR（目标与关键结果）工作法的全面实施，包括季度目标设定、进度跟踪和回顾。通过这个模板，您可以轻松组织和记录 OKR 流程，提升团队协作效率。\n\n## 采用 Vika OKR 工作法能够带来哪些价值？\n\n使用 Vika OKR 管理模板可以实现\n\n- **自动化收集 OKR**：定时通知团队成员填写 OKR 报告，简化 OKR 数据收集过程。\n- **自动化 OKR 审核流程**：提交 OKR 后自动提醒上级审核，确保 OKR 的质量和对齐。\n- **定期检查 OKR 进展**：每周和每月自动提醒成员检查和更新 OKR 进展，保持目标的动态管理。\n- **季度报告自动生成**：自动生成季度末 OKR 完成情况报告，助力团队回顾和规划。\n\n## 如何使用 Vika OKR 管理模板？\n\n- 如果你是新加入的同学，我们建议你可以先在「OKR Database」中查看其他同学的 OKR，了解 OKR 的设定和填写方式。\n\n- 每个季度我们都会自动提醒成员填写 OKR，你可以在 OKR Database 下「我的 OKR」视图中查看和更新您的 OKR。同时，你也可以在「我负责评审的 OKR」视图中查看需要审核的 OKR。\n\n- 如果你是团队的领导，你可以在「所有 OKR」视图中查看所有成员的 OKR，以便及时了解团队的目标和进展。", "zh-TW": "## OKR 系統介紹\n\nVika OKR 管理模板旨在協助團隊高效運作，支持 OKR（目標與關鍵結果）工作法的全面實施，包括季度目標設定、進度跟踪和回顧。通過這個模板，您可以輕鬆組織和記錄 OKR 流程，提升團隊協作效率。\n\n## 採用 Vika OKR 工作法能夠帶來哪些價值？\n\n使用 Vika OKR 管理模板可以實現\n\n- **自動化收集 OKR**：定時通知團隊成員填寫 OKR 報告，簡化 OKR 數據收集過程。\n- **自動化 OKR 審核流程**：提交 OKR 後自動提醒上級審核，確保 OKR 的質量和對齊。\n- **定期檢查 OKR 進展**：每週和每月自動提醒成員檢查和更新 OKR 進展，保持目標的動態管理。\n- **季度報告自動生成**：自動生成季度末 OKR 完成情況報告，助力團隊回顧和規劃。\n\n## 如何使用 Vika OKR 管理模板？\n\n- 如果你是新加入的同學，我們建議你可以先在「OKR Database」中查看其他同學的 OKR，了解 OKR 的設定和填寫方式。\n\n- 每個季度我們都會自動提醒成員填寫 OKR，你可以在 OKR Database 下「我的 OKR」視圖中查看和更新您的 OKR。同時，你也可以在「我負責評審的 OKR」視圖中查看需要審核的 OKR。\n\n- 如果你是團隊的領導，你可以在「所有 OKR」視圖中查看所有成員的 OKR，以便及時了解團隊的目標和進展。", "en": "## Introduction to the OKR system\n\nThe Vika OKR management template is designed to help teams operate efficiently, supporting the full implementation of the OKR (Objectives and Key Results) methodology, including setting quarterly objectives, tracking progress, and reviewing. With this template, you can easily organize and record the OKR process, enhancing team collaboration efficiency.\n\n## What value can adopting the Vika OKR methodology bring?\n\nUsing the Vika OKR management template can achieve\n\n- **Automated OKR Collection**: Regularly notify team members to fill in the OKR report, simplifying the OKR data collection process.\n- **Automated OKR Review Process**: Automatically remind superiors to review after OKR submission, ensuring the quality and alignment of OKRs.\n- **Regular OKR Progress Checks**: Automatically remind members to check and update OKR progress weekly and monthly, maintaining dynamic management of objectives.\n- **Quarterly Report Generation**: Automatically generate end-of-quarter OKR completion reports, aiding team review and planning.\n\n## How to use the Vika OKR management template?\n\n- If you are new, we suggest you first check other students' OKRs in the \"OKR Database\" to understand the setting and filling method of OKRs.\n\n- Every quarter, we will automatically remind members to fill in OKRs, and you can view and update your OKRs in the \"My OKRs\" view under OKR Database. At the same time, you can also view OKRs that need to be reviewed in the \"OKRs I'm Responsible for Reviewing\" view.\n\n- If you are a team leader, you can view all members' OKRs in the \"All OKRs\" view to timely understand the team's objectives and progress.", "ja": "## OKRシステム紹介\n\nVika OKR 管理テンプレートは、チームの効率的な運営を支援し、四半期ごとの目標設定、進捗追跡、レビューを含むOKR（目標と主要成果）方法論の全面的な実施をサポートするように設計されています。このテンプレートを使用すると、OKRプロセスを簡単に整理して記録し、チームのコラボレーション効率を向上させることができます。\n\n## Vika OKR 方法論を採用することで得られる価値は？\n\nVika OKR 管理テンプレートを使用することで、以下を実現できます\n\n- **OKRの自動収集**：定期的にチームメンバーにOKRレポートの記入を通知し、OKRデータ収集プロセスを簡素化します。\n- **自動化されたOKRレビュープロセス**：OKR提出後に自動的に上司にレビューをリマインドし、OKRの品質と整合性を保証します。\n- **定期的なOKR進捗チェック**：週次および月次でメンバーにOKR進捗のチェックと更新を自動的にリマインドし、目標のダイナミックな管理を維持します。\n- **四半期報告の自動生成**：四半期末のOKR達成状況レポートを自動生成し、チームのレビューと計画を支援します。\n\n## Vika OKR 管理テンプレートの使用方法は？\n\n- 新しく参加した場合は、「OKR Database」で他の学生のOKRを最初に確認し、OKRの設定方法と記入方法を理解することをお勧めします。\n\n- 毎四半期、メンバーにOKRの記入を自動的にリマインドし、OKR Databaseの「My OKRs」ビューでOKRを確認および更新できます。同時に、「OKRs I'm Responsible for Reviewing」ビューでレビューが必要なOKRを確認することもできます。\n\n- チームリーダーの場合、「All OKRs」ビューで全メンバーのOKRを確認し、チームの目標と進捗をタイムリーに把握できます。"}, "canCompleteManually": true, "assignType": "DEDICATED", "to": [{"type": "SPECIFY_UNITS", "unitIds": ["<%= member.id %>"], "requireRoleTemplateId": "role_okr_member"}], "nodeTemplateId": "okr_database"}], "presetUnits": [{"type": "ROLE", "name": {"zh-CN": "OKR 参与者", "zh-TW": "OKR 參與者", "en": "OKR Participant", "ja": "OKR 参加者"}, "templateId": "role_okr_member"}]}