---
title: How to Submit a MCP Server Configuration to ToolSDK.ai
sidebar_label: How to Submit a MCP Server Configuration to ToolSDK.ai
slug: formapp/help/en/tutorials/how-to-submit-a-mcp-server-to-toolsdk-ai
sidebar_position: 1
---

# How to Submit a MCP Server Configuration to ToolSDK.ai

> **TL;DR**: Fork the repo ➝ Add a JSON file ➝ Submit a PR ➝ Get reviewed ➝ Automated deployment of your MCP Server!

Anyone can submit MCP server configurations in JSON format via a Pull Request. We will automatically publish it as a npm package, generate documentation, website content, and API interfaces.

---

📌 **Prefer a visual guide?**  
Watch this video for a step-by-step walkthrough:

[Awesome MCP Registry: How to submit a MCP server in JSON file?](https://www.youtube.com/watch?v=J_oaDtCoVVo)

<iframe
  width="560"
  height="315"
  src="https://www.youtube.com/embed/J_oaDtCoVVo"
  frameborder="0"
  allowfullscreen
></iframe>

---

## Detailed Guide

### 1. ✅ Fork the Original Repository

1. Open the original project repository page (`https://github.com/toolsdk-ai/awesome-mcp-registry`).
2. Click the **Fork** button at the top right to create a copy under your account.
3. Clone your forked repository to your local machine.

```bash
git clone https://github.com/${your-username}$/bika.git
```

### 2. ✅ Navigate to the Target Directory

Go to the root directory of your local repository and locate the `packages/uncategorized` folder. This is where you should place your new MCP server configuration file. _(You just need to put it here; AI will help categorize it later.)_

### 3. ✅ Create a New JSON File

Add a new file named `xxx.json` (e.g., `my-mcp-server.json`) inside the `packages/uncategorized` directory. Fill it with the following structure:

```json
{
  "type": "mcp-server",
  "packageName": "@yourname/your-mcp-server",
  "description": "描述你的 MCP 服务器功能",
  "url": "https://github.com/yourname/your-mcp-server",
  "runtime": "node",
  "license": "MIT",
  "env": {
    "GITHUB_PERSONAL_ACCESS_TOKEN": {
      "description": "Personal access token for GitHub API access",
      "required": true
    }
  }
}
```

📌 **Required Fields**:

| Field Name  | Required | Description                                     |
| ----------- | -------- | ----------------------------------------------- |
| type        | ✅ Yes   | Fixed value `"mcp-server"`                      |
| packageName | ✅ Yes   | NPM package name or unique identifier           |
| description | ✅ Yes   | Describe what this MCP server does              |
| runtime     | ✅ Yes   | Supports `"node"`, `"python"`, `"java"`, `"go"` |

> Optional fields such as license, url, env, etc., can be added as needed.

### 4. ✅ Commit Changes to Your Forked Repo

1. Add the file and commit:

```bash
git add packages/uncategorized/my-mcp-server.json
git commit -m "Add new MCP server: my-mcp-server"
git push origin main
```

2. Go back to the GitHub page and click **New Pull Request** to submit your changes to the original repository.

### 5. ✅Awaiting Review and Automation

After your PR is merged, we will review your submission. Once the review is approved, the system will automatically perform the following actions. You can check the deployment status of your MCP server at the following locations:

- Publish your MCP server as an npm package;
- Generate a README documentation file;
- Update the `packages-list.json` file;
- Build the online API documentation;
- Display your service on the ToolSDK.ai website.

### 6. ✅ Check the Results

If uncertain about the JSON structure, refer to examples or validate with Zod Schema.

- Don’t forget to write a clear [description](file:///home/<USER>/bika/bika/scripts/toolsdk/crawl-mcp-get.ts#L16-L16) — it helps users understand your service.
- If there are environment variables required, make sure to clearly document each field and whether it’s mandatory.

---

## 🚀 Tips

- If you're unsure whether the JSON is correctly structured, refer to existing examples or use a Zod Schema to validate it.
- Don’t forget to provide a clear description — it helps users understand your service.
- If your service requires environment variables, be sure to explain each field and indicate whether it is required.
