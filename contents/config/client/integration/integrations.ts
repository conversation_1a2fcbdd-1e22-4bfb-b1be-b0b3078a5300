import type { ValidationRule } from 'react-hook-form';
import type { ILocaleContext } from '@bika/contents/i18n';
import type { IntegrationType, IntegrationCategory } from '@bika/types/integration/bo';
import type { iString } from '@bika/types/system';

const isValidURL = (str: string) => {
  // 使用正则表达式来检查字符串是否是合法的 URL
  const pattern = new RegExp(
    '^(https?:\\/\\/)?' + // protocol
      '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.?)+[a-z]{2,}|' + // domain name and extension
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
      '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
      '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
      '(\\#[-a-z\\d_]*)?$',
    'i',
  ); // fragment locator
  return !!pattern.test(str);
};
interface IIntegrationDisplayConfig {
  icon: string;
  label: string;
  display: 'SHOW' | 'COMING_SOON' | 'HIDDEN';
  description: string;
  // 官方网站，会合并到links
  websiteUrl?: string;
  screenshots?: { url: string }[];
  links?: { url: string; text?: iString }[]; // The help document , related URL for the integration
}

interface IIntegrationAdvertisementConfig {
  description: string;
  redirectUrl?: string;
  useCases?: string[];
}

export type IntegrationConfig = Record<
  IntegrationType,
  {
    /**
     *
     * @deprecated 改用 types-form 了
     */
    formConfig: IInegrationConfigItem[];

    displayConfig: IIntegrationDisplayConfig;
    advertisementConfig: IIntegrationAdvertisementConfig;

    /**
     * @deprecated 改用 category
     */
    type: 'INTEGRATION' | 'AUTH' | 'ADVERTISE';
    categories?: IntegrationCategory[];
  }
>;

export interface IInegrationConfigItem {
  key: string;
  label: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  inputType?: 'text' | 'password' | 'number' | 'email' | 'tel' | 'url';
  placeholder: string;
  helpLink?: string; // the link to the help page for the input field
  helperText?: string; // the description for the input field
  errorMsg?: string;
  validate?: (value: any) => boolean | string;
  required?: boolean;
  pattern?: ValidationRule<RegExp>;
  preSubmitDataProcessing?: <T, P>(value: T) => P;
}

export function getIntegrationTypesConfig(locale: ILocaleContext): IntegrationConfig {
  const { t } = locale;
  return {
    OPENAI: {
      type: 'INTEGRATION',
      categories: ['AI_TEXT_MODEL'],
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.openai.apikey,
          placeholder: t.integration.openai.apikey_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/openai.png',
        label: t.integration.openai.title,
        display: 'SHOW',
        websiteUrl: 'https://openai.com',
        description: t.integration.openai.description,
      },
      advertisementConfig: {
        description: t.integration.openai.description,
      },
    },
    AMAZON_BEDROCK: {
      type: 'INTEGRATION',
      categories: ['AI_TEXT_MODEL'],
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.openai.apikey,
          placeholder: t.integration.openai.apikey_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/openai.png',
        label: t.integration.openai.title,
        display: 'SHOW',
        websiteUrl: 'https://openai.com',
        description: t.integration.openai.description,
      },
      advertisementConfig: {
        description: t.integration.openai.description,
      },
    },
    AZURE_AI: {
      type: 'INTEGRATION',
      categories: ['AI_TEXT_MODEL'],
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.openai.apikey,
          placeholder: t.integration.openai.apikey_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/openai.png',
        label: t.integration.openai.title,
        display: 'SHOW',
        websiteUrl: 'https://openai.com',
        description: t.integration.openai.description,
      },
      advertisementConfig: {
        description: t.integration.openai.description,
      },
    },
    SLACK: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'webHookUrl',
          type: 'string',
          label: t.integration.slack.form_item_1_label,
          placeholder: t.integration.slack.form_item_1_placeholder,
          validate: (value) => isValidURL(value) || 'Invalid URL',
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/slack.png',
        label: t.integration.slack.title,
        description: t.integration.slack.description,
        websiteUrl: 'https://slack.com',
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.slack.description,
      },
    },
    GOOGLE: {
      type: 'AUTH',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/google.png',
        label: t.integration.google.title,
        display: 'SHOW',
        description: t.integration.google.description,
      },
      advertisementConfig: {
        description: t.integration.google.description,
      },
    },
    TWITTER: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'clientId',
          type: 'string',
          label: t.integration.twitter.form_item_1_label,
          placeholder: t.integration.twitter.form_item_1_placeholder,
        },
        {
          key: 'clientSecret',
          type: 'string',
          label: t.integration.twitter.form_item_2_label,
          placeholder: t.integration.twitter.form_item_2_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/x.png',
        label: t.integration.twitter.title,
        description: t.integration.twitter.description,
        websiteUrl: 'https://x.com',
        links: [{ url: '/help/guide/integration/twitter-x-oauth2-integration' }],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.twitter.description,
      },
    },
    TWITTER_OAUTH_1A: {
      type: 'INTEGRATION',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/x.png',
        label: t.integration.twitter_oauth_1a.title,
        description: t.integration.twitter_oauth_1a.description,
        websiteUrl: 'https://x.com',
        links: [{ url: 'https://docs.x.com/resources/fundamentals/authentication/oauth-1-0a/api-key-and-secret' }],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.twitter_oauth_1a.description,
      },
    },
    ZOOM: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/zoom.png',
        label: t.integration.zoom.title,
        description: t.integration.zoom.description,
        websiteUrl: 'https://zoom.us',
        links: [],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.zoom.description,
        redirectUrl: 'https://zapier.com/apps/zoom/integrations',
        useCases: t.integration.zoom.use_cases,
      },
    },
    TELEGRAM: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.telegram.field_bot_token,
          placeholder: t.integration.telegram.field_bot_token_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/telegram.png',
        label: t.integration.telegram.title,
        description: t.integration.telegram.description,
        websiteUrl: 'https://telegram.org',
        links: [{ url: '/help/guide/integration/telegram-bot' }],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.telegram.description,
      },
    },
    DEEPSEEK: {
      type: 'INTEGRATION',
      categories: ['AI_TEXT_MODEL'],
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.openai.apikey,
          placeholder: t.integration.openai.apikey_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/deepseek.png',
        label: t.integration.deepseek.title,
        display: 'SHOW',
        websiteUrl: 'https://www.deepseek.com/',
        description: t.integration.deepseek.description,
      },
      advertisementConfig: {
        description: t.integration.deepseek.description,
      },
    },
    IMAP_EMAIL_ACCOUNT: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'host',
          type: 'string',
          label: t.integration.imap.server_label,
          placeholder: t.integration.imap.server_placeholder,
          helperText: t.integration.imap.server_helper_text,
        },
        {
          key: 'port',
          type: 'string',
          label: t.integration.imap.port_label,
          placeholder: t.integration.imap.port_placeholder,
          helperText: t.integration.imap.port_helper_text,
          pattern: { value: /^[0-9]+$/, message: t.integration.imap.port_err_msg },
          preSubmitDataProcessing: <T, P>(value: T) => Number.parseInt(value as string, 10) as P,
        },
        {
          key: 'username',
          type: 'string',
          label: t.integration.imap.user_name_label,
          placeholder: t.integration.imap.user_name_placeholder,
          helpLink: '/help/reference/integration/imap-email-account',
        },
        {
          key: 'password',
          type: 'string',
          inputType: 'password', // 'password' input type to hide the input value
          label: t.integration.imap.password_label,
          placeholder: t.integration.imap.password_placeholder,
        },
        {
          key: 'tls',
          type: 'boolean',
          label: t.integration.imap.tls_label,
          placeholder: '',
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/email.png',
        label: t.integration.imap.title,
        description: t.integration.imap.description,
        websiteUrl: undefined,
        links: [],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.imap.description,
      },
    },
    AIRTABLE: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.airtable.airtable_token,
          placeholder: t.integration.airtable.airtable_token_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/airtable.png',
        label: t.integration.airtable.title,
        description: t.integration.airtable.description,
        websiteUrl: 'https://airtable.com',
        links: [],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.airtable.description,
      },
    },
    APITABLE: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.apitable.apitable_token,
          placeholder: t.integration.apitable.apitable_token_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/apitable.png',
        label: t.integration.apitable.title,
        description: t.integration.apitable.description,
        websiteUrl: 'https://github.com/apitable/apitable',
        links: [],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.apitable.description,
      },
    },
    AITABLE: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.aitable.aitable_token,
          placeholder: t.integration.aitable.aitable_token_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/aitable.png',
        label: t.integration.aitable.title,
        description: t.integration.aitable.description,
        websiteUrl: 'https://aitable.ai',
        links: [],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.aitable.description,
      },
    },
    SMTP_EMAIL_ACCOUNT: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'host',
          type: 'string',
          label: t.integration.smtp.server_label,
          placeholder: t.integration.smtp.server_placeholder,
          helperText: t.integration.smtp.server_helper_text,
        },
        {
          key: 'port',
          type: 'string',
          label: t.integration.smtp.port_label,
          placeholder: t.integration.smtp.port_placeholder,
          helperText: t.integration.smtp.port_helper_text,
          pattern: { value: /^[0-9]+$/, message: t.integration.smtp.port_err_msg },
          preSubmitDataProcessing: <T, P>(value: T) => Number.parseInt(value as string, 10) as P,
        },
        {
          key: 'username',
          type: 'string',
          label: t.integration.smtp.user_name_label,
          placeholder: t.integration.smtp.user_name_placeholder,
          helpLink: '/help/reference/integration/smtp-email-account',
        },
        {
          key: 'password',
          type: 'string',
          inputType: 'password', // 'password' input type to hide the input value
          label: t.integration.smtp.password_label,
          placeholder: t.integration.smtp.password_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/email.png',
        label: t.integration.smtp.title,
        description: t.integration.smtp.description,
        links: [
          {
            url: '/help/reference/integration/smtp-email-account',
          },
        ],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.smtp.description,
      },
    },
    WE_COM: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'webHookUrl',
          type: 'string',
          label: t.integration.wecom.form_item_1_label,
          placeholder: t.integration.wecom.form_item_1_placeholder,
          validate: (value) => isValidURL(value) || 'Invalid URL',
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/we_com.png',
        label: t.integration.wecom.title,
        description: t.integration.wecom.description,
        websiteUrl: 'https://work.weixin.qq.com/',
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.wecom.description,
      },
    },
    DING_TALK: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'webHookUrl',
          type: 'string',
          label: t.integration.dingtalk.form_item_1_label,
          placeholder: t.integration.dingtalk.form_item_1_placeholder,
          validate: (value) => isValidURL(value) || 'Invalid URL',
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/ding_talk.png',
        label: t.integration.dingtalk.title,
        websiteUrl: 'https://www.dingtalk.com',
        description: t.integration.dingtalk.description,
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.dingtalk.description,
      },
    },
    FEI_SHU: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'webHookUrl',
          type: 'string',
          label: t.integration.feishu.form_item_1_label,
          placeholder: t.integration.feishu.form_item_1_placeholder,
          validate: (value) => isValidURL(value) || 'Invalid URL',
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/feishu.png',
        label: t.integration.feishu.title,
        websiteUrl: 'https://www.larksuite.com',
        description: t.integration.feishu.description,
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.feishu.description,
      },
    },
    WEBHOOK: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'webHookUrl',
          type: 'string',
          label: t.integration.webhook.form_item_1_label,
          placeholder: t.integration.webhook.form_item_1_placeholder,
          validate: (value) => isValidURL(value) || 'Invalid URL',
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/webhook.png',
        label: t.integration.webhook.title,
        description: t.integration.webhook.description,
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.webhook.description,
      },
    },
    MYSQL: {
      type: 'INTEGRATION',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/mysql.png',
        label: t.integration.mysql.title,
        description: t.integration.mysql.description,
        display: 'SHOW',
        websiteUrl: 'https://mysql.com',
      },
      advertisementConfig: {
        description: t.integration.mysql.description,
      },
    },
    POSTGRESQL: {
      type: 'INTEGRATION',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/postgre-sql.png',
        label: t.integration.postgresql.title,
        description: t.integration.postgresql.description,
        display: 'SHOW',
        websiteUrl: 'https://postgresql.org',
      },
      advertisementConfig: {
        description: t.integration.postgresql.description,
      },
    },
    VIKA: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.vika.vika_token,
          placeholder: t.integration.vika.vika_token_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/vika.png',
        label: t.integration.vika.title,
        description: t.integration.vika.description,
        display: 'SHOW',
        websiteUrl: 'https://vika.cn',
      },
      advertisementConfig: {
        description: t.integration.vika.description,
      },
    },
    HARDWARE_DEVICE: {
      type: 'INTEGRATION',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/hardware.png',
        label: 'Computer',
        display: 'HIDDEN',
        description: '',
      },
      advertisementConfig: {
        description: '',
      },
    },
    SIRI: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/siri.png',
        label: t.integration.siri.title,
        websiteUrl: 'https://www.apple.com/siri/',
        display: 'SHOW',
        description: t.integration.siri.description,
      },
      advertisementConfig: {
        description: t.integration.siri.description,
        redirectUrl: 'https://www.apple.com/siri/',
        useCases: t.integration.siri.use_cases,
      },
    },
    WECHAT: {
      type: 'AUTH',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/wechat.png',
        label: t.integration.wechat.title,
        display: 'SHOW',
        description: t.integration.wechat.description,
      },
      advertisementConfig: {
        description: t.integration.wechat.description,
      },
    },
    GITHUB: {
      type: 'AUTH',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/github.png',
        label: t.integration.github.title,
        display: 'SHOW',
        description: t.integration.github.description,
      },
      advertisementConfig: {
        description: t.integration.github.description,
      },
    },
    LINKEDIN: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/linkedin.png',
        label: t.integration.linkedin.title,
        display: 'SHOW',
        description: t.integration.linkedin.description,
        websiteUrl: 'https://linkedin.com',
      },
      advertisementConfig: {
        description: t.integration.linkedin.description,
        redirectUrl: 'https://zapier.com/apps/linkedin/integrations',
        useCases: t.integration.linkedin.use_cases,
      },
    },
    AWS_OCR: {
      type: 'INTEGRATION',
      formConfig: [
        {
          key: 'token',
          type: 'string',
          label: t.integration.awsocr.aws_ocr_token,
          placeholder: t.integration.awsocr.aws_ocr_token_placeholder,
        },
      ],
      displayConfig: {
        icon: '/assets/icons/automation/aws-ocr.png',
        label: t.integration.awsocr.title,
        display: 'SHOW',
        websiteUrl: 'https://aws.amazon.com/textract/',
        description: t.integration.awsocr.description,
      },
      advertisementConfig: {
        description: t.integration.awsocr.description,
      },
    },
    MAKE_COM: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/make.png',
        label: t.integration.make.title,
        display: 'SHOW',
        websiteUrl: 'https://make.com',
        description: t.integration.make.description,
      },
      advertisementConfig: {
        description: t.integration.make.description,
        redirectUrl: 'https://make.com',
        useCases: t.integration.make.use_cases,
      },
    },
    ZAPIER: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/zapier.png',
        label: t.integration.zapier.title,
        websiteUrl: 'https://zapier.com',
        display: 'SHOW',
        description: t.integration.zapier.description,
      },
      advertisementConfig: {
        description: t.integration.zapier.description,
        redirectUrl: 'https://zapier.com',
        useCases: t.integration.zapier.use_cases,
      },
    },
    ALICLOUD_TONGYI: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/alicloud-tongyiqianwen.png',
        label: t.integration.tongyiqianwen.title,
        description: t.integration.tongyiqianwen.description,
        websiteUrl: 'https://tongyi.aliyun.com/',
        links: [],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.tongyiqianwen.description,
        redirectUrl: 'https://www.alibabacloud.com/en/solutions/generative-ai/qwen',
        useCases: t.integration.tongyiqianwen.use_cases,
      },
    },
    CLAUDE_AI: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/anthropic-claude.png',
        label: t.integration.claudeai.title,
        display: 'SHOW',
        websiteUrl: 'https://www.anthropic.com/api',
        description: t.integration.claudeai.description,
      },
      advertisementConfig: {
        description: t.integration.claudeai.description,
        redirectUrl: 'https://www.anthropic.com/api',
        useCases: t.integration.claudeai.use_cases,
      },
    },
    GOOGLE_AI: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/google-gemini.png',
        label: t.integration.googleai.title,
        description: t.integration.googleai.description,
        websiteUrl: 'https://ai.google.dev',
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.googleai.description,
        redirectUrl: 'https://ai.google.dev',
        useCases: t.integration.googleai.use_cases,
      },
    },
    TENCENT_HUNYUAN: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/tencent-hunyuan.png',
        label: t.integration.tencenthunyuan.title,
        description: t.integration.tencenthunyuan.description,
        websiteUrl: 'https://hunyuan.tencent.com/',
        links: [],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.tencenthunyuan.description,
        redirectUrl: 'https://hunyuan.tencent.com/',
        useCases: t.integration.tencenthunyuan.use_cases,
      },
    },
    BYTEDANCE_DOUBAO: {
      type: 'ADVERTISE',
      formConfig: [],
      displayConfig: {
        icon: '/assets/icons/automation/byte-doubao.png',
        label: t.integration.byte_doubao.title,
        description: t.integration.byte_doubao.description,
        websiteUrl: 'https://www.volcengine.com/product/doubao/',
        links: [],
        display: 'SHOW',
      },
      advertisementConfig: {
        description: t.integration.byte_doubao.description,
        redirectUrl: 'https://www.volcengine.com/product/doubao/',
        useCases: t.integration.byte_doubao.use_cases,
      },
    },
  };
}
