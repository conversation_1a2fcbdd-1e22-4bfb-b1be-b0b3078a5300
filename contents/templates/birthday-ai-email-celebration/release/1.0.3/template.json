{"templateId": "birthday-ai-email-celebration", "name": "Automated Birthday Email Celebration", "description": "Personalize customer interactions by automatically sending birthday greetings, fostering loyalty and goodwill.", "cover": "/assets/template/template-cover-birthday-ai-email-celebration.png", "author": "<PERSON> <<EMAIL>>", "category": ["email", "automation", "daily-life"], "keywords": "Automated birthday wishes, Personalized interaction, Customer engagement, Customer loyalty, Relationship building", "personas": "Customer service representative, Marketing manager, CRM specialist, Retail store manager", "useCases": "Send birthday emails, Offer birthday discounts, Personalize birthday messages, Notify about upcoming birthdays, Schedule birthday reminders, Track customer birthdays, Send birthday SMS, Automate birthday campaigns, Provide birthday gift suggestions, Create birthday event invites, Notify sales team of birthdays, Send birthday cards, Automate birthday social media posts, Offer birthday rewards, Track birthday campaign success, Send birthday surveys, Follow up on birthday offers, Send personalized birthday videos, Send birthday thank-you notes, Provide birthday loyalty points, Alert for VIP customer birthdays, Notify about birthday responses, Send birthday anniversary wishes, Automate birthday email lists", "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.3", "resources": [{"resourceType": "AUTOMATION", "templateId": "atoSKO2Za3US51I4RHBy1Ykz", "name": "Automated Birthday Email Celebration", "description": "Send birthday email reminders at 10 AM on the specified date", "triggers": [{"triggerType": "SCHEDULER", "templateId": "trg5JBYC8x6dRgmoGclXhMme", "description": "Triggered at 10 AM on the specified date", "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "DAY", "interval": 1}}, "datetime": "2024-12-25T02:00:55.886Z"}}}], "actions": [{"templateId": "actkp4XBJkKhNos9PEnNLM6R", "description": " Whether the match is today's birthday", "actionType": "RUN_SCRIPT", "input": {"type": "SCRIPT", "language": "javascript", "script": "const today = new Date();\nconst month = today.getMonth() + 1;\nconst day = today.getDate();\nconst date = `${month}-${day}`;\nconst json = { date } ;\njson"}}, {"templateId": "actZsSzGdNNWrLW4AlneOPi2", "description": "Find all data", "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fldLZgrWAoq4TnUKPsbPqywG", "fieldType": "DATETIME", "clause": {"operator": "IsNotEmpty"}}]}, "databaseTemplateId": "datmdm3eI4Nh7FGoJ64RxHwP"}}, {"templateId": "actpqmsuX0CB6k0b3vZs3Urz", "description": "Send birthday emails to users matching the date", "actionType": "LOOP", "input": {"type": "PREV_ACTION", "actionTemplateId": "actZsSzGdNNWrLW4AlneOPi2", "path": "records"}, "actions": [{"description": "Filter matching records", "actionType": "FILTER", "templateId": "actbJKZhzpIBTabh1kKzE3di", "input": {"type": "FILTER", "filters": {"conjunction": "And", "conditions": [{"dataType": "string", "operator": "Contains", "input": "<%= _item.cells.fldLZgrWAoq4TnUKPsbPqywG.value %>", "value": "<%= _actions.actkp4XBJkKhNos9PEnNLM6R.date %>"}]}}}, {"description": "Send birthday emails to customers", "actionType": "SEND_EMAIL", "templateId": "act0zxIlcAV7FekzL26YTv1v", "input": {"subject": "Happy Birthday!", "body": {"markdown": "Dear <%= _item.cells.fldZH2hVYEGTCD1jGiWUHkBj.value %> :\n\nOn this special day, the sun shines even brighter as we celebrate your birthday. Time flies, and it has been a pleasure having you as our valued customer.\n\nMay this year bring you an abundance of joy, good health, and prosperity. May all your dreams and wishes come true, and may you continue to inspire those around you with your unique charm and grace.\n\nThank you for your trust and support. Have a wonderful birthday filled with love and laughter!\n\nBest regards,\n[Your Name/Company Name]", "json": {"type": "doc", "content": [{"type": "paragraph", "content": [{"text": "Dear ", "type": "text"}, {"type": "variable", "attrs": {"ids": ["_item", "cells", "fldZH2hVYEGTCD1jGiWUHkBj", "value"], "tips": "", "names": ["当前项", "单元格", "Customer Name", "text"]}}, {"text": " :", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "On this special day, the sun shines even brighter as we celebrate your birthday. Time flies, and it has been a pleasure having you as our valued customer.", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "May this year bring you an abundance of joy, good health, and prosperity. May all your dreams and wishes come true, and may you continue to inspire those around you with your unique charm and grace.", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "Thank you for your trust and support. Have a wonderful birthday filled with love and laughter!", "type": "text"}]}, {"type": "paragraph"}, {"type": "paragraph", "content": [{"text": "Best regards,", "type": "text"}]}, {"type": "paragraph", "content": [{"text": "[Your Name/Company Name]", "type": "text"}]}]}}, "to": [{"type": "EMAIL_STRING", "email": "<%= _item.cells.fldo1YkP0qYWL3bhjBwJ9af7.data %>"}], "senderName": "", "cc": [], "bcc": [], "replyTo": [], "type": "SERVICE"}}]}]}, {"resourceType": "DATABASE", "templateId": "datmdm3eI4Nh7FGoJ64RxHwP", "name": "Customer Birthday Data", "description": "Stores customer birthday data", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viw8uekxvm3TLl6kV78PysMV", "name": "All", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldZH2hVYEGTCD1jGiWUHkBj", "hidden": false, "width": 150}, {"templateId": "fldLZgrWAoq4TnUKPsbPqywG", "hidden": false}, {"templateId": "fldo1YkP0qYWL3bhjBwJ9af7", "hidden": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldZH2hVYEGTCD1jGiWUHkBj", "privilege": "TYPE_EDIT", "name": "Customer Name", "primary": true}, {"type": "EMAIL", "templateId": "fldo1YkP0qYWL3bhjBwJ9af7", "privilege": "FULL_EDIT", "name": "Customer <PERSON><PERSON>", "primary": false}, {"type": "DATETIME", "templateId": "fldLZgrWAoq4TnUKPsbPqywG", "privilege": "FULL_EDIT", "name": "Customer Birthday", "property": {"timeZone": "AUTO", "dateFormat": "YYYY-MM-DD", "includeTime": true}, "primary": false}], "records": [{"templateId": "recRCf7dmgvwFmZg8bpRa58V", "data": {"fldLZgrWAoq4TnUKPsbPqywG": "1996-05-09T04:00:00.000Z", "fldZH2hVYEGTCD1jGiWUHkBj": "<PERSON>", "fldo1YkP0qYWL3bhjBwJ9af7": "<EMAIL>"}, "values": {"fldLZgrWAoq4TnUKPsbPqywG": "1996-05-09 04:00", "fldZH2hVYEGTCD1jGiWUHkBj": "<PERSON>", "fldo1YkP0qYWL3bhjBwJ9af7": "<EMAIL>"}}, {"templateId": "recF644syi6aQ2WF4Cqhh4Sn", "data": {"fldLZgrWAoq4TnUKPsbPqywG": "1990-12-25T04:00:00.000Z", "fldZH2hVYEGTCD1jGiWUHkBj": "<PERSON>", "fldo1YkP0qYWL3bhjBwJ9af7": "<EMAIL>"}, "values": {"fldLZgrWAoq4TnUKPsbPqywG": "1990-12-25 04:00", "fldZH2hVYEGTCD1jGiWUHkBj": "<PERSON>", "fldo1YkP0qYWL3bhjBwJ9af7": "<EMAIL>"}}, {"templateId": "recSsueeNft5Oz58xwH8EAPv", "data": {"fldLZgrWAoq4TnUKPsbPqywG": "1988-12-25T04:00:00.000Z", "fldZH2hVYEGTCD1jGiWUHkBj": "<PERSON>", "fldo1YkP0qYWL3bhjBwJ9af7": "<EMAIL>"}, "values": {"fldLZgrWAoq4TnUKPsbPqywG": "1988-12-25 04:00", "fldZH2hVYEGTCD1jGiWUHkBj": "<PERSON>", "fldo1YkP0qYWL3bhjBwJ9af7": "<EMAIL>"}}]}], "initMissions": []}