{"templateId": "business-development-crm", "name": "Business Development CRM", "description": "A business development CRM template to manage partnership opportunities, track partner details, log interactions, and organize contact information for streamlined collaboration", "cover": "/assets/template/business-development-crm/cover.png", "author": "<PERSON><PERSON> <<EMAIL>>", "category": ["marketing", "sales"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.4", "resources": [{"resourceType": "DATABASE", "templateId": "database_interactions", "name": "Interactions", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all_interactions", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "interaction_name"}, {"templateId": "when", "hidden": false}, {"templateId": "interaction_type", "hidden": false}, {"templateId": "link_partners"}, {"templateId": "link_contacts"}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "interaction_name", "privilege": "TYPE_EDIT", "name": "Name", "primary": true}, {"type": "SINGLE_SELECT", "templateId": "interaction_type", "privilege": "FULL_EDIT", "name": "Type", "property": {"options": [{"id": "opt_1", "name": "Email", "color": "deepPurple"}, {"id": "opt_2", "name": "Phone", "color": "indigo"}, {"id": "opt_3", "name": "In person", "color": "blue"}], "defaultValue": ""}, "primary": false}, {"type": "DATETIME", "templateId": "when", "privilege": "FULL_EDIT", "name": "When", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "LINK", "templateId": "link_partners", "privilege": "NAME_EDIT", "name": "Partners", "property": {"foreignDatabaseTemplateId": "database_partners", "brotherFieldTemplateId": "database_partners:link_to_inter"}, "primary": false}, {"type": "LINK", "templateId": "link_contacts", "privilege": "NAME_EDIT", "name": "Contacts", "property": {"foreignDatabaseTemplateId": "database_contacts", "brotherFieldTemplateId": "database_contacts:link_interactions"}, "primary": false}], "records": [{"templateId": "record1", "data": {"interaction_name": "Contract Discussion", "when": "2024-02-04T16:00:00.000Z", "interaction_type": ["opt_1"], "link_contacts": ["record_g", "record_e", "record_c"], "link_partners": ["re_4", "re_3"]}, "values": {"interaction_name": "Contract Discussion", "when": "2024-02-04", "interaction_type": ["Email"], "link_contacts": ["<PERSON>", "<PERSON>", "<PERSON>"], "link_partners": ["EdgePoint Group", "FutureWave Technologies"]}}, {"templateId": "record2", "data": {"interaction_name": "Follow-Up Call", "when": "2024-01-19T16:00:00.000Z", "interaction_type": ["opt_3"], "link_contacts": ["record_h", "record_f", "record_b"], "link_partners": ["re_4"]}, "values": {"interaction_name": "Follow-Up Call", "when": "2024-01-19", "interaction_type": ["In person"], "link_contacts": ["<PERSON>", "<PERSON>", "<PERSON>"], "link_partners": ["EdgePoint Group"]}}, {"templateId": "record3", "data": {"interaction_name": "Initial Meeting", "when": "2024-01-09T16:00:00.000Z", "interaction_type": ["opt_2"], "link_contacts": ["record_h", "record_d", "record_a"], "link_partners": ["re_8", "re_6"]}, "values": {"interaction_name": "Initial Meeting", "when": "2024-01-09", "interaction_type": ["Phone"], "link_contacts": ["<PERSON>", "<PERSON>", "<PERSON>"], "link_partners": ["TechFusion Ltd", "BrightPath Solutions"]}}]}, {"resourceType": "DATABASE", "templateId": "database_contacts", "name": "Contacts", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all_contacts", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "cantact_name", "hidden": false, "width": 211}, {"templateId": "link_company", "hidden": false}, {"templateId": "email", "hidden": false}, {"templateId": "phone", "hidden": false}, {"templateId": "link_interactions", "hidden": false}], "groups": []}], "fields": [{"type": "SINGLE_TEXT", "templateId": "cantact_name", "privilege": "TYPE_EDIT", "name": "Name", "primary": true}, {"type": "EMAIL", "templateId": "email", "privilege": "NAME_EDIT", "name": "Email", "primary": false}, {"type": "PHONE", "templateId": "phone", "privilege": "NAME_EDIT", "name": "Phone", "primary": false}, {"type": "LINK", "templateId": "link_company", "privilege": "NAME_EDIT", "name": "Company", "property": {"foreignDatabaseTemplateId": "database_partners", "brotherFieldTemplateId": "database_partners:link_to_contacts"}, "primary": false}, {"type": "LINK", "templateId": "link_interactions", "privilege": "NAME_EDIT", "name": "Interactions", "property": {"foreignDatabaseTemplateId": "database_interactions", "brotherFieldTemplateId": "database_interactions:link_contacts"}, "primary": false}], "records": [{"templateId": "record_a", "data": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["re_5", "re_4"], "link_interactions": ["record3"]}, "values": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["NovaLink Ventures", "EdgePoint Group"], "link_interactions": ["<PERSON>"]}}, {"templateId": "record_b", "data": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["re_8", "re_4"], "link_interactions": ["record2"]}, "values": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["TechFusion Ltd", "EdgePoint Group"], "link_interactions": ["<PERSON>"]}}, {"templateId": "record_c", "data": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["re_7"], "link_interactions": ["record1"]}, "values": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["Global Synergy Inc"], "link_interactions": ["<PERSON>"]}}, {"templateId": "record_d", "data": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["re_7"], "link_interactions": ["record3"]}, "values": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["Global Synergy Inc"], "link_interactions": ["<PERSON>"]}}, {"templateId": "record_e", "data": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["re_7"], "link_interactions": ["record1"]}, "values": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["Global Synergy Inc"], "link_interactions": ["<PERSON>"]}}, {"templateId": "record_f", "data": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["re_6"], "link_interactions": ["record2"]}, "values": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["BrightPath Solutions"], "link_interactions": ["<PERSON>"]}}, {"templateId": "record_g", "data": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["re_6"], "link_interactions": ["record1"]}, "values": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["BrightPath Solutions"], "link_interactions": ["<PERSON>"]}}, {"templateId": "record_h", "data": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["re_7"], "link_interactions": ["record3", "record2"]}, "values": {"email": "<EMAIL>", "cantact_name": "<PERSON>", "phone": "(*************", "link_company": ["Global Synergy Inc"], "link_interactions": ["<PERSON>", "<PERSON>"]}}]}, {"resourceType": "DATABASE", "templateId": "database_partners", "name": "Partners", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all_partners", "name": "<PERSON><PERSON><PERSON>", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "partner_name"}, {"templateId": "hq", "width": 193}, {"templateId": "employees", "width": 175}, {"templateId": "industry"}, {"templateId": "website"}, {"templateId": "partner_obj", "width": 221}, {"templateId": "link_oppo"}, {"templateId": "notes"}, {"templateId": "link_to_contacts"}, {"templateId": "link_to_inter"}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "partner_name", "privilege": "TYPE_EDIT", "name": "Partner Name", "primary": true}, {"type": "SINGLE_SELECT", "templateId": "hq", "privilege": "NAME_EDIT", "name": "HQ", "property": {"options": [{"id": "opt_a", "name": "Atlanta, GA", "color": "teal3"}, {"id": "opt_b", "name": "New York, NY", "color": "deepPurple"}, {"id": "opt_c", "name": "San Francisco, CA", "color": "indigo3"}, {"id": "opt_d", "name": "www.edgepointgroup.com", "color": "red"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "employees", "privilege": "NAME_EDIT", "name": "Employees", "property": {"options": [{"id": "opt1", "name": "10000+", "color": "red3"}, {"id": "opt2", "name": "5001-10000", "color": "pink3"}, {"id": "opt3", "name": "1001-5000", "color": "teal2"}, {"id": "opt4", "name": "501-1000", "color": "blue3"}, {"id": "opt5", "name": "51-500", "color": "indigo2"}, {"id": "opt6", "name": "1-50", "color": "deepPurple3"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "industry", "privilege": "NAME_EDIT", "name": "Industry", "property": {"options": [{"id": "opta", "name": "Automotive", "color": "indigo2"}, {"id": "optb", "name": "Consumer Products", "color": "deepPurple3"}, {"id": "optc", "name": "Finance", "color": "blue3"}, {"id": "optd", "name": "Infrastructure", "color": "teal3"}, {"id": "opte", "name": "Insurance", "color": "green3"}, {"id": "optf", "name": "Public Sector", "color": "pink3"}], "defaultValue": ""}, "primary": false}, {"type": "URL", "templateId": "website", "privilege": "NAME_EDIT", "name": "Website", "primary": false}, {"type": "LONG_TEXT", "templateId": "partner_obj", "privilege": "NAME_EDIT", "name": "Partner Objectives", "primary": false}, {"type": "LINK", "templateId": "link_oppo", "privilege": "NAME_EDIT", "name": "Opportunities", "property": {"foreignDatabaseTemplateId": "database_oppo", "brotherFieldTemplateId": "database_oppo:linkpartners"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "notes", "privilege": "NAME_EDIT", "name": "Notes", "primary": false}, {"type": "LINK", "templateId": "link_to_contacts", "privilege": "NAME_EDIT", "name": "Contacts", "property": {"foreignDatabaseTemplateId": "database_contacts", "brotherFieldTemplateId": "database_contacts:link_company"}, "primary": false}, {"type": "LINK", "templateId": "link_to_inter", "privilege": "NAME_EDIT", "name": "Interactions", "property": {"foreignDatabaseTemplateId": "database_interactions", "brotherFieldTemplateId": "database_interactions:link_partners"}, "primary": false}], "records": [{"templateId": "re_1", "data": {"employees": ["opt6"], "partner_name": "CoreVision Consulting", "hq": ["opt_c"], "link_oppo": ["rec_1"], "partner_obj": "Leverage shared resources for growth", "industry": ["optf"], "notes": "Initial discussions show potential alignment", "website": "www.corevisionconsulting.com", "link_to_contacts": ["record_a"]}, "values": {"employees": ["1-50"], "partner_name": "CoreVision Consulting", "hq": ["San Francisco, CA"], "link_oppo": ["BrightFuture Contract"], "partner_obj": "Leverage shared resources for growth", "industry": ["Public Sector"], "notes": "Initial discussions show potential alignment", "website": "www.corevisionconsulting.com", "link_to_contacts": ["<PERSON>"]}}, {"templateId": "re_2", "data": {"employees": ["opt1"], "partner_name": "Skyline Partners", "hq": ["opt_c"], "link_oppo": ["rec_2"], "partner_obj": "Co-develop innovative products", "industry": ["optd"], "notes": "Discussing financial terms", "website": "www.skylinepartners.com", "link_to_contacts": ["record_b"]}, "values": {"employees": ["10000+"], "partner_name": "Skyline Partners", "hq": ["San Francisco, CA"], "link_oppo": ["Summit Integration"], "partner_obj": "Co-develop innovative products", "industry": ["Infrastructure"], "notes": "Discussing financial terms", "website": "www.skylinepartners.com", "link_to_contacts": ["<PERSON>"]}}, {"templateId": "re_3", "data": {"employees": ["opt5"], "partner_name": "FutureWave Technologies", "hq": ["opt_a"], "link_oppo": ["rec_5"], "partner_obj": "Improve operational efficiency with tech solutions", "industry": ["optb"], "notes": "Need to align strategic goals", "link_to_inter": ["record1"], "website": "www.futurewavetech.com", "link_to_contacts": ["record_e", "record_b"]}, "values": {"employees": ["51-500"], "partner_name": "FutureWave Technologies", "hq": ["Atlanta, GA"], "link_oppo": ["ClearPath Merger"], "partner_obj": "Improve operational efficiency with tech solutions", "industry": ["Consumer Products"], "notes": "Need to align strategic goals", "link_to_inter": ["<PERSON>"], "website": "www.futurewavetech.com", "link_to_contacts": ["<PERSON>", "<PERSON>"]}}, {"templateId": "re_4", "data": {"employees": ["opt1"], "partner_name": "EdgePoint Group", "hq": ["opt_b"], "link_oppo": ["rec_6", "rec_4"], "partner_obj": "Explore new sales channels", "industry": ["optb"], "notes": "Awaiting stakeholder approval", "link_to_inter": ["record1", "record2"], "website": "www.edgepointgroup.com", "link_to_contacts": ["record_a", "record_f", "record_c"]}, "values": {"employees": ["10000+"], "partner_name": "EdgePoint Group", "hq": ["New York, NY"], "link_oppo": ["Elevate Expansion", "Atlas Collaboration"], "partner_obj": "Explore new sales channels", "industry": ["Consumer Products"], "notes": "Awaiting stakeholder approval", "link_to_inter": ["<PERSON>", "<PERSON>"], "website": "www.edgepointgroup.com", "link_to_contacts": ["<PERSON>", "<PERSON>", "<PERSON>"]}}, {"templateId": "re_5", "data": {"employees": ["opt4"], "partner_name": "NovaLink Ventures", "hq": ["opt_b"], "link_oppo": ["rec_6", "rec_4"], "partner_obj": "Strengthen R&D partnerships", "industry": ["optc"], "notes": "Finalizing joint project proposal", "link_to_inter": [], "website": "www.novalinkventures.com", "link_to_contacts": ["record_f", "record_a"]}, "values": {"employees": ["501-1000"], "partner_name": "NovaLink Ventures", "hq": ["New York, NY"], "link_oppo": ["Elevate Expansion", "Atlas Collaboration"], "partner_obj": "Strengthen R&D partnerships", "industry": ["Finance"], "notes": "Finalizing joint project proposal", "website": "www.novalinkventures.com", "link_to_contacts": ["<PERSON>", "<PERSON>"]}}, {"templateId": "re_6", "data": {"employees": ["opt2"], "partner_name": "BrightPath Solutions", "hq": ["opt_c"], "link_oppo": ["rec_3"], "partner_obj": "Enhance customer service capabilities", "industry": ["optd"], "notes": "Exploring partnership synergies", "link_to_inter": ["record3"], "website": "www.brightpathsolutions.com", "link_to_contacts": ["record_f"]}, "values": {"employees": ["5001-10000"], "partner_name": "BrightPath Solutions", "hq": ["San Francisco, CA"], "link_oppo": ["Infinity Technologies"], "partner_obj": "Enhance customer service capabilities", "industry": ["Infrastructure"], "notes": "Exploring partnership synergies", "link_to_inter": ["<PERSON>"], "website": "www.brightpathsolutions.com", "link_to_contacts": ["<PERSON>"]}}, {"templateId": "re_7", "data": {"employees": ["opt3"], "partner_name": "Global Synergy Inc", "hq": ["opt_a"], "link_oppo": ["rec_7", "rec_8", "rec_5"], "partner_obj": "Increase product offerings via collaborations", "industry": ["optb"], "notes": "Pending follow-up meeting", "website": "www.globalsynergy.com", "link_to_contacts": ["record_h", "record_e", "record_d", "record_c"]}, "values": {"employees": ["1001-5000"], "partner_name": "Global Synergy Inc", "hq": ["Atlanta, GA"], "link_oppo": ["Synergy Solutions", "Vanguard Partnership", "ClearPath Merger"], "partner_obj": "Increase product offerings via collaborations", "industry": ["Consumer Products"], "notes": "Pending follow-up meeting", "website": "www.globalsynergy.com", "link_to_contacts": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}}, {"templateId": "re_8", "data": {"employees": ["opt2"], "partner_name": "TechFusion Ltd", "hq": ["opt_a"], "link_oppo": [], "partner_obj": "Expand market reach through joint ventures", "industry": ["opta"], "notes": "Signed initial MOU", "link_to_inter": ["record3"], "website": "www.techfusion.com", "link_to_contacts": ["record_b"]}, "values": {"employees": ["5001-10000"], "partner_name": "TechFusion Ltd", "hq": ["Atlanta, GA"], "partner_obj": "Expand market reach through joint ventures", "industry": ["Automotive"], "notes": "Signed initial MOU", "link_to_inter": ["<PERSON>"], "website": "www.techfusion.com", "link_to_contacts": ["<PERSON>"]}}]}, {"resourceType": "DATABASE", "templateId": "database_oppo", "name": "Opportunities", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "by_type", "name": "All Opportunities: By Type", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "opportunity_name", "hidden": false}, {"templateId": "linkpartners", "hidden": false}, {"templateId": "partnership", "hidden": false}, {"templateId": "stage", "hidden": false}, {"templateId": "value", "hidden": false}, {"templateId": "start", "hidden": false}, {"templateId": "end", "hidden": false}, {"templateId": "notes", "hidden": false}, {"templateId": "contracts", "hidden": true}], "groups": [{"fieldTemplateId": "opportunity_name", "asc": true}]}, {"type": "TABLE", "templateId": "negotiations", "name": "Negotiations", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "stage", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "opb"}}]}, "sorts": [], "fields": [{"templateId": "opportunity_name", "hidden": false}, {"templateId": "linkpartners", "hidden": false}, {"templateId": "partnership", "hidden": false, "width": 201}, {"templateId": "stage", "hidden": false}, {"templateId": "value", "hidden": false}, {"templateId": "start", "hidden": false}, {"templateId": "end", "hidden": false}, {"templateId": "notes", "hidden": false}, {"templateId": "contracts", "hidden": false}]}, {"type": "TABLE", "templateId": "by_stage", "name": "All Opportunities: By Stage", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "opportunity_name", "width": 365}, {"templateId": "linkpartners"}, {"templateId": "partnership", "width": 247}, {"templateId": "stage"}, {"templateId": "value"}, {"templateId": "start"}, {"templateId": "end"}, {"templateId": "notes"}, {"templateId": "contracts", "hidden": true}], "groups": [{"fieldTemplateId": "stage", "asc": true}]}, {"type": "KANBAN", "templateId": "deal_pipeline", "name": "Deal Pipeline", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "opportunity_name", "hidden": false}, {"templateId": "partnership", "hidden": false}, {"templateId": "stage", "hidden": false}, {"templateId": "value", "hidden": false}, {"templateId": "start", "hidden": false}, {"templateId": "end", "hidden": false}, {"templateId": "notes", "hidden": false}, {"templateId": "linkpartners", "hidden": false}, {"templateId": "contracts", "hidden": false}], "groups": []}], "fields": [{"type": "FORMULA", "templateId": "opportunity_name", "privilege": "TYPE_EDIT", "name": "Opportunity Name", "property": {"expressionTemplate": "{linkpartners}+“: ”+{partnership}+“ Partnership”"}, "primary": true}, {"type": "SINGLE_SELECT", "templateId": "partnership", "privilege": "NAME_EDIT", "name": "Partnership", "property": {"options": [{"id": "op_1", "name": "Advocacy", "color": "indigo3"}, {"id": "op_2", "name": "Co-marketing", "color": "teal3"}, {"id": "op_3", "name": "Referral", "color": "tangerine3"}, {"id": "op_4", "name": "Strategic", "color": "red3"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "stage", "privilege": "NAME_EDIT", "name": "Contract Stage", "property": {"options": [{"id": "opa", "name": "Request", "color": "pink3"}, {"id": "opb", "name": "Negotiation", "color": "teal3"}, {"id": "opc", "name": "Approval", "color": "indigo3"}, {"id": "opd", "name": "Executed", "color": "deepPurple3"}], "defaultValue": ""}, "primary": false}, {"type": "CURRENCY", "templateId": "value", "privilege": "NAME_EDIT", "name": "Est. Value", "property": {"precision": 2, "commaStyle": "thousand", "symbol": "$", "symbolAlign": "left"}, "primary": false}, {"type": "DATETIME", "templateId": "start", "privilege": "NAME_EDIT", "name": "Term Start", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "DATETIME", "templateId": "end", "privilege": "NAME_EDIT", "name": "Term End", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "LONG_TEXT", "templateId": "notes", "privilege": "NAME_EDIT", "name": "Notes", "primary": false}, {"type": "LINK", "templateId": "linkpartners", "privilege": "NAME_EDIT", "name": "Partners", "property": {"foreignDatabaseTemplateId": "database_partners", "brotherFieldTemplateId": "database_partners:link_oppo"}, "primary": false}, {"type": "ATTACHMENT", "templateId": "contracts", "privilege": "NAME_EDIT", "name": "Contracts", "primary": false}], "records": [{"templateId": "rec_1", "data": {"partnership": ["op_2"], "opportunity_name": "BrightFuture Contract", "stage": ["opb"], "notes": "Initial feedback received from client", "linkpartners": ["re_1"], "end": "2026-01-14T16:00:00.000Z", "start": "2025-01-14T16:00:00.000Z", "value": 350000}, "values": {"partnership": ["Co-marketing"], "opportunity_name": "CoreVision Consulting: Co-marketing Partnership", "stage": ["Negotiation"], "notes": "Initial feedback received from client", "linkpartners": ["CoreVision Consulting"], "end": "2026-01-14", "start": "2025-01-14", "value": "$350000"}}, {"templateId": "rec_2", "data": {"partnership": ["op_3"], "opportunity_name": "Summit Integration", "stage": ["opa"], "notes": "Contract draft in review", "linkpartners": ["re_2"], "end": "2025-12-31T16:00:00.000Z", "start": "2024-12-31T16:00:00.000Z", "value": 220000}, "values": {"partnership": ["Referral"], "opportunity_name": "Skyline Partners: Referral Partnership", "stage": ["Request"], "notes": "Contract draft in review", "linkpartners": ["Skyline Partners"], "end": "2025-12-31", "start": "2024-12-31", "value": "$220000"}}, {"templateId": "rec_3", "data": {"partnership": ["op_4"], "opportunity_name": "Infinity Technologies", "stage": ["opc"], "notes": "Final offer being prepared", "linkpartners": ["re_6"], "end": "2025-12-19T16:00:00.000Z", "start": "2024-12-19T16:00:00.000Z", "value": 500000}, "values": {"partnership": ["Strategic"], "opportunity_name": "BrightPath Solutions: Strategic Partnership", "stage": ["Approval"], "notes": "Final offer being prepared", "linkpartners": ["BrightPath Solutions"], "end": "2025-12-19", "start": "2024-12-19", "value": "$500000"}}, {"templateId": "rec_4", "data": {"partnership": ["op_1"], "opportunity_name": "Atlas Collaboration", "stage": ["opc"], "notes": "Requires additional stakeholder input", "linkpartners": ["re_4", "re_5"], "end": "2025-12-09T16:00:00.000Z", "start": "2024-12-09T16:00:00.000Z", "value": 400000}, "values": {"partnership": ["Advocacy"], "opportunity_name": "EdgePoint Group, NovaLink Ventures: Advocacy Partnership", "stage": ["Approval"], "notes": "Requires additional stakeholder input", "linkpartners": ["EdgePoint Group", "NovaLink Ventures"], "end": "2025-12-09", "start": "2024-12-09", "value": "$400000"}}, {"templateId": "rec_5", "data": {"partnership": ["op_4"], "opportunity_name": "ClearPath Merger", "stage": ["opa"], "notes": "Negotiations in progress", "linkpartners": ["re_3", "re_7"], "end": "2025-11-30T16:00:00.000Z", "start": "2024-11-30T16:00:00.000Z", "value": 150000}, "values": {"partnership": ["Strategic"], "opportunity_name": "FutureWave Technologies, Global Synergy Inc: Strategic Partnership", "stage": ["Request"], "notes": "Negotiations in progress", "linkpartners": ["FutureWave Technologies", "Global Synergy Inc"], "end": "2025-11-30", "start": "2024-11-30", "value": "$150000"}}, {"templateId": "rec_6", "data": {"partnership": ["op_3"], "opportunity_name": "Elevate Expansion", "stage": ["opc"], "notes": "Proposal under consideration", "linkpartners": ["re_5", "re_4"], "end": "2025-11-14T16:00:00.000Z", "start": "2024-11-14T16:00:00.000Z", "value": 320000}, "values": {"partnership": ["Referral"], "opportunity_name": "NovaLink Ventures, EdgePoint Group: Referral Partnership", "stage": ["Approval"], "notes": "Proposal under consideration", "linkpartners": ["NovaLink Ventures", "EdgePoint Group"], "end": "2025-11-14", "start": "2024-11-14", "value": "$320000"}}, {"templateId": "rec_7", "data": {"partnership": ["op_3"], "opportunity_name": "Synergy Solutions", "stage": ["opb"], "notes": "Awaiting legal review", "linkpartners": ["re_7"], "end": "2025-11-09T16:00:00.000Z", "start": "2024-11-09T16:00:00.000Z", "value": 250000}, "values": {"partnership": ["Referral"], "opportunity_name": "Global Synergy Inc: Referral Partnership", "stage": ["Negotiation"], "notes": "Awaiting legal review", "linkpartners": ["Global Synergy Inc"], "end": "2025-11-09", "start": "2024-11-09", "value": "$250000"}}, {"templateId": "rec_8", "data": {"partnership": ["op_1"], "opportunity_name": "Vanguard Partnership", "stage": ["opa"], "notes": "Pending approval from finance", "linkpartners": ["re_7"], "end": "2025-10-31T16:00:00.000Z", "start": "2024-10-31T16:00:00.000Z", "value": 180000}, "values": {"partnership": ["Advocacy"], "opportunity_name": "Global Synergy Inc: Advocacy Partnership", "stage": ["Request"], "notes": "Pending approval from finance", "linkpartners": ["Global Synergy Inc"], "end": "2025-10-31", "start": "2024-10-31", "value": "$180000"}}]}], "initMissions": []}