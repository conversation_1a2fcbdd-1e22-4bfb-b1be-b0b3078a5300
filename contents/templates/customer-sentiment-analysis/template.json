{"templateId": "customer-sentiment-analysis", "name": "Customer Sentiment Analysis", "description": "The Customer Sentiment Analysis Template is a comprehensive tool designed to help businesses systematically analyze customer feedback and make data-driven decisions to enhance customer satisfaction and loyalty. ", "cover": "/assets/template/customer-sentiment-analysis/customer-sentiment-analysis.png", "author": "<PERSON> <<EMAIL>>", "category": ["project"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "0.1.2", "resources": [{"resourceType": "DATABASE", "templateId": "datkQcpvjdWZ787wEvucgL3b", "name": "Onboarding", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwq0VCqbIarZX2yYW6LlD5m", "name": "ALL", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldWQu2vbQPGzm0W2pQVTuSB", "hidden": false}, {"templateId": "fldvAThk8bT20Cxd7atjyuEB", "hidden": false}, {"templateId": "fldfUu5cwLTeiHA6SKdG38tl", "hidden": false, "width": 150}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldWQu2vbQPGzm0W2pQVTuSB", "privilege": "TYPE_EDIT", "name": "Steps", "primary": true}, {"type": "LONG_TEXT", "templateId": "fldvAThk8bT20Cxd7atjyuEB", "privilege": "FULL_EDIT", "name": "Notes", "primary": false}, {"type": "ATTACHMENT", "templateId": "fldfUu5cwLTeiHA6SKdG38tl", "privilege": "FULL_EDIT", "name": "Attachments", "primary": false}], "records": [{"templateId": "recr3vfrM3rBdOliMqEPEHDL", "data": {"fldWQu2vbQPGzm0W2pQVTuSB": "Monitor Product Performance", "fldfUu5cwLTeiHA6SKdG38tl": [{"name": "image.png", "id": "tplattXaww07goLPYwx7p1Dhh56", "path": "template/tplattXaww07goLPYwx7p1Dhh56.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 3444110}], "fldvAThk8bT20Cxd7atjyuEB": "After the product launch, monitor sales data and user feedback to adjust marketing strategies in a timely manner."}, "values": {"fldWQu2vbQPGzm0W2pQVTuSB": "Monitor Product Performance", "fldfUu5cwLTeiHA6SKdG38tl": ["image.png"], "fldvAThk8bT20Cxd7atjyuEB": "After the product launch, monitor sales data and user feedback to adjust marketing strategies in a timely manner."}}, {"templateId": "recmp8IINcLbWXdZW0UeRa2m", "data": {"fldWQu2vbQPGzm0W2pQVTuSB": "Step 4: Prepare for Product Launch", "fldfUu5cwLTeiHA6SKdG38tl": [{"name": "image.png", "id": "tplatt2Auf3V2sgvQU0sVKaw9dt", "path": "template/tplatt2Auf3V2sgvQU0sVKaw9dt.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 3317835}], "fldvAThk8bT20Cxd7atjyuEB": "Prepare all materials needed for product launch, including marketing copy, product introduction videos, etc."}, "values": {"fldWQu2vbQPGzm0W2pQVTuSB": "Step 4: Prepare for Product Launch", "fldfUu5cwLTeiHA6SKdG38tl": ["image.png"], "fldvAThk8bT20Cxd7atjyuEB": "Prepare all materials needed for product launch, including marketing copy, product introduction videos, etc."}}, {"templateId": "recOjEHrQApCg6PmDE4X4ZWR", "data": {"fldWQu2vbQPGzm0W2pQVTuSB": "Step 3: User Testing", "fldfUu5cwLTeiHA6SKdG38tl": [{"name": "image.png", "id": "tplattZlViyxhdqEVgAyPbMzb5f", "path": "template/tplattZlViyxhdqEVgAyPbMzb5f.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 2481204}], "fldvAThk8bT20Cxd7atjyuEB": "Select a group of target users for product testing, collect feedback, and optimize product features."}, "values": {"fldWQu2vbQPGzm0W2pQVTuSB": "Step 3: User Testing", "fldfUu5cwLTeiHA6SKdG38tl": ["image.png"], "fldvAThk8bT20Cxd7atjyuEB": "Select a group of target users for product testing, collect feedback, and optimize product features."}}, {"templateId": "recooaIivQZYrZCoSdhtskA7", "data": {"fldWQu2vbQPGzm0W2pQVTuSB": "Step 2: Design Product Prototype", "fldfUu5cwLTeiHA6SKdG38tl": [{"name": "image.png", "id": "tplatt1jRy957uXHjyr3zgySTg5", "path": "template/tplatt1jRy957uXHjyr3zgySTg5.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 2048507}], "fldvAThk8bT20Cxd7atjyuEB": "Based on the market research results, design an initial product prototype and conduct an internal review."}, "values": {"fldWQu2vbQPGzm0W2pQVTuSB": "Step 2: Design Product Prototype", "fldfUu5cwLTeiHA6SKdG38tl": ["image.png"], "fldvAThk8bT20Cxd7atjyuEB": "Based on the market research results, design an initial product prototype and conduct an internal review."}}, {"templateId": "recm3Hl9VyTJG0KK3nQ74Hwr", "data": {"fldWQu2vbQPGzm0W2pQVTuSB": "Step 1: Complete Market Research", "fldfUu5cwLTeiHA6SKdG38tl": [{"name": "image.png", "id": "tplattLxZIkWthn4NOkfYuRtHO0", "path": "template/tplattLxZIkWthn4NOkfYuRtHO0.png", "bucket": "bika-staging", "mimeType": "image/png", "size": 1206727}], "fldvAThk8bT20Cxd7atjyuEB": "Gather and analyze the market performance of competitors, identify the target customer base."}, "values": {"fldWQu2vbQPGzm0W2pQVTuSB": "Step 1: Complete Market Research", "fldfUu5cwLTeiHA6SKdG38tl": ["image.png"], "fldvAThk8bT20Cxd7atjyuEB": "Gather and analyze the market performance of competitors, identify the target customer base."}}]}, {"resourceType": "DATABASE", "templateId": "datDC3EOG3vj420Ai0YJsj1c", "name": "Months", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwpe4BxTK4DWjSbM7uQP9XG", "name": "monthly data", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldqOAVBYXz197zQRAbZ1PFH", "hidden": false}, {"templateId": "fldS9r7n6iRL715nKQcvxJ5Q", "hidden": false, "width": 321}, {"templateId": "fldXmo3aQSuXVP8AWFmD9JX8", "hidden": false}, {"templateId": "flduT7qvk1vVG5oeHg0VZD98", "hidden": true}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldqOAVBYXz197zQRAbZ1PFH", "privilege": "TYPE_EDIT", "name": "Month", "primary": true}, {"type": "LINK", "templateId": "fldS9r7n6iRL715nKQcvxJ5Q", "privilege": "FULL_EDIT", "name": "Customer Reviews", "property": {"foreignDatabaseTemplateId": "datHj5H7FKdr7m8iKNeeMjwO", "brotherFieldTemplateId": "fldSDe4Jrvmsui77nXLxJm9N"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldXmo3aQSuXVP8AWFmD9JX8", "privilege": "FULL_EDIT", "name": "Month analysis", "primary": false}, {"type": "LINK", "templateId": "flduT7qvk1vVG5oeHg0VZD98", "privilege": "FULL_EDIT", "name": "Customer Reviews 1", "property": {"foreignDatabaseTemplateId": "datHj5H7FKdr7m8iKNeeMjwO", "brotherFieldTemplateId": "fldQVJMFCZNzvMdWpVUJYhNz"}, "primary": false}], "records": [{"templateId": "recTGgIRQINrappz6tkotGzA", "data": {"fldS9r7n6iRL715nKQcvxJ5Q": ["recbEOXxogBVgkOjDSYxA8mR"], "fldXmo3aQSuXVP8AWFmD9JX8": "July 2024 saw a significant boost in sales due to our summer promotion campaign. We successfully engaged with customers through social media contests and experienced a surge in brand awareness. For August, we plan to analyze the campaign's impact and use the insights to inform our future marketing strategies.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-07", "flduT7qvk1vVG5oeHg0VZD98": ["recbEOXxogBVgkOjDSYxA8mR"]}, "values": {"fldS9r7n6iRL715nKQcvxJ5Q": ["Wireless Bluetooth Headphones"], "fldXmo3aQSuXVP8AWFmD9JX8": "July 2024 saw a significant boost in sales due to our summer promotion campaign. We successfully engaged with customers through social media contests and experienced a surge in brand awareness. For August, we plan to analyze the campaign's impact and use the insights to inform our future marketing strategies.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-07", "flduT7qvk1vVG5oeHg0VZD98": ["Wireless Bluetooth Headphones"]}}, {"templateId": "recEb21p3H4Atz6rAcDK7PVU", "data": {"fldS9r7n6iRL715nKQcvxJ5Q": ["recMMj8Mmq4bx4tUCWaEG74v"], "fldXmo3aQSuXVP8AWFmD9JX8": "April 2024 was a month of reflection and goal setting. We conducted a thorough review of our annual targets and found that we exceeded expectations in customer satisfaction. Our focus for May will be to maintain this momentum and prepare for a strong start to 2025.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-04", "flduT7qvk1vVG5oeHg0VZD98": ["recMMj8Mmq4bx4tUCWaEG74v"]}, "values": {"fldS9r7n6iRL715nKQcvxJ5Q": ["Super Cleanser"], "fldXmo3aQSuXVP8AWFmD9JX8": "April 2024 was a month of reflection and goal setting. We conducted a thorough review of our annual targets and found that we exceeded expectations in customer satisfaction. Our focus for May will be to maintain this momentum and prepare for a strong start to 2025.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-04", "flduT7qvk1vVG5oeHg0VZD98": ["Super Cleanser"]}}, {"templateId": "recgD6DaUwuGyQhyM9qAbhpT", "data": {"fldS9r7n6iRL715nKQcvxJ5Q": ["reczYsZJpkSlGzdL2CCyEtwt"], "fldXmo3aQSuXVP8AWFmD9JX8": "In August 2024, we successfully launched a new product line that was well-received by the market, leading to a 15% increase in our customer base. We also expanded our presence into new geographical regions. For September, we plan to further penetrate these new markets and enhance our product offerings based on initial customer feedback.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-08", "flduT7qvk1vVG5oeHg0VZD98": ["reczYsZJpkSlGzdL2CCyEtwt"]}, "values": {"fldS9r7n6iRL715nKQcvxJ5Q": ["Portable Coffee Machine"], "fldXmo3aQSuXVP8AWFmD9JX8": "In August 2024, we successfully launched a new product line that was well-received by the market, leading to a 15% increase in our customer base. We also expanded our presence into new geographical regions. For September, we plan to further penetrate these new markets and enhance our product offerings based on initial customer feedback.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-08", "flduT7qvk1vVG5oeHg0VZD98": ["Portable Coffee Machine"]}}, {"templateId": "recKh9qjd039uhsDGde82xCp", "data": {"fldS9r7n6iRL715nKQcvxJ5Q": ["recdwei2u2sQI2u9wtRztmIs"], "fldXmo3aQSuXVP8AWFmD9JX8": "June 2024 was a month of consolidation for our business. We focused on streamlining our operations and improving internal processes, which resulted in a 10% increase in efficiency. Our customer feedback system has been instrumental in identifying areas for improvement. For July, we plan to implement these changes and prepare for the holiday season.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-06", "flduT7qvk1vVG5oeHg0VZD98": ["recdwei2u2sQI2u9wtRztmIs"]}, "values": {"fldS9r7n6iRL715nKQcvxJ5Q": ["Eco-Friendly Tote Bag"], "fldXmo3aQSuXVP8AWFmD9JX8": "June 2024 was a month of consolidation for our business. We focused on streamlining our operations and improving internal processes, which resulted in a 10% increase in efficiency. Our customer feedback system has been instrumental in identifying areas for improvement. For July, we plan to implement these changes and prepare for the holiday season.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-06", "flduT7qvk1vVG5oeHg0VZD98": ["Eco-Friendly Tote Bag"]}}, {"templateId": "recC1fiYimVAXBtjV9ryE3dH", "data": {"fldS9r7n6iRL715nKQcvxJ5Q": ["recDXmFXbatOk0jq0Y5GGla9"], "fldXmo3aQSuXVP8AWFmD9JX8": "May 2024 was a record-breaking month for sales, driven by our holiday marketing campaigns and exceptional customer service. We successfully managed to meet the high demand and maintain our delivery times. As we close the year, we are analyzing the success factors to replicate in 2025 and beyond.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-05", "flduT7qvk1vVG5oeHg0VZD98": ["recMMj8Mmq4bx4tUCWaEG74v", "recDXmFXbatOk0jq0Y5GGla9"]}, "values": {"fldS9r7n6iRL715nKQcvxJ5Q": ["Smartwatch X1000"], "fldXmo3aQSuXVP8AWFmD9JX8": "May 2024 was a record-breaking month for sales, driven by our holiday marketing campaigns and exceptional customer service. We successfully managed to meet the high demand and maintain our delivery times. As we close the year, we are analyzing the success factors to replicate in 2025 and beyond.", "fldqOAVBYXz197zQRAbZ1PFH": "2024-05", "flduT7qvk1vVG5oeHg0VZD98": ["Super Cleanser", "Smartwatch X1000"]}}]}, {"resourceType": "DATABASE", "templateId": "datTQJQWJ17lxe7Oe4UhFjW1", "name": "Sentiments", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwtmks1PHOT8uIMrH4T1KbX", "name": "Sentiment Score", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fld1azXPOGFpIVS78d2YYWmA"}, {"templateId": "fldhcVzN3FhL4j6Gwh7i37XH", "width": 198}, {"templateId": "fldMdgkruUS5UaBHk9GoZSRz", "hidden": true, "width": 280}, {"templateId": "fldkyr1VmWHWmShmzqejG3kY", "hidden": false}, {"templateId": "fldDTaqqCcRnrb6JedvMJMSG", "hidden": false}, {"templateId": "fldvC4seXzd2Pj6fmXsJSlXe", "hidden": false, "width": 150}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fld1azXPOGFpIVS78d2YYWmA", "privilege": "TYPE_EDIT", "name": "Sentiment", "primary": true}, {"type": "LINK", "templateId": "fldhcVzN3FhL4j6Gwh7i37XH", "privilege": "FULL_EDIT", "name": {"en": "Multi Select", "ja": "複数選択", "zh-CN": "Customer Reviews", "zh-TW": "多選"}, "property": {"foreignDatabaseTemplateId": "datHj5H7FKdr7m8iKNeeMjwO"}, "primary": false}, {"type": "LINK", "templateId": "fldMdgkruUS5UaBHk9GoZSRz", "privilege": "FULL_EDIT", "name": "Customer Reviews 1", "property": {"foreignDatabaseTemplateId": "datHj5H7FKdr7m8iKNeeMjwO", "brotherFieldTemplateId": "fldTmRnxvUjiGoTbZSdDeJFV"}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldkyr1VmWHWmShmzqejG3kY", "privilege": "FULL_EDIT", "name": "Report", "primary": false}, {"type": "LONG_TEXT", "templateId": "fldDTaqqCcRnrb6JedvMJMSG", "privilege": "FULL_EDIT", "name": "Summary", "primary": false}, {"type": "RATING", "templateId": "fldvC4seXzd2Pj6fmXsJSlXe", "privilege": "FULL_EDIT", "name": "Sentiment Score", "property": {"icon": {"type": "EMOJI", "emoji": "⭐️"}, "max": 10}, "primary": false}], "records": [{"templateId": "reczLV8GWar7v5MaUzWU2UVt", "data": {"fld1azXPOGFpIVS78d2YYWmA": "Neutral", "fldDTaqqCcRnrb6JedvMJMSG": "Customers have a neutral stance, with the product meeting basic expectations but not exceeding them.", "fldMdgkruUS5UaBHk9GoZSRz": ["reczYsZJpkSlGzdL2CCyEtwt", "recbEOXxogBVgkOjDSYxA8mR"], "fldhcVzN3FhL4j6Gwh7i37XH": ["reczYsZJpkSlGzdL2CCyEtwt", "recbEOXxogBVgkOjDSYxA8mR"], "fldkyr1VmWHWmShmzqejG3kY": "The Portable Coffee Machine received mixed reviews, with some customers appreciating its convenience while others found it lacking in features compared to competitors.", "fldvC4seXzd2Pj6fmXsJSlXe": 6}, "values": {"fld1azXPOGFpIVS78d2YYWmA": "Neutral", "fldDTaqqCcRnrb6JedvMJMSG": "Customers have a neutral stance, with the product meeting basic expectations but not exceeding them.", "fldMdgkruUS5UaBHk9GoZSRz": ["Portable Coffee Machine", "Wireless Bluetooth Headphones"], "fldhcVzN3FhL4j6Gwh7i37XH": ["Portable Coffee Machine", "Wireless Bluetooth Headphones"], "fldkyr1VmWHWmShmzqejG3kY": "The Portable Coffee Machine received mixed reviews, with some customers appreciating its convenience while others found it lacking in features compared to competitors.", "fldvC4seXzd2Pj6fmXsJSlXe": "6"}}, {"templateId": "recc0ihuaIGqX0hd3utfIpnJ", "data": {"fld1azXPOGFpIVS78d2YYWmA": "Negative", "fldDTaqqCcRnrb6JedvMJMSG": "Customers express disappointment with the product's performance, particularly in battery longevity and reliability.", "fldMdgkruUS5UaBHk9GoZSRz": ["recDXmFXbatOk0jq0Y5GGla9"], "fldhcVzN3FhL4j6Gwh7i37XH": ["recDXmFXbatOk0jq0Y5GGla9"], "fldkyr1VmWHWmShmzqejG3kY": "The Smartwatch X1000 has encountered some negative reviews, with customers reporting issues with battery life and connectivity.", "fldvC4seXzd2Pj6fmXsJSlXe": 4}, "values": {"fld1azXPOGFpIVS78d2YYWmA": "Negative", "fldDTaqqCcRnrb6JedvMJMSG": "Customers express disappointment with the product's performance, particularly in battery longevity and reliability.", "fldMdgkruUS5UaBHk9GoZSRz": ["Smartwatch X1000"], "fldhcVzN3FhL4j6Gwh7i37XH": ["Smartwatch X1000"], "fldkyr1VmWHWmShmzqejG3kY": "The Smartwatch X1000 has encountered some negative reviews, with customers reporting issues with battery life and connectivity.", "fldvC4seXzd2Pj6fmXsJSlXe": "4"}}, {"templateId": "rec7wnp5aJ52xsegEowG4IJ4", "data": {"fld1azXPOGFpIVS78d2YYWmA": "Positive", "fldDTaqqCcRnrb6JedvMJMSG": "High customer satisfaction with excellent product performance and eco-friendliness.", "fldMdgkruUS5UaBHk9GoZSRz": ["recMMj8Mmq4bx4tUCWaEG74v", "recdwei2u2sQI2u9wtRztmIs"], "fldhcVzN3FhL4j6Gwh7i37XH": ["recMMj8Mmq4bx4tUCWaEG74v", "recdwei2u2sQI2u9wtRztmIs"], "fldkyr1VmWHWmShmzqejG3kY": "The Super Cleanser and Eco-Friendly Tote Bag have received positive feedback from customers, with many praising their effectiveness and environmental benefits.", "fldvC4seXzd2Pj6fmXsJSlXe": 7}, "values": {"fld1azXPOGFpIVS78d2YYWmA": "Positive", "fldDTaqqCcRnrb6JedvMJMSG": "High customer satisfaction with excellent product performance and eco-friendliness.", "fldMdgkruUS5UaBHk9GoZSRz": ["Super Cleanser", "Eco-Friendly Tote Bag"], "fldhcVzN3FhL4j6Gwh7i37XH": ["Super Cleanser", "Eco-Friendly Tote Bag"], "fldkyr1VmWHWmShmzqejG3kY": "The Super Cleanser and Eco-Friendly Tote Bag have received positive feedback from customers, with many praising their effectiveness and environmental benefits.", "fldvC4seXzd2Pj6fmXsJSlXe": "7"}}]}, {"resourceType": "DATABASE", "templateId": "datHj5H7FKdr7m8iKNeeMjwO", "name": "Customer Reviews", "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwOTE2D5rrs4EJMnGisDw3k", "name": "Reviews", "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fld38shHzJsmuk1z2THR5nHX"}, {"templateId": "fldKCJ6cff3BCwtqCNHqZ6sJ"}, {"templateId": "fldCk4BWiMnOjzGVoYoBsXXn"}, {"templateId": "fldj7TUmOQ0mnn0x7rBZx0Nq"}, {"templateId": "fldlp3s9BZLf2N7kSPM6bYoC"}, {"templateId": "fldXTU7RaXYMnoolVs3MIJ6F"}, {"templateId": "fldzIKkyqCtAWbJalWbAvxCZ", "hidden": true}, {"templateId": "fldTmRnxvUjiGoTbZSdDeJFV"}, {"templateId": "fldQVJMFCZNzvMdWpVUJYhNz"}, {"templateId": "fldG1mGaObFy7AhBAcIKfC1Q"}, {"templateId": "fldSDe4Jrvmsui77nXLxJm9N", "hidden": true}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fld38shHzJsmuk1z2THR5nHX", "privilege": "TYPE_EDIT", "name": "Title", "primary": true}, {"type": "DATETIME", "templateId": "fldlp3s9BZLf2N7kSPM6bYoC", "privilege": "FULL_EDIT", "name": "Review Date", "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "SINGLE_TEXT", "templateId": "fldXTU7RaXYMnoolVs3MIJ6F", "privilege": "FULL_EDIT", "name": "Customer Name", "primary": false}, {"type": "LINK", "templateId": "fldTmRnxvUjiGoTbZSdDeJFV", "privilege": "FULL_EDIT", "name": "Sentiment", "property": {"foreignDatabaseTemplateId": "datTQJQWJ17lxe7Oe4UhFjW1", "brotherFieldTemplateId": "fldMdgkruUS5UaBHk9GoZSRz"}, "primary": false}, {"type": "LINK", "templateId": "fldQVJMFCZNzvMdWpVUJYhNz", "privilege": "FULL_EDIT", "name": "Month", "property": {"foreignDatabaseTemplateId": "datDC3EOG3vj420Ai0YJsj1c", "brotherFieldTemplateId": "flduT7qvk1vVG5oeHg0VZD98"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldG1mGaObFy7AhBAcIKfC1Q", "privilege": "FULL_EDIT", "name": "Review source", "property": {"options": [{"id": "optZhsBrZDJhxmZhGtADoR9D", "name": "Capterra", "color": "deepPurple5"}, {"id": "opt0LswXPIQq4W3aCFKONrde", "name": "Trustpilot", "color": "red"}, {"id": "optNsDF0qI9MYZWiU0NTf9XG", "name": "Garter", "color": "pink"}, {"id": "opt2aFgpkUfSmssFjwvhnzeA", "name": "GetApp", "color": "yellow"}, {"id": "opt6AajddZFf96m1YRuQ8Imv", "name": "G2", "color": "pink"}, {"id": "optGf8WWcG0ePXdWqvPNPCqr", "name": "FinancesOnline", "color": "orange"}, {"id": "optP6YAdb5e0fp6xUkDDE1gZ", "name": "TrustRadius", "color": "tangerine"}, {"id": "optNFC9Zy8QG4GWQYCFR8pyk", "name": "PeerSpot", "color": "teal"}, {"id": "optKnJwN7vD4seTnEOiEdzSy", "name": "Slashdot", "color": "green"}], "defaultValue": ""}, "primary": false}, {"type": "AUTO_NUMBER", "templateId": "fldzIKkyqCtAWbJalWbAvxCZ", "privilege": "FULL_EDIT", "name": "Number", "property": {"nextId": 1}, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldKCJ6cff3BCwtqCNHqZ6sJ", "privilege": "FULL_EDIT", "name": "Review title", "primary": false}, {"type": "LONG_TEXT", "templateId": "fldCk4BWiMnOjzGVoYoBsXXn", "privilege": "FULL_EDIT", "name": "Review text", "primary": false}, {"type": "RATING", "templateId": "fldj7TUmOQ0mnn0x7rBZx0Nq", "privilege": "FULL_EDIT", "name": "Rating", "property": {"icon": {"type": "EMOJI", "emoji": "⭐️"}, "max": 5}, "primary": false}, {"type": "LINK", "templateId": "fldSDe4Jrvmsui77nXLxJm9N", "privilege": "FULL_EDIT", "name": "Months 1", "property": {"foreignDatabaseTemplateId": "datDC3EOG3vj420Ai0YJsj1c", "brotherFieldTemplateId": "fldS9r7n6iRL715nKQcvxJ5Q"}, "primary": false}], "records": [{"templateId": "reczYsZJpkSlGzdL2CCyEtwt", "data": {"fld38shHzJsmuk1z2THR5nHX": "Portable Coffee Machine", "fldCk4BWiMnOjzGVoYoBsXXn": "This compact coffee machine is easy to use and allows me to enjoy a good cup of coffee even when I'm outdoors.", "fldG1mGaObFy7AhBAcIKfC1Q": ["optGf8WWcG0ePXdWqvPNPCqr"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Enjoy Coffee Anytime, Anywhere", "fldQVJMFCZNzvMdWpVUJYhNz": ["recgD6DaUwuGyQhyM9qAbhpT"], "fldSDe4Jrvmsui77nXLxJm9N": ["recgD6DaUwuGyQhyM9qAbhpT"], "fldTmRnxvUjiGoTbZSdDeJFV": ["reczLV8GWar7v5MaUzWU2UVt"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": 3, "fldlp3s9BZLf2N7kSPM6bYoC": "2024-08-08T16:00:00.000Z"}, "values": {"fld38shHzJsmuk1z2THR5nHX": "Portable Coffee Machine", "fldCk4BWiMnOjzGVoYoBsXXn": "This compact coffee machine is easy to use and allows me to enjoy a good cup of coffee even when I'm outdoors.", "fldG1mGaObFy7AhBAcIKfC1Q": ["FinancesOnline"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Enjoy Coffee Anytime, Anywhere", "fldQVJMFCZNzvMdWpVUJYhNz": ["2024-08"], "fldSDe4Jrvmsui77nXLxJm9N": ["2024-08"], "fldTmRnxvUjiGoTbZSdDeJFV": ["Neutral"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": "3", "fldlp3s9BZLf2N7kSPM6bYoC": "2024-08-08"}}, {"templateId": "recbEOXxogBVgkOjDSYxA8mR", "data": {"fld38shHzJsmuk1z2THR5nHX": "Wireless Bluetooth Headphones", "fldCk4BWiMnOjzGVoYoBsXXn": "The sound quality is excellent, and I haven't experienced any disconnections. A great pair of headphones for the price.", "fldG1mGaObFy7AhBAcIKfC1Q": ["optP6YAdb5e0fp6xUkDDE1gZ"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Clear Sound and Stable Connection", "fldQVJMFCZNzvMdWpVUJYhNz": ["recTGgIRQINrappz6tkotGzA"], "fldSDe4Jrvmsui77nXLxJm9N": ["recTGgIRQINrappz6tkotGzA"], "fldTmRnxvUjiGoTbZSdDeJFV": ["reczLV8GWar7v5MaUzWU2UVt"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": 4, "fldlp3s9BZLf2N7kSPM6bYoC": "2024-07-24T16:00:00.000Z"}, "values": {"fld38shHzJsmuk1z2THR5nHX": "Wireless Bluetooth Headphones", "fldCk4BWiMnOjzGVoYoBsXXn": "The sound quality is excellent, and I haven't experienced any disconnections. A great pair of headphones for the price.", "fldG1mGaObFy7AhBAcIKfC1Q": ["TrustRadius"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Clear Sound and Stable Connection", "fldQVJMFCZNzvMdWpVUJYhNz": ["2024-07"], "fldSDe4Jrvmsui77nXLxJm9N": ["2024-07"], "fldTmRnxvUjiGoTbZSdDeJFV": ["Neutral"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": "4", "fldlp3s9BZLf2N7kSPM6bYoC": "2024-07-24"}}, {"templateId": "recdwei2u2sQI2u9wtRztmIs", "data": {"fld38shHzJsmuk1z2THR5nHX": "Eco-Friendly Tote Bag", "fldCk4BWiMnOjzGVoYoBsXXn": "This tote bag is incredibly sturdy and I love that it's environmentally friendly. It's my go-to for grocery shopping.", "fldG1mGaObFy7AhBAcIKfC1Q": ["optZhsBrZDJhxmZhGtADoR9D"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Durable and Eco-Conscious", "fldQVJMFCZNzvMdWpVUJYhNz": ["recKh9qjd039uhsDGde82xCp"], "fldSDe4Jrvmsui77nXLxJm9N": ["recKh9qjd039uhsDGde82xCp"], "fldTmRnxvUjiGoTbZSdDeJFV": ["rec7wnp5aJ52xsegEowG4IJ4"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": 5, "fldlp3s9BZLf2N7kSPM6bYoC": "2024-06-09T16:00:00.000Z"}, "values": {"fld38shHzJsmuk1z2THR5nHX": "Eco-Friendly Tote Bag", "fldCk4BWiMnOjzGVoYoBsXXn": "This tote bag is incredibly sturdy and I love that it's environmentally friendly. It's my go-to for grocery shopping.", "fldG1mGaObFy7AhBAcIKfC1Q": ["Capterra"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Durable and Eco-Conscious", "fldQVJMFCZNzvMdWpVUJYhNz": ["2024-06"], "fldSDe4Jrvmsui77nXLxJm9N": ["2024-06"], "fldTmRnxvUjiGoTbZSdDeJFV": ["Positive"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": "5", "fldlp3s9BZLf2N7kSPM6bYoC": "2024-06-09"}}, {"templateId": "recDXmFXbatOk0jq0Y5GGla9", "data": {"fld38shHzJsmuk1z2THR5nHX": "Smartwatch X1000", "fldCk4BWiMnOjzGVoYoBsXXn": "The watch is stylishly designed, has all the features I need, and the battery life is impressive.", "fldG1mGaObFy7AhBAcIKfC1Q": ["opt6AajddZFf96m1YRuQ8Imv"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Feature-Rich and Affordable", "fldQVJMFCZNzvMdWpVUJYhNz": ["recC1fiYimVAXBtjV9ryE3dH"], "fldSDe4Jrvmsui77nXLxJm9N": ["recC1fiYimVAXBtjV9ryE3dH"], "fldTmRnxvUjiGoTbZSdDeJFV": ["recc0ihuaIGqX0hd3utfIpnJ"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": 4, "fldlp3s9BZLf2N7kSPM6bYoC": "2024-05-19T16:00:00.000Z"}, "values": {"fld38shHzJsmuk1z2THR5nHX": "Smartwatch X1000", "fldCk4BWiMnOjzGVoYoBsXXn": "The watch is stylishly designed, has all the features I need, and the battery life is impressive.", "fldG1mGaObFy7AhBAcIKfC1Q": ["G2"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Feature-Rich and Affordable", "fldQVJMFCZNzvMdWpVUJYhNz": ["2024-05"], "fldSDe4Jrvmsui77nXLxJm9N": ["2024-05"], "fldTmRnxvUjiGoTbZSdDeJFV": ["Negative"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": "4", "fldlp3s9BZLf2N7kSPM6bYoC": "2024-05-19"}}, {"templateId": "recMMj8Mmq4bx4tUCWaEG74v", "data": {"fld38shHzJsmuk1z2THR5nHX": "Super Cleanser", "fldCk4BWiMnOjzGVoYoBsXXn": "This cleanser has exceeded my expectations with its stain removal capabilities, leaving my kitchen looking brand new.", "fldG1mGaObFy7AhBAcIKfC1Q": ["opt0LswXPIQq4W3aCFKONrde"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Amazing Cleaning Powe", "fldQVJMFCZNzvMdWpVUJYhNz": ["recEb21p3H4Atz6rAcDK7PVU"], "fldSDe4Jrvmsui77nXLxJm9N": ["recEb21p3H4Atz6rAcDK7PVU"], "fldTmRnxvUjiGoTbZSdDeJFV": ["rec7wnp5aJ52xsegEowG4IJ4"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": 5, "fldlp3s9BZLf2N7kSPM6bYoC": "2024-04-14T16:00:00.000Z"}, "values": {"fld38shHzJsmuk1z2THR5nHX": "Super Cleanser", "fldCk4BWiMnOjzGVoYoBsXXn": "This cleanser has exceeded my expectations with its stain removal capabilities, leaving my kitchen looking brand new.", "fldG1mGaObFy7AhBAcIKfC1Q": ["Trustpilot"], "fldKCJ6cff3BCwtqCNHqZ6sJ": "Amazing Cleaning Powe", "fldQVJMFCZNzvMdWpVUJYhNz": ["2024-04"], "fldSDe4Jrvmsui77nXLxJm9N": ["2024-04"], "fldTmRnxvUjiGoTbZSdDeJFV": ["Positive"], "fldXTU7RaXYMnoolVs3MIJ6F": "<PERSON>", "fldj7TUmOQ0mnn0x7rBZx0Nq": "5", "fldlp3s9BZLf2N7kSPM6bYoC": "2024-04-14"}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}