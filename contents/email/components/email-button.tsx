import { Button } from '@react-email/components';
import React from 'react';
import type { CSSProperties, ReactNode } from 'react';

interface Props {
  url: string;
  children: ReactNode;
  style?: CSSProperties;
}

function EmailButton(props: Props) {
  const { url, children, style } = props;
  return (
    <Button
      href={url}
      style={{
        background: '#8355EC',
        borderRadius: '87px',
        fontSize: '14px',
        color: 'white',
        display: 'inline-block',
        minWidth: '164px',
        height: '48px',
        lineHeight: '48px',
        marginTop: '40px',
        textAlign: 'center',
        ...style,
      }}
    >
      {children}
    </Button>
  );
}

export default EmailButton;
