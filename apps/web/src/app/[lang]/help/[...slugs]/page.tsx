import { getFeatureInfo } from '@bika/contents/config/client/system/features';
import { FeaturesTable } from '@bika/contents/docs/components/detail-features';
import { getServerDictionary, getServerLocaleContext } from '@bika/contents/i18n/server';
import { generateLanguageUrls } from '@bika/domains/shared/client/utils';
import { StoreTemplateSO } from '@bika/domains/store/server/store-template-so';
import { ArticleContent } from '@bika/domains/website/client/article';
import { AutoHelpDetailView } from '@bika/domains/website/client/help/auto-help-detail-view';
import { AIGCSO } from '@bika/domains/website/server/aigc/aigc-so';
import { generateTocAndModifyHtml } from '@bika/domains/website/server/utils';
import { LocalContentLoader } from '@bika/server-orm';
import { AutoHelpGenSO } from '@bika/server-orm/managers/auto-help-gen-so';
import type { Locale } from '@bika/types/i18n/bo';
import type { TemplateCardInfoVO } from '@bika/types/template/vo';
import { type FeatureType, FeatureTypes } from '@bika/types/website/bo';
import { MarkdownBody } from '@bika/ui/markdown';
import type { Metadata } from 'next';
import { notFound, redirect } from 'next/navigation';
import React from 'react';
import style from '../index.module.css';

interface RouteParams {
  slugs: string[];
  lang: string;
}

export interface PageProps {
  params: Promise<RouteParams>;
}

function isAutoHelp(slugs: string[]) {
  const slug1 = slugs[0];
  const slug2 = slugs[1];
  if (slug1 === 'reference') {
    if (FeatureTypes.includes(slug2 as FeatureType)) {
      return true;
    }
  }
  return false;
}
async function getTemplateInfos(locale: Locale) {
  const randomTemplatesSOs = await StoreTemplateSO.getRandomTemplates();
  const templateVOs: TemplateCardInfoVO[] = [];
  for (const tplSO of randomTemplatesSOs) {
    const tplVO = await tplSO.toInfoVO(locale);
    templateVOs.push(tplVO);
  }
  return templateVOs;
}
export default async function HelpDetail(params: PageProps) {
  const { slugs, lang } = await params.params;

  try {
    if (isAutoHelp(slugs)) {
      // 最后一位是'features-list'
      if (slugs[slugs.length - 1] === 'features-list') {
        const featureType = slugs[slugs.length - 2] as FeatureType;
        return (
          <div className={style.content}>
            <ArticleContent relatedLinks={undefined} locale={lang as Locale}>
              <MarkdownBody>
                <FeaturesTable type={featureType} locale={lang as Locale} />
              </MarkdownBody>
            </ArticleContent>
          </div>
        );
      }

      const helpRoute = slugs[1] as FeatureType;
      const helpName = slugs[2];

      const content = <AutoHelpDetailView locale={lang as Locale} route={helpRoute} helpName={helpName} />;
      // auto help内置了模板推荐，所以这个位置不放模板推荐
      const relatedLinks = await AIGCSO.getRandomBlogsLinks(lang as Locale);
      return (
        <div className={style.content}>
          <ArticleContent relatedLinks={relatedLinks} locale={lang as Locale}>
            {content}
          </ArticleContent>
        </div>
      );
    }

    const { content } = await LocalContentLoader.help.autoHelp(lang, slugs);
    const { tree, modifiedHtml } = generateTocAndModifyHtml(content);
    const templateInfos = await getTemplateInfos(lang as Locale);
    const relatedLinks = await AIGCSO.getRandomBlogsLinks(lang as Locale);
    return (
      <div className={style.content}>
        <ArticleContent
          relatedLinks={relatedLinks}
          templateInfos={templateInfos}
          locale={lang as Locale}
          html={modifiedHtml}
          toc={tree}
        />
      </div>
    );
  } catch (e) {
    if (lang !== 'en') {
      redirect(`/help/${slugs.join('/')}`);
    } else {
      console.error(e);
      notFound();
    }
  }
}

export async function generateMetadata(params: PageProps): Promise<Metadata> {
  const { slugs, lang } = await params.params;
  const localeContext = getServerLocaleContext(lang as Locale);
  const dict = getServerDictionary(lang as Locale);

  if (isAutoHelp(slugs)) {
    // 最后一位是'features-list'
    if (slugs[slugs.length - 1] === 'features-list') {
      const featureInfo = getFeatureInfo('automation-trigger', localeContext);
      return {
        title: featureInfo.label,
        description: featureInfo.description || dict.slogan.slogan_prd_xl,
        keywords: dict.slogan.keywords,
        openGraph: {
          title: featureInfo.label,
          description: featureInfo.description || dict.slogan.slogan_prd_xl,
          type: 'website',
          locale: lang,
        },
        twitter: {
          card: 'summary_large_image',
          site: '@bika_ai',
          title: featureInfo.label,
          description: featureInfo.description || dict.slogan.slogan_prd_xl,
          creator: '@bika_ai',
        },
      };
    }

    const helpRoute = slugs[1] as FeatureType;
    const helpName = slugs[2];
    const { label, description } = await AutoHelpGenSO.getAutoHelpConfig(lang as Locale, helpRoute, helpName);
    return {
      title: label,
      description,
      keywords: dict.slogan.keywords,
      openGraph: {
        title: label,
        description,
        type: 'article',
        locale: lang,
      },
      twitter: {
        card: 'summary_large_image',
        site: '@bika_ai',
        title: label,
        description,
        creator: '@bika_ai',
      },
    };
  }

  try {
    // MDX 加载
    const { meta: frontMatter } = await LocalContentLoader.help.autoHelp(lang, slugs);
    return {
      title: frontMatter.title || frontMatter.sidebar_label || slugs.join('-'),
      description: frontMatter.description || dict.slogan.slogan_prd_xl,
      keywords: frontMatter.keywords || dict.slogan.keywords,
      openGraph: {
        title: frontMatter.title || frontMatter.sidebar_label || slugs.join('-'),
        description: frontMatter.description || dict.slogan.slogan_prd_xl,
        type: 'article',
        locale: lang,
      },
      twitter: {
        card: 'summary_large_image',
        site: '@bika_ai',
        title: frontMatter.title || frontMatter.sidebar_label || slugs.join('-'),
        description: frontMatter.description || dict.slogan.slogan_prd_xl,
        creator: '@bika_ai',
      },
      alternates: {
        languages: await generateLanguageUrls(`help/${slugs.join('/')}`, 'en', async (locale) => {
          try {
            const ret = await LocalContentLoader.help.autoHelp(locale, slugs);
            return true;
          } catch (e) {
            return false;
          }
        }),
      },
    };
  } catch (e) {
    notFound();
  }
}
