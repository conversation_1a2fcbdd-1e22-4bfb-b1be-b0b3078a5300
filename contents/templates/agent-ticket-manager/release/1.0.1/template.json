{"templateId": "agent-ticket-manager", "name": {"en": "Ticket Manager", "zh-TW": "工單管理員", "zh-CN": "工单管理员", "ja": "チケットマネージャー"}, "description": {"en": "Collects, analyzes, and manages support tickets from forms and databases, helping you track, prioritize, and respond efficiently.", "zh-TW": "收集、分析和管理來自表單和數據庫的支持工單，幫助您高效地跟踪、優先處理和回應。", "zh-CN": "收集、分析和管理来自表单和数据库的支持工单，帮助您高效地跟踪、优先处理和回应。", "ja": "フォームやデータベースからのサポートチケットを収集、分析、管理し、効率的に追跡、優先順位付け、応答を支援します。"}, "keywords": {"en": "ticket management, support tickets, ai agent, customer support, issue tracking", "zh-TW": "工單管理, 支持工單, AI agent, 客戶支持, 問題跟踪", "zh-CN": "工单管理, 支持工单, AI智能体, 客户支持, 问题跟踪", "ja": "チケット管理、サポートチケット、AIエージェント、カスタマーサポート、問題追跡"}, "cover": {"type": "ATTACHMENT", "attachmentId": "tplattK5LtPRVzrO2ObWJQHPFNY", "relativePath": "template/tplattK5LtPRVzrO2ObWJQHPFNY.png"}, "author": "Thea <<EMAIL>>", "category": ["project", "ai", "operation"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.1", "resources": [{"resourceType": "AI", "templateId": "ainxvo6mibdyjKUmbBbGV0ch", "name": {"en": "Ticket Manager", "zh-TW": "工單管理員", "zh-CN": "工单管理员", "ja": "チケットマネージャー"}, "description": {"en": "Collects, analyzes, and manages support tickets from forms and databases, helping you track, prioritize, and respond efficiently.", "zh-TW": "收集、分析和管理來自表單和數據庫的支持工單，幫助您高效地跟踪、優先處理和回應。", "zh-CN": "收集、分析和管理来自表单和数据库的支持工单，帮助您高效地跟踪、优先处理和回应。", "ja": "フォームやデータベースからのサポートチケットを収集、分析、管理し、効率的に追跡、優先順位付け、応答を支援します。"}, "icon": {"type": "ATTACHMENT", "attachmentId": "tplattK5LtPRVzrO2ObWJQHPFNY", "relativePath": "template/tplattK5LtPRVzrO2ObWJQHPFNY.png"}, "prompt": "# Role\nYou are a professional support ticket analyst. You analyze support tickets from the \"Feedback Tickets\" database and generate clear, concise markdown reports to help the team understand ticket trends and take action.\n\n# Responsibilities\n- Connect to the **Feedback Tickets** data source.\n- Read and query ticket data to answer user questions such as:\n  - How many new tickets came in today?\n  - How many tickets are still open?\n  - What is the most frequently reported issue lately?\n  - How many tickets with the “bug” label were logged last month?\n- Always use the most up-to-date data from the database. Do not guess or fabricate numbers.\n\n# Output\n- Call the `generate_markdown_document` skill from the **bika-office** skillset to create a clear, business-friendly markdown report summarizing your findings.\n- The markdown report should include:\n  - A title summarizing the question answered.\n  - Key metrics or counts, displayed as bullet points or in a short paragraph.\n  - If applicable, highlight top issues or trends in plain English.\n\n# Limitations\n- Only use the **Feedback Tickets** database for your analysis.\n- If no relevant data is found, clearly state so in the markdown report.\n- Do not exceed 100 words per report.", "sources": [{"type": "NODE", "nodeId": "dat83t77L2PmUAUfIy7Rr772"}], "skillsets": [{"kind": "preset", "key": "bika-office", "includes": ["generate_markdown_document"], "needApprovals": []}, {"kind": "preset", "key": "bika-database", "includes": ["list_records", "get_fields_schema", "get_database_detail", "aggregate_records"], "needApprovals": []}], "asMember": true}, {"resourceType": "FORM", "templateId": "fom0dk1UMTODT2VtEjAUwiCr", "name": {"en": "Feedback Form", "zh-TW": "反饋表單", "zh-CN": "反馈表单", "ja": "フィードバックフォーム"}, "description": "Share your feedback, suggestions, or report an issue.\nPlease include your contact email and any files or screenshots that can help us understand better.\nWe’ll review your message and get back to you if needed — thank you!", "formType": "DATABASE", "databaseTemplateId": "dat83t77L2PmUAUfIy7Rr772", "metadata": {"type": "VIEW", "viewTemplateId": "viwQYjEExYCvW1BaO5QMcxhj"}}, {"resourceType": "DATABASE", "templateId": "dat83t77L2PmUAUfIy7Rr772", "name": {"en": "Feedback Tickets", "zh-TW": "反饋工單庫", "zh-CN": "反馈工单库", "ja": "フィードバックチケットデータベース"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwQYjEExYCvW1BaO5QMcxhj", "name": "Form", "filters": {"conjunction": "And", "conditions": [], "conds": []}, "sorts": [], "fields": [{"templateId": "fldmDM8wdqiVfwkZPVWQS9EO", "hidden": false}, {"templateId": "fldfvKMpO2pWXcGRwfhKcSac", "hidden": false}, {"templateId": "fldwWcGSYCp5R65dA9Etdd8m", "hidden": false}, {"templateId": "fld6Qsez7HewZcBrhiBW6HPG", "hidden": false}, {"templateId": "flduJCmipJ5lzOHK0gjFFkHv", "hidden": true}, {"templateId": "fldA8qsyzAKMuoZwbtKr3Zud", "hidden": true}, {"templateId": "fldKGEDH3UCkIUh3BMlqjlFi", "hidden": true}]}, {"type": "TABLE", "templateId": "viw4cgdP3h4HcP6HfcA8psBQ", "name": "All", "filters": {"conjunction": "And", "conditions": [], "conds": []}, "sorts": [], "fields": [{"templateId": "fldmDM8wdqiVfwkZPVWQS9EO", "hidden": false}, {"templateId": "fldfvKMpO2pWXcGRwfhKcSac", "hidden": false}, {"templateId": "fldwWcGSYCp5R65dA9Etdd8m", "hidden": false, "width": 296}, {"templateId": "fld6Qsez7HewZcBrhiBW6HPG", "hidden": false, "width": 144}, {"templateId": "flduJCmipJ5lzOHK0gjFFkHv", "hidden": false}, {"templateId": "fldA8qsyzAKMuoZwbtKr3Zud", "hidden": false}, {"templateId": "fldKGEDH3UCkIUh3BMlqjlFi", "hidden": false}], "groups": []}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldmDM8wdqiVfwkZPVWQS9EO", "privilege": "TYPE_EDIT", "name": "Name", "primary": true}, {"type": "EMAIL", "templateId": "fldfvKMpO2pWXcGRwfhKcSac", "privilege": "FULL_EDIT", "name": "Contact email", "description": "For example: <EMAIL>", "required": true, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldwWcGSYCp5R65dA9Etdd8m", "privilege": "FULL_EDIT", "name": "Any other feedback or suggestions?", "description": "Feel free to share more thoughts.", "required": true, "primary": false}, {"type": "ATTACHMENT", "templateId": "fld6Qsez7HewZcBrhiBW6HPG", "privilege": "FULL_EDIT", "name": "Attachment", "description": "If you have any screenshots, please attach them to help us better understand your inquiry.", "required": false, "primary": false}, {"type": "CREATED_TIME", "templateId": "flduJCmipJ5lzOHK0gjFFkHv", "privilege": "FULL_EDIT", "name": "Created Time", "required": false, "property": {"dateFormat": "YYYY-MM-DD", "includeTime": false}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldA8qsyzAKMuoZwbtKr3Zud", "privilege": "FULL_EDIT", "name": "Status", "property": {"options": [{"id": "optiiLUDJRH5r", "name": "New", "color": "deepPurple"}, {"id": "opt8MbUfulzje", "name": "In Progress", "color": "indigo"}, {"id": "optzJmo4hDP1W", "name": "Resolved", "color": "blue"}, {"id": "optAywjU8SQMn", "name": "Closed", "color": "teal"}], "defaultValue": ""}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fldKGEDH3UCkIUh3BMlqjlFi", "privilege": "FULL_EDIT", "name": "Category", "property": {"options": [{"id": "optXD3j3Yx6sP", "name": "Bug", "color": "deepPurple"}, {"id": "optuZpn7bDra9", "name": "Feature Request", "color": "indigo"}, {"id": "optMoTsyhcwQr", "name": "Improvement", "color": "blue"}, {"id": "optLnTiWsy6No", "name": "Other", "color": "teal"}], "defaultValue": ""}, "primary": false}], "records": [{"templateId": "recSG6BzNg5PTjXJdZNGUHvZ", "data": {"fldA8qsyzAKMuoZwbtKr3Zud": ["opt8MbUfulzje"], "fldKGEDH3UCkIUh3BMlqjlFi": ["optXD3j3Yx6sP"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "flduJCmipJ5lzOHK0gjFFkHv": "2025-07-23T10:03:59.795Z", "fldwWcGSYCp5R65dA9Etdd8m": "Found a typo in the signup page."}, "values": {"fldA8qsyzAKMuoZwbtKr3Zud": ["In Progress"], "fldKGEDH3UCkIUh3BMlqjlFi": ["Bug"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "fldwWcGSYCp5R65dA9Etdd8m": "Found a typo in the signup page."}}, {"templateId": "recaAl59ZHctD8ciGjnYacXm", "data": {"fldA8qsyzAKMuoZwbtKr3Zud": ["optiiLUDJRH5r"], "fldKGEDH3UCkIUh3BMlqjlFi": ["optuZpn7bDra9"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "flduJCmipJ5lzOHK0gjFFkHv": "2025-07-23T10:03:59.795Z", "fldwWcGSYCp5R65dA9Etdd8m": "Could you add dark mode?"}, "values": {"fldA8qsyzAKMuoZwbtKr3Zud": ["New"], "fldKGEDH3UCkIUh3BMlqjlFi": ["Feature Request"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "fldwWcGSYCp5R65dA9Etdd8m": "Could you add dark mode?"}}, {"templateId": "recgHLkjX3YfOthszXARTLHC", "data": {"fldA8qsyzAKMuoZwbtKr3Zud": ["opt8MbUfulzje"], "fldKGEDH3UCkIUh3BMlqjlFi": ["optMoTsyhcwQr"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "flduJCmipJ5lzOHK0gjFFkHv": "2025-07-23T10:03:59.795Z", "fldwWcGSYCp5R65dA9Etdd8m": "The dashboard feels a bit slow today."}, "values": {"fldA8qsyzAKMuoZwbtKr3Zud": ["In Progress"], "fldKGEDH3UCkIUh3BMlqjlFi": ["Improvement"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "fldwWcGSYCp5R65dA9Etdd8m": "The dashboard feels a bit slow today."}}, {"templateId": "reccNv9QNlAinJWEU6AXFNS7", "data": {"fld6Qsez7HewZcBrhiBW6HPG": [], "fldA8qsyzAKMuoZwbtKr3Zud": ["optzJmo4hDP1W"], "fldKGEDH3UCkIUh3BMlqjlFi": ["optLnTiWsy6No"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "flduJCmipJ5lzOHK0gjFFkHv": "2025-07-23T10:03:59.795Z", "fldwWcGSYCp5R65dA9Etdd8m": "Great tool! Would love <PERSON><PERSON> support."}, "values": {"fld6Qsez7HewZcBrhiBW6HPG": [], "fldA8qsyzAKMuoZwbtKr3Zud": ["Resolved"], "fldKGEDH3UCkIUh3BMlqjlFi": ["Other"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "flduJCmipJ5lzOHK0gjFFkHv": "2025-07-23", "fldwWcGSYCp5R65dA9Etdd8m": "Great tool! Would love <PERSON><PERSON> support."}}, {"templateId": "rec1IMosJWf20lIKflymaURj", "data": {"fldA8qsyzAKMuoZwbtKr3Zud": ["optiiLUDJRH5r"], "fldKGEDH3UCkIUh3BMlqjlFi": ["optXD3j3Yx6sP"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "flduJCmipJ5lzOHK0gjFFkHv": "2025-07-23T10:03:59.796Z", "fldwWcGSYCp5R65dA9Etdd8m": "Unable to upload files larger than 5MB."}, "values": {"fldA8qsyzAKMuoZwbtKr3Zud": ["New"], "fldKGEDH3UCkIUh3BMlqjlFi": ["Bug"], "fldfvKMpO2pWXcGRwfhKcSac": "<EMAIL>", "fldmDM8wdqiVfwkZPVWQS9EO": "<PERSON>", "flduJCmipJ5lzOHK0gjFFkHv": "2025-07-23", "fldwWcGSYCp5R65dA9Etdd8m": "Unable to upload files larger than 5MB."}}]}], "initMissions": []}