import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'customer-order-management',
  name: {
    en: 'Customer Order Management',
    'zh-CN': '客户订单管理',
  },
  description: {
    en: 'The customer order management template is suitable for highly customized products that require manual entry. It covers key processes from order input to customer notification, sales follow-up, and weekly report generation. By using automated email reminders and weekly order summaries, it ensures that customers receive timely order information. Additionally, it generates and sends weekly transaction reports to sales personnel and team leaders, enhancing the efficiency of the customer team and helping them effectively track customer information and manage order processes uniformly.',
    'zh-CN':
      '客户订单管理模板适用于需手工录入的高度定制化产品，涵盖从订单录入到客户通知、销售跟进、周报生成等关键流程。通过自动邮件提醒和每周订单汇总，确保客户及时获取订单信息，还能每周定时生成并推送销售人员与团队负责人的成交报告，提升客户团队的效率，帮助团队高效追踪客户信息、统一管理订单流程。\n',
  },
  cover: '/assets/template/customer-order-management/customer-order-management.png',
  author: 'zhan<PERSON><PERSON><PERSON> <<EMAIL>>',
  category: ['sales', 'project'],
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.1.2',
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoEJ6LmEu2ayGSTOakPaGco',
      name: {
        en: 'Email Notification of Completed Orders',
        'zh-CN': '邮箱推送已成交订单',
      },
      description: {
        en: "When a new record is created in the contract order table, send a purchase detail notification to the corresponding customer email and the sales follow-up person's email.",
        'zh-CN': '当合同订单表有新的记录创建时，向对应的客户邮箱和销售跟进人邮箱推送一份购买详情。',
      },
      triggers: [
        {
          triggerType: 'RECORD_CREATED',
          templateId: 'trgJpnuhEk1D3JK8OW0OEUoV',
          description: {
            en: 'When a new order is entered.',
            'zh-CN': '有新的订单录入时',
          },
          input: {
            type: 'DATABASE',
            databaseTemplateId: 'datczF7CI7SercIAFwSKAFKe',
          },
        },
      ],
      actions: [
        {
          templateId: 'actFL7GoF5Y4Z8hRr52FAUxu',
          description: {
            en: "Send purchase details to the corresponding customer's email and the sales follow-up person.",
            'zh-CN': '推送购买详情给对应客户邮箱和销售跟进人',
          },
          actionType: 'SEND_EMAIL',
          input: {
            type: 'SERVICE',
            subject: 'Thank You for Your Purchase! ',
            body: {
              markdown:
                "Dear <%= _triggers.trgJpnuhEk1D3JK8OW0OEUoV.record.cells.fldrvhkwW5AftWCS3niZMZ1J.value.join(', ') %> Customer,   \n" +
                '\n' +
                'Thank you for purchasing our products! We are very pleased that you chose us. Here are the details of your order:            \n' +
                '            \n' +
                "Selected Version: <%= _triggers.trgJpnuhEk1D3JK8OW0OEUoV.record.cells.fld9t90Ri6nsFKUv1MjezsvG.value.join(', ') %>     \n" +
                'Quantity:  <%= _triggers.trgJpnuhEk1D3JK8OW0OEUoV.record.cells.fldZTlhpBrqabWo1HXoUFGzz.value %>        \n' +
                'Contract Amount: <%= _triggers.trgJpnuhEk1D3JK8OW0OEUoV.record.cells.fldwXfBPStDLoRwxwaP7lWTs.value %>          \n' +
                '            \n' +
                'If you have any questions, please feel free to contact us. We are here to serve you!            \n' +
                'Thank you once again for your trust and support!            \n' +
                '            \n' +
                'Best regards,            \n' +
                'Wegoo Cloud Co., Ltd.',
            },
            to: [
              {
                type: 'USER',
                userId: '<%= _triggers.trgJpnuhEk1D3JK8OW0OEUoV.record.cells.fldgaJx62Adi3yzdHiq8cMp6.data %>',
              },
              {
                type: 'EMAIL_STRING',
                email: '<%= _triggers.trgJpnuhEk1D3JK8OW0OEUoV.record.cells.fldMy20lTRjJ43FMqOmUEiyp.value %>',
              },
            ],
            senderName: '',
            cc: [],
            bcc: [],
            replyTo: [],
          },
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atoOXdDJdOJeooIQPTuCdrHk',
      name: {
        en: 'Summarize Personal Orders for This Week',
        'zh-CN': '汇总个人本周订单',
      },
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'trgqoOGZ8dOstIore3ecWgq4',
          description: {
            en: 'Start every Friday at 6 PM.',
            'zh-CN': '每周五18点启动',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['FRI'],
                },
              },
              datetime: '2025-01-03T10:00:48.878Z',
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'acteV3RuLUeEGr6irK498MzE',
          description: {
            en: "Find the member's transaction orders for this week.",
            'zh-CN': '查找成员本周成交订单',
          },
          actionType: 'FIND_MEMBERS',
          input: {
            type: 'MEMBER',
            by: [
              {
                type: 'MEMBER_FIELD',
                fieldTemplateId: 'fldgaJx62Adi3yzdHiq8cMp6',
                databaseTemplateId: 'datczF7CI7SercIAFwSKAFKe',
              },
            ],
          },
        },
        {
          templateId: 'actHRAymE63K9iw4u9LgBcgo',
          actionType: 'LOOP',
          input: {
            type: 'LOOP',
            actionTemplateId: 'acteV3RuLUeEGr6irK498MzE',
            path: 'members',
          },
          actions: [
            {
              templateId: 'actPlUdnyuzAHt2hkreB6h7w',
              description: {
                en: 'Find the sales orders for each member this week.',
                'zh-CN': '查找本周每个成员成交订单',
              },
              actionType: 'FIND_RECORDS',
              input: {
                type: 'DATABASE_WITH_FILTER',
                filters: {
                  conjunction: 'And',
                  conditions: [],
                  conds: [
                    {
                      fieldTemplateId: 'fldgaJx62Adi3yzdHiq8cMp6',
                      fieldType: 'CREATED_BY',
                      clause: {
                        operator: 'Is',
                        value: '<%= _item.id %>',
                      },
                    },
                    {
                      fieldTemplateId: 'fldUlWIaAmECteaDMjSRjyBD',
                      fieldType: 'DATETIME',
                      clause: {
                        operator: 'Is',
                        value: ['ThisWeek'],
                      },
                    },
                    {
                      fieldTemplateId: 'fldwXfBPStDLoRwxwaP7lWTs',
                      fieldType: 'FORMULA',
                      clause: {
                        operator: 'IsNotEmpty',
                      },
                    },
                  ],
                },
                databaseTemplateId: 'datczF7CI7SercIAFwSKAFKe',
              },
            },
            {
              templateId: 'actdJxvRlMJgsSzdYcDAtty0',
              description: {
                en: "Send this week's individual sales report to the corresponding sales follow-up person.",
                'zh-CN': '发送本周个人成交报告给对应销售跟进人',
              },
              actionType: 'SEND_REPORT',
              input: {
                to: [
                  {
                    type: 'SPECIFY_UNITS',
                    unitIds: ['<%= _item.id %>'],
                  },
                ],
                markdown:
                  '## You have a total of <%= _itemActions.actPlUdnyuzAHt2hkreB6h7w.records.length %> sales orders this week.  \n' +
                  '      \n' +
                  '<%= _renderRecordsAsGrid(_itemActions.actPlUdnyuzAHt2hkreB6h7w.records, ["fldgaJx62Adi3yzdHiq8cMp6","fldrvhkwW5AftWCS3niZMZ1J","fld9t90Ri6nsFKUv1MjezsvG","fldZTlhpBrqabWo1HXoUFGzz","fld7UT0AcRQaDANLa6hhhZhT","fldwXfBPStDLoRwxwaP7lWTs"]) %>',
                json: {
                  type: 'doc',
                  content: [
                    {
                      type: 'paragraph',
                      content: [
                        {
                          text: '## You have a total of ',
                          type: 'text',
                        },
                        {
                          type: 'variable',
                          attrs: {
                            ids: '_itemActions,actPlUdnyuzAHt2hkreB6h7w,records,length',
                            tips: '',
                            names: '当前执行器,查找记录,records,length',
                          },
                        },
                        {
                          text: 'sales orders this week.',
                          type: 'text',
                        },
                        {
                          type: 'hardBreak',
                        },
                        {
                          text: '    ',
                          type: 'text',
                        },
                        {
                          type: 'hardBreak',
                        },
                        {
                          type: 'variable',
                          attrs: {
                            ids: '_renderRecordsAsGrid(_itemActions.actPlUdnyuzAHt2hkreB6h7w.records, ["fldgaJx62Adi3yzdHiq8cMp6","fldrvhkwW5AftWCS3niZMZ1J","fld9t90Ri6nsFKUv1MjezsvG","fldZTlhpBrqabWo1HXoUFGzz","fld7UT0AcRQaDANLa6hhhZhT","fldwXfBPStDLoRwxwaP7lWTs"])',
                            tips: '选中的字段: 销售跟进人, 公司名字, 选购产品, 采购时长, 单价, 合同金额',
                            names: '当前执行器,查找记录,网格列表',
                          },
                        },
                      ],
                    },
                  ],
                },
                subject: 'Personal Sales Report for This Week',
                type: 'MARKDOWN',
              },
            },
          ],
        },
      ],
    },
    {
      resourceType: 'AUTOMATION',
      templateId: 'atow5gc5pqcFqoNbB6rxS2qF',
      name: {
        en: 'Summarize Team Orders for This Week',
        'zh-CN': '汇总团队本周订单',
      },
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'trgz8Ca4EtQfQa7ArTxFYZvh',
          description: {
            en: 'Start every Friday at 6 PM.',
            'zh-CN': '每周五18点启动',
          },
          input: {
            type: 'SCHEDULER',
            scheduler: {
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['FRI'],
                },
              },
              datetime: '2024-12-11T10:00:44.786Z',
            },
          },
        },
      ],
      actions: [
        {
          templateId: 'actYBPGDViEk9Ogp78Z8V9Ce',
          description: {
            en: "Find Team's Completed Orders for This Week",
            'zh-CN': '查找团队本周成交订单',
          },
          actionType: 'FIND_RECORDS',
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldUlWIaAmECteaDMjSRjyBD',
                  fieldType: 'DATETIME',
                  clause: {
                    operator: 'Is',
                    value: ['ThisWeek'],
                  },
                },
                {
                  fieldTemplateId: 'fldwXfBPStDLoRwxwaP7lWTs',
                  fieldType: 'FORMULA',
                  clause: {
                    operator: 'IsNotEmpty',
                  },
                },
              ],
            },
            databaseTemplateId: 'datczF7CI7SercIAFwSKAFKe',
          },
        },
        {
          templateId: 'actyX1tKBQmaeicGzqOf0Zns',
          description: {
            en: "Send this week's team sales report to the team leader.",
            'zh-CN': '发送本周团队成交报告给团队负责人',
          },
          actionType: 'SEND_REPORT',
          input: {
            to: [
              {
                type: 'SPECIFY_UNITS',
                unitIds: [],
              },
              {
                type: 'CURRENT_OPERATOR',
              },
            ],
            markdown:
              '### Number of Sales Orders: <%= _actions.actYBPGDViEk9Ogp78Z8V9Ce.records.length %>   \n' +
              '      \n' +
              '### Sales Order & Procurement Report:  \n' +
              '      \n' +
              '<%= _renderRecordsAsGrid(_actions.actYBPGDViEk9Ogp78Z8V9Ce.records, ["fldgaJx62Adi3yzdHiq8cMp6","fldrvhkwW5AftWCS3niZMZ1J","fld9t90Ri6nsFKUv1MjezsvG","fldZTlhpBrqabWo1HXoUFGzz","fld7UT0AcRQaDANLa6hhhZhT","fldwXfBPStDLoRwxwaP7lWTs"]) %>',
            json: {
              type: 'doc',
              content: [
                {
                  type: 'paragraph',
                  content: [
                    {
                      text: '### Number of Sales Orders: ',
                      type: 'text',
                    },
                    {
                      type: 'variable',
                      attrs: {
                        ids: '_actions,actYBPGDViEk9Ogp78Z8V9Ce,records,length',
                        tips: '',
                        names: '执行器,查找记录,records,length',
                      },
                    },
                    {
                      text: ' ',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '    ',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '### Sales TCV Detail Report:',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      text: '    ',
                      type: 'text',
                    },
                    {
                      type: 'hardBreak',
                    },
                    {
                      type: 'variable',
                      attrs: {
                        ids: '_renderRecordsAsGrid(_actions.actYBPGDViEk9Ogp78Z8V9Ce.records, ["fldgaJx62Adi3yzdHiq8cMp6","fldrvhkwW5AftWCS3niZMZ1J","fld9t90Ri6nsFKUv1MjezsvG","fldZTlhpBrqabWo1HXoUFGzz","fld7UT0AcRQaDANLa6hhhZhT","fldwXfBPStDLoRwxwaP7lWTs"])',
                        tips: '选中的字段: 销售跟进人, 公司名字, 选购产品, 采购时长, 单价, 合同金额',
                        names: '执行器,查找记录,网格列表',
                      },
                    },
                  ],
                },
              ],
            },
            subject: 'Team Sales Report for This Week',
            type: 'MARKDOWN',
          },
        },
      ],
    },
    {
      resourceType: 'FORM',
      templateId: 'fomXqMM7YUl4xIv53ek0Xlo9',
      name: {
        en: 'Order Entry Form',
        'zh-CN': '订单录入表',
      },
      description: {
        en: 'Please fill in the customer transaction information based on the actual situation.\n',
        'zh-CN': '请根据实际情况填写客户交易信息。\n',
      },
      brandLogo: {
        type: 'EMOJI',
        backgroundColor: '',
        emoji: '✍️',
      },
      formType: 'DATABASE',
      databaseTemplateId: 'datczF7CI7SercIAFwSKAFKe',
      metadata: {
        type: 'VIEW',
        viewTemplateId: 'viw2UjQ92SD2VRzKjozoMip6',
      },
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datczF7CI7SercIAFwSKAFKe',
      name: {
        en: 'Contract Order',
        'zh-CN': '合同订单表',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viw2UjQ92SD2VRzKjozoMip6',
          name: {
            en: 'Order Entry View',
            'zh-CN': '订单录入视图',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldQoz1M9Ax4ewQ121d7ckhj',
              hidden: false,
              width: 231,
            },
            {
              templateId: 'fldrvhkwW5AftWCS3niZMZ1J',
              hidden: false,
              width: 148,
            },
            {
              templateId: 'fldkgm8xMWetVNgGQphJUYZy',
              hidden: false,
              width: 123,
            },
            {
              templateId: 'fld9t90Ri6nsFKUv1MjezsvG',
              hidden: false,
              width: 145,
            },
            {
              templateId: 'fldzc6IwGvCvIc1eu3I0rGJy',
              hidden: false,
              width: 123,
            },
            {
              templateId: 'fldZTlhpBrqabWo1HXoUFGzz',
              hidden: false,
              width: 141,
            },
            {
              templateId: 'fldwXfBPStDLoRwxwaP7lWTs',
              hidden: false,
              width: 173,
            },
            {
              templateId: 'fldUlWIaAmECteaDMjSRjyBD',
              hidden: false,
              width: 172,
            },
            {
              templateId: 'fldITOJm5YNv5JmlwhfxUDTi',
              hidden: false,
              width: 150,
            },
            {
              templateId: 'fldmhlnt76eNVCskKvgC3z1W',
              hidden: false,
              width: 157,
            },
            {
              templateId: 'fld9RS15admbouPZENxJaMA3',
              hidden: false,
            },
            {
              templateId: 'fldMy20lTRjJ43FMqOmUEiyp',
              hidden: false,
            },
            {
              templateId: 'fldfBadlWhM1Ltgdh9PxgFs4',
              hidden: true,
            },
            {
              templateId: 'fldcbEQK2pyM3s697XwePYww',
              hidden: true,
            },
            {
              templateId: 'fldgaJx62Adi3yzdHiq8cMp6',
              hidden: false,
              width: 147,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwXQ1wXaslWVpjHOzSsQIoU',
          name: {
            en: 'Sales Orders for This Week',
            'zh-CN': '本周成交订单',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldUlWIaAmECteaDMjSRjyBD',
                fieldType: 'DATETIME',
                clause: {
                  operator: 'Is',
                  value: ['ThisWeek'],
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldQoz1M9Ax4ewQ121d7ckhj',
              hidden: false,
              width: 165,
            },
            {
              templateId: 'fldrvhkwW5AftWCS3niZMZ1J',
              hidden: false,
              width: 141,
            },
            {
              templateId: 'fldkgm8xMWetVNgGQphJUYZy',
              hidden: false,
              width: 123,
            },
            {
              templateId: 'fldgaJx62Adi3yzdHiq8cMp6',
              hidden: false,
              width: 164,
            },
            {
              templateId: 'fldITOJm5YNv5JmlwhfxUDTi',
              hidden: true,
            },
            {
              templateId: 'fld9RS15admbouPZENxJaMA3',
              hidden: true,
            },
            {
              templateId: 'fldUlWIaAmECteaDMjSRjyBD',
              hidden: true,
            },
            {
              templateId: 'fldMy20lTRjJ43FMqOmUEiyp',
              hidden: true,
            },
            {
              templateId: 'fldmhlnt76eNVCskKvgC3z1W',
              hidden: true,
            },
            {
              templateId: 'fldfBadlWhM1Ltgdh9PxgFs4',
              hidden: true,
            },
            {
              templateId: 'fld9t90Ri6nsFKUv1MjezsvG',
              hidden: false,
              width: 152,
            },
            {
              templateId: 'fldZTlhpBrqabWo1HXoUFGzz',
              hidden: false,
              width: 142,
            },
            {
              templateId: 'fldwXfBPStDLoRwxwaP7lWTs',
              hidden: false,
              width: 145,
            },
            {
              templateId: 'fldcbEQK2pyM3s697XwePYww',
              hidden: true,
            },
            {
              templateId: 'fldzc6IwGvCvIc1eu3I0rGJy',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwTbr3gnaASgMmYVj5GFkUf',
          name: {
            en: 'Pending Contract Recovery',
            'zh-CN': '待回收合同',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fldfBadlWhM1Ltgdh9PxgFs4',
                fieldType: 'ATTACHMENT',
                clause: {
                  operator: 'IsEmpty',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldQoz1M9Ax4ewQ121d7ckhj',
              hidden: false,
              width: 127,
            },
            {
              templateId: 'fldrvhkwW5AftWCS3niZMZ1J',
              hidden: false,
              width: 139,
            },
            {
              templateId: 'fldkgm8xMWetVNgGQphJUYZy',
              hidden: true,
            },
            {
              templateId: 'fldgaJx62Adi3yzdHiq8cMp6',
              hidden: false,
              width: 173,
            },
            {
              templateId: 'fldITOJm5YNv5JmlwhfxUDTi',
              hidden: true,
              width: 157,
            },
            {
              templateId: 'fld9RS15admbouPZENxJaMA3',
              hidden: true,
            },
            {
              templateId: 'fldUlWIaAmECteaDMjSRjyBD',
              hidden: true,
            },
            {
              templateId: 'fldMy20lTRjJ43FMqOmUEiyp',
              hidden: true,
            },
            {
              templateId: 'fldmhlnt76eNVCskKvgC3z1W',
              hidden: true,
            },
            {
              templateId: 'fldfBadlWhM1Ltgdh9PxgFs4',
              hidden: false,
              width: 161,
            },
            {
              templateId: 'fld9t90Ri6nsFKUv1MjezsvG',
              hidden: true,
            },
            {
              templateId: 'fldZTlhpBrqabWo1HXoUFGzz',
              hidden: true,
            },
            {
              templateId: 'fldwXfBPStDLoRwxwaP7lWTs',
              hidden: true,
            },
            {
              templateId: 'fldcbEQK2pyM3s697XwePYww',
              hidden: true,
            },
            {
              templateId: 'fldzc6IwGvCvIc1eu3I0rGJy',
              hidden: false,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwY9DQVt7T118B1iAeMyzYo',
          name: {
            en: 'Pending Invoice View',
            'zh-CN': '待开票视图',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'fld9RS15admbouPZENxJaMA3',
                fieldType: 'CHECKBOX',
                clause: {
                  operator: 'Is',
                  value: true,
                },
              },
              {
                fieldTemplateId: 'fldcbEQK2pyM3s697XwePYww',
                fieldType: 'ATTACHMENT',
                clause: {
                  operator: 'IsEmpty',
                },
              },
            ],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldQoz1M9Ax4ewQ121d7ckhj',
              hidden: false,
              width: 176,
            },
            {
              templateId: 'fldkgm8xMWetVNgGQphJUYZy',
              hidden: true,
            },
            {
              templateId: 'fldrvhkwW5AftWCS3niZMZ1J',
              hidden: false,
              width: 142,
            },
            {
              templateId: 'fldgaJx62Adi3yzdHiq8cMp6',
              hidden: false,
              width: 140,
            },
            {
              templateId: 'fldwXfBPStDLoRwxwaP7lWTs',
              hidden: false,
              width: 173,
            },
            {
              templateId: 'fldUlWIaAmECteaDMjSRjyBD',
              hidden: true,
            },
            {
              templateId: 'fldMy20lTRjJ43FMqOmUEiyp',
              hidden: true,
            },
            {
              templateId: 'fldmhlnt76eNVCskKvgC3z1W',
              hidden: true,
            },
            {
              templateId: 'fldfBadlWhM1Ltgdh9PxgFs4',
              hidden: true,
            },
            {
              templateId: 'fld9RS15admbouPZENxJaMA3',
              hidden: false,
              width: 174,
            },
            {
              templateId: 'fld9t90Ri6nsFKUv1MjezsvG',
              hidden: true,
            },
            {
              templateId: 'fldZTlhpBrqabWo1HXoUFGzz',
              hidden: true,
            },
            {
              templateId: 'fldITOJm5YNv5JmlwhfxUDTi',
              hidden: false,
              width: 141,
            },
            {
              templateId: 'fldcbEQK2pyM3s697XwePYww',
              hidden: false,
            },
            {
              templateId: 'fldzc6IwGvCvIc1eu3I0rGJy',
              hidden: true,
            },
          ],
          groups: [],
        },
        {
          type: 'TABLE',
          templateId: 'viwu9SjvHFPKT3SLooZugKLc',
          name: {
            en: 'All',
            'zh-CN': '总视图',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldQoz1M9Ax4ewQ121d7ckhj',
              hidden: false,
              width: 302,
            },
            {
              templateId: 'fldrvhkwW5AftWCS3niZMZ1J',
              hidden: false,
              width: 146,
            },
            {
              templateId: 'fld9t90Ri6nsFKUv1MjezsvG',
              hidden: false,
              width: 122,
            },
            {
              templateId: 'fldZTlhpBrqabWo1HXoUFGzz',
              hidden: false,
              width: 140,
            },
            {
              templateId: 'fldwXfBPStDLoRwxwaP7lWTs',
              hidden: false,
              width: 149,
            },
            {
              templateId: 'fldkgm8xMWetVNgGQphJUYZy',
              hidden: false,
              width: 158,
            },
            {
              templateId: 'fldgaJx62Adi3yzdHiq8cMp6',
              hidden: false,
              width: 159,
            },
            {
              templateId: 'fldITOJm5YNv5JmlwhfxUDTi',
              hidden: false,
              width: 150,
            },
            {
              templateId: 'fld9RS15admbouPZENxJaMA3',
              hidden: false,
              width: 169,
            },
            {
              templateId: 'fldUlWIaAmECteaDMjSRjyBD',
              hidden: false,
            },
            {
              templateId: 'fldMy20lTRjJ43FMqOmUEiyp',
              hidden: false,
            },
            {
              templateId: 'fldmhlnt76eNVCskKvgC3z1W',
              hidden: false,
            },
            {
              templateId: 'fldfBadlWhM1Ltgdh9PxgFs4',
              hidden: false,
            },
            {
              templateId: 'fldcbEQK2pyM3s697XwePYww',
              hidden: false,
            },
            {
              templateId: 'fldzc6IwGvCvIc1eu3I0rGJy',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'FORMULA',
          templateId: 'fldQoz1M9Ax4ewQ121d7ckhj',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Name',
            'zh-CN': '明细',
          },
          required: false,
          property: {
            expressionTemplate:
              '{fldrvhkwW5AftWCS3niZMZ1J}&“--”&{fld9t90Ri6nsFKUv1MjezsvG}&“--”&{fldwXfBPStDLoRwxwaP7lWTs}',
          },
          primary: true,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldkgm8xMWetVNgGQphJUYZy',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Type',
            'zh-CN': '类型',
          },
          required: true,
          property: {
            options: [
              {
                id: 'optVbrQlBtz5ok3S6N3if83u',
                name: 'new',
                color: 'deepPurple',
              },
              {
                id: 'optq5d9KqMMKbanFK1r6NHF2',
                name: 'renewal',
                color: 'indigo',
              },
            ],
          },
          primary: false,
        },
        {
          type: 'CREATED_BY',
          templateId: 'fldgaJx62Adi3yzdHiq8cMp6',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Sales Follow-up Person',
            'zh-CN': '销售跟进人',
          },
          required: false,
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldITOJm5YNv5JmlwhfxUDTi',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Payment Receipt',
            'zh-CN': '付款凭证',
          },
          required: true,
          primary: false,
        },
        {
          type: 'CHECKBOX',
          templateId: 'fld9RS15admbouPZENxJaMA3',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Is Invoice Required',
            'zh-CN': '是否需要开票',
          },
          description: {
            en: 'If you need an invoice, please check the box: ✅',
            'zh-CN': '如需要开票请打✅',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldUlWIaAmECteaDMjSRjyBD',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Activation Time',
            'zh-CN': '开通时间',
          },
          required: false,
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'EMAIL',
          templateId: 'fldMy20lTRjJ43FMqOmUEiyp',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Email Address📮',
            'zh-CN': '邮箱📮',
          },
          description: {
            en: 'Customer order receipt email.',
            'zh-CN': '客户接收订单邮箱~',
          },
          required: true,
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldmhlnt76eNVCskKvgC3z1W',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Source of Funds',
            'zh-CN': '款项来源',
          },
          property: {
            options: [
              {
                id: 'optnKsfeMQCJmDygshyukQXX',
                name: 'PayPal',
                color: 'deepPurple',
              },
              {
                id: 'optyVAn6T8haIYe7MecxZ7Aq',
                name: 'Stripe',
                color: 'indigo',
              },
              {
                id: 'optnThbHBzO3tmZnQACNj5KD',
                name: 'Alipay',
                color: 'blue',
              },
            ],
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldfBadlWhM1Ltgdh9PxgFs4',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Contract Attachment',
            'zh-CN': '合同附件',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldrvhkwW5AftWCS3niZMZ1J',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Company Name',
            'zh-CN': '公司名字',
          },
          required: true,
          property: {
            foreignDatabaseTemplateId: 'datAJnaHnFCqiO9TMvOTh5df',
            brotherFieldTemplateId: 'fld9kWdUlBFHf5AVrkRUAoRK',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld9t90Ri6nsFKUv1MjezsvG',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Selected Version',
            'zh-CN': '选购版本',
          },
          required: true,
          property: {
            foreignDatabaseTemplateId: 'dat7WFmRdOls4NtGBczPc1Qg',
            brotherFieldTemplateId: 'fld5xc2JPYzrQiZ4aWShmCKY',
          },
          primary: false,
        },
        {
          type: 'NUMBER',
          templateId: 'fldZTlhpBrqabWo1HXoUFGzz',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Procurement Duration (Unit: Years)',
            'zh-CN': '采购时长（单位：年）',
          },
          required: true,
          property: {
            precision: 0,
            commaStyle: 'thousand',
            symbol: '',
            symbolAlign: 'right',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldwXfBPStDLoRwxwaP7lWTs',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Contract Amount',
            'zh-CN': '合同金额',
          },
          required: false,
          property: {
            expressionTemplate: '{fldzc6IwGvCvIc1eu3I0rGJy}*{fldZTlhpBrqabWo1HXoUFGzz}',
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldcbEQK2pyM3s697XwePYww',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Invoice Attachment',
            'zh-CN': '发票附件',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldzc6IwGvCvIc1eu3I0rGJy',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Unit Price',
            'zh-CN': '单价',
          },
          required: false,
          property: {
            relatedLinkFieldTemplateId: 'fld9t90Ri6nsFKUv1MjezsvG',
            lookupTargetFieldTemplateId: 'fldVnd0Cgg3tkCEMYaaaTz3O',
            lookupTargetFieldType: 'CURRENCY',
            dataType: 'NUMBER',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
            formatting: {
              type: 'NUMBER',
              property: {},
            },
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recW5FGbHlVAD4iml0StZm9r',
          data: {
            fld9RS15admbouPZENxJaMA3: true,
            fld9t90Ri6nsFKUv1MjezsvG: ['recUPfaj08QvogCNEcEmloq9'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Future Tech LLC--Premium Custom--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 1,
            fldcbEQK2pyM3s697XwePYww: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldfBadlWhM1Ltgdh9PxgFs4: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldgaJx62Adi3yzdHiq8cMp6: null,
            fldkgm8xMWetVNgGQphJUYZy: ['optq5d9KqMMKbanFK1r6NHF2'],
            fldmhlnt76eNVCskKvgC3z1W: ['optnThbHBzO3tmZnQACNj5KD'],
            fldrvhkwW5AftWCS3niZMZ1J: ['recHBcIRSka7kBWFQKCWrILy'],
            fldwXfBPStDLoRwxwaP7lWTs: 100000,
            fldzc6IwGvCvIc1eu3I0rGJy: 50000,
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '1',
            fld9t90Ri6nsFKUv1MjezsvG: ['Premium Custom'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Future Tech LLC--Premium Custom--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20',
            fldZTlhpBrqabWo1HXoUFGzz: '1',
            fldcbEQK2pyM3s697XwePYww: ['image.png'],
            fldfBadlWhM1Ltgdh9PxgFs4: ['image.png'],
            fldgaJx62Adi3yzdHiq8cMp6: 'linxiaoxin',
            fldkgm8xMWetVNgGQphJUYZy: ['renewal'],
            fldmhlnt76eNVCskKvgC3z1W: ['Alipay'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Future Tech LLC'],
            fldwXfBPStDLoRwxwaP7lWTs: '100000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['50000'],
          },
        },
        {
          templateId: 'recafmUbOJaigchM5ZbAUiQV',
          data: {
            fld9RS15admbouPZENxJaMA3: true,
            fld9t90Ri6nsFKUv1MjezsvG: ['recoJGzrFl1TlV0Il2HXaFlp'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Health Plus Corp--Dedicated Deployment--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 1,
            fldcbEQK2pyM3s697XwePYww: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldfBadlWhM1Ltgdh9PxgFs4: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldgaJx62Adi3yzdHiq8cMp6: null,
            fldkgm8xMWetVNgGQphJUYZy: ['optVbrQlBtz5ok3S6N3if83u'],
            fldmhlnt76eNVCskKvgC3z1W: ['optnKsfeMQCJmDygshyukQXX'],
            fldrvhkwW5AftWCS3niZMZ1J: ['recRmnmrDuKfsm0NBzIZksiB'],
            fldwXfBPStDLoRwxwaP7lWTs: 100000,
            fldzc6IwGvCvIc1eu3I0rGJy: 30000,
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '1',
            fld9t90Ri6nsFKUv1MjezsvG: ['Dedicated Deployment'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Health Plus Corp--Dedicated Deployment--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20',
            fldZTlhpBrqabWo1HXoUFGzz: '1',
            fldcbEQK2pyM3s697XwePYww: ['image.png'],
            fldfBadlWhM1Ltgdh9PxgFs4: ['image.png'],
            fldgaJx62Adi3yzdHiq8cMp6: 'linxiaoxin',
            fldkgm8xMWetVNgGQphJUYZy: ['new'],
            fldmhlnt76eNVCskKvgC3z1W: ['PayPal'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Health Plus Corp'],
            fldwXfBPStDLoRwxwaP7lWTs: '100000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['30000'],
          },
        },
        {
          templateId: 'recYaWw04vJ2L4oj7CeIwcUi',
          data: {
            fld9RS15admbouPZENxJaMA3: true,
            fld9t90Ri6nsFKUv1MjezsvG: ['recnjwPnpQR6CUm3L2iKWYWj'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Green Energy Inc.--Private Deployment--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 1,
            fldfBadlWhM1Ltgdh9PxgFs4: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldgaJx62Adi3yzdHiq8cMp6: null,
            fldkgm8xMWetVNgGQphJUYZy: ['optVbrQlBtz5ok3S6N3if83u'],
            fldmhlnt76eNVCskKvgC3z1W: ['optyVAn6T8haIYe7MecxZ7Aq'],
            fldrvhkwW5AftWCS3niZMZ1J: ['recEuqHFZCJkvCOU5DuhNJY5'],
            fldwXfBPStDLoRwxwaP7lWTs: 100000,
            fldzc6IwGvCvIc1eu3I0rGJy: 20000,
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '1',
            fld9t90Ri6nsFKUv1MjezsvG: ['Private Deployment'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Green Energy Inc.--Private Deployment--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20',
            fldZTlhpBrqabWo1HXoUFGzz: '1',
            fldfBadlWhM1Ltgdh9PxgFs4: ['image.png'],
            fldgaJx62Adi3yzdHiq8cMp6: 'zhanpeiwei',
            fldkgm8xMWetVNgGQphJUYZy: ['new'],
            fldmhlnt76eNVCskKvgC3z1W: ['Stripe'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Green Energy Inc.'],
            fldwXfBPStDLoRwxwaP7lWTs: '100000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['20000'],
          },
        },
        {
          templateId: 'recKecz8BRO1WVxl7tvYz7YV',
          data: {
            fld9RS15admbouPZENxJaMA3: true,
            fld9t90Ri6nsFKUv1MjezsvG: ['recYq5AMwPlYdtylqTXcSIku'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Health Plus Corp--Enterprise Exclusive--26800',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 1,
            fldgaJx62Adi3yzdHiq8cMp6: null,
            fldkgm8xMWetVNgGQphJUYZy: ['optq5d9KqMMKbanFK1r6NHF2'],
            fldmhlnt76eNVCskKvgC3z1W: ['optyVAn6T8haIYe7MecxZ7Aq'],
            fldrvhkwW5AftWCS3niZMZ1J: ['recRmnmrDuKfsm0NBzIZksiB'],
            fldwXfBPStDLoRwxwaP7lWTs: 26800,
            fldzc6IwGvCvIc1eu3I0rGJy: 100000,
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '1',
            fld9t90Ri6nsFKUv1MjezsvG: ['Enterprise Exclusive'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Health Plus Corp--Enterprise Exclusive--26800',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20',
            fldZTlhpBrqabWo1HXoUFGzz: '1',
            fldgaJx62Adi3yzdHiq8cMp6: 'zhanpeiwei',
            fldkgm8xMWetVNgGQphJUYZy: ['renewal'],
            fldmhlnt76eNVCskKvgC3z1W: ['Stripe'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Health Plus Corp'],
            fldwXfBPStDLoRwxwaP7lWTs: '26800',
            fldzc6IwGvCvIc1eu3I0rGJy: ['100000'],
          },
        },
        {
          templateId: 'recJ89UdkJN9HGhLr0EWN0yt',
          data: {
            fld9RS15admbouPZENxJaMA3: true,
            fld9t90Ri6nsFKUv1MjezsvG: ['recoJGzrFl1TlV0Il2HXaFlp'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattHt34a3Jn6nvhthj4TuBUf',
                name: 'image.png',
                path: 'template/tplattHt34a3Jn6nvhthj4TuBUf.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 24551,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Dedicated Deployment--30000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 1,
            fldgaJx62Adi3yzdHiq8cMp6: 'usrjnFj4IsQSBTWmWCUoWg20',
            fldkgm8xMWetVNgGQphJUYZy: ['optq5d9KqMMKbanFK1r6NHF2'],
            fldmhlnt76eNVCskKvgC3z1W: ['optnKsfeMQCJmDygshyukQXX'],
            fldrvhkwW5AftWCS3niZMZ1J: ['reczmToii8LAMKJxwk1ILOc9'],
            fldwXfBPStDLoRwxwaP7lWTs: 30000,
            fldzc6IwGvCvIc1eu3I0rGJy: 30000,
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '1',
            fld9t90Ri6nsFKUv1MjezsvG: ['Dedicated Deployment'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Dedicated Deployment--30000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-20',
            fldZTlhpBrqabWo1HXoUFGzz: '1',
            fldgaJx62Adi3yzdHiq8cMp6: 'zhanpeiwei',
            fldkgm8xMWetVNgGQphJUYZy: ['renewal'],
            fldmhlnt76eNVCskKvgC3z1W: ['PayPal'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Tech Innovations'],
            fldwXfBPStDLoRwxwaP7lWTs: '30000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['30000'],
          },
        },
        {
          templateId: 'recB9E1VFakqWqkWe8kzBqxH',
          data: {
            fld9RS15admbouPZENxJaMA3: true,
            fld9t90Ri6nsFKUv1MjezsvG: ['recYq5AMwPlYdtylqTXcSIku'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattH5xdTuH4QPpoENIeaikdT',
                name: 'image.png',
                path: 'template/tplattH5xdTuH4QPpoENIeaikdT.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 13731,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Enterprise Exclusive--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-22T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 1,
            fldgaJx62Adi3yzdHiq8cMp6: 'usrjnFj4IsQSBTWmWCUoWg20',
            fldkgm8xMWetVNgGQphJUYZy: ['optVbrQlBtz5ok3S6N3if83u'],
            fldmhlnt76eNVCskKvgC3z1W: ['optnKsfeMQCJmDygshyukQXX'],
            fldrvhkwW5AftWCS3niZMZ1J: ['reczmToii8LAMKJxwk1ILOc9'],
            fldwXfBPStDLoRwxwaP7lWTs: 100000,
            fldzc6IwGvCvIc1eu3I0rGJy: 100000,
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '1',
            fld9t90Ri6nsFKUv1MjezsvG: ['Enterprise Exclusive'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Enterprise Exclusive--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-22',
            fldZTlhpBrqabWo1HXoUFGzz: '1',
            fldgaJx62Adi3yzdHiq8cMp6: 'zhanpeiwei',
            fldkgm8xMWetVNgGQphJUYZy: ['new'],
            fldmhlnt76eNVCskKvgC3z1W: ['PayPal'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Tech Innovations'],
            fldwXfBPStDLoRwxwaP7lWTs: '100000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['100000'],
          },
        },
        {
          templateId: 'recHtGXKPIl3wvTCHsT1MRFF',
          data: {
            fld9t90Ri6nsFKUv1MjezsvG: ['rec2DEv6wR1VNz4WzQjgAcpd'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattJz41KGcXGgCtLETdfrz6l',
                name: 'image.png',
                path: 'template/tplattJz41KGcXGgCtLETdfrz6l.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 31049,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Custom Edition--80000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-22T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 2,
            fldgaJx62Adi3yzdHiq8cMp6: 'usrjnFj4IsQSBTWmWCUoWg20',
            fldkgm8xMWetVNgGQphJUYZy: ['optVbrQlBtz5ok3S6N3if83u'],
            fldmhlnt76eNVCskKvgC3z1W: ['optnKsfeMQCJmDygshyukQXX'],
            fldrvhkwW5AftWCS3niZMZ1J: ['reczmToii8LAMKJxwk1ILOc9'],
            fldwXfBPStDLoRwxwaP7lWTs: 80000,
            fldzc6IwGvCvIc1eu3I0rGJy: 40000,
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '0',
            fld9t90Ri6nsFKUv1MjezsvG: ['Custom Edition'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Custom Edition--80000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-04-22',
            fldZTlhpBrqabWo1HXoUFGzz: '2',
            fldgaJx62Adi3yzdHiq8cMp6: 'zhanpeiwei',
            fldkgm8xMWetVNgGQphJUYZy: ['new'],
            fldmhlnt76eNVCskKvgC3z1W: ['PayPal'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Tech Innovations'],
            fldwXfBPStDLoRwxwaP7lWTs: '80000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['40000'],
          },
        },
        {
          templateId: 'rectmY1cqm177zXrSEkCS6vs',
          data: {
            fld9RS15admbouPZENxJaMA3: true,
            fld9t90Ri6nsFKUv1MjezsvG: ['recYq5AMwPlYdtylqTXcSIku'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattaWlkGq7teng5XjuxyqdKy',
                name: 'image.png',
                path: 'template/tplattaWlkGq7teng5XjuxyqdKy.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 15424,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Enterprise Exclusive--22200000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-05-13T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 222,
            fldgaJx62Adi3yzdHiq8cMp6: 'usrjnFj4IsQSBTWmWCUoWg20',
            fldkgm8xMWetVNgGQphJUYZy: ['optVbrQlBtz5ok3S6N3if83u'],
            fldmhlnt76eNVCskKvgC3z1W: ['optnKsfeMQCJmDygshyukQXX'],
            fldrvhkwW5AftWCS3niZMZ1J: ['reczmToii8LAMKJxwk1ILOc9'],
            fldwXfBPStDLoRwxwaP7lWTs: 22200000,
            fldzc6IwGvCvIc1eu3I0rGJy: [100000],
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '1',
            fld9t90Ri6nsFKUv1MjezsvG: ['Enterprise Exclusive'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Enterprise Exclusive--22200000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-05-13',
            fldZTlhpBrqabWo1HXoUFGzz: '222',
            fldgaJx62Adi3yzdHiq8cMp6: 'zhanpeiwei',
            fldkgm8xMWetVNgGQphJUYZy: ['new'],
            fldmhlnt76eNVCskKvgC3z1W: ['PayPal'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Tech Innovations'],
            fldwXfBPStDLoRwxwaP7lWTs: '22200000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['100000'],
          },
        },
        {
          templateId: 'recBxWdKN9GGHgesedRYrGkJ',
          data: {
            fld9RS15admbouPZENxJaMA3: true,
            fld9t90Ri6nsFKUv1MjezsvG: ['recYq5AMwPlYdtylqTXcSIku'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattzCPS15bvFZnKyAge8QzyH',
                name: 'image.png',
                path: 'template/tplattzCPS15bvFZnKyAge8QzyH.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 13717,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Enterprise Exclusive--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-05-13T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 1,
            fldgaJx62Adi3yzdHiq8cMp6: 'usrjnFj4IsQSBTWmWCUoWg20',
            fldkgm8xMWetVNgGQphJUYZy: ['optVbrQlBtz5ok3S6N3if83u'],
            fldmhlnt76eNVCskKvgC3z1W: ['optnKsfeMQCJmDygshyukQXX'],
            fldrvhkwW5AftWCS3niZMZ1J: ['reczmToii8LAMKJxwk1ILOc9'],
            fldwXfBPStDLoRwxwaP7lWTs: 100000,
            fldzc6IwGvCvIc1eu3I0rGJy: [100000],
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '1',
            fld9t90Ri6nsFKUv1MjezsvG: ['Enterprise Exclusive'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Enterprise Exclusive--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-05-13',
            fldZTlhpBrqabWo1HXoUFGzz: '1',
            fldgaJx62Adi3yzdHiq8cMp6: 'zhanpeiwei',
            fldkgm8xMWetVNgGQphJUYZy: ['new'],
            fldmhlnt76eNVCskKvgC3z1W: ['PayPal'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Tech Innovations'],
            fldwXfBPStDLoRwxwaP7lWTs: '100000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['100000'],
          },
        },
        {
          templateId: 'recqJSUfDSxmImGbo3XMjvNZ',
          data: {
            fld9RS15admbouPZENxJaMA3: true,
            fld9t90Ri6nsFKUv1MjezsvG: ['recYq5AMwPlYdtylqTXcSIku'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattrPMhC6D6zzFOrjXOML2Lt',
                name: 'image.png',
                path: 'template/tplattrPMhC6D6zzFOrjXOML2Lt.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 46455,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'E-Commerce Inc.--Enterprise Exclusive--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-05-13T16:00:00.000Z',
            fldZTlhpBrqabWo1HXoUFGzz: 1,
            fldgaJx62Adi3yzdHiq8cMp6: 'usrPQWH8Bgf4Geh1COsD7yBj',
            fldkgm8xMWetVNgGQphJUYZy: ['optVbrQlBtz5ok3S6N3if83u'],
            fldmhlnt76eNVCskKvgC3z1W: ['optnThbHBzO3tmZnQACNj5KD'],
            fldrvhkwW5AftWCS3niZMZ1J: ['recWIZIdMlol19VSlCpiZ110'],
            fldwXfBPStDLoRwxwaP7lWTs: 100000,
            fldzc6IwGvCvIc1eu3I0rGJy: [100000],
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '1',
            fld9t90Ri6nsFKUv1MjezsvG: ['Enterprise Exclusive'],
            fldITOJm5YNv5JmlwhfxUDTi: ['image.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'E-Commerce Inc.--Enterprise Exclusive--100000',
            fldUlWIaAmECteaDMjSRjyBD: '2025-05-13',
            fldZTlhpBrqabWo1HXoUFGzz: '1',
            fldgaJx62Adi3yzdHiq8cMp6: 'linxiaoxin',
            fldkgm8xMWetVNgGQphJUYZy: ['new'],
            fldmhlnt76eNVCskKvgC3z1W: ['Alipay'],
            fldrvhkwW5AftWCS3niZMZ1J: ['E-Commerce Inc.'],
            fldwXfBPStDLoRwxwaP7lWTs: '100000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['100000'],
          },
        },
        {
          templateId: 'rechFVRxlwnwk7xgBrwT8KSJ',
          data: {
            fld9t90Ri6nsFKUv1MjezsvG: ['recYq5AMwPlYdtylqTXcSIku'],
            fldITOJm5YNv5JmlwhfxUDTi: [
              {
                id: 'tplattPt7aoAkp9NejP66ejdMnH',
                name: 'iShot_2025-05-20_17.39.07.png',
                path: 'template/tplattPt7aoAkp9NejP66ejdMnH.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 762966,
              },
            ],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Enterprise Exclusive--100000',
            fldZTlhpBrqabWo1HXoUFGzz: 1,
            fldgaJx62Adi3yzdHiq8cMp6: 'usrEJXVBqVqSE4tETURYhgo2',
            fldkgm8xMWetVNgGQphJUYZy: ['optVbrQlBtz5ok3S6N3if83u'],
            fldrvhkwW5AftWCS3niZMZ1J: ['reczmToii8LAMKJxwk1ILOc9'],
            fldwXfBPStDLoRwxwaP7lWTs: 100000,
            fldzc6IwGvCvIc1eu3I0rGJy: [100000],
          },
          values: {
            fld9RS15admbouPZENxJaMA3: '0',
            fld9t90Ri6nsFKUv1MjezsvG: ['Enterprise Exclusive'],
            fldITOJm5YNv5JmlwhfxUDTi: ['iShot_2025-05-20_17.39.07.png'],
            fldMy20lTRjJ43FMqOmUEiyp: '<EMAIL>',
            fldQoz1M9Ax4ewQ121d7ckhj: 'Tech Innovations--Enterprise Exclusive--100000',
            fldZTlhpBrqabWo1HXoUFGzz: '1',
            fldgaJx62Adi3yzdHiq8cMp6: 'Thea',
            fldkgm8xMWetVNgGQphJUYZy: ['new'],
            fldrvhkwW5AftWCS3niZMZ1J: ['Tech Innovations'],
            fldwXfBPStDLoRwxwaP7lWTs: '100000',
            fldzc6IwGvCvIc1eu3I0rGJy: ['100000'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datAJnaHnFCqiO9TMvOTh5df',
      name: {
        en: 'Customer Information',
        'zh-CN': '客户信息表',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwDbVfoDw6dNXWXHyckxmLC',
          name: {
            en: 'All',
            'zh-CN': '总视图',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldyd5l56hr9qNcK6XwsyNbr',
              hidden: false,
              width: 150,
            },
            {
              templateId: 'fld63GvvlD1cfHMmKZ0ADIQ6',
              hidden: false,
              width: 150,
            },
            {
              templateId: 'fldNrTZmWj8HaOqxIwt6mIsd',
              hidden: false,
            },
            {
              templateId: 'fldawJmi79mEwLfHSI2dBvxu',
              hidden: false,
            },
            {
              templateId: 'fldhZrEAF2xlUgA0sKfl20Or',
              hidden: false,
            },
            {
              templateId: 'fldAqt26wHkWsHQHj5gDJmX8',
              hidden: false,
              width: 183,
            },
            {
              templateId: 'fldxwCea74Tms8s52Ute76i1',
              hidden: false,
            },
            {
              templateId: 'fldKc66LSgGBMClKV3qDsX5u',
              hidden: false,
            },
            {
              templateId: 'fldny22a1eV8nYf2BIu8X0so',
              hidden: false,
            },
            {
              templateId: 'fldTKq3TQW5sv2IMxiggjH1D',
              hidden: false,
            },
            {
              templateId: 'fld9kWdUlBFHf5AVrkRUAoRK',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldyd5l56hr9qNcK6XwsyNbr',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Company Name',
            'zh-CN': '公司名称',
          },
          primary: true,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldhZrEAF2xlUgA0sKfl20Or',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Source of Leads',
            'zh-CN': '线索来源',
          },
          property: {
            options: [
              {
                id: 'optNfbT6fex7qyBBAUU0K2PK',
                name: 'SEO Search',
                color: 'deepPurple',
              },
              {
                id: 'optrBSAGXGOCoUupCUT9x5kX',
                name: 'Social Media',
                color: 'indigo',
              },
              {
                id: 'optwVyLh5wcWjdS9NnX4le21',
                name: 'Referral from Friends',
                color: 'blue',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldAqt26wHkWsHQHj5gDJmX8',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Description',
            'zh-CN': '描述',
          },
          primary: false,
        },
        {
          type: 'NUMBER',
          templateId: 'fldxwCea74Tms8s52Ute76i1',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Company Size',
            'zh-CN': '公司规模',
          },
          property: {
            precision: 0,
            commaStyle: 'thousand',
            symbol: '',
            symbolAlign: 'right',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldKc66LSgGBMClKV3qDsX5u',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Company Address',
            'zh-CN': '公司所在地址',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldny22a1eV8nYf2BIu8X0so',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Created At',
            'zh-CN': '创建时间',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'CREATED_BY',
          templateId: 'fldTKq3TQW5sv2IMxiggjH1D',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Creator',
            'zh-CN': '创建人',
          },
          required: false,
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fld63GvvlD1cfHMmKZ0ADIQ6',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Contact Person',
            'zh-CN': '联系人',
          },
          primary: false,
        },
        {
          type: 'PHONE',
          templateId: 'fldNrTZmWj8HaOqxIwt6mIsd',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Contact Phone Numbe',
            'zh-CN': '联系电话',
          },
          primary: false,
        },
        {
          type: 'EMAIL',
          templateId: 'fldawJmi79mEwLfHSI2dBvxu',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Email Address',
            'zh-CN': '邮件地址',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld9kWdUlBFHf5AVrkRUAoRK',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Contract Order Form',
            'zh-CN': '合同订单表',
          },
          property: {
            foreignDatabaseTemplateId: 'datczF7CI7SercIAFwSKAFKe',
            brotherFieldTemplateId: 'fldrvhkwW5AftWCS3niZMZ1J',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recWIZIdMlol19VSlCpiZ110',
          data: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'Sarah Davis',
            fld9kWdUlBFHf5AVrkRUAoRK: ['recqJSUfDSxmImGbo3XMjvNZ'],
            fldAqt26wHkWsHQHj5gDJmX8: 'Channel Partner Management',
            fldKc66LSgGBMClKV3qDsX5u: 'USA',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 222-1111',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['optNfbT6fex7qyBBAUU0K2PK'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-20',
            fldxwCea74Tms8s52Ute76i1: 70,
            fldyd5l56hr9qNcK6XwsyNbr: 'E-Commerce Inc.',
          },
          values: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'Sarah Davis',
            fld9kWdUlBFHf5AVrkRUAoRK: ['E-Commerce Inc.--Enterprise Exclusive--100000'],
            fldAqt26wHkWsHQHj5gDJmX8: 'Channel Partner Management',
            fldKc66LSgGBMClKV3qDsX5u: 'USA',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 222-1111',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['SEO Search'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-20',
            fldxwCea74Tms8s52Ute76i1: '70',
            fldyd5l56hr9qNcK6XwsyNbr: 'E-Commerce Inc.',
          },
        },
        {
          templateId: 'recHBcIRSka7kBWFQKCWrILy',
          data: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'Michael Brown',
            fld9kWdUlBFHf5AVrkRUAoRK: ['recW5FGbHlVAD4iml0StZm9r'],
            fldAqt26wHkWsHQHj5gDJmX8: 'Collaborative Management',
            fldKc66LSgGBMClKV3qDsX5u: 'Canada\n',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 444-3333',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['optwVyLh5wcWjdS9NnX4le21'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-20',
            fldxwCea74Tms8s52Ute76i1: 50,
            fldyd5l56hr9qNcK6XwsyNbr: 'Future Tech LLC',
          },
          values: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'Michael Brown',
            fld9kWdUlBFHf5AVrkRUAoRK: ['Future Tech LLC--Premium Custom--100000'],
            fldAqt26wHkWsHQHj5gDJmX8: 'Collaborative Management',
            fldKc66LSgGBMClKV3qDsX5u: 'Canada\n',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 444-3333',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['Referral from Friends'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-20',
            fldxwCea74Tms8s52Ute76i1: '50',
            fldyd5l56hr9qNcK6XwsyNbr: 'Future Tech LLC',
          },
        },
        {
          templateId: 'recRmnmrDuKfsm0NBzIZksiB',
          data: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'Dr. Emily Johnson',
            fld9kWdUlBFHf5AVrkRUAoRK: ['recafmUbOJaigchM5ZbAUiQV', 'recKecz8BRO1WVxl7tvYz7YV'],
            fldAqt26wHkWsHQHj5gDJmX8: 'Customer Management',
            fldKc66LSgGBMClKV3qDsX5u: 'China',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 555-5555',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['optrBSAGXGOCoUupCUT9x5kX'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-20',
            fldxwCea74Tms8s52Ute76i1: 80,
            fldyd5l56hr9qNcK6XwsyNbr: 'Health Plus Corp',
          },
          values: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'Dr. Emily Johnson',
            fld9kWdUlBFHf5AVrkRUAoRK: [
              'Health Plus Corp--Dedicated Deployment--100000',
              'Health Plus Corp--Enterprise Exclusive--26800',
            ],
            fldAqt26wHkWsHQHj5gDJmX8: 'Customer Management',
            fldKc66LSgGBMClKV3qDsX5u: 'China',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 555-5555',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['Social Media'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-20',
            fldxwCea74Tms8s52Ute76i1: '80',
            fldyd5l56hr9qNcK6XwsyNbr: 'Health Plus Corp',
          },
        },
        {
          templateId: 'recEuqHFZCJkvCOU5DuhNJY5',
          data: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'Jane Smith',
            fld9kWdUlBFHf5AVrkRUAoRK: ['recYaWw04vJ2L4oj7CeIwcUi'],
            fldAqt26wHkWsHQHj5gDJmX8: 'Information Management',
            fldKc66LSgGBMClKV3qDsX5u: 'USA',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 987-6543',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['optNfbT6fex7qyBBAUU0K2PK'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-27',
            fldxwCea74Tms8s52Ute76i1: 100,
            fldyd5l56hr9qNcK6XwsyNbr: 'Green Energy Inc.',
          },
          values: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'Jane Smith',
            fld9kWdUlBFHf5AVrkRUAoRK: ['Green Energy Inc.--Private Deployment--100000'],
            fldAqt26wHkWsHQHj5gDJmX8: 'Information Management',
            fldKc66LSgGBMClKV3qDsX5u: 'USA',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 987-6543',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['SEO Search'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-27',
            fldxwCea74Tms8s52Ute76i1: '100',
            fldyd5l56hr9qNcK6XwsyNbr: 'Green Energy Inc.',
          },
        },
        {
          templateId: 'reczmToii8LAMKJxwk1ILOc9',
          data: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'John Doe',
            fld9kWdUlBFHf5AVrkRUAoRK: [
              'recJ89UdkJN9HGhLr0EWN0yt',
              'recB9E1VFakqWqkWe8kzBqxH',
              'recHtGXKPIl3wvTCHsT1MRFF',
              'rectmY1cqm177zXrSEkCS6vs',
              'recBxWdKN9GGHgesedRYrGkJ',
              'rechFVRxlwnwk7xgBrwT8KSJ',
            ],
            fldAqt26wHkWsHQHj5gDJmX8: 'Collaborative Office Management',
            fldKc66LSgGBMClKV3qDsX5u: 'Canada',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 123-4567',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['optrBSAGXGOCoUupCUT9x5kX'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-27',
            fldxwCea74Tms8s52Ute76i1: 50,
            fldyd5l56hr9qNcK6XwsyNbr: 'Tech Innovations',
          },
          values: {
            fld63GvvlD1cfHMmKZ0ADIQ6: 'John Doe',
            fld9kWdUlBFHf5AVrkRUAoRK: [
              'Tech Innovations--Dedicated Deployment--30000',
              'Tech Innovations--Enterprise Exclusive--100000',
              'Tech Innovations--Custom Edition--80000',
              'Tech Innovations--Enterprise Exclusive--22200000',
              'Tech Innovations--Enterprise Exclusive--100000',
              'Tech Innovations--Enterprise Exclusive--100000',
            ],
            fldAqt26wHkWsHQHj5gDJmX8: 'Collaborative Office Management',
            fldKc66LSgGBMClKV3qDsX5u: 'Canada',
            fldNrTZmWj8HaOqxIwt6mIsd: '+1 (800) 123-4567',
            fldTKq3TQW5sv2IMxiggjH1D: 'zhanpeiwei',
            fldawJmi79mEwLfHSI2dBvxu: '<EMAIL>',
            fldhZrEAF2xlUgA0sKfl20Or: ['Social Media'],
            fldny22a1eV8nYf2BIu8X0so: '2024-12-27',
            fldxwCea74Tms8s52Ute76i1: '50',
            fldyd5l56hr9qNcK6XwsyNbr: 'Tech Innovations',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'dat7WFmRdOls4NtGBczPc1Qg',
      name: {
        en: 'Product Details',
        'zh-CN': '产品明细表',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwBDB9iCF9gOOU074EfWlQf',
          name: {
            en: 'All',
            'zh-CN': '总视图',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fld5JUYTmCkYM5YRKW8ASGWE',
              hidden: false,
              width: 155,
            },
            {
              templateId: 'fldnXeEAwcfHFmUeuQSTyDhZ',
              hidden: false,
              width: 253,
            },
            {
              templateId: 'fldVnd0Cgg3tkCEMYaaaTz3O',
              hidden: false,
              width: 252,
            },
            {
              templateId: 'fld5xc2JPYzrQiZ4aWShmCKY',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'fld5JUYTmCkYM5YRKW8ASGWE',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Version Name',
            'zh-CN': '版本名称',
          },
          primary: true,
        },
        {
          type: 'NUMBER',
          templateId: 'fldnXeEAwcfHFmUeuQSTyDhZ',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Duration (Unit: Year)',
            'zh-CN': '时长（单位：年）',
          },
          description: {
            en: 'Unit: Year',
            'zh-CN': '',
          },
          property: {
            precision: 0,
            commaStyle: '',
            symbol: '',
            symbolAlign: 'right',
          },
          primary: false,
        },
        {
          type: 'CURRENCY',
          templateId: 'fldVnd0Cgg3tkCEMYaaaTz3O',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Unit Price (Yuan)',
            'zh-CN': '单价（元）',
          },
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '￥',
            symbolAlign: 'left',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fld5xc2JPYzrQiZ4aWShmCKY',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Completed Orders',
            'zh-CN': '已成交订单',
          },
          property: {
            foreignDatabaseTemplateId: 'datczF7CI7SercIAFwSKAFKe',
            brotherFieldTemplateId: 'fld9t90Ri6nsFKUv1MjezsvG',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recoJGzrFl1TlV0Il2HXaFlp',
          data: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Dedicated Deployment',
            fld5xc2JPYzrQiZ4aWShmCKY: ['recafmUbOJaigchM5ZbAUiQV', 'recJ89UdkJN9HGhLr0EWN0yt'],
            fldVnd0Cgg3tkCEMYaaaTz3O: 30000,
            fldnXeEAwcfHFmUeuQSTyDhZ: 1,
          },
          values: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Dedicated Deployment',
            fld5xc2JPYzrQiZ4aWShmCKY: [
              'Health Plus Corp--Dedicated Deployment--100000',
              'Tech Innovations--Dedicated Deployment--30000',
            ],
            fldVnd0Cgg3tkCEMYaaaTz3O: '￥30000',
            fldnXeEAwcfHFmUeuQSTyDhZ: '1',
          },
        },
        {
          templateId: 'recUPfaj08QvogCNEcEmloq9',
          data: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Premium Custom',
            fld5xc2JPYzrQiZ4aWShmCKY: ['recW5FGbHlVAD4iml0StZm9r'],
            fldVnd0Cgg3tkCEMYaaaTz3O: 50000,
            fldnXeEAwcfHFmUeuQSTyDhZ: 1,
          },
          values: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Premium Custom',
            fld5xc2JPYzrQiZ4aWShmCKY: ['Future Tech LLC--Premium Custom--100000'],
            fldVnd0Cgg3tkCEMYaaaTz3O: '￥50000',
            fldnXeEAwcfHFmUeuQSTyDhZ: '1',
          },
        },
        {
          templateId: 'recnjwPnpQR6CUm3L2iKWYWj',
          data: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Private Deployment',
            fld5xc2JPYzrQiZ4aWShmCKY: ['recYaWw04vJ2L4oj7CeIwcUi'],
            fldVnd0Cgg3tkCEMYaaaTz3O: 20000,
            fldnXeEAwcfHFmUeuQSTyDhZ: 1,
          },
          values: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Private Deployment',
            fld5xc2JPYzrQiZ4aWShmCKY: ['Green Energy Inc.--Private Deployment--100000'],
            fldVnd0Cgg3tkCEMYaaaTz3O: '￥20000',
            fldnXeEAwcfHFmUeuQSTyDhZ: '1',
          },
        },
        {
          templateId: 'rec2DEv6wR1VNz4WzQjgAcpd',
          data: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Custom Edition',
            fld5xc2JPYzrQiZ4aWShmCKY: ['recHtGXKPIl3wvTCHsT1MRFF'],
            fldVnd0Cgg3tkCEMYaaaTz3O: 40000,
            fldnXeEAwcfHFmUeuQSTyDhZ: 1,
          },
          values: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Custom Edition',
            fld5xc2JPYzrQiZ4aWShmCKY: ['Tech Innovations--Custom Edition--80000'],
            fldVnd0Cgg3tkCEMYaaaTz3O: '￥40000',
            fldnXeEAwcfHFmUeuQSTyDhZ: '1',
          },
        },
        {
          templateId: 'recYq5AMwPlYdtylqTXcSIku',
          data: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Enterprise Exclusive',
            fld5xc2JPYzrQiZ4aWShmCKY: [
              'recKecz8BRO1WVxl7tvYz7YV',
              'recB9E1VFakqWqkWe8kzBqxH',
              'rectmY1cqm177zXrSEkCS6vs',
              'recBxWdKN9GGHgesedRYrGkJ',
              'recqJSUfDSxmImGbo3XMjvNZ',
              'rechFVRxlwnwk7xgBrwT8KSJ',
            ],
            fldVnd0Cgg3tkCEMYaaaTz3O: 100000,
            fldnXeEAwcfHFmUeuQSTyDhZ: 1,
          },
          values: {
            fld5JUYTmCkYM5YRKW8ASGWE: 'Enterprise Exclusive',
            fld5xc2JPYzrQiZ4aWShmCKY: [
              'Health Plus Corp--Enterprise Exclusive--26800',
              'Tech Innovations--Enterprise Exclusive--100000',
              'Tech Innovations--Enterprise Exclusive--22200000',
              'Tech Innovations--Enterprise Exclusive--100000',
              'E-Commerce Inc.--Enterprise Exclusive--100000',
              'Tech Innovations--Enterprise Exclusive--100000',
            ],
            fldVnd0Cgg3tkCEMYaaaTz3O: '￥100000',
            fldnXeEAwcfHFmUeuQSTyDhZ: '1',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
