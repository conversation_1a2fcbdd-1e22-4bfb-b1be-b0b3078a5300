import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: '<PERSON> <<EMAIL>>',
  visibility: 'WAITING_LIST',

  // 根据你的模板作用，起一个系统唯一的templateId，模板文件夹的名称与templateId 必须相同！
  templateId: 'automated-ai-text-to-speech',

  // 模板的名称，会出现在模板中心，安装后也会显示该名称
  name: {
    en: 'Automation Call to Third-Party AI Platform for Text-to-Speech',
    'zh-CN': '通过自动化调用第三方AI平台实现文本转语音',
    'zh-TW': '通過自動化調用第三方AI平台實現文本轉語音',
    ja: 'サードパーティAIプラットフォームを呼び出してテキストを音声に変換する自動化',
  },

  // 模板的封面图
  cover: '/assets/template/template-cover-automated-ai-text-to-speech.png',

  // 模板的简短描述，会出现在模板中心和模板文件夹详情页
  description: {
    en: 'This template allows you to automate the process of calling a third-party AI platform API to convert text from a table into an MP3 audio file. It is designed to streamline workflows and enhance productivity by leveraging advanced AI capabilities.',
    'zh-CN':
      '该模板允许您自动化调用第三方AI平台API，将表格中的文本转换为MP3音频文件。它旨在通过利用先进的AI能力来简化工作流程并提高生产力。',
    'zh-TW':
      '該模板允許您自動化調用第三方AI平台API，將表格中的文本轉換為MP3音頻文件。它旨在通過利用先進的AI能力來簡化工作流程並提高生產力。',
    ja: 'このテンプレートを使用すると、テーブルのテキストをMP3音声ファイルに変換するためにサードパーティのAIプラットフォームAPIを呼び出すプロセスを自動化できます。先進的なAI機能を活用してワークフローを簡素化し、生産性を向上させることを目的としています。',
  },

  // 模板的SEO关键词
  keywords: 'AI, text-to-speech, automation, API, MP3',
  personas: {
    'zh-CN': '内容创作者, 教育工作者, 企业培训师, 开发者',
    'zh-TW': '內容創作者, 教育工作者, 企業培訓師, 開發者',
    en: 'Content Creators, Educators, Corporate Trainers, Developers',
    ja: 'コンテンツクリエイター,教育者,企業トレーナー,開発者',
  },
  useCases: {
    'zh-CN':
      '视频配音, 播客脚本, 有声书制作, 在线课程音频, 讲座录音, 社交媒体内容, 教学视频, 课件音频, 在线教学, 语言学习资料, 课堂讲解, 远程教学, 培训视频, 员工培训材料, 产品演示音频, 培训课程录音, 知识库音频, 操作指南, 应用内语音提示, 语音助手集成, 设备语音控制, 客户服务自动化, 支持内容音频化, 技术文档音频',
    'zh-TW':
      '視頻配音, 播客腳本, 有聲書製作, 在線課程音頻, 講座錄音, 社交媒體內容, 教學視頻, 課件音頻, 在線教學, 語言學習資料, 課堂講解, 遠程教學, 培訓視頻, 員工培訓材料, 產品演示音頻, 培訓課程錄音, 知識庫音頻, 操作指南, 應用內語音提示, 語音助手集成, 設備語音控制, 客戶服務自動化, 支持內容音頻化, 技術文檔音頻',
    en: 'Video voiceover, Podcast scripts, Audiobook production, Online course audio, Lecture recordings, Social media content, Educational videos, Courseware audio, Online teaching, Language learning materials, Classroom explanations, Remote teaching, Training videos, Employee training materials, Product demo audio, Training course recordings, Knowledge base audio, Operation guides, In-app voice prompts, Voice assistant integration, Device voice control, Customer service automation, Support content audio, Technical document audio',
    ja: 'ビデオナレーション, ポッドキャストスクリプト, オーディオブック制作, オンラインコースオーディオ, 講義録音, ソーシャルメディアコンテンツ, 教育ビデオ, コースウェアオーディオ, オンライン教育, 言語学習資料, クラスルームの説明, リモート教育, トレーニングビデオ, 従業員トレーニング資料, 製品デモオーディオ, トレーニングコース録音, ナレッジベースオーディオ, 操作ガイド, アプリ内音声プロンプト, 音声アシスタント統合, デバイス音声制御, 顧客サービス自動化, サポートコンテンツオーディオ, 技術文書オーディオ',
  },

  // 模板的当前迭代版本
  version: '0.1.6',

  // 模板的分类，不能随意填写，请从 packages/types/src/template/template.ts 声明的schema 中选择合适的分类。模板支持同时存在于多个分类。
  category: 'automation',

  // 模板内置的新手任务。"Comming Soon" 类型的模板，可忽略不填
  initMissions: [
    {
      name: {
        'zh-CN': 'AI 语音转文字模板使用须知',
        en: 'Read the guide before using the AI voice-to-text template',
        'zh-TW': 'AI 語音轉文字模板使用須知',
        ja: 'AI音声からテキストへのテンプレートを使用する前にガイドを読んでください',
      },
      type: 'READ_TEMPLATE_README',
      templateId: 'automated-ai-text-to-speech',
      time: 10,
      beforeText: {
        title: {
          'zh-CN': '恭喜您，模板已经安装完毕',
          'zh-TW': '恭喜您，模板已經安裝完畢',
          ja: 'おめでとうございます、テンプレートのインストールが完了しました',
          en: 'Congratulations, the template has been installed',
        },
        description: {
          'zh-CN': '在开始使用之前，请阅读以下使用须知，以便更好地了解如何使用此模板',
          'zh-TW': '在開始使用之前，請閱讀以下使用須知，以便更好地了解如何使用此模板',
          ja: '使用を開始する前に、以下の使用方法をお読みいただき、このテンプレートの使用方法をよりよく理解してください',
          en: 'Before you start using it, please read the following instructions to better understand how to use this template',
        },
      },
      assignType: 'DEDICATED',
      forcePopup: true,
      wizardGuideId: 'COMMON_MY_TODO_TUTORIAL',
      redirect: {
        type: 'MY_MISSIONS',
      },
      to: [
        {
          type: 'CURRENT_OPERATOR',
        },
      ],
    },
  ],

  // 模板包含的资源节点，例如 automation、database。"Comming Soon" 类型的模板，可忽略不填，但建议填写，以令模板详情页的内容更加丰富
  resources: [
    {
      resourceType: 'AUTOMATION',
      name: {
        'zh-CN': '文本转语音自动化',
        'zh-TW': '文本轉語音自動化',
        ja: 'テキスト音声変換の自動化',
        en: 'Text-to-Speech Automation',
      },
      templateId: 'auto_tts_ai_call',
      description: {
        'zh-CN': '将表格中的文本转换为MP3音频文件',
        'zh-TW': '將表格中的文本轉換為MP3音頻文件',
        ja: 'テーブルのテキストをMP3音声ファイルに変換する',
        en: 'Convert text from a table into MP3 audio files',
      },
      status: 'INACTIVE',
      triggers: [
        {
          triggerType: 'DUMMY_TRIGGER',
          description: {
            'zh-CN': '“状态”字段变更为“开始转换”',
            'zh-TW': '“狀態”字段變更為“開始轉換”',
            ja: '「ステータス」フィールドが「変換開始」に変更された場合',
            en: 'when the "Status" field is changed to "Start Conversion"',
          },
        },
      ],
      actions: [
        {
          actionType: 'DUMMY_ACTION',
          description: {
            en: 'Call third-party AI platform API to convert text to speech',
            'zh-CN': '调用第三方AI平台API将文本转换为语音',
            'zh-TW': '調用第三方AI平台API將文本轉換為語音',
            ja: 'サードパーティのAIプラットフォームAPIを呼び出してテキストを音声に変換する',
          },
        },
        {
          actionType: 'DUMMY_ACTION',
          description: {
            en: 'Store the resulting MP3 file',
            'zh-CN': '存储生成的MP3文件',
            'zh-TW': '存儲生成的MP3文件',
            ja: '生成されたMP3ファイルを保存する',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'db_texts_and_speeches',
      databaseType: 'DATUM',
      name: {
        en: 'Texts and Speeches database',
        'zh-CN': '文本和语音库',
        'zh-TW': '文本和語音庫',
        ja: 'テキストと音声データベース',
      },
      description: {
        en: 'Store text and speech data',
        'zh-CN': '存储文本和语音数据',
        'zh-TW': '存儲文本和語音數據',
        ja: 'テキストと音声データを保存',
      },
      views: [
        {
          templateId: 'all',
          name: {
            en: 'All',
            'zh-CN': '全部',
            'zh-TW': '全部',
            ja: 'すべて',
          },
          type: 'TABLE',
        },
      ],
      fields: [
        {
          templateId: 'convert_text',
          name: {
            'zh-CN': '需转换的文本',
            en: 'Text to convert',
            'zh-TW': '需轉換的文本',
            ja: '変換するテキスト',
          },
          type: 'SINGLE_TEXT',
          required: true,
          description: {
            'zh-CN': '要转换为语音的文本',
            en: 'The text to convert to speech',
            'zh-TW': '要轉換為語音的文本',
            ja: '音声に変換するテキスト',
          },
        },
        {
          templateId: 'converted_status',
          name: {
            'zh-CN': '转换状态',
            en: 'Conversion status',
            'zh-TW': '轉換狀態',
            ja: '変換ステータス',
          },
          type: 'SINGLE_SELECT',
          required: true,
          description: {
            'zh-CN': '文本转换为语音的状态',
            en: 'The status of the text-to-speech conversion',
            'zh-TW': '文本轉換為語音的狀態',
            ja: 'テキストから音声への変換の状態',
          },
          property: {
            options: [
              {
                templateId: 'pending',
                id: 'pending',
                name: 'Pending',
                color: 'deepPurple',
              },
              {
                templateId: 'started',
                id: 'started',
                name: 'Started',
                color: 'Indigo',
              },
              {
                templateId: 'completed',
                id: 'completed',
                name: 'Completed',
                color: 'Teal',
              },
            ],
          },
        },
        {
          templateId: 'audio_file',
          name: {
            'zh-CN': '转换的音频文件',
            en: 'Converted audio file',
            'zh-TW': '轉換的音頻文件',
            ja: '変換された音声ファイル',
          },
          type: 'ATTACHMENT',
          required: false,
          description: {
            'zh-CN': '转换后的音频文件',
            en: 'The audio file after conversion',
            'zh-TW': '轉換後的音頻文件',
            ja: '変換後の音声ファイル',
          },
        },
        {
          templateId: 'created_at',
          name: {
            en: 'Created At',
            'zh-CN': '创建时间',
            'zh-TW': '創建時間',
            ja: '作成日時',
          },
          type: 'CREATED_TIME',
          property: {
            timeZone: 'Asia/Shanghai',
            dateFormat: 'YYYY/MM/DD',
            includeTime: true,
          },
        },
      ],
      records: [
        {
          data: {
            convert_text: 'Hello, how are you?',
            converted_status: 'Pending',
          },
        },
        {
          data: {
            convert_text: 'Hello, how are you?',
            converted_status: 'Pending',
          },
        },
        {
          data: {
            convert_text: 'Hello, how are you?',
            converted_status: 'completed',
          },
        },
      ],
    },
  ],
};

export default template;
