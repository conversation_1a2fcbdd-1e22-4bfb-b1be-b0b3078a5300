import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'ai-marketing-campaign-analysis',
  name: {
    en: 'AI Marketing Campaign Analysis',
    'zh-CN': 'AI营销活动分析',
  },
  description: {
    en: 'The Marketing Campaign Tracking Template is a tool designed to help you centralize, plan, and optimize campaigns across platforms, improving team collaboration and simplifying performance monitoring.',
    'zh-CN':
      '该模板旨在协助营销团队高效整合数据，智能分析关键指标，并自动生成报告，从而显著提升决策效率。借助 AI 自动化功能，团队不仅能够轻松生成和分发报告，还能实现更流畅的协作与更精准的绩效监控。',
  },
  cover: '/assets/template/ai-marketing-campaign-analysis/ai-marketing-campaign-analysis.png',
  author: 'Xinchen <<EMAIL>>',
  category: ['marketing'],
  keywords:
    'Email scheduling, Email notifications, Scheduled tasks, Automated reminders, Task management, Time management, Event reminders, Workflow, Information reminders',
  useCases:
    'Setting task reminders, Scheduling meeting notifications, Tracking ticket status, Reminding project deadlines, Notifying team members, Sending progress updates, Sending task assignment notifications, Reminding important matters, Planning events, Managing schedules, Coordinating team work, Tracking project progress, Sending periodic reports, Reminding to-do items, Notifying changes, Sending meeting minutes, Requesting status updates, Delegating tasks, Following up on action items, Confirming appointments, Requesting approvals, Sending thank-you notes, Distributing resources, Sharing announcements, Inviting to events, Issuing reminders for deadlines, Providing feedback, Sending alerts for updates, Summarizing completed tasks, Highlighting project milestones, Generating summary reports, Escalating issues',
  detach: false,
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.1.2',
  resources: [
    {
      resourceType: 'AUTOMATION',
      templateId: 'ato3gvrEtUSh6CLmQOLsnlhR',
      name: {
        en: 'AI Marketing Report Delivery',
        'zh-CN': 'AI 营销报告交付',
      },
      triggers: [
        {
          triggerType: 'RECORD_MATCH',
          templateId: 'trgXbWfrbXa1gZkfzUlDZ1ms',
          description: {
            en: 'When the status of Next Steps is Analysis',
            'zh-CN': '下一步的状态为分析时',
          },
          input: {
            type: 'DATABASE_WITH_FILTER',
            filters: {
              conjunction: 'And',
              conditions: [],
              conds: [
                {
                  fieldTemplateId: 'fldbFxiNqQCkyLo5ttyXBr6C',
                  fieldType: 'SINGLE_SELECT',
                  clause: {
                    operator: 'Is',
                    value: 'optH6S53uJCg3bpBOWXKhBhM',
                  },
                },
              ],
            },
            databaseTemplateId: 'dat4MBnTVQc29cA9Qo3P4lPF',
          },
        },
      ],
      actions: [
        {
          templateId: 'actjYL7hHxQIMFHSui46YbVU',
          description: {
            en: 'Generate AI reports for marketing campaigns',
            'zh-CN': '生成营销活动AI报告',
          },
          actionType: 'OPENAI_GENERATE_TEXT',
          input: {
            urlType: 'INTEGRATION',
            type: 'OPENAI_GENERATE_TEXT',
            integrationId: '',
            prompt:
              'You are a data analyst specializing in summarizing and providing actionable insights for marketing campaigns. You excel at analyzing campaign data and offering clear, concise suggestions for optimization.                                                \n' +
              '                                                  \n' +
              'Please summarize the key insights and optimization suggestions for the marketing campaign data <%= _renderRecordsAsGrid(_triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record, ["fldymvyq9iTlJvc9ToSCfJdf","fldL6OyJukfgMvSBXtUz8ZLG","fldCJ9N87oejjgY1kMjweecg","fldwHLpBuGy4Rr42wjI9z0AE","fldXoeSKNvvemrG2EZhzETja","fldyPaKkYbTkdg2zEfBhLvhq","fldbFxiNqQCkyLo5ttyXBr6C","fldPC8foyw8Hr9DO13SHMKkT","fldtMT4jDj2oW1Ks7S84CtEU","fldW6330CwNUFs9qOSQkM2aU"]) %>in a Slack-compatible Markdown format. Focus on the following:                                \n' +
              '                                \n' +
              '*Current Goals vs. Actual Performance*:                                \n' +
              '- Target CPM: X                                \n' +
              '- Actual CPM: Y                                \n' +
              '- Result: [Describe whether the goal was achieved, such as "Exceeded the target, with CPM xx% lower than the goal."]                              \n' +
              '                                \n' +
              '*Key Metrics That Need Improvement*:                                \n' +
              '- Metric A                                \n' +
              '- Metric B                                \n' +
              '                                \n' +
              '*Budget Adjustments or Actions Required*:                                \n' +
              '- ？                                \n' +
              '- ？                                \n' +
              '                                \n' +
              '*Optimization Recommendations*:                                \n' +
              '> - ？                               \n' +
              '> - ？                               \n' +
              '> - ？                             \n' +
              '                                \n' +
              'Ensure that the output content is concise and clear, avoid lengthy descriptions, and make the information easy to scan and understand                                \n' +
              '                                \n' +
              '------------------------                           \n' +
              '                               \n' +
              '*Campaign Summary and Optimization Suggestions*     \n' +
              '                          \n' +
              'Results Data：                        \n' +
              '> - *Name*:<%= _triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.cells.single_text.data %>                        \n' +
              '> - *Goals*:<%= _joinArrayAsText(_triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.cells.fldL6OyJukfgMvSBXtUz8ZLG.value) %>                        \n' +
              '> - *Spend*:<%= _triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.cells.fldCJ9N87oejjgY1kMjweecg.data %>                        \n' +
              '> - *Exposure amount*:<%= _triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.cells.fldwHLpBuGy4Rr42wjI9z0AE.data %>                        \n' +
              '> - *Clicks*:<%= _triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.cells.fldXoeSKNvvemrG2EZhzETja.data %>                        \n' +
              '> - *Conversion volume*:<%= _triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.cells.fldyPaKkYbTkdg2zEfBhLvhq.data %>                        \n' +
              '> - *CPM($)*:<%= _triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.cells.fldPC8foyw8Hr9DO13SHMKkT.value %>                        \n' +
              '> - *CPC($)*:<%= _triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.cells.fldtMT4jDj2oW1Ks7S84CtEU.value %>                        \n' +
              '> - *CPA($)*:<%= _triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.cells.fldW6330CwNUFs9qOSQkM2aU.value %>                        \n' +
              '                          \n' +
              '*Current Goals vs. Actual Performance*:                                  \n' +
              '- Target CPM: X                                  \n' +
              '- Actual CPM: Y                                  \n' +
              '- Result: [Describe whether the goal was achieved, such as "Exceeded the target, with CPM xx% lower than the goal."]                            \n' +
              '                      \n' +
              '*Key Metrics That Need Improvement*:                                  \n' +
              '- Metric A                    \n' +
              '- Metric B\n' +
              '                      \n' +
              '*Budget Adjustments or Actions Required*:                                  \n' +
              '- ？                               \n' +
              '- ？                               \n' +
              '                              \n' +
              '*Optimization Recommendations*:                                \n' +
              '> - ？                               \n' +
              '> - ？                               \n' +
              '> - ？                           \n' +
              '                                    \n' +
              '--------------------                                    \n' +
              '                                    \n' +
              'Notes:\n' +
              '- Please output the report in the above format.\n' +
              '- The lower the CPM, the better. Do not calculate using "Achievement Rate." Instead, describe whether the target is met, such as "Exceeded the target, with CPM xx% lower than the target value."\n' +
              '- The output content should be concise and clear, avoiding lengthy descriptions to ensure the information is easy to read.',
            baseUrl: 'https://api.moonshot.cn/v1',
            apiKey: '',
            model: '',
            timeout: 180,
          },
        },
        {
          templateId: 'actUQXohWBPugZWrHOQMXTN6',
          description: {
            en: 'Send the analysis report to Slack',
            'zh-CN': '将分析报告发送给Slack',
          },
          actionType: 'SLACK_WEBHOOK',
          input: {
            type: 'SLACK_WEBHOOK',
            data: {
              msgtype: 'text',
              text:
                '_🙋‍♀️Hi Marketing Team,_        \n' +
                '          \n' +
                '<%= _actions.actjYL7hHxQIMFHSui46YbVU.body.choices[0].message.content %>',
            },
            urlType: 'URL',
            url: '',
          },
        },
        {
          templateId: 'actUCG3GGODHdJ4GzeYZWo72',
          description: {
            en: 'The report content is written into the Results datasheet',
            'zh-CN': '报告内容写入营销数据表中',
          },
          actionType: 'UPDATE_RECORD',
          input: {
            type: 'SPECIFY_RECORD_BODY',
            recordId: '<%= _triggers.trgXbWfrbXa1gZkfzUlDZ1ms.record.id %>',
            data: {
              flduh9E6h52sGNO9Zz4wMlKF: '<%= _actions.actjYL7hHxQIMFHSui46YbVU.body.choices[0].message.content %>',
            },
            databaseTemplateId: 'dat4MBnTVQc29cA9Qo3P4lPF',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'daty9DtDNVDPaeJyVLpP1XCA',
      name: {
        en: 'Campaigns',
        'zh-CN': '营销活动',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwrxnKGftR3PtxMyvMwHavQ',
          name: {
            en: 'All campaigns',
            'zh-CN': '查看所有活动',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldMKy8n7G7SJ24VA4w85gtW',
              width: 511,
            },
            {
              templateId: 'fldnDukLDPY2fDT2HCy2OFKp',
              hidden: false,
            },
            {
              templateId: 'fldQlknpnIogOU7vsboki9Wr',
              width: 379,
            },
            {
              templateId: 'fldoAZr3c4rJ13umpqy6H2UT',
              width: 145,
            },
            {
              templateId: 'fldg6A5uFlZl1GOgb45dBeQw',
              width: 154,
            },
            {
              templateId: 'fldJWq95nxT7alvFew5LJstG',
              width: 154,
            },
            {
              templateId: 'fldEzq5juPq0KaSCWMZG3YgE',
            },
            {
              templateId: 'fldEQiekV4tWY2MfJCtJ4n9s',
            },
            {
              templateId: 'fldz2uNj7MN0D1PnJdINONdX',
              hidden: false,
            },
            {
              templateId: 'fldru3ghTtAZjgCEcRszZbWQ',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'FORMULA',
          templateId: 'fldMKy8n7G7SJ24VA4w85gtW',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Title',
            'zh-CN': '活动标题',
          },
          required: false,
          property: {
            expressionTemplate: '{fldnDukLDPY2fDT2HCy2OFKp}&“-”&{fldQlknpnIogOU7vsboki9Wr}',
          },
          primary: true,
        },
        {
          type: 'MULTI_SELECT',
          templateId: 'fldEzq5juPq0KaSCWMZG3YgE',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Platform',
            'zh-CN': '平台',
          },
          property: {
            options: [
              {
                id: '1',
                name: 'Google',
                color: 'deepPurple4',
              },
              {
                id: '2',
                name: 'Instagram',
                color: 'indigo4',
              },
              {
                id: 'optKJz9Tl08fJXOug5Wio7fF',
                name: 'Facebook',
                color: 'blue4',
              },
              {
                id: 'optjGrmrhy4JQ2hbQmjy5tya',
                name: 'Twitter',
                color: 'teal4',
              },
              {
                id: 'optaNgORiwV68wyffkH9sjs7',
                name: 'Print',
                color: 'green4',
              },
              {
                id: 'optLO3FZ15nw3GQuAOMebPlU',
                name: 'Out of home',
                color: 'yellow4',
              },
            ],
            defaultValue: [],
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldEQiekV4tWY2MfJCtJ4n9s',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Creative',
            'zh-CN': '创意',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldQlknpnIogOU7vsboki9Wr',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Goal',
            'zh-CN': '目标',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldoAZr3c4rJ13umpqy6H2UT',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Status',
            'zh-CN': '状态',
          },
          property: {
            options: [
              {
                id: 'opth8ceWTuViz88ig84c59Hh',
                name: 'Development',
                color: 'indigo5',
              },
              {
                id: 'optfvn0aI0O1UiAOGdfudS9w',
                name: 'Planning',
                color: 'orange5',
              },
              {
                id: 'optYjYM22qShPPybube3ENp2',
                name: 'Reviews',
                color: 'blue5',
              },
              {
                id: 'optabtBA4sysPwSoSUIpuAkk',
                name: 'Published',
                color: 'teal5',
              },
              {
                id: 'opt75y5wWM7mj0snEiY54Ffu',
                name: 'On hold',
                color: 'pink5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldg6A5uFlZl1GOgb45dBeQw',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Start date',
            'zh-CN': '开始日期',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldJWq95nxT7alvFew5LJstG',
          privilege: 'NAME_EDIT',
          name: {
            en: 'End date',
            'zh-CN': '结束日期',
          },
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldnDukLDPY2fDT2HCy2OFKp',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Campaigns',
            'zh-CN': '活动',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldz2uNj7MN0D1PnJdINONdX',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Results',
            'zh-CN': '营销数据',
          },
          property: {
            foreignDatabaseTemplateId: 'dat4MBnTVQc29cA9Qo3P4lPF',
            brotherFieldTemplateId: 'fldL6OyJukfgMvSBXtUz8ZLG',
          },
          primary: false,
        },
        {
          type: 'LOOKUP',
          templateId: 'fldru3ghTtAZjgCEcRszZbWQ',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Analysis results',
            'zh-CN': '分析结果',
          },
          required: false,
          property: {
            relatedLinkFieldTemplateId: 'fldz2uNj7MN0D1PnJdINONdX',
            lookupTargetFieldTemplateId: 'flduh9E6h52sGNO9Zz4wMlKF',
            lookupTargetFieldType: 'LONG_TEXT',
            dataType: 'STRING',
            lookUpLimit: 'ALL',
            rollUpType: 'VALUES',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'rec1H77HWWrxZij0NpzDSZZo',
          data: {
            fldEQiekV4tWY2MfJCtJ4n9s: [
              {
                id: 'tplattjK9fbCmKMi2dBukaTNkAL',
                name: 'image.png',
                path: 'template/tplattjK9fbCmKMi2dBukaTNkAL.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 6165133,
              },
            ],
            fldEzq5juPq0KaSCWMZG3YgE: ['2'],
            fldJWq95nxT7alvFew5LJstG: '2024-11-07T16:00:00.000Z',
            fldMKy8n7G7SJ24VA4w85gtW: 'Instagram testing-Drive awareness to new key accounts, CPM <$2.34',
            fldQlknpnIogOU7vsboki9Wr: 'Drive awareness to new key accounts, CPM <$2.34',
            fldg6A5uFlZl1GOgb45dBeQw: '2024-05-05T16:00:00.000Z',
            fldnDukLDPY2fDT2HCy2OFKp: 'Instagram testing',
            fldoAZr3c4rJ13umpqy6H2UT: ['optYjYM22qShPPybube3ENp2'],
            fldz2uNj7MN0D1PnJdINONdX: ['recJlrgtyz2zLH0sUXOivQT0'],
            fldru3ghTtAZjgCEcRszZbWQ: [
              '*Campaign Summary and Optimization Suggestions*\n' +
                '\n' +
                'Results Data：\n' +
                '> - *Name*: Instagram testing\n' +
                '> - *Goals*: Instagram testing-Drive awareness to new key accounts, CPM <$2.34\n' +
                '> - *Spend*: $2300\n' +
                '> - *Exposure amount*: 3,638,352\n' +
                '> - *Clicks*: 897\n' +
                '> - *Conversion volume*: 46\n' +
                '> - *CPM($)*: $0.63\n' +
                '> - *CPC($)*: $2.56\n' +
                '> - *CPA($)*: $50\n' +
                '\n' +
                '*Current Goals vs. Actual Performance*:  \n' +
                '- Target CPM: $2.34  \n' +
                '- Actual CPM: $0.63  \n' +
                '- Result: Exceeded the target, with CPM 73.4% lower than the goal.\n' +
                '\n' +
                '*Key Metrics That Need Improvement*:  \n' +
                '- *Clicks*: The number of clicks is relatively low compared to the exposure amount.\n' +
                '- *Conversion volume*: The conversion volume is low, indicating a need for better conversion optimization.\n' +
                '\n' +
                '*Budget Adjustments or Actions Required*:  \n' +
                '- Increase budget allocation to improve ad targeting and creatives to boost clicks and conversions.\n' +
                '- Consider A/B testing different ad creatives to identify which ones drive higher engagement and conversions.\n' +
                '\n' +
                '*Optimization Recommendations*:  \n' +
                '> - Optimize ad targeting to better align with the target audience to increase click-through rates.\n' +
                '> - Experiment with different ad formats and placements to improve engagement and conversion rates.\n' +
                '> - Monitor and analyze the performance of the campaign regularly to make data-driven adjustments.',
            ],
          },
          values: {
            fldEQiekV4tWY2MfJCtJ4n9s: ['image.png'],
            fldEzq5juPq0KaSCWMZG3YgE: ['Instagram'],
            fldJWq95nxT7alvFew5LJstG: '2024-11-07',
            fldMKy8n7G7SJ24VA4w85gtW: 'Instagram testing-Drive awareness to new key accounts, CPM <$2.34',
            fldQlknpnIogOU7vsboki9Wr: 'Drive awareness to new key accounts, CPM <$2.34',
            fldg6A5uFlZl1GOgb45dBeQw: '2024-05-05',
            fldnDukLDPY2fDT2HCy2OFKp: 'Instagram testing',
            fldoAZr3c4rJ13umpqy6H2UT: ['Reviews'],
            fldru3ghTtAZjgCEcRszZbWQ: [
              '*Campaign Summary and Optimization Suggestions*\n' +
                '\n' +
                'Results Data：\n' +
                '> - *Name*: Instagram testing\n' +
                '> - *Goals*: Instagram testing-Drive awareness to new key accounts, CPM <$2.34\n' +
                '> - *Spend*: $2300\n' +
                '> - *Exposure amount*: 3,638,352\n' +
                '> - *Clicks*: 897\n' +
                '> - *Conversion volume*: 46\n' +
                '> - *CPM($)*: $0.63\n' +
                '> - *CPC($)*: $2.56\n' +
                '> - *CPA($)*: $50\n' +
                '\n' +
                '*Current Goals vs. Actual Performance*:  \n' +
                '- Target CPM: $2.34  \n' +
                '- Actual CPM: $0.63  \n' +
                '- Result: Exceeded the target, with CPM 73.4% lower than the goal.\n' +
                '\n' +
                '*Key Metrics That Need Improvement*:  \n' +
                '- *Clicks*: The number of clicks is relatively low compared to the exposure amount.\n' +
                '- *Conversion volume*: The conversion volume is low, indicating a need for better conversion optimization.\n' +
                '\n' +
                '*Budget Adjustments or Actions Required*:  \n' +
                '- Increase budget allocation to improve ad targeting and creatives to boost clicks and conversions.\n' +
                '- Consider A/B testing different ad creatives to identify which ones drive higher engagement and conversions.\n' +
                '\n' +
                '*Optimization Recommendations*:  \n' +
                '> - Optimize ad targeting to better align with the target audience to increase click-through rates.\n' +
                '> - Experiment with different ad formats and placements to improve engagement and conversion rates.\n' +
                '> - Monitor and analyze the performance of the campaign regularly to make data-driven adjustments.',
            ],
            fldz2uNj7MN0D1PnJdINONdX: ['Instagram testing'],
          },
        },
        {
          templateId: 'rec6uFl0wRQyRMIMtpCPHen0',
          data: {
            fldEQiekV4tWY2MfJCtJ4n9s: [
              {
                id: 'tplattxZ23OAaevetRIDlqIN59t',
                name: 'image.png',
                path: 'template/tplattxZ23OAaevetRIDlqIN59t.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 5605675,
              },
            ],
            fldEzq5juPq0KaSCWMZG3YgE: ['optLO3FZ15nw3GQuAOMebPlU'],
            fldJWq95nxT7alvFew5LJstG: '2024-07-19T16:00:00.000Z',
            fldMKy8n7G7SJ24VA4w85gtW: 'Search campaign-Drive traffic to site, CPC <$2.2',
            fldQlknpnIogOU7vsboki9Wr: 'Drive traffic to site, CPC <$2.2',
            fldg6A5uFlZl1GOgb45dBeQw: '2024-05-31T16:00:00.000Z',
            fldnDukLDPY2fDT2HCy2OFKp: 'Search campaign',
            fldoAZr3c4rJ13umpqy6H2UT: ['optfvn0aI0O1UiAOGdfudS9w'],
            fldru3ghTtAZjgCEcRszZbWQ: '',
            fldz2uNj7MN0D1PnJdINONdX: ['recv1FpjGJ48vI6XDwJo8jaL'],
          },
          values: {
            fldEQiekV4tWY2MfJCtJ4n9s: ['image.png'],
            fldEzq5juPq0KaSCWMZG3YgE: ['Out of home'],
            fldJWq95nxT7alvFew5LJstG: '2024-07-19',
            fldMKy8n7G7SJ24VA4w85gtW: 'Search campaign-Drive traffic to site, CPC <$2.2',
            fldQlknpnIogOU7vsboki9Wr: 'Drive traffic to site, CPC <$2.2',
            fldg6A5uFlZl1GOgb45dBeQw: '2024-05-31',
            fldnDukLDPY2fDT2HCy2OFKp: 'Search campaign',
            fldoAZr3c4rJ13umpqy6H2UT: ['Planning'],
            fldru3ghTtAZjgCEcRszZbWQ: [''],
            fldz2uNj7MN0D1PnJdINONdX: ['Search campaign'],
          },
        },
        {
          templateId: 'recnwDJqJTUygvV3PnJyY4eH',
          data: {
            fldEQiekV4tWY2MfJCtJ4n9s: [
              {
                id: 'tplattSnY7yJeoqkQetbbA2o4mV',
                name: 'image.png',
                path: 'template/tplattSnY7yJeoqkQetbbA2o4mV.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 5486815,
              },
            ],
            fldEzq5juPq0KaSCWMZG3YgE: ['1'],
            fldJWq95nxT7alvFew5LJstG: '2024-12-20T16:00:00.000Z',
            fldMKy8n7G7SJ24VA4w85gtW: 'Brand campaign-Drive traffic to site, CPC <$3.4',
            fldQlknpnIogOU7vsboki9Wr: 'Drive traffic to site, CPC <$3.4',
            fldg6A5uFlZl1GOgb45dBeQw: '2024-05-20T16:00:00.000Z',
            fldnDukLDPY2fDT2HCy2OFKp: 'Brand campaign',
            fldoAZr3c4rJ13umpqy6H2UT: ['opth8ceWTuViz88ig84c59Hh'],
            fldru3ghTtAZjgCEcRszZbWQ: '',
            fldz2uNj7MN0D1PnJdINONdX: ['reciG1vbjaUhCtipJLrorIEo'],
          },
          values: {
            fldEQiekV4tWY2MfJCtJ4n9s: ['image.png'],
            fldEzq5juPq0KaSCWMZG3YgE: ['Google'],
            fldJWq95nxT7alvFew5LJstG: '2024-12-20',
            fldMKy8n7G7SJ24VA4w85gtW: 'Brand campaign-Drive traffic to site, CPC <$3.4',
            fldQlknpnIogOU7vsboki9Wr: 'Drive traffic to site, CPC <$3.4',
            fldg6A5uFlZl1GOgb45dBeQw: '2024-05-20',
            fldnDukLDPY2fDT2HCy2OFKp: 'Brand campaign',
            fldoAZr3c4rJ13umpqy6H2UT: ['Development'],
            fldru3ghTtAZjgCEcRszZbWQ: [],
            fldz2uNj7MN0D1PnJdINONdX: ['Brand campaign'],
          },
        },
        {
          templateId: 'recclS0ReKoS1KGGn7UHWYm0',
          data: {
            fldEQiekV4tWY2MfJCtJ4n9s: [
              {
                id: 'tplattXR2uHiG1ULASBoFQKHRc4',
                name: 'image.png',
                path: 'template/tplattXR2uHiG1ULASBoFQKHRc4.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 3850402,
              },
            ],
            fldEzq5juPq0KaSCWMZG3YgE: ['optjGrmrhy4JQ2hbQmjy5tya', 'optaNgORiwV68wyffkH9sjs7'],
            fldJWq95nxT7alvFew5LJstG: '2024-06-26T16:00:00.000Z',
            fldMKy8n7G7SJ24VA4w85gtW: 'Expanded audience test-Convert lookalikes of high-value purchasers, CPA <$27',
            fldQlknpnIogOU7vsboki9Wr: 'Convert lookalikes of high-value purchasers, CPA <$27',
            fldg6A5uFlZl1GOgb45dBeQw: '2024-06-15T16:00:00.000Z',
            fldnDukLDPY2fDT2HCy2OFKp: 'Expanded audience test',
            fldoAZr3c4rJ13umpqy6H2UT: ['opt75y5wWM7mj0snEiY54Ffu'],
            fldru3ghTtAZjgCEcRszZbWQ: '',
            fldz2uNj7MN0D1PnJdINONdX: ['recx49f54Mm72dxCfDZaDpIM'],
          },
          values: {
            fldEQiekV4tWY2MfJCtJ4n9s: ['image.png'],
            fldEzq5juPq0KaSCWMZG3YgE: ['Twitter', 'Print'],
            fldJWq95nxT7alvFew5LJstG: '2024-06-26',
            fldMKy8n7G7SJ24VA4w85gtW: 'Expanded audience test-Convert lookalikes of high-value purchasers, CPA <$27',
            fldQlknpnIogOU7vsboki9Wr: 'Convert lookalikes of high-value purchasers, CPA <$27',
            fldg6A5uFlZl1GOgb45dBeQw: '2024-06-15',
            fldnDukLDPY2fDT2HCy2OFKp: 'Expanded audience test',
            fldoAZr3c4rJ13umpqy6H2UT: ['On hold'],
            fldru3ghTtAZjgCEcRszZbWQ: [],
            fldz2uNj7MN0D1PnJdINONdX: ['Expanded audience test'],
          },
        },
        {
          templateId: 'recmen4TPp6WWn2Gz4hZJWke',
          data: {
            fldEQiekV4tWY2MfJCtJ4n9s: [
              {
                id: 'tplattqz1Xr6MkDDxNvrUbSdx3F',
                name: 'image.png',
                path: 'template/tplattqz1Xr6MkDDxNvrUbSdx3F.png',
                bucket: 'bika-staging',
                mimeType: 'image/png',
                size: 2170364,
              },
            ],
            fldEzq5juPq0KaSCWMZG3YgE: ['optKJz9Tl08fJXOug5Wio7fF'],
            fldJWq95nxT7alvFew5LJstG: '2024-08-27T16:00:00.000Z',
            fldMKy8n7G7SJ24VA4w85gtW: 'Lapsed users campaign-Convert lapsed users, CPA <$13',
            fldQlknpnIogOU7vsboki9Wr: 'Convert lapsed users, CPA <$13',
            fldg6A5uFlZl1GOgb45dBeQw: '2025-01-23T16:00:00.000Z',
            fldnDukLDPY2fDT2HCy2OFKp: 'Lapsed users campaign',
            fldoAZr3c4rJ13umpqy6H2UT: ['optabtBA4sysPwSoSUIpuAkk'],
            fldru3ghTtAZjgCEcRszZbWQ: [
              '*Campaign Summary and Optimization Suggestions*\n' +
                '\n' +
                'Results Data：\n' +
                '> - *Name*: Resurrect lapsed users\n' +
                '> - *Goals*: Lapsed users campaign-Convert lapsed users, CPA <$13\n' +
                '> - *Spend*: $34509\n' +
                '> - *Exposure amount*: 2,362,000\n' +
                '> - *Clicks*: 4,356\n' +
                '> - *Conversion volume*: 344\n' +
                '> - *CPM($)*: $14.61\n' +
                '> - *CPC($)*: $7.92\n' +
                '> - *CPA($)*: $100.32\n' +
                '\n' +
                '*Current Goals vs. Actual Performance*:  \n' +
                '- Target CPM: $10\n' +
                '- Actual CPM: $14.61\n' +
                '- Result: The campaign did not meet the target CPM, with CPM 46% higher than the goal.\n' +
                '\n' +
                '*Key Metrics That Need Improvement*:  \n' +
                '- CPA: The CPA is significantly higher than the target, indicating a need for optimization.\n' +
                '- Conversion volume: The conversion volume is lower than expected, suggesting that the campaign may not be effectively reaching or engaging the target audience.\n' +
                '\n' +
                '*Budget Adjustments or Actions Required*:  \n' +
                '- Reduce ad spend: Consider reducing the overall budget to align with the higher-than-expected CPA.\n' +
                '- Re-evaluate targeting: Review and adjust the targeting parameters to better reach the lapsed users.\n' +
                '\n' +
                '*Optimization Recommendations*:  \n' +
                '> - Retargeting strategy: Implement a more refined retargeting strategy to increase the conversion rate among lapsed users.\n' +
                '> - A/B testing: Conduct A/B testing on ad creatives and messaging to identify what resonates best with the target audience.\n' +
                "> - Analyze竞争对手: Study competitors' campaigns to identify successful strategies that can be adapted for this campaign.",
            ],
            fldz2uNj7MN0D1PnJdINONdX: ['rec8UOVVtXn9mLvbTqsSvPbL'],
          },
          values: {
            fldEQiekV4tWY2MfJCtJ4n9s: ['image.png'],
            fldEzq5juPq0KaSCWMZG3YgE: ['Facebook'],
            fldJWq95nxT7alvFew5LJstG: '2024-08-27',
            fldMKy8n7G7SJ24VA4w85gtW: 'Lapsed users campaign-Convert lapsed users, CPA <$13',
            fldQlknpnIogOU7vsboki9Wr: 'Convert lapsed users, CPA <$13',
            fldg6A5uFlZl1GOgb45dBeQw: '2025-01-23',
            fldnDukLDPY2fDT2HCy2OFKp: 'Lapsed users campaign',
            fldoAZr3c4rJ13umpqy6H2UT: ['Published'],
            fldru3ghTtAZjgCEcRszZbWQ: [
              '*Campaign Summary and Optimization Suggestions*\n' +
                '\n' +
                'Results Data：\n' +
                '> - *Name*: Resurrect lapsed users\n' +
                '> - *Goals*: Lapsed users campaign-Convert lapsed users, CPA <$13\n' +
                '> - *Spend*: $34509\n' +
                '> - *Exposure amount*: 2,362,000\n' +
                '> - *Clicks*: 4,356\n' +
                '> - *Conversion volume*: 344\n' +
                '> - *CPM($)*: $14.61\n' +
                '> - *CPC($)*: $7.92\n' +
                '> - *CPA($)*: $100.32\n' +
                '\n' +
                '*Current Goals vs. Actual Performance*:  \n' +
                '- Target CPM: $10\n' +
                '- Actual CPM: $14.61\n' +
                '- Result: The campaign did not meet the target CPM, with CPM 46% higher than the goal.\n' +
                '\n' +
                '*Key Metrics That Need Improvement*:  \n' +
                '- CPA: The CPA is significantly higher than the target, indicating a need for optimization.\n' +
                '- Conversion volume: The conversion volume is lower than expected, suggesting that the campaign may not be effectively reaching or engaging the target audience.\n' +
                '\n' +
                '*Budget Adjustments or Actions Required*:  \n' +
                '- Reduce ad spend: Consider reducing the overall budget to align with the higher-than-expected CPA.\n' +
                '- Re-evaluate targeting: Review and adjust the targeting parameters to better reach the lapsed users.\n' +
                '\n' +
                '*Optimization Recommendations*:  \n' +
                '> - Retargeting strategy: Implement a more refined retargeting strategy to increase the conversion rate among lapsed users.\n' +
                '> - A/B testing: Conduct A/B testing on ad creatives and messaging to identify what resonates best with the target audience.\n' +
                "> - Analyze竞争对手: Study competitors' campaigns to identify successful strategies that can be adapted for this campaign.",
            ],
            fldz2uNj7MN0D1PnJdINONdX: ['Resurrect lapsed users'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'dat4MBnTVQc29cA9Qo3P4lPF',
      name: {
        en: 'Results',
        'zh-CN': '营销数据',
      },
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwU6sZx1elgeGJhIo6Q6CBL',
          name: {
            en: 'All results',
            'zh-CN': '总视图',
          },
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'single_text',
              hidden: false,
              width: 205,
            },
            {
              templateId: 'fldL6OyJukfgMvSBXtUz8ZLG',
              hidden: false,
              width: 182,
            },
            {
              templateId: 'fldCJ9N87oejjgY1kMjweecg',
              hidden: false,
              width: 149,
            },
            {
              templateId: 'fldwHLpBuGy4Rr42wjI9z0AE',
              hidden: false,
              width: 180,
            },
            {
              templateId: 'fldXoeSKNvvemrG2EZhzETja',
              hidden: false,
              width: 144,
            },
            {
              templateId: 'fldyPaKkYbTkdg2zEfBhLvhq',
              hidden: false,
              width: 172,
            },
            {
              templateId: 'fldbFxiNqQCkyLo5ttyXBr6C',
              hidden: false,
              width: 163,
            },
            {
              templateId: 'fldPC8foyw8Hr9DO13SHMKkT',
              hidden: false,
              width: 157,
            },
            {
              templateId: 'fldtMT4jDj2oW1Ks7S84CtEU',
              hidden: false,
              width: 152,
            },
            {
              templateId: 'fldW6330CwNUFs9qOSQkM2aU',
              hidden: false,
              width: 157,
            },
            {
              templateId: 'flduh9E6h52sGNO9Zz4wMlKF',
              hidden: false,
            },
          ],
        },
      ],
      fields: [
        {
          type: 'SINGLE_TEXT',
          templateId: 'single_text',
          privilege: 'TYPE_EDIT',
          name: {
            en: 'Name',
            'zh-CN': '名称',
          },
          primary: true,
        },
        {
          type: 'LINK',
          templateId: 'fldL6OyJukfgMvSBXtUz8ZLG',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Goals',
            'zh-CN': '目标',
          },
          property: {
            foreignDatabaseTemplateId: 'daty9DtDNVDPaeJyVLpP1XCA',
            brotherFieldTemplateId: 'fldz2uNj7MN0D1PnJdINONdX',
          },
          primary: false,
        },
        {
          type: 'CURRENCY',
          templateId: 'fldCJ9N87oejjgY1kMjweecg',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Spend',
            'zh-CN': '花费',
          },
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '$',
            symbolAlign: 'left',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldwHLpBuGy4Rr42wjI9z0AE',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Exposure amount',
            'zh-CN': '曝光量',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldXoeSKNvvemrG2EZhzETja',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Clicks',
            'zh-CN': '点击次数',
          },
          primary: false,
        },
        {
          type: 'SINGLE_TEXT',
          templateId: 'fldyPaKkYbTkdg2zEfBhLvhq',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Conversion volume',
            'zh-CN': '转化量',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldbFxiNqQCkyLo5ttyXBr6C',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Next steps',
            'zh-CN': '后续步骤',
          },
          property: {
            options: [
              {
                id: 'optynQVIFyo1bn1tNOL8yfjk',
                name: 'Continue',
                color: 'green5',
              },
              {
                id: 'optoGWhVxuCT1lGqSgjoZ3h4',
                name: 'Stop',
                color: 'pink5',
              },
              {
                id: 'opttHVSHCkXSmFjEyjZ1eGzK',
                name: 'Pause',
                color: 'tangerine5',
              },
              {
                id: 'optH6S53uJCg3bpBOWXKhBhM',
                name: 'Analysis',
                color: 'blue5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldPC8foyw8Hr9DO13SHMKkT',
          privilege: 'NAME_EDIT',
          name: {
            en: 'CPM（$）',
            'zh-CN': 'CPM（$）',
          },
          required: false,
          property: {
            expressionTemplate: '“$"&ROUND(1000*({fldCJ9N87oejjgY1kMjweecg}/{fldwHLpBuGy4Rr42wjI9z0AE}), 2)',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldtMT4jDj2oW1Ks7S84CtEU',
          privilege: 'NAME_EDIT',
          name: {
            en: 'CPC（$）',
            'zh-CN': 'CPC（$）',
          },
          required: false,
          property: {
            expressionTemplate: '“$"&ROUND({fldCJ9N87oejjgY1kMjweecg}/{fldXoeSKNvvemrG2EZhzETja},2)',
          },
          primary: false,
        },
        {
          type: 'FORMULA',
          templateId: 'fldW6330CwNUFs9qOSQkM2aU',
          privilege: 'NAME_EDIT',
          name: {
            en: 'CPA（$）',
            'zh-CN': 'CPA（$）',
          },
          required: false,
          property: {
            expressionTemplate: '“$"&ROUND(({fldCJ9N87oejjgY1kMjweecg}/{fldyPaKkYbTkdg2zEfBhLvhq}), 2)',
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'flduh9E6h52sGNO9Zz4wMlKF',
          privilege: 'NAME_EDIT',
          name: {
            en: 'Analysis results',
            'zh-CN': '分析结果',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recJlrgtyz2zLH0sUXOivQT0',
          data: {
            single_text: 'Instagram testing',
            fldCJ9N87oejjgY1kMjweecg: 2300,
            fldL6OyJukfgMvSBXtUz8ZLG: ['rec1H77HWWrxZij0NpzDSZZo'],
            fldPC8foyw8Hr9DO13SHMKkT: '$0.63',
            fldW6330CwNUFs9qOSQkM2aU: '$50',
            fldXoeSKNvvemrG2EZhzETja: '897',
            fldbFxiNqQCkyLo5ttyXBr6C: ['optH6S53uJCg3bpBOWXKhBhM'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$2.56',
            flduh9E6h52sGNO9Zz4wMlKF:
              '*Campaign Summary and Optimization Suggestions*\n' +
              '\n' +
              'Results Data：\n' +
              '> - *Name*: Instagram testing\n' +
              '> - *Goals*: Instagram testing-Drive awareness to new key accounts, CPM <$2.34\n' +
              '> - *Spend*: $2300\n' +
              '> - *Exposure amount*: 3,638,352\n' +
              '> - *Clicks*: 897\n' +
              '> - *Conversion volume*: 46\n' +
              '> - *CPM($)*: $0.63\n' +
              '> - *CPC($)*: $2.56\n' +
              '> - *CPA($)*: $50\n' +
              '\n' +
              '*Current Goals vs. Actual Performance*:  \n' +
              '- Target CPM: $2.34  \n' +
              '- Actual CPM: $0.63  \n' +
              '- Result: Exceeded the target, with CPM 73.4% lower than the goal.\n' +
              '\n' +
              '*Key Metrics That Need Improvement*:  \n' +
              '- *Clicks*: The number of clicks is relatively low compared to the exposure amount.\n' +
              '- *Conversion volume*: The conversion volume is low, indicating a need for better conversion optimization.\n' +
              '\n' +
              '*Budget Adjustments or Actions Required*:  \n' +
              '- Increase budget allocation to improve ad targeting and creatives to boost clicks and conversions.\n' +
              '- Consider A/B testing different ad creatives to identify which ones drive higher engagement and conversions.\n' +
              '\n' +
              '*Optimization Recommendations*:  \n' +
              '> - Optimize ad targeting to better align with the target audience to increase click-through rates.\n' +
              '> - Experiment with different ad formats and placements to improve engagement and conversion rates.\n' +
              '> - Monitor and analyze the performance of the campaign regularly to make data-driven adjustments.',
            fldwHLpBuGy4Rr42wjI9z0AE: '3638352',
            fldyPaKkYbTkdg2zEfBhLvhq: '46',
          },
          values: {
            single_text: 'Instagram testing',
            fldCJ9N87oejjgY1kMjweecg: '$2300',
            fldL6OyJukfgMvSBXtUz8ZLG: ['Instagram testing-Drive awareness to new key accounts, CPM <$2.34'],
            fldPC8foyw8Hr9DO13SHMKkT: '$0.63',
            fldW6330CwNUFs9qOSQkM2aU: '$50',
            fldXoeSKNvvemrG2EZhzETja: '897',
            fldbFxiNqQCkyLo5ttyXBr6C: ['Analysis'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$2.56',
            flduh9E6h52sGNO9Zz4wMlKF:
              '*Campaign Summary and Optimization Suggestions*\n' +
              '\n' +
              'Results Data：\n' +
              '> - *Name*: Instagram testing\n' +
              '> - *Goals*: Instagram testing-Drive awareness to new key accounts, CPM <$2.34\n' +
              '> - *Spend*: $2300\n' +
              '> - *Exposure amount*: 3,638,352\n' +
              '> - *Clicks*: 897\n' +
              '> - *Conversion volume*: 46\n' +
              '> - *CPM($)*: $0.63\n' +
              '> - *CPC($)*: $2.56\n' +
              '> - *CPA($)*: $50\n' +
              '\n' +
              '*Current Goals vs. Actual Performance*:  \n' +
              '- Target CPM: $2.34  \n' +
              '- Actual CPM: $0.63  \n' +
              '- Result: Exceeded the target, with CPM 73.4% lower than the goal.\n' +
              '\n' +
              '*Key Metrics That Need Improvement*:  \n' +
              '- *Clicks*: The number of clicks is relatively low compared to the exposure amount.\n' +
              '- *Conversion volume*: The conversion volume is low, indicating a need for better conversion optimization.\n' +
              '\n' +
              '*Budget Adjustments or Actions Required*:  \n' +
              '- Increase budget allocation to improve ad targeting and creatives to boost clicks and conversions.\n' +
              '- Consider A/B testing different ad creatives to identify which ones drive higher engagement and conversions.\n' +
              '\n' +
              '*Optimization Recommendations*:  \n' +
              '> - Optimize ad targeting to better align with the target audience to increase click-through rates.\n' +
              '> - Experiment with different ad formats and placements to improve engagement and conversion rates.\n' +
              '> - Monitor and analyze the performance of the campaign regularly to make data-driven adjustments.',
            fldwHLpBuGy4Rr42wjI9z0AE: '3638352',
            fldyPaKkYbTkdg2zEfBhLvhq: '46',
          },
        },
        {
          templateId: 'recx49f54Mm72dxCfDZaDpIM',
          data: {
            single_text: 'Expanded audience test',
            fldCJ9N87oejjgY1kMjweecg: 4500,
            fldL6OyJukfgMvSBXtUz8ZLG: ['recclS0ReKoS1KGGn7UHWYm0'],
            fldPC8foyw8Hr9DO13SHMKkT: '$7.93',
            fldW6330CwNUFs9qOSQkM2aU: '$562.5',
            fldXoeSKNvvemrG2EZhzETja: '78',
            fldbFxiNqQCkyLo5ttyXBr6C: ['opttHVSHCkXSmFjEyjZ1eGzK'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$57.69',
            fldwHLpBuGy4Rr42wjI9z0AE: '567699',
            fldyPaKkYbTkdg2zEfBhLvhq: '8',
          },
          values: {
            single_text: 'Expanded audience test',
            fldCJ9N87oejjgY1kMjweecg: '$4500',
            fldL6OyJukfgMvSBXtUz8ZLG: ['Expanded audience test-Convert lookalikes of high-value purchasers, CPA <$27'],
            fldPC8foyw8Hr9DO13SHMKkT: '$7.93',
            fldW6330CwNUFs9qOSQkM2aU: '$562.5',
            fldXoeSKNvvemrG2EZhzETja: '78',
            fldbFxiNqQCkyLo5ttyXBr6C: ['Pause'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$57.69',
            fldwHLpBuGy4Rr42wjI9z0AE: '567699',
            fldyPaKkYbTkdg2zEfBhLvhq: '8',
          },
        },
        {
          templateId: 'recv1FpjGJ48vI6XDwJo8jaL',
          data: {
            single_text: 'Search campaign',
            fldCJ9N87oejjgY1kMjweecg: 2200,
            fldL6OyJukfgMvSBXtUz8ZLG: ['rec6uFl0wRQyRMIMtpCPHen0'],
            fldPC8foyw8Hr9DO13SHMKkT: '$0.67',
            fldW6330CwNUFs9qOSQkM2aU: '$48.89',
            fldXoeSKNvvemrG2EZhzETja: '567',
            fldbFxiNqQCkyLo5ttyXBr6C: ['optynQVIFyo1bn1tNOL8yfjk'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$3.88',
            flduh9E6h52sGNO9Zz4wMlKF: '',
            fldwHLpBuGy4Rr42wjI9z0AE: '3283648',
            fldyPaKkYbTkdg2zEfBhLvhq: '45',
          },
          values: {
            single_text: 'Search campaign',
            fldCJ9N87oejjgY1kMjweecg: '$2200',
            fldL6OyJukfgMvSBXtUz8ZLG: ['Search campaign-Drive traffic to site, CPC <$2.2'],
            fldPC8foyw8Hr9DO13SHMKkT: '$0.67',
            fldW6330CwNUFs9qOSQkM2aU: '$48.89',
            fldXoeSKNvvemrG2EZhzETja: '567',
            fldbFxiNqQCkyLo5ttyXBr6C: ['Continue'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$3.88',
            flduh9E6h52sGNO9Zz4wMlKF: '',
            fldwHLpBuGy4Rr42wjI9z0AE: '3283648',
            fldyPaKkYbTkdg2zEfBhLvhq: '45',
          },
        },
        {
          templateId: 'reciG1vbjaUhCtipJLrorIEo',
          data: {
            single_text: 'Brand campaign',
            fldCJ9N87oejjgY1kMjweecg: 4940,
            fldL6OyJukfgMvSBXtUz8ZLG: ['recnwDJqJTUygvV3PnJyY4eH'],
            fldPC8foyw8Hr9DO13SHMKkT: '$1.14',
            fldW6330CwNUFs9qOSQkM2aU: '$20.16',
            fldXoeSKNvvemrG2EZhzETja: '3453',
            fldbFxiNqQCkyLo5ttyXBr6C: ['optoGWhVxuCT1lGqSgjoZ3h4'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$1.43',
            fldwHLpBuGy4Rr42wjI9z0AE: '4337532',
            fldyPaKkYbTkdg2zEfBhLvhq: '245',
          },
          values: {
            single_text: 'Brand campaign',
            fldCJ9N87oejjgY1kMjweecg: '$4940',
            fldL6OyJukfgMvSBXtUz8ZLG: ['Brand campaign-Drive traffic to site, CPC <$3.4'],
            fldPC8foyw8Hr9DO13SHMKkT: '$1.14',
            fldW6330CwNUFs9qOSQkM2aU: '$20.16',
            fldXoeSKNvvemrG2EZhzETja: '3453',
            fldbFxiNqQCkyLo5ttyXBr6C: ['Stop'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$1.43',
            fldwHLpBuGy4Rr42wjI9z0AE: '4337532',
            fldyPaKkYbTkdg2zEfBhLvhq: '245',
          },
        },
        {
          templateId: 'rec8UOVVtXn9mLvbTqsSvPbL',
          data: {
            single_text: 'Resurrect lapsed users',
            fldCJ9N87oejjgY1kMjweecg: 34509,
            fldL6OyJukfgMvSBXtUz8ZLG: ['recmen4TPp6WWn2Gz4hZJWke'],
            fldPC8foyw8Hr9DO13SHMKkT: '$14.61',
            fldW6330CwNUFs9qOSQkM2aU: '$100.32',
            fldXoeSKNvvemrG2EZhzETja: '4356',
            fldbFxiNqQCkyLo5ttyXBr6C: ['optH6S53uJCg3bpBOWXKhBhM'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$7.92',
            flduh9E6h52sGNO9Zz4wMlKF:
              '*Campaign Summary and Optimization Suggestions*\n' +
              '\n' +
              'Results Data：\n' +
              '> - *Name*: Resurrect lapsed users\n' +
              '> - *Goals*: Lapsed users campaign-Convert lapsed users, CPA <$13\n' +
              '> - *Spend*: $34509\n' +
              '> - *Exposure amount*: 2,362,000\n' +
              '> - *Clicks*: 4,356\n' +
              '> - *Conversion volume*: 344\n' +
              '> - *CPM($)*: $14.61\n' +
              '> - *CPC($)*: $7.92\n' +
              '> - *CPA($)*: $100.32\n' +
              '\n' +
              '*Current Goals vs. Actual Performance*:  \n' +
              '- Target CPM: $10\n' +
              '- Actual CPM: $14.61\n' +
              '- Result: The campaign did not meet the target CPM, with CPM 46% higher than the goal.\n' +
              '\n' +
              '*Key Metrics That Need Improvement*:  \n' +
              '- CPA: The CPA is significantly higher than the target, indicating a need for optimization.\n' +
              '- Conversion volume: The conversion volume is lower than expected, suggesting that the campaign may not be effectively reaching or engaging the target audience.\n' +
              '\n' +
              '*Budget Adjustments or Actions Required*:  \n' +
              '- Reduce ad spend: Consider reducing the overall budget to align with the higher-than-expected CPA.\n' +
              '- Re-evaluate targeting: Review and adjust the targeting parameters to better reach the lapsed users.\n' +
              '\n' +
              '*Optimization Recommendations*:  \n' +
              '> - Retargeting strategy: Implement a more refined retargeting strategy to increase the conversion rate among lapsed users.\n' +
              '> - A/B testing: Conduct A/B testing on ad creatives and messaging to identify what resonates best with the target audience.\n' +
              "> - Analyze竞争对手: Study competitors' campaigns to identify successful strategies that can be adapted for this campaign.",
            fldwHLpBuGy4Rr42wjI9z0AE: '2362000',
            fldyPaKkYbTkdg2zEfBhLvhq: '344',
          },
          values: {
            single_text: 'Resurrect lapsed users',
            fldCJ9N87oejjgY1kMjweecg: '$34509',
            fldL6OyJukfgMvSBXtUz8ZLG: ['Lapsed users campaign-Convert lapsed users, CPA <$13'],
            fldPC8foyw8Hr9DO13SHMKkT: '$14.61',
            fldW6330CwNUFs9qOSQkM2aU: '$100.32',
            fldXoeSKNvvemrG2EZhzETja: '4356',
            fldbFxiNqQCkyLo5ttyXBr6C: ['Analysis'],
            fldtMT4jDj2oW1Ks7S84CtEU: '$7.92',
            flduh9E6h52sGNO9Zz4wMlKF:
              '*Campaign Summary and Optimization Suggestions*\n' +
              '\n' +
              'Results Data：\n' +
              '> - *Name*: Resurrect lapsed users\n' +
              '> - *Goals*: Lapsed users campaign-Convert lapsed users, CPA <$13\n' +
              '> - *Spend*: $34509\n' +
              '> - *Exposure amount*: 2,362,000\n' +
              '> - *Clicks*: 4,356\n' +
              '> - *Conversion volume*: 344\n' +
              '> - *CPM($)*: $14.61\n' +
              '> - *CPC($)*: $7.92\n' +
              '> - *CPA($)*: $100.32\n' +
              '\n' +
              '*Current Goals vs. Actual Performance*:  \n' +
              '- Target CPM: $10\n' +
              '- Actual CPM: $14.61\n' +
              '- Result: The campaign did not meet the target CPM, with CPM 46% higher than the goal.\n' +
              '\n' +
              '*Key Metrics That Need Improvement*:  \n' +
              '- CPA: The CPA is significantly higher than the target, indicating a need for optimization.\n' +
              '- Conversion volume: The conversion volume is lower than expected, suggesting that the campaign may not be effectively reaching or engaging the target audience.\n' +
              '\n' +
              '*Budget Adjustments or Actions Required*:  \n' +
              '- Reduce ad spend: Consider reducing the overall budget to align with the higher-than-expected CPA.\n' +
              '- Re-evaluate targeting: Review and adjust the targeting parameters to better reach the lapsed users.\n' +
              '\n' +
              '*Optimization Recommendations*:  \n' +
              '> - Retargeting strategy: Implement a more refined retargeting strategy to increase the conversion rate among lapsed users.\n' +
              '> - A/B testing: Conduct A/B testing on ad creatives and messaging to identify what resonates best with the target audience.\n' +
              "> - Analyze竞争对手: Study competitors' campaigns to identify successful strategies that can be adapted for this campaign.",
            fldwHLpBuGy4Rr42wjI9z0AE: '2362000',
            fldyPaKkYbTkdg2zEfBhLvhq: '344',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
