{"templateId": "3day-outreach-emails", "name": {"en": "3-Day Outreach Email Campaign", "zh-CN": "连续3天自动化邮件触达"}, "description": {"en": "Quickly set up a 3-day automated email outreach, especially suitable for scenarios including: continuous contact with potential customers, product launch countdown marketing, and ongoing welcome emails for new registrants.", "zh-CN": "快速建立一个连续 3 天邮件自动触达的用户旅程，特别适用于以下场景：潜在客户的持续跟进、产品发布倒计时的连续营销、新注册用户的多阶段欢迎邮件等。"}, "keywords": {"zh-CN": "自动化邮件营销, 客户互动, 个性化邮件, 市场营销自动化, 电子邮件模板", "zh-TW": "自動化郵件營銷, 客戶互動, 個性化郵件, 市場營銷自動化, 電子郵件模板", "ja": "自動化メールマーケティング, 顧客インタラクション, パーソナライズドメール, マーケティングオートメーション, メールテンプレート", "en": "Automated email marketing, Customer interaction, Personalized emails, Marketing automation, Email templates"}, "personas": {"zh-CN": "电子商务企业主, SaaS公司营销人, 客户关系管理团队, 活动策划公司", "zh-TW": "電子商務企業主, SaaS公司行銷人, 客戶關係管理團隊, 活動策劃公司", "ja": "Eコマースビジネスオーナー, SaaS企業のマーケター, 顧客関係管理チーム, イベント企画会社", "en": "E-commerce business owner, SaaS company marketing manager, CRM team, Event planning company"}, "useCases": {"en": "Welcome new users, Send product recommendations, Track order status, Remind about abandoned cart, Offer discount promotions, Collect customer feedback, Onboard new users, Notify software updates, Promote webinars, Send user tutorials, Provide customer support, Conduct satisfaction surveys, Maintain customer relationships, Remind about renewals, Send personalized recommendations, Update customer information, Run loyalty programs, Send thank-you notes, Send event invitations, Provide event schedules, Send registration confirmations, Remind about event start, Collect event feedback, Provide follow-up materials", "zh-CN": "欢迎新用户, 发送产品推荐, 跟踪订单状态, 提醒购物车遗弃, 提供折扣优惠, 收集客户反馈, 引导新用户上手, 通知软件更新, 宣传网络研讨会, 发送用户教程, 提供客户支持, 进行满意度调查, 维护客户关系, 提醒客户续约, 发送个性化推荐, 更新客户信息, 进行忠诚度计划, 发送感谢信, 发送活动邀请, 提供活动日程, 发送报名确认, 提醒活动开始, 收集活动反馈, 提供后续资料", "zh-TW": "歡迎新用戶, 發送產品推薦, 跟蹤訂單狀態, 提醒購物車遺棄, 提供折扣優惠, 收集客戶反饋, 引導新用戶上手, 通知軟件更新, 宣傳網絡研討會, 發送用戶教程, 提供客戶支持, 進行滿意度調查, 維護客戶關係, 提醒客戶續約, 發送個性化推薦, 更新客戶信息, 進行忠誠度計劃, 發送感謝信, 發送活動邀請, 提供活動日程, 發送報名確認, 提醒活動開始, 收集活動反饋, 提供後續資料", "ja": "新規ユーザーの歓迎, 商品の推奨, 注文状況の追跡, カート放棄のリマインダー, 割引プロモーションの提供, 顧客フィードバックの収集, 新規ユーザーのオンボーディング, ソフトウェア更新の通知, ウェビナーの宣伝, ユーザーガイドの送信, 顧客サポートの提供, 満足度調査の実施, 顧客関係の維持, 更新のリマインダー, パーソナライズド推奨の送信, 顧客情報の更新, ロイヤルティプログラムの実施, 感謝のメッセージを送信, イベント招待の送信, イベントスケジュールの提供, 登録確認の送信, イベント開始のリマインダー, イベントフィードバックの収集, フォローアップ資料の提供"}, "cover": "/assets/template/template-cover-3day-outreach-emails.png", "author": "Tianlu <<EMAIL>>", "category": ["marketing", "email", "automation"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.2", "resources": [{"resourceType": "AUTOMATION", "templateId": "email_series_automation", "name": {"en": "Email Series Automation", "zh-CN": "邮件序列自动化"}, "description": {"en": "Send a series of emails to new users over a period of 3 days", "zh-CN": "在 3 天内向新用户发送一系列邮件"}, "triggers": [{"triggerType": "RECORD_MATCH", "templateId": "trigger_new_user", "description": {"en": "When the new user status is \"Confirm send email\"", "zh-CN": "当状态等于确认发送时"}, "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "status", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "confirm_send"}}]}, "databaseTemplateId": "new_users_database"}}], "actions": [{"templateId": "first_email", "description": {"en": "Send first welcome email to new users", "ja": "新規ユーザーに歓迎メールを送信", "zh-CN": "向新用户发送第一份欢迎邮件", "zh-TW": "向新用戶發送第一份歡迎郵件"}, "actionType": "SEND_EMAIL", "input": {"type": "SMTP_INTEGRATION", "subject": "Discover the Use Cases of Bika.ai!(1/3)", "body": {"html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <style>\n        body {\n            background-color: white;\n        }\n       .hidden-content {\n            color: white;\n        }\n    </style>\n</head>\n<body>\n    <p>Dear User,</p>\n    <p>Welcome back! In our previous email, we introduced the basics of Bika.ai. Now, we would like to guide you through the core features of our platform, helping you make the most of our services.</p>\n    <p>Bika.ai is a collaborative AI automation platform that proactively reminds and initiates tasks for people. You don’t need to converse with the AI; instead, the AI automatically assists you or your team at scheduled times to complete tasks. Bika.ai offers the following features:</p>\n    <h2>Smart Tasks</h2>\n    <p>AI proactively creates tasks, assigns them, and determines completion automatically without human intervention, such as daily accounting, daily journaling, team tasks, morning meeting reports, and quarterly OKR reviews.</p>\n    <h2>AI Reports</h2>\n    <p>AI generates reports based on your activities or tasks, such as weekly journal summaries, income and expense analysis, sales weekly reports, attendance statistics, etc.</p>\n    <h2>Data Visualization</h2>\n    <p>AI automates task creation, assigns people to collect data, and generates multidimensional tables supporting billions of rows, with charts generated from your data, such as sales figures, customer growth, team performance, and population surveys.</p>\n    <h2>Smart Reminders</h2>\n    <p>Thoughtful reminders for every anniversary and important event, such as friends' birthdays, client gifts, sales visits, and performance tracking.</p>\n    <h2>Voice Input</h2>\n    <p>Talk to Bika to convert your voice into structured data, such as quick inspiration notes, client visit entries, personal journals, and team CRM.</p>\n    <p>To help you quickly master these features, we recommend the following resources:</p>\n    <p>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Feature Demo Videos</a><br>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Detailed User Manual</a>\n    </p>\n    <p>If you have any questions or need further assistance, please feel free to contact us. We look forward to your success with Bika.ai!</p>\n    <p>Best regards,</p>\n    <p>The Bika.ai Team</p>\n    <p class=\"hidden-content\">[<%= _automation.runHistoryId %>]</p>\n</body>\n</html>"}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trigger_new_user.record.cells.email.data %>"}], "integrationId": ""}}, {"templateId": "first_delay", "description": {"en": "Delay 1 day", "ja": "1 日遅れ", "zh-CN": "延迟 1 天", "zh-TW": "延遲 1 天"}, "actionType": "DELAY", "input": {"type": "DELAY", "unit": "DAY", "value": 1}}, {"templateId": "second_email", "description": {"en": "Send second email to new users", "ja": "新規ユーザーに第2のメールを送信", "zh-CN": "向新用户发送第二份邮件", "zh-TW": "向新用戶發送第二份郵件"}, "actionType": "SEND_EMAIL", "input": {"type": "SMTP_INTEGRATION", "subject": "Discover the Use Cases of Bika.ai!(2/3)", "body": {"html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <style>\n        body {\n            background-color: white;\n        }\n       .hidden-content {\n            color: white;\n        }\n    </style>\n</head>\n<body>\n    <p>Dear User,</p>\n    <p>Welcome back! In our previous email, we introduced the basics of Bika.ai. Now, we would like to guide you through the core features of our platform, helping you make the most of our services.</p>\n    <p>Bika.ai is a collaborative AI automation platform that proactively reminds and initiates tasks for people. You don’t need to converse with the AI; instead, the AI automatically assists you or your team at scheduled times to complete tasks. Bika.ai offers the following features:</p>\n    <h2>Smart Tasks</h2>\n    <p>AI proactively creates tasks, assigns them, and determines completion automatically without human intervention, such as daily accounting, daily journaling, team tasks, morning meeting reports, and quarterly OKR reviews.</p>\n    <h2>AI Reports</h2>\n    <p>AI generates reports based on your activities or tasks, such as weekly journal summaries, income and expense analysis, sales weekly reports, attendance statistics, etc.</p>\n    <h2>Data Visualization</h2>\n    <p>AI automates task creation, assigns people to collect data, and generates multidimensional tables supporting billions of rows, with charts generated from your data, such as sales figures, customer growth, team performance, and population surveys.</p>\n    <h2>Smart Reminders</h2>\n    <p>Thoughtful reminders for every anniversary and important event, such as friends' birthdays, client gifts, sales visits, and performance tracking.</p>\n    <h2>Voice Input</h2>\n    <p>Talk to Bika to convert your voice into structured data, such as quick inspiration notes, client visit entries, personal journals, and team CRM.</p>\n    <p>To help you quickly master these features, we recommend the following resources:</p>\n    <p>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Feature Demo Videos</a><br>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Detailed User Manual</a>\n    </p>\n    <p>If you have any questions or need further assistance, please feel free to contact us. We look forward to your success with Bika.ai!</p>\n    <p>Best regards,</p>\n    <p>The Bika.ai Team</p>\n    <p class=\"hidden-content\">[<%= _automation.runHistoryId %>]</p>\n</body>\n</html>\n    "}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trigger_new_user.record.cells.email.data %>"}], "integrationId": ""}}, {"templateId": "second_delay", "description": {"en": "Delay 1 day", "ja": "1 日遅れ", "zh-CN": "延迟 1 天", "zh-TW": "延遲 1 天"}, "actionType": "DELAY", "input": {"type": "DELAY", "unit": "DAY", "value": 1}}, {"templateId": "third_email", "description": {"en": "Send third email to new users", "ja": "新規ユーザーに第3のメールを送信", "zh-CN": "向新用户发送第三份邮件", "zh-TW": "向新用戶發送第三份郵件"}, "actionType": "SEND_EMAIL", "input": {"type": "SERVICE", "subject": "Who Can Benefit from Bika.ai!(3/3)", "body": {"html": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <style>\n        body {\n            background-color: white;\n        }\n       .hidden-content {\n            color: white;\n        }\n    </style>\n</head>\n<body>\n    <p>Dear User,</p>\n    <p>Welcome back! In our previous email, we introduced the basics of Bika.ai. Now, we would like to guide you through the core features of our platform, helping you make the most of our services.</p>\n    <p>Bika.ai is a collaborative AI automation platform that proactively reminds and initiates tasks for people. You don’t need to converse with the AI; instead, the AI automatically assists you or your team at scheduled times to complete tasks. Bika.ai offers the following features:</p>\n    <h2>Smart Tasks</h2>\n    <p>AI proactively creates tasks, assigns them, and determines completion automatically without human intervention, such as daily accounting, daily journaling, team tasks, morning meeting reports, and quarterly OKR reviews.</p>\n    <h2>AI Reports</h2>\n    <p>AI generates reports based on your activities or tasks, such as weekly journal summaries, income and expense analysis, sales weekly reports, attendance statistics, etc.</p>\n    <h2>Data Visualization</h2>\n    <p>AI automates task creation, assigns people to collect data, and generates multidimensional tables supporting billions of rows, with charts generated from your data, such as sales figures, customer growth, team performance, and population surveys.</p>\n    <h2>Smart Reminders</h2>\n    <p>Thoughtful reminders for every anniversary and important event, such as friends' birthdays, client gifts, sales visits, and performance tracking.</p>\n    <h2>Voice Input</h2>\n    <p>Talk to Bika to convert your voice into structured data, such as quick inspiration notes, client visit entries, personal journals, and team CRM.</p>\n    <p>To help you quickly master these features, we recommend the following resources:</p>\n    <p>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Feature Demo Videos</a><br>\n        <a href=\"#\" target=\"_blank\" rel=\"noopener noreferrer\">Detailed User Manual</a>\n    </p>\n    <p>If you have any questions or need further assistance, please feel free to contact us. We look forward to your success with Bika.ai!</p>\n    <p>Best regards,</p>\n    <p>The Bika.ai Team</p>\n    <p class=\"hidden-content\">[<%= _automation.runHistoryId %>]</p>\n</body>\n</html>\n    "}, "to": [{"type": "EMAIL_STRING", "email": "<%= _triggers.trigger_new_user.record.cells.email.data %>"}]}}]}, {"resourceType": "AUTOMATION", "templateId": "atowmbb01GcIgD4aA4q3Vgdu", "name": {"en": "Interrupt Series Delay Automation", "zh-CN": "打断序列延时自动化"}, "description": {"en": "When user replies to the email, stop sending subsequent messages in the sequence.", "zh-CN": "当用户回复邮件时，打断后续的序列发送"}, "triggers": [{"triggerType": "INBOUND_EMAIL", "templateId": "trgGv0xsCXkXfo2DR1W9MWgl", "description": {"en": "When email is replied ", "zh-CN": "当邮件被回复时"}, "input": {"mailboxName": "INBOX", "searchCriteria": "", "downloadAttachments": false, "type": "IMAP_INTEGRATION", "integrationId": ""}}], "actions": [{"templateId": "actpKFTPry8VvsJMVvoN04Ez", "description": {"en": " Extract run history ID", "zh-CN": "提取序列自动化的运行ID"}, "actionType": "RUN_SCRIPT", "input": {"type": "SCRIPT", "language": "python", "script": "import re\n\n# Email text\ncontent = \"\"\"<%= _triggers.trgGv0xsCXkXfo2DR1W9MWgl.text %>\"\"\"\n\ntry:\n    # Define a regular expression pattern to match the content inside square brackets\n    pattern = r'\\[([^\\]]+)\\]'\n    # Use the re.findall function to find all matches\n    matches = re.findall(pattern,content)\n    if matches:\n        # Get the last match\n        last_match = matches[-1]\n        print(last_match)\n    else:\n        print(\"No content inside square brackets was found.\")\nexcept re.error as e:\n    print(f\"Regular expression error: {e}\")\nexcept Exception as e:\n    print(f\"An unknown error occurred: {e}\")\n    "}}, {"templateId": "actOLnaauS3ANt1l8M0dchOP", "description": {"en": "Send request to interrupt delay", "zh-CN": "发送请求打断延迟"}, "actionType": "WEBHOOK", "input": {"type": "WEBHOOK", "method": "POST", "headers": [{"key": "Authorization", "value": ""}], "url": "https://staging.bika.ai/api/openapi/bika/v1/automations/runs/<%= _actions.actpKFTPry8VvsJMVvoN04Ez.last_match %>/cancel/", "timeout": 120}}]}, {"resourceType": "DATABASE", "templateId": "new_users_database", "name": {"en": "New Users", "ja": "新規ユーザーテーブル", "zh-CN": "新用户表", "zh-TW": "新用戶表"}, "description": {"en": "Store new user information", "ja": "新規ユーザー情報を保存", "zh-CN": "存储新用户信息", "zh-TW": "存儲新用戶信息"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "all", "name": {"en": "All Users", "ja": "すべてのユーザー", "zh-CN": "所有用户", "zh-TW": "所有用戶"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "email", "hidden": false}, {"templateId": "user", "hidden": false}, {"templateId": "status", "hidden": false}, {"templateId": "created_at", "hidden": false}]}, {"type": "TABLE", "templateId": "filter_allow_sent", "name": {"en": "Can be sent", "ja": "送信可能なユーザー", "zh-CN": "可发送的用户", "zh-TW": "可發送的用戶"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "status", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "confirm_send"}}]}, "sorts": [], "fields": [{"templateId": "email", "hidden": false}, {"templateId": "user", "hidden": false}, {"templateId": "status", "hidden": false}, {"templateId": "created_at", "hidden": false}]}], "fields": [{"type": "EMAIL", "templateId": "email", "privilege": "TYPE_EDIT", "name": {"en": "Email", "ja": "メールアドレス", "zh-CN": "邮箱", "zh-TW": "郵箱"}, "description": {"en": "Email address of the new user", "ja": "新規ユーザーのメールアドレス", "zh-CN": "新用户的邮箱地址", "zh-TW": "新用戶的郵箱地址"}, "required": true, "primary": true}, {"type": "SINGLE_TEXT", "templateId": "user", "privilege": "NAME_EDIT", "name": {"en": "User name", "ja": "ユーザー名", "zh-CN": "用户名", "zh-TW": "用戶名"}, "description": {"en": "Name of the new user", "ja": "新規ユーザーのニックネーム", "zh-CN": "新用户的昵称", "zh-TW": "新用戶的暱稱"}, "required": false, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "status", "privilege": "NAME_EDIT", "name": {"en": "Status", "ja": "ステータス", "zh-CN": "状态", "zh-TW": "狀態"}, "required": true, "property": {"options": [{"id": "pending", "name": "Pending", "color": "indigo"}, {"id": "confirm_send", "name": "Confirm send email", "color": "teal"}, {"id": "email_sent", "name": "Email sent", "color": "deepPurple"}]}, "primary": false}, {"type": "CREATED_TIME", "templateId": "created_at", "privilege": "NAME_EDIT", "name": {"en": "Created At", "ja": "作成日時", "zh-CN": "创建时间", "zh-TW": "創建時間"}, "property": {"timeZone": "Asia/Shanghai", "dateFormat": "YYYY/MM/DD", "includeTime": true}, "primary": false}], "records": [{"templateId": "rec71fFn8dtga8147RouOgz6", "data": {"user": "Your test name", "email": "<EMAIL>", "status": ["Pending"], "created_at": "2025-03-21T07:22:25.911Z"}, "values": {"user": "Your test name", "email": "<EMAIL>", "status": ["Pending"], "created_at": "2025-02-28T10:01:25.514Z"}}, {"templateId": "rectVKqY50rKknLUworcVKqs", "data": {"user": "Your test name", "email": "<EMAIL>", "status": ["Pending"], "created_at": "2025-03-21T07:22:25.911Z"}, "values": {"user": "Your test name", "email": "<EMAIL>", "status": ["Pending"], "created_at": "2025-02-28T10:01:25.514Z"}}, {"templateId": "recA1ZWufFxPXzgyuZ9sV4RF", "data": {"user": "Your test name", "email": "<EMAIL>", "status": ["confirm_send"], "created_at": "2025-03-21T07:22:25.911Z"}, "values": {"user": "Your test name", "email": "<EMAIL>", "status": ["Confirm send email"], "created_at": "2025-02-28T10:01:25.514Z"}}]}], "initMissions": [], "$schema": "https://dev.bika.ai/api/schema/custom-template.json"}