import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  schemaVersion: 'v1',
  author: 'Vika',
  templateId: '@vika/crm',
  name: 'Vika CRM模板',
  cover: '/assets/template/template-cover-vika-crm.png',
  description: '用于Vika团队的销售团队拜访记录管理',
  version: '0.0.14',
  category: 'project',
  visibility: 'PRIVATE',
  // 预设“角色”
  presetUnits: [
    {
      type: 'ROLE',
      templateId: 'sales_partner_role',
      name: '销售团队',
    },
  ],
  initMissions: [
    {
      name: '点我试试手动录入一条销售拜访记录吧~',
      description: '点击任务详情-开始填写拜访记录。或点击“前往自动化”按钮，尝试一下手动录入一条拜访记录吧~',
      type: 'CREATE_RECORD', // 提醒点击任务详情 -  弹出任务详情 - 关联拜访记录与对应视图 - 录入拜访记录
      databaseTemplateId: 'crm-notes',
      viewTemplateId: 'my_followup_view',
      to: [{ type: 'CURRENT_OPERATOR' }], // 发给应用模板的创建者
    },
    {
      name: '欢迎来使用CRM模板，销售拜访记录自动化提醒',
      description:
        '每周一早上8点30分，已为您自动开启拜访记录录入定时提醒，届时会自动发送邮件和消息通知销售人员录入拜访记录。',
      type: 'REDIRECT_SPACE_NODE',
      nodeTemplateId: 'crm-notes',
      canCompleteManually: true,
      to: [{ type: 'CURRENT_OPERATOR' }],
    },
    {
      name: '邀请销售人员一起加入空间站~',
      type: 'INVITE_MEMBER', // 弹出邀请成员的对话框
      description: '您可以开始邀请销售人员进入空间站了~~',
      to: [{ type: 'CURRENT_OPERATOR' }], // 发给应用模板的创建者
    },
  ],
  // 可以安装多次
  installOnce: false,
  dependencies: {
    'base-crm': '*',
    'base-team': '*',
  },
  resources: [
    {
      resourceType: 'DATABASE',
      templateId: 'crm-notes',
      databaseType: 'DATUM',
      name: '拜访记录表',
      description: '客户销售拜访记录,关联organization表',
      fields: [
        {
          type: 'ONE_WAY_LINK',
          templateId: 'sales',
          name: '客户',
          property: {
            foreignDatabaseTemplateId: 'base-crm:organization',
          },
          description: '对应销售跟进的客户',
        },
        {
          type: 'LONG_TEXT',
          templateId: 'update_status',
          name: '客户情况更新',
          description: '目前客户情况描述，遇到什么问题，场景需求是什么，决策链等',
        },
        {
          type: 'LONG_TEXT',
          templateId: 'next_step',
          name: '下一步行动',
          description: '针对上述情况，采取什么策略，需要哪些协助有助于签单？',
        },
        {
          type: 'ATTACHMENT',
          name: '附件',
          description:
            '1. 可以提交语音、聊天截图、上钟视频；\n' +
            '2. 推荐企微微信聊天截图，如果是绿色微信，聊天截图需要显示到具体客户名字、具体日期，可采取”滚动屏幕的方式“截屏。\n' +
            '3. 如线下拜访无材料或其它原因，也可提交>100字的文字记录；',
        },
        {
          templateId: 'followup_creator',
          name: '提交人',
          type: 'CREATED_BY',
        },
        {
          templateId: 'created_time',
          name: 'Created Time',
          type: 'CREATED_TIME',
          property: {
            dateFormat: 'YYYY-MM-DD',
            timeFormat: 'HH:mm',
            includeTime: false,
          },
        },
      ],
      views: [
        {
          templateId: 'all_view',
          name: '所有记录',
          type: 'TABLE',
        },
        {
          templateId: 'my_followup_view',
          name: '我的拜访记录',
          type: 'TABLE',
          filters: {
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'followup_creator',
                fieldType: 'CREATED_BY',
                clause: {
                  operator: 'Is',
                  value: 'Self',
                },
              },
            ],
            conjunction: 'And',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'sales',
      databaseType: 'DATUM',
      name: '客户项目',
      description: '记录每个客户的销售状态，关联organization',
      fields: [
        {
          type: 'ONE_WAY_LINK',
          templateId: 'sales',
          name: '客户',
          property: {
            foreignDatabaseTemplateId: 'base-crm:organization',
          },
          description: '对应销售跟进的客户',
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'is_renewal',
          name: '是否续费客户',
          property: {
            options: [
              { id: 'new', name: '新客户' },
              { id: 'renewal', name: '续费客户' },
            ],
          },
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'stage',
          name: '销售阶段',
          property: {
            options: [
              { id: 'onboarding', name: '上钟演示' },
              { id: 'steady', name: '稳健发展' },
              { id: 'trial', name: '试用阶段' },
              { id: 'contract', name: '合同阶段' },
              { id: 'deal', name: '回款阶段' },
              { id: 'renewal', name: '续费阶段' },
              { id: 'churned', name: '客戶流失' },
            ],
          },
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'close_confidence',
          name: '客户健康指数',
          description:
            '优秀 (Excellent)客户与产品或服务的互动频繁，显示出高度的满意度和忠诚度。客户经常推荐该产品或服务，并且续订的可能性非常高。\n' +
            '良好 (Good)客户对产品或服务比较满意，使用情况稳定。虽然可能不像优秀客户那样积极推荐，但他们有一个健康的利用率，并且续订的可能性也很高。\n' +
            '危险 (Risk)客户对产品或服务的满意度下降，出现了一些问题或挑战，可能需要额外的关注和支持。续订可能性不确定，且存在流失的风险。\n' +
            '失败 (Fail)客户对产品或服务表现出显著的不满，几乎不使用或完全停止使用。续订的可能性极低，且极有可能在合同结束时流失。',
          property: {
            options: [
              { id: 'excellent', name: '优秀' },
              { id: 'good', name: '良好' },
              { id: 'fail', name: '流失' },
              { id: 'risk', name: '危险' },
            ],
          },
        },
        {
          type: 'NUMBER',
          templateId: 'estimate_contract_value',
          name: '预估合同金额(TCV)',
          property: {
            precision: 2,
            symbol: '元',
          },
        },
        {
          type: 'MEMBER',
          templateId: 'sales-person',
          name: '跟进的销售',
          property: {
            many: true,
          },
        },
      ],
      views: [
        {
          templateId: 'all_customer_project_view',
          name: '所有记录',
          type: 'TABLE',
        },
        {
          templateId: 'my_project_view',
          name: '我的客户项目',
          type: 'TABLE',
          filters: {
            conditions: [],
            conds: [
              {
                fieldTemplateId: 'sales-person',
                fieldType: 'MEMBER',
                clause: {
                  operator: 'Is',
                  value: ['Self'],
                },
              },
            ],
            conjunction: 'And',
          },
        },
      ],
    },
    // 每周一创建拜访记录任务，每周50个
    {
      resourceType: 'AUTOMATION',
      templateId: 'automation-crm-notes',
      name: '每周给销售人员自动创建拜访记录',
      description: '每周一自动触发base:sales表中销售人员，创建拜访记录任务',
      status: 'ACTIVE',
      triggers: [
        {
          triggerType: 'SCHEDULER',
          templateId: 'okr_created',
          description: '当成员提交 拜访记录 时触发',
          input: {
            type: 'SCHEDULER',
            scheduler: {
              timezone: 'AUTO',
              datetime: '2024-01-01T11:30:00Z', // 从这天开始往后
              repeat: {
                every: {
                  type: 'WEEK',
                  interval: 1,
                  weekdays: ['THU'],
                },
              },
            },
          },
        },
      ],
      actions: [
        // 每周一创建mission，请求创建50条拜访记录
        {
          actionType: 'CREATE_MISSION',
          templateId: 'create_50_crm_notes',
          description: '填写50条拜访记录，周日前结束、周五汇总',
          input: {
            type: 'MISSION_BODY',
            mission: {
              type: 'CREATE_MULTI_RECORDS',
              name: '填写50条拜访记录',
              databaseTemplateId: 'crm-notes',
              amount: 50,
              to: [
                {
                  // 找到基础表里的花名册的销售人员视图
                  type: 'MEMBER_FIELD',
                  databaseTemplateId: 'base-team:employee',
                  viewTemplateId: 'sales-employees',
                  fieldTemplateId: 'member',
                },
              ],
            },
          },
        },
      ],
    },
  ],
};

export default template;
