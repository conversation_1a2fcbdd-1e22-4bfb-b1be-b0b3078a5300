import { CustomTemplate } from '@bika/types/template/bo';

const template: CustomTemplate = {
  templateId: 'customer-projects',
  name: 'Customer projects',
  description:
    'This template is ideal for consulting companies, law firms, and sales since it helps calculate the hours worked that require compensation, provides a high-level overview of the progress of your projects, and creates a collaborative platform.',
  cover: '/assets/template/customer-projects/customer-projects.png',
  author: '<PERSON> <<EMAIL>>',
  category: ['sales'],
  keywords: 'projects, customer, sales',
  useCases:
    'project management, customer management, sales management, task management, time tracking, project tracking',
  visibility: 'PUBLIC',
  schemaVersion: 'v1',
  version: '1.0.4',
  resources: [
    {
      resourceType: 'DASHBOARD',
      templateId: 'dsb8xPEUF8UrJgoEoabicMy0',
      name: 'Projects Board',
      widgets: [
        {
          templateId: 'wdtfuBhyj1gIjNskZvTjKutQ',
          type: 'CHART',
          name: 'Project Status',
          datasource: {
            databaseTemplateId: 'datyGBvMfMABaprcDBB8Cjdg',
            viewTemplateId: 'viwNsLAJhdps4dn29fU4Eb1n',
            type: 'DATABASE',
            chartType: 'bar',
            metricsType: 'COUNT_RECORDS',
            dimensionTemplateId: 'fldxR9v6LS2SzRA099cdV48H',
          },
        },
        {
          templateId: 'wdtHAOaGaK9PojpkeHikcE4j',
          type: 'NUMBER',
          name: '# of Project',
          datasource: {
            databaseTemplateId: 'datyGBvMfMABaprcDBB8Cjdg',
            viewTemplateId: 'viwNsLAJhdps4dn29fU4Eb1n',
            type: 'DATABASE',
            metricsType: 'COUNT_RECORDS',
          },
        },
        {
          templateId: 'wdtWsnGCkebDkIZ3Eub26bNm',
          type: 'NUMBER',
          name: '# of Task',
          datasource: {
            databaseTemplateId: 'datnTAXyoNNCSeCqozPITDva',
            viewTemplateId: 'viw7fetFvi3KAsnSLjZNCnTR',
            type: 'DATABASE',
            metricsType: 'COUNT_RECORDS',
          },
        },
        {
          templateId: 'wdtKkPs0pgcSrR8gJu8k0q3A',
          type: 'CHART',
          name: 'Task Status',
          datasource: {
            databaseTemplateId: 'datnTAXyoNNCSeCqozPITDva',
            viewTemplateId: 'viw7fetFvi3KAsnSLjZNCnTR',
            type: 'DATABASE',
            chartType: 'bar',
            metricsType: 'COUNT_RECORDS',
            dimensionTemplateId: 'fldEGRPVFlDTpSkp9kCREkTs',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datyGBvMfMABaprcDBB8Cjdg',
      name: 'Projects',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viwb9EkenZFeQVtl0Hc3xmOW',
          name: 'Project View',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldWt4uc5VIJv2loeGbWsclH',
              hidden: false,
            },
            {
              templateId: 'fldwjBVp1qBGXoJ3osShLBvR',
              hidden: false,
            },
            {
              templateId: 'fldxR9v6LS2SzRA099cdV48H',
              hidden: false,
            },
            {
              templateId: 'fldaLcbEnWHXsgVNw0QzcUGS',
              hidden: false,
            },
            {
              templateId: 'fldIv5qmv8Hltv4mWVWUTWy5',
              hidden: false,
            },
            {
              templateId: 'fldjbAgp8jwOY6H9Hf15YliB',
              hidden: false,
            },
            {
              templateId: 'fldfRQ2XZP12Itf5iPV66PlJ',
              hidden: false,
            },
          ],
          groups: [
            {
              fieldTemplateId: 'fldxR9v6LS2SzRA099cdV48H',
              asc: true,
            },
          ],
        },
        {
          type: 'TABLE',
          templateId: 'viwNsLAJhdps4dn29fU4Eb1n',
          name: 'All',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldWt4uc5VIJv2loeGbWsclH',
            },
            {
              templateId: 'fldxR9v6LS2SzRA099cdV48H',
            },
            {
              templateId: 'fldaLcbEnWHXsgVNw0QzcUGS',
            },
            {
              templateId: 'fldIv5qmv8Hltv4mWVWUTWy5',
            },
            {
              templateId: 'fldjbAgp8jwOY6H9Hf15YliB',
            },
            {
              templateId: 'fldwjBVp1qBGXoJ3osShLBvR',
            },
            {
              templateId: 'fldfRQ2XZP12Itf5iPV66PlJ',
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fldWt4uc5VIJv2loeGbWsclH',
          privilege: 'TYPE_EDIT',
          name: 'Projects',
          primary: true,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldxR9v6LS2SzRA099cdV48H',
          privilege: 'NAME_EDIT',
          name: 'Status',
          property: {
            options: [
              {
                id: 'optnoe6upYQekT31anYX5lX6',
                name: 'Working on it',
                color: 'deepPurple5',
              },
              {
                id: 'opt7mGoFy17s43peL8dU5emd',
                name: 'Done',
                color: 'indigo5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldaLcbEnWHXsgVNw0QzcUGS',
          privilege: 'NAME_EDIT',
          name: 'Project - Start',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldIv5qmv8Hltv4mWVWUTWy5',
          privilege: 'NAME_EDIT',
          name: 'Projec - End',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldjbAgp8jwOY6H9Hf15YliB',
          privilege: 'NAME_EDIT',
          name: 'Notes',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldwjBVp1qBGXoJ3osShLBvR',
          privilege: 'NAME_EDIT',
          name: 'Contacts',
          property: {
            foreignDatabaseTemplateId: 'datgpTnLfjTIbh98R1K50zk2',
            brotherFieldTemplateId: 'fldIMHoBK3arVhMwjy9hosxP',
          },
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldfRQ2XZP12Itf5iPV66PlJ',
          privilege: 'NAME_EDIT',
          name: 'Customer Projects Task',
          property: {
            foreignDatabaseTemplateId: 'datnTAXyoNNCSeCqozPITDva',
            brotherFieldTemplateId: 'fldKTacCa0YyldICsmNm3bmT',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'reczVUgrNLcDjQfcvOLWf0G3',
          data: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-29T00:00:00.000Z',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 1',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-20T00:00:00.000Z',
            fldfRQ2XZP12Itf5iPV66PlJ: ['recbKPsRIoCdBxwmbecXywAI'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldwjBVp1qBGXoJ3osShLBvR: ['recnrF9GZRcG2kuGGaocuceS'],
            fldxR9v6LS2SzRA099cdV48H: ['optnoe6upYQekT31anYX5lX6'],
          },
          values: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-29',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 1',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-20',
            fldfRQ2XZP12Itf5iPV66PlJ: ['Campaign management'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldwjBVp1qBGXoJ3osShLBvR: ['Hermit'],
            fldxR9v6LS2SzRA099cdV48H: ['Working on it'],
          },
        },
        {
          templateId: 'rec9UhkMiUe8YEH0NlBwp58u',
          data: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-29T00:00:00.000Z',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 2',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-26T00:00:00.000Z',
            fldfRQ2XZP12Itf5iPV66PlJ: ['reclzXkaCrT8yGJmF2oyDjqh'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n' +
              '\n',
            fldwjBVp1qBGXoJ3osShLBvR: ['recQc6AIWkRpLVDZSby66RVA'],
            fldxR9v6LS2SzRA099cdV48H: ['opt7mGoFy17s43peL8dU5emd'],
          },
          values: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-29',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 2',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-26',
            fldfRQ2XZP12Itf5iPV66PlJ: ['Create a website'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\n' +
              '\n',
            fldwjBVp1qBGXoJ3osShLBvR: ['NIKO'],
            fldxR9v6LS2SzRA099cdV48H: ['Done'],
          },
        },
        {
          templateId: 'recy981QDjT14VqAbuDfjwsr',
          data: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-02T00:00:00.000Z',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 3',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-16T00:00:00.000Z',
            fldfRQ2XZP12Itf5iPV66PlJ: ['recfvyD0uJkSRgbWtTHASZr8', 'rec3yplsixzDt0L8KkMaDB8x'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldwjBVp1qBGXoJ3osShLBvR: ['recOkPFCxo03vtjngYii5tYO'],
            fldxR9v6LS2SzRA099cdV48H: ['opt7mGoFy17s43peL8dU5emd'],
          },
          values: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-02',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 3',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-16',
            fldfRQ2XZP12Itf5iPV66PlJ: ['Market analysis', 'Create a website'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldwjBVp1qBGXoJ3osShLBvR: ['zoe'],
            fldxR9v6LS2SzRA099cdV48H: ['Done'],
          },
        },
        {
          templateId: 'rec1sFX8JsnIjI9JUL8k4a5q',
          data: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-02T00:00:00.000Z',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 4',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-16T00:00:00.000Z',
            fldfRQ2XZP12Itf5iPV66PlJ: ['recGKGGbJXM14n3svnm6Z0Z1'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldwjBVp1qBGXoJ3osShLBvR: ['recxdWDsa5snNDQRGJuoEvLF'],
            fldxR9v6LS2SzRA099cdV48H: ['optnoe6upYQekT31anYX5lX6'],
          },
          values: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-02',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 4',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-16',
            fldfRQ2XZP12Itf5iPV66PlJ: ['Design logos'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldwjBVp1qBGXoJ3osShLBvR: ['Simon'],
            fldxR9v6LS2SzRA099cdV48H: ['Working on it'],
          },
        },
        {
          templateId: 'reccGz8aZ51wfW0g70sqt2d4',
          data: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-29T00:00:00.000Z',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 5',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-18T00:00:00.000Z',
            fldfRQ2XZP12Itf5iPV66PlJ: ['recVvfXJNr0wuJCJtl02OENx', 'recO8rEkwPoliCT6OEZ1nvIX'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldwjBVp1qBGXoJ3osShLBvR: ['recKqluRBe6RMlZ2MRDoJe9y'],
            fldxR9v6LS2SzRA099cdV48H: ['opt7mGoFy17s43peL8dU5emd'],
          },
          values: {
            fldIv5qmv8Hltv4mWVWUTWy5: '2024-04-29',
            fldWt4uc5VIJv2loeGbWsclH: 'Projects 5',
            fldaLcbEnWHXsgVNw0QzcUGS: '2024-03-18',
            fldfRQ2XZP12Itf5iPV66PlJ: ['Video production', 'Create a website'],
            fldjbAgp8jwOY6H9Hf15YliB:
              'Lorem ipsum dolor sit amet, consectetur adipisci elit, sed eiusmod tempor incidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrum exercitationem ullam corporis suscipit laboriosam, nisi ut aliquid ex ea commodi consequatur. Quis aute iure reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint obcaecat cupiditat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
            fldwjBVp1qBGXoJ3osShLBvR: ['Erik'],
            fldxR9v6LS2SzRA099cdV48H: ['Done'],
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datnTAXyoNNCSeCqozPITDva',
      name: 'Task of Projects',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viw7fetFvi3KAsnSLjZNCnTR',
          name: 'Customer Projects Task',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldvakN9KOo1bQbYIL2ewDRq',
              hidden: false,
            },
            {
              templateId: 'fldYWrOcJoLxy92FZJS2Go8Z',
              hidden: false,
            },
            {
              templateId: 'fldKTacCa0YyldICsmNm3bmT',
              hidden: false,
            },
            {
              templateId: 'fld3bg98TFS12EPo3cYc2h7X',
              hidden: false,
            },
            {
              templateId: 'fldfshntaQ4pugovtADF2uWY',
              hidden: false,
            },
            {
              templateId: 'fldEGRPVFlDTpSkp9kCREkTs',
              hidden: false,
            },
            {
              templateId: 'flde9MoK2XdxEOpxZEvPCeDW',
              hidden: false,
            },
            {
              templateId: 'flddj5uG2R8Qn6r5Z9NidYQB',
              hidden: false,
            },
            {
              templateId: 'fldY8cggeReCLHZ8vEKAq4gJ',
              hidden: false,
            },
            {
              templateId: 'fldWApDA1YyFFe00MWtJdCp6',
              hidden: false,
            },
          ],
          groups: [],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fldvakN9KOo1bQbYIL2ewDRq',
          privilege: 'TYPE_EDIT',
          name: 'Task',
          primary: true,
        },
        {
          type: 'MEMBER',
          templateId: 'fldYWrOcJoLxy92FZJS2Go8Z',
          privilege: 'NAME_EDIT',
          name: 'Owner',
          property: {},
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fld3bg98TFS12EPo3cYc2h7X',
          privilege: 'NAME_EDIT',
          name: 'Task - Start',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'DATETIME',
          templateId: 'fldfshntaQ4pugovtADF2uWY',
          privilege: 'NAME_EDIT',
          name: 'Task - End',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldEGRPVFlDTpSkp9kCREkTs',
          privilege: 'NAME_EDIT',
          name: 'Status',
          property: {
            options: [
              {
                id: 'opt3YkSSFtOl7XoyDiKLgpjJ',
                name: 'Working on it',
                color: 'deepPurple5',
              },
              {
                id: 'opt5RIY9lPf4sBAbzFTpqQUv',
                name: 'Done',
                color: 'indigo5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'NUMBER',
          templateId: 'flde9MoK2XdxEOpxZEvPCeDW',
          privilege: 'NAME_EDIT',
          name: 'Est. Hours',
          property: {
            precision: 0,
            commaStyle: '',
            symbol: 'h',
            symbolAlign: 'default',
          },
          primary: false,
        },
        {
          type: 'CURRENCY',
          templateId: 'flddj5uG2R8Qn6r5Z9NidYQB',
          privilege: 'NAME_EDIT',
          name: 'Client Cost',
          property: {
            precision: 2,
            commaStyle: 'thousand',
            symbol: '$',
            symbolAlign: 'left',
          },
          primary: false,
        },
        {
          type: 'CREATED_TIME',
          templateId: 'fldY8cggeReCLHZ8vEKAq4gJ',
          privilege: 'NAME_EDIT',
          name: 'Date Added',
          property: {
            dateFormat: 'YYYY-MM-DD',
            includeTime: false,
          },
          primary: false,
        },
        {
          type: 'ATTACHMENT',
          templateId: 'fldWApDA1YyFFe00MWtJdCp6',
          privilege: 'NAME_EDIT',
          name: 'Related Files',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldKTacCa0YyldICsmNm3bmT',
          privilege: 'NAME_EDIT',
          name: 'Projects',
          property: {
            foreignDatabaseTemplateId: 'datyGBvMfMABaprcDBB8Cjdg',
            brotherFieldTemplateId: 'fldfRQ2XZP12Itf5iPV66PlJ',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recbKPsRIoCdBxwmbecXywAI',
          data: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-20T00:00:00.000Z',
            fldEGRPVFlDTpSkp9kCREkTs: ['opt3YkSSFtOl7XoyDiKLgpjJ'],
            fldKTacCa0YyldICsmNm3bmT: ['reczVUgrNLcDjQfcvOLWf0G3'],
            fldY8cggeReCLHZ8vEKAq4gJ: null,
            flddj5uG2R8Qn6r5Z9NidYQB: 1100,
            flde9MoK2XdxEOpxZEvPCeDW: '35h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/19',
            fldvakN9KOo1bQbYIL2ewDRq: 'Campaign management',
          },
          values: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-20',
            fldEGRPVFlDTpSkp9kCREkTs: ['Working on it'],
            fldKTacCa0YyldICsmNm3bmT: ['Projects 1'],
            fldY8cggeReCLHZ8vEKAq4gJ: '2024-12-10',
            flddj5uG2R8Qn6r5Z9NidYQB: '$1100',
            flde9MoK2XdxEOpxZEvPCeDW: '35h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/19',
            fldvakN9KOo1bQbYIL2ewDRq: 'Campaign management',
          },
        },
        {
          templateId: 'reclzXkaCrT8yGJmF2oyDjqh',
          data: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-26T00:00:00.000Z',
            fldEGRPVFlDTpSkp9kCREkTs: ['opt5RIY9lPf4sBAbzFTpqQUv'],
            fldKTacCa0YyldICsmNm3bmT: ['rec9UhkMiUe8YEH0NlBwp58u'],
            fldY8cggeReCLHZ8vEKAq4gJ: null,
            flddj5uG2R8Qn6r5Z9NidYQB: 4460,
            flde9MoK2XdxEOpxZEvPCeDW: '45h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/29',
            fldvakN9KOo1bQbYIL2ewDRq: 'Create a website',
          },
          values: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-26',
            fldEGRPVFlDTpSkp9kCREkTs: ['Done'],
            fldKTacCa0YyldICsmNm3bmT: ['Projects 2'],
            fldY8cggeReCLHZ8vEKAq4gJ: '2024-12-10',
            flddj5uG2R8Qn6r5Z9NidYQB: '$4460',
            flde9MoK2XdxEOpxZEvPCeDW: '45h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/29',
            fldvakN9KOo1bQbYIL2ewDRq: 'Create a website',
          },
        },
        {
          templateId: 'recO8rEkwPoliCT6OEZ1nvIX',
          data: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-26T00:00:00.000Z',
            fldEGRPVFlDTpSkp9kCREkTs: ['opt5RIY9lPf4sBAbzFTpqQUv'],
            fldKTacCa0YyldICsmNm3bmT: ['reccGz8aZ51wfW0g70sqt2d4'],
            fldY8cggeReCLHZ8vEKAq4gJ: null,
            flddj5uG2R8Qn6r5Z9NidYQB: 4460,
            flde9MoK2XdxEOpxZEvPCeDW: '45h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/29',
            fldvakN9KOo1bQbYIL2ewDRq: 'Create a website',
          },
          values: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-26',
            fldEGRPVFlDTpSkp9kCREkTs: ['Done'],
            fldKTacCa0YyldICsmNm3bmT: ['Projects 5'],
            fldY8cggeReCLHZ8vEKAq4gJ: '2024-12-10',
            flddj5uG2R8Qn6r5Z9NidYQB: '$4460',
            flde9MoK2XdxEOpxZEvPCeDW: '45h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/29',
            fldvakN9KOo1bQbYIL2ewDRq: 'Create a website',
          },
        },
        {
          templateId: 'rec3yplsixzDt0L8KkMaDB8x',
          data: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-26T00:00:00.000Z',
            fldEGRPVFlDTpSkp9kCREkTs: ['opt5RIY9lPf4sBAbzFTpqQUv'],
            fldKTacCa0YyldICsmNm3bmT: ['recy981QDjT14VqAbuDfjwsr'],
            fldY8cggeReCLHZ8vEKAq4gJ: null,
            flddj5uG2R8Qn6r5Z9NidYQB: 4030,
            flde9MoK2XdxEOpxZEvPCeDW: '45h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/29',
            fldvakN9KOo1bQbYIL2ewDRq: 'Create a website',
          },
          values: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-26',
            fldEGRPVFlDTpSkp9kCREkTs: ['Done'],
            fldKTacCa0YyldICsmNm3bmT: ['Projects 3'],
            fldY8cggeReCLHZ8vEKAq4gJ: '2024-12-10',
            flddj5uG2R8Qn6r5Z9NidYQB: '$4030',
            flde9MoK2XdxEOpxZEvPCeDW: '45h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/29',
            fldvakN9KOo1bQbYIL2ewDRq: 'Create a website',
          },
        },
        {
          templateId: 'recGKGGbJXM14n3svnm6Z0Z1',
          data: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-04-15T00:00:00.000Z',
            fldEGRPVFlDTpSkp9kCREkTs: ['opt5RIY9lPf4sBAbzFTpqQUv'],
            fldKTacCa0YyldICsmNm3bmT: ['rec1sFX8JsnIjI9JUL8k4a5q'],
            fldY8cggeReCLHZ8vEKAq4gJ: null,
            flddj5uG2R8Qn6r5Z9NidYQB: 6650,
            flde9MoK2XdxEOpxZEvPCeDW: '70h',
            fldfshntaQ4pugovtADF2uWY: '2022/05/15',
            fldvakN9KOo1bQbYIL2ewDRq: 'Design logos',
          },
          values: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-04-15',
            fldEGRPVFlDTpSkp9kCREkTs: ['Done'],
            fldKTacCa0YyldICsmNm3bmT: ['Projects 4'],
            fldY8cggeReCLHZ8vEKAq4gJ: '2024-12-10',
            flddj5uG2R8Qn6r5Z9NidYQB: '$6650',
            flde9MoK2XdxEOpxZEvPCeDW: '70h',
            fldfshntaQ4pugovtADF2uWY: '2022/05/15',
            fldvakN9KOo1bQbYIL2ewDRq: 'Design logos',
          },
        },
        {
          templateId: 'recfvyD0uJkSRgbWtTHASZr8',
          data: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-16T00:00:00.000Z',
            fldEGRPVFlDTpSkp9kCREkTs: ['opt3YkSSFtOl7XoyDiKLgpjJ'],
            fldKTacCa0YyldICsmNm3bmT: ['recy981QDjT14VqAbuDfjwsr'],
            fldY8cggeReCLHZ8vEKAq4gJ: null,
            flddj5uG2R8Qn6r5Z9NidYQB: 6650,
            flde9MoK2XdxEOpxZEvPCeDW: '80h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/02',
            fldvakN9KOo1bQbYIL2ewDRq: 'Market analysis',
          },
          values: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-16',
            fldEGRPVFlDTpSkp9kCREkTs: ['Working on it'],
            fldKTacCa0YyldICsmNm3bmT: ['Projects 3'],
            fldY8cggeReCLHZ8vEKAq4gJ: '2024-12-10',
            flddj5uG2R8Qn6r5Z9NidYQB: '$6650',
            flde9MoK2XdxEOpxZEvPCeDW: '80h',
            fldfshntaQ4pugovtADF2uWY: '2022/04/02',
            fldvakN9KOo1bQbYIL2ewDRq: 'Market analysis',
          },
        },
        {
          templateId: 'recVvfXJNr0wuJCJtl02OENx',
          data: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-18T00:00:00.000Z',
            fldEGRPVFlDTpSkp9kCREkTs: ['opt5RIY9lPf4sBAbzFTpqQUv'],
            fldKTacCa0YyldICsmNm3bmT: ['reccGz8aZ51wfW0g70sqt2d4'],
            fldY8cggeReCLHZ8vEKAq4gJ: null,
            flddj5uG2R8Qn6r5Z9NidYQB: 4365,
            flde9MoK2XdxEOpxZEvPCeDW: '65h',
            fldfshntaQ4pugovtADF2uWY: '2022/03/31',
            fldvakN9KOo1bQbYIL2ewDRq: 'Video production',
          },
          values: {
            fld3bg98TFS12EPo3cYc2h7X: '2022-03-18',
            fldEGRPVFlDTpSkp9kCREkTs: ['Done'],
            fldKTacCa0YyldICsmNm3bmT: ['Projects 5'],
            fldY8cggeReCLHZ8vEKAq4gJ: '2024-12-10',
            flddj5uG2R8Qn6r5Z9NidYQB: '$4365',
            flde9MoK2XdxEOpxZEvPCeDW: '65h',
            fldfshntaQ4pugovtADF2uWY: '2022/03/31',
            fldvakN9KOo1bQbYIL2ewDRq: 'Video production',
          },
        },
      ],
    },
    {
      resourceType: 'DATABASE',
      templateId: 'datgpTnLfjTIbh98R1K50zk2',
      name: 'Contacts',
      databaseType: 'DATUM',
      views: [
        {
          type: 'TABLE',
          templateId: 'viw0w8Arh27Ef2qLg5C5qIjK',
          name: 'Client Directory',
          filters: {
            conjunction: 'And',
            conditions: [],
          },
          sorts: [],
          fields: [
            {
              templateId: 'fldHMyDq755JgWWheKL1rH2R',
            },
            {
              templateId: 'fldpPYx3nhbcjZTsiq6lTWk4',
            },
            {
              templateId: 'fldcdN9MAvngkbO1fGRGhFhR',
            },
            {
              templateId: 'fldijPD3ysf9p3To6rUQx6UK',
            },
            {
              templateId: 'fldjQF6PXHC0Hdz1fpuTHiqE',
            },
            {
              templateId: 'fldWNzGE58LLqMMVoJjEYNPv',
            },
            {
              templateId: 'fldqgR4B5RyZNQpEDIbMHSxO',
            },
            {
              templateId: 'fldIMHoBK3arVhMwjy9hosxP',
            },
          ],
        },
      ],
      fields: [
        {
          type: 'LONG_TEXT',
          templateId: 'fldHMyDq755JgWWheKL1rH2R',
          privilege: 'TYPE_EDIT',
          name: 'Contacts',
          primary: true,
        },
        {
          type: 'LONG_TEXT',
          templateId: 'fldpPYx3nhbcjZTsiq6lTWk4',
          privilege: 'NAME_EDIT',
          name: 'Company',
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldcdN9MAvngkbO1fGRGhFhR',
          privilege: 'NAME_EDIT',
          name: 'Title',
          property: {
            options: [
              {
                id: 'optIJuJpkerGjfoyvQ9oT8R1',
                name: 'CEO',
                color: 'deepPurple5',
              },
              {
                id: 'optdhnNfYkfjiT7orJBcKOmW',
                name: 'CFO',
                color: 'indigo5',
              },
              {
                id: 'optkXH0um0IyI6GZ9nlIna5d',
                name: 'COO',
                color: 'blue5',
              },
              {
                id: 'optMqzYpvyxaQ0xzkJMy8R3S',
                name: 'Salles Manager',
                color: 'teal5',
              },
              {
                id: 'opthr07lAVykkydm8PaS3FsL',
                name: 'PM',
                color: 'green5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'SINGLE_SELECT',
          templateId: 'fldijPD3ysf9p3To6rUQx6UK',
          privilege: 'NAME_EDIT',
          name: 'Type',
          property: {
            options: [
              {
                id: 'optcN3AilxCrk9UZmRSRhJu5',
                name: 'Enterprise',
                color: 'deepPurple5',
              },
              {
                id: 'optxl9njsRh44OObPQK2Ah0N',
                name: 'Small business',
                color: 'indigo5',
              },
              {
                id: 'optvSJ1UsulscLcQPyvxppvF',
                name: 'Medium business',
                color: 'blue5',
              },
            ],
            defaultValue: '',
          },
          primary: false,
        },
        {
          type: 'PHONE',
          templateId: 'fldjQF6PXHC0Hdz1fpuTHiqE',
          privilege: 'NAME_EDIT',
          name: 'Phone',
          primary: false,
        },
        {
          type: 'EMAIL',
          templateId: 'fldWNzGE58LLqMMVoJjEYNPv',
          privilege: 'NAME_EDIT',
          name: 'Email',
          primary: false,
        },
        {
          type: 'URL',
          templateId: 'fldqgR4B5RyZNQpEDIbMHSxO',
          privilege: 'NAME_EDIT',
          name: 'Website',
          primary: false,
        },
        {
          type: 'LINK',
          templateId: 'fldIMHoBK3arVhMwjy9hosxP',
          privilege: 'NAME_EDIT',
          name: 'Customer Projects',
          property: {
            foreignDatabaseTemplateId: 'datyGBvMfMABaprcDBB8Cjdg',
            brotherFieldTemplateId: 'fldwjBVp1qBGXoJ3osShLBvR',
          },
          primary: false,
        },
      ],
      records: [
        {
          templateId: 'recnrF9GZRcG2kuGGaocuceS',
          data: {
            fldHMyDq755JgWWheKL1rH2R: 'Hermit',
            fldIMHoBK3arVhMwjy9hosxP: ['reczVUgrNLcDjQfcvOLWf0G3'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['optIJuJpkerGjfoyvQ9oT8R1'],
            fldijPD3ysf9p3To6rUQx6UK: ['optcN3AilxCrk9UZmRSRhJu5'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '12025550135',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company1',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
          values: {
            fldHMyDq755JgWWheKL1rH2R: 'Hermit',
            fldIMHoBK3arVhMwjy9hosxP: ['Projects 1'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['CEO'],
            fldijPD3ysf9p3To6rUQx6UK: ['Enterprise'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '12025550135',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company1',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
        },
        {
          templateId: 'recQc6AIWkRpLVDZSby66RVA',
          data: {
            fldHMyDq755JgWWheKL1rH2R: 'NIKO',
            fldIMHoBK3arVhMwjy9hosxP: ['rec9UhkMiUe8YEH0NlBwp58u'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['optdhnNfYkfjiT7orJBcKOmW'],
            fldijPD3ysf9p3To6rUQx6UK: ['optcN3AilxCrk9UZmRSRhJu5'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '12025550134',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company2',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
          values: {
            fldHMyDq755JgWWheKL1rH2R: 'NIKO',
            fldIMHoBK3arVhMwjy9hosxP: ['Projects 2'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['CFO'],
            fldijPD3ysf9p3To6rUQx6UK: ['Enterprise'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '12025550134',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company2',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
        },
        {
          templateId: 'recOkPFCxo03vtjngYii5tYO',
          data: {
            fldHMyDq755JgWWheKL1rH2R: 'zoe',
            fldIMHoBK3arVhMwjy9hosxP: ['recy981QDjT14VqAbuDfjwsr'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['optkXH0um0IyI6GZ9nlIna5d'],
            fldijPD3ysf9p3To6rUQx6UK: ['optxl9njsRh44OObPQK2Ah0N'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '***********',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company3',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
          values: {
            fldHMyDq755JgWWheKL1rH2R: 'zoe',
            fldIMHoBK3arVhMwjy9hosxP: ['Projects 3'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['COO'],
            fldijPD3ysf9p3To6rUQx6UK: ['Small business'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '***********',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company3',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
        },
        {
          templateId: 'recxdWDsa5snNDQRGJuoEvLF',
          data: {
            fldHMyDq755JgWWheKL1rH2R: 'Simon',
            fldIMHoBK3arVhMwjy9hosxP: ['rec1sFX8JsnIjI9JUL8k4a5q'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['optMqzYpvyxaQ0xzkJMy8R3S'],
            fldijPD3ysf9p3To6rUQx6UK: ['optvSJ1UsulscLcQPyvxppvF'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '***********',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company4',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
          values: {
            fldHMyDq755JgWWheKL1rH2R: 'Simon',
            fldIMHoBK3arVhMwjy9hosxP: ['Projects 4'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['Salles Manager'],
            fldijPD3ysf9p3To6rUQx6UK: ['Medium business'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '***********',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company4',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
        },
        {
          templateId: 'recKqluRBe6RMlZ2MRDoJe9y',
          data: {
            fldHMyDq755JgWWheKL1rH2R: 'Erik',
            fldIMHoBK3arVhMwjy9hosxP: ['reccGz8aZ51wfW0g70sqt2d4'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['opthr07lAVykkydm8PaS3FsL'],
            fldijPD3ysf9p3To6rUQx6UK: ['optcN3AilxCrk9UZmRSRhJu5'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '***********',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company5',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
          values: {
            fldHMyDq755JgWWheKL1rH2R: 'Erik',
            fldIMHoBK3arVhMwjy9hosxP: ['Projects 5'],
            fldWNzGE58LLqMMVoJjEYNPv: '<EMAIL>',
            fldcdN9MAvngkbO1fGRGhFhR: ['PM'],
            fldijPD3ysf9p3To6rUQx6UK: ['Enterprise'],
            fldjQF6PXHC0Hdz1fpuTHiqE: '***********',
            fldpPYx3nhbcjZTsiq6lTWk4: 'Company5',
            fldqgR4B5RyZNQpEDIbMHSxO: 'bika.ai',
          },
        },
      ],
    },
  ],
  initMissions: [],
};

export default template;
