{"templateId": "ai-batch-image-recognition", "name": {"en": "AI Batch Image Recognition(OpenAI gpt-4o)", "zh-CN": "AI批量图片识别（DeepSeek-vl2）"}, "description": {"en": "Bika.ai uses OpenAI gpt-4o for image recognition. When an image is uploaded to the table, the automation is triggered to send the data to OpenAI for recognition, and update the information to the \"Image Text Content\" column.", "zh-CN": "Bika.ai 利用 DeepSeek-vl2 模型进行图像识别。当图片上传到表格时，触发自动化将数据发送给 DeepSeek ，识别图片，并将信息更新到“图片文字内容”列。"}, "cover": "/assets/template/template-cover-ai-batch-image-recognition.png", "author": "Na<PERSON>a<PERSON>on <<EMAIL>>", "category": ["ai"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.0.9", "resources": [{"resourceType": "AUTOMATION", "templateId": "atoVQAr3V2GZ4ZsSpXnW9mfl", "name": {"en": "Automated Batch Image Recognition", "zh-CN": "批量图片识别自动化"}, "triggers": [{"triggerType": "MANUALLY", "templateId": "trgCzH7pnYABnnG1N4IPJM7w", "description": {"en": "Manually trigger automation", "zh-CN": "手动触发自动化"}, "input": {"fields": []}}], "actions": [{"templateId": "actpfHCZVt9DJHdWy5ODbiC3", "description": {"en": "Find pictures to be processed", "zh-CN": "查找待处理的图片"}, "actionType": "FIND_RECORDS", "input": {"interruptIfNoRecord": true, "type": "DATABASE_VIEW", "viewTemplateId": "viwf0K1LhghYtM3YskpchnDo", "databaseTemplateId": "datJO9HTWbLyXrVymwuC5CJL"}}, {"templateId": "actPvXR6RQQ7ghg9KfF9LBX6", "description": {"en": "Extract text from each picture", "zh-CN": "提取每张图片的文字"}, "actionType": "LOOP", "input": {"type": "LOOP", "actionTemplateId": "actpfHCZVt9DJHdWy5ODbiC3", "path": "records"}, "actions": [{"description": {"en": "Get picture address", "zh-CN": "获取图片地址"}, "actionType": "RUN_SCRIPT", "templateId": "act48FwgVLMhhbUAfyhifi64", "input": {"type": "SCRIPT", "language": "python", "script": "fileData = <%= JSON.stringify(_item.cells.fldT4eQS6udhC5T3AJFPE2n9.data) %>\nIMAGE_MIME_TYPES = {'image/jpeg', 'image/png', 'image/webp'}\n\nif len(fileData) == 1:\n    file_info = fileData[0]\n    mime_type = file_info.get(\"mimeType\", \"\").lower()\n    \n    if (mime_type in IMAGE_MIME_TYPES):\n        fileURL = \"https://s1.bika.ai/\" + file_info[\"path\"]\n    else:\n        fileURL = \"File is not a supported image format: \" + mime_type\nelse: \n    fileURL = \"Expected exactly 1 file, got \" + str(len(fileData))"}}, {"description": {"en": "Identify the text in the picture through AI", "zh-CN": "通过 AI 识别图片文本"}, "actionType": "WEBHOOK", "templateId": "actw4awWjGg0nY13LaG32P5N", "input": {"type": "WEBHOOK", "method": "POST", "headers": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer _YOUR_API_KEY_"}], "url": "", "body": {"type": "raw", "format": "json", "data": {"model": "_MODEL_NAME_", "messages": [{"role": "user", "content": [{"text": "Please help me extract the text in the picture. Only reply with the text information you extracted. Do not reply with other content.", "type": "text"}, {"type": "image_url", "image_url": {"url": "<%= _itemActions.act48FwgVLMhhbUAfyhifi64.fileURL %>"}}]}]}}, "timeout": 120}}, {"description": {"en": "Update the recognition result to the table", "zh-CN": "将识别结果更新到表内"}, "actionType": "UPDATE_RECORD", "templateId": "actvDkq3E3sz5Yc6uReBQB9c", "input": {"type": "SPECIFY_RECORD_BODY", "recordId": "<%= _item.id %>", "fieldKeyType": "ID", "data": {"fldH5IPkCYjFmPb3qb7znOxz": "<%= _itemActions.actw4awWjGg0nY13LaG32P5N.body.choices[0].message.content %>", "fld2SbBKfXf6qGNKjPSBXuwu": ["optuOI98XRxOO"]}, "databaseTemplateId": "datJO9HTWbLyXrVymwuC5CJL"}}]}]}, {"resourceType": "DATABASE", "templateId": "datJO9HTWbLyXrVymwuC5CJL", "name": {"en": "AI Image Recognition Database", "zh-CN": "AI图片识别数据库"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwdb2qePyoyl67PcljpCDww", "name": {"en": "Database", "zh-CN": "数据库"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "fldwShKlxmxGcuDFJUvKkaZm", "hidden": false}, {"templateId": "fldT4eQS6udhC5T3AJFPE2n9", "hidden": false}, {"templateId": "fldH5IPkCYjFmPb3qb7znOxz", "hidden": false, "width": 356}, {"templateId": "fld2SbBKfXf6qGNKjPSBXuwu", "hidden": false}]}, {"type": "TABLE", "templateId": "viwf0K1LhghYtM3YskpchnDo", "name": {"en": "Images to be processed", "zh-CN": "待处理的图片"}, "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "fld2SbBKfXf6qGNKjPSBXuwu", "fieldType": "SINGLE_SELECT", "clause": {"operator": "Is", "value": "optizVkcNHFll"}}]}, "sorts": [], "fields": [{"templateId": "fldwShKlxmxGcuDFJUvKkaZm", "hidden": false}, {"templateId": "fldT4eQS6udhC5T3AJFPE2n9", "hidden": false}, {"templateId": "fldH5IPkCYjFmPb3qb7znOxz", "hidden": false, "width": 354}, {"templateId": "fld2SbBKfXf6qGNKjPSBXuwu", "hidden": false}], "groups": []}], "fields": [{"type": "SINGLE_TEXT", "templateId": "fldwShKlxmxGcuDFJUvKkaZm", "privilege": "TYPE_EDIT", "name": {"en": "Title", "zh-CN": "标题"}, "required": false, "primary": true}, {"type": "ATTACHMENT", "templateId": "fldT4eQS6udhC5T3AJFPE2n9", "privilege": "NAME_EDIT", "name": {"en": "Upload Image", "zh-CN": "上传图片"}, "required": true, "primary": false}, {"type": "LONG_TEXT", "templateId": "fldH5IPkCYjFmPb3qb7znOxz", "privilege": "NAME_EDIT", "name": {"en": "Image Text Content", "zh-CN": "图片文字内容"}, "primary": false}, {"type": "SINGLE_SELECT", "templateId": "fld2SbBKfXf6qGNKjPSBXuwu", "privilege": "NAME_EDIT", "name": {"en": "Trigger Automation", "zh-CN": "触发自动化"}, "property": {"options": [{"id": "optizVkcNHFll", "name": "To be identified", "color": "orange5"}, {"id": "optuOI98XRxOO", "name": "Identification completed", "color": "teal5"}], "defaultValue": "optizVkcNHFll"}, "primary": false}], "records": [{"templateId": "recJYIPmzoLJDka888o3UlAx", "data": {"fld2SbBKfXf6qGNKjPSBXuwu": ["optuOI98XRxOO"], "fldH5IPkCYjFmPb3qb7znOxz": "English: Hello, what can I do for you? Français: <PERSON><PERSON><PERSON>, que puis-je faire pour vous? Deutsch: <PERSON><PERSON>, was kann ich für Sie tun? Español: <PERSON><PERSON>, ¿en qué puedo ayudarte? Русский язык: Здравствуйте, чем я могу вам помочь? 日本語: こんにちは、何かお手伝いできますか? 한국어: 안녕하세요, پایvoir 도와드리겠어요? 简体中文: 你好，请问我能帮你做什么", "fldT4eQS6udhC5T3AJFPE2n9": [{"id": "tplattqWJQOOQVnYO3auNIl3ada", "name": "SmapleImage6.png", "path": "template/tplattqWJQOOQVnYO3auNIl3ada.png", "bucket": "bika-dev", "mimeType": "image/png", "size": 815800}], "fldwShKlxmxGcuDFJUvKkaZm": "Images containing texts in multiple languages"}, "values": {"fld2SbBKfXf6qGNKjPSBXuwu": ["Identification completed"], "fldH5IPkCYjFmPb3qb7znOxz": "English: Hello, what can I do for you? Français: <PERSON><PERSON><PERSON>, que puis-je faire pour vous? Deutsch: <PERSON><PERSON>, was kann ich für Sie tun? Español: <PERSON><PERSON>, ¿en qué puedo ayudarte? Русский язык: Здравствуйте, чем я могу вам помочь? 日本語: こんにちは、何かお手伝いできますか? 한국어: 안녕하세요, پایvoir 도와드리겠어요? 简体中文: 你好，请问我能帮你做什么", "fldT4eQS6udhC5T3AJFPE2n9": ["SmapleImage6.png"], "fldwShKlxmxGcuDFJUvKkaZm": "Images containing texts in multiple languages"}}, {"templateId": "recktlyWqTUUZUVhe0kklem5", "data": {"fld2SbBKfXf6qGNKjPSBXuwu": ["optuOI98XRxOO"], "fldH5IPkCYjFmPb3qb7znOxz": "MERRY CHRISTMAS  \nand  \nhappy new year", "fldT4eQS6udhC5T3AJFPE2n9": [{"id": "tplattwEBu6HCjGyIG3DT8yIXqL", "name": "istockphoto-1187101992-612x612.jpg", "path": "template/tplattwEBu6HCjGyIG3DT8yIXqL.jpeg", "bucket": "bika-dev", "mimeType": "image/jpeg", "size": 39320}], "fldwShKlxmxGcuDFJUvKkaZm": "Images with complex backgrounds"}, "values": {"fld2SbBKfXf6qGNKjPSBXuwu": ["Identification completed"], "fldH5IPkCYjFmPb3qb7znOxz": "MERRY CHRISTMAS  \nand  \nhappy new year", "fldT4eQS6udhC5T3AJFPE2n9": ["istockphoto-1187101992-612x612.jpg"], "fldwShKlxmxGcuDFJUvKkaZm": "Images with complex backgrounds"}}, {"templateId": "recLJHUWVaQQz1o65a562kxU", "data": {"fld2SbBKfXf6qGNKjPSBXuwu": ["optuOI98XRxOO"], "fldH5IPkCYjFmPb3qb7znOxz": "SAMPLE", "fldT4eQS6udhC5T3AJFPE2n9": [{"id": "tplattQJy9QeSvIrocMViPcV8F3", "name": "Sample.png", "path": "template/tplattQJy9QeSvIrocMViPcV8F3.png", "bucket": "bika-dev", "mimeType": "image/png", "size": 1009295}], "fldwShKlxmxGcuDFJUvKkaZm": "Example images"}, "values": {"fld2SbBKfXf6qGNKjPSBXuwu": ["Identification completed"], "fldH5IPkCYjFmPb3qb7znOxz": "SAMPLE", "fldT4eQS6udhC5T3AJFPE2n9": ["Sample.png"], "fldwShKlxmxGcuDFJUvKkaZm": "Example images"}}, {"templateId": "recaQX3AdnpL4jxzHLZfHlCw", "data": {"fld2SbBKfXf6qGNKjPSBXuwu": ["optuOI98XRxOO"], "fldH5IPkCYjFmPb3qb7znOxz": "What is Bika.ai?\n\n- AI Organizer for Building Agentic AI Teams\n\nBika.ai is an AI automation database platform that proactively helps everyone complete work, process reports, and data collection daily.\n\nFree up your work time, escape the hassle of tedious tasks, and truly enjoy more time living your life.", "fldT4eQS6udhC5T3AJFPE2n9": [{"id": "tplattsfPrxyBB83P5O1hEpXePx", "name": "SampleImage5.png", "path": "template/tplattsfPrxyBB83P5O1hEpXePx.png", "bucket": "bika-dev", "mimeType": "image/png", "size": 1795814}], "fldwShKlxmxGcuDFJUvKkaZm": "Images with complex backgrounds"}, "values": {"fld2SbBKfXf6qGNKjPSBXuwu": ["Identification completed"], "fldH5IPkCYjFmPb3qb7znOxz": "What is Bika.ai?\n\n- AI Organizer for Building Agentic AI Teams\n\nBika.ai is an AI automation database platform that proactively helps everyone complete work, process reports, and data collection daily.\n\nFree up your work time, escape the hassle of tedious tasks, and truly enjoy more time living your life.", "fldT4eQS6udhC5T3AJFPE2n9": ["SampleImage5.png"], "fldwShKlxmxGcuDFJUvKkaZm": "Images with complex backgrounds"}}, {"templateId": "rec45APowVr4z1D1vJK5dJhY", "data": {"fld2SbBKfXf6qGNKjPSBXuwu": ["optuOI98XRxOO"], "fldH5IPkCYjFmPb3qb7znOxz": "Bika.ai\n\nAutomate Your Work with Business AI Agents\n\nMarketing Automation • Lead Management • AI Reporting\nSend Email in Bulk • Social Media Automation\n\nGet Stared\nIt's FREE\n\nBika.ai", "fldT4eQS6udhC5T3AJFPE2n9": [{"id": "tplatti9JeOnVnP0nStoyo9CTmL", "name": "SampleImage4.png", "path": "template/tplatti9JeOnVnP0nStoyo9CTmL.png", "bucket": "bika-dev", "mimeType": "image/png", "size": 293446}], "fldwShKlxmxGcuDFJUvKkaZm": "Example images"}, "values": {"fld2SbBKfXf6qGNKjPSBXuwu": ["Identification completed"], "fldH5IPkCYjFmPb3qb7znOxz": "Bika.ai\n\nAutomate Your Work with Business AI Agents\n\nMarketing Automation • Lead Management • AI Reporting\nSend Email in Bulk • Social Media Automation\n\nGet Stared\nIt's FREE\n\nBika.ai", "fldT4eQS6udhC5T3AJFPE2n9": ["SampleImage4.png"], "fldwShKlxmxGcuDFJUvKkaZm": "Example images"}}]}], "initMissions": []}