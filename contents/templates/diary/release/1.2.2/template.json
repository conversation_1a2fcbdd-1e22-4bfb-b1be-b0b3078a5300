{"templateId": "diary", "name": {"zh-CN": "填写日记提醒", "en": "Diary reminder", "zh-TW": "填寫日記提醒", "ja": "日記記入リマインダー"}, "description": {"en": "A simple diary template that helps you record your thoughts and feelings every day. It includes a reminder to fill in your diary every night and a weekly summary report of your diary entries.", "ja": "毎日の思考や感情を記録するのに役立つシンプルな日記テンプレートです。毎晩日記を記入するリマインダーと、週間の日記エントリのサマリーレポートが含まれています。", "zh-CN": "一个简单的日记模板，帮助你每天记录你的想法和感受。它包括每晚填写日记的提醒和每周日记条目的汇总报告。", "zh-TW": "一個簡單的日記模板，幫助您每天記錄您的想法和感受。它包括每晚填寫日記的提醒和每週日記條目的彙總報告。"}, "cover": "/assets/template/template-cover-diary.png", "author": "zhanpeiwei <<EMAIL>>", "category": ["daily-life", "automation"], "detach": false, "visibility": "PUBLIC", "schemaVersion": "v1", "version": "1.2.2", "keywords": "Diary reminder, Daily, Weekly summary", "personas": {"zh-CN": "学生, 自我管理者, 心理健康患者, 创作者", "en": "Students, Self-managers, Mental health patients, Creators", "zh-TW": "學生, 自我管理者, 心理健康患者, 創作者", "ja": "学生, 自己管理者, メンタルヘルス患者, 創作者"}, "useCases": {"zh-CN": "学习进度记录, 心情日志, 思维整理, 目标设定, 读书笔记, 每日总结, 时间管理, 个人目标追踪, 自我反思, 习惯养成, 日常计划, 每周回顾, 情绪管理, 治疗进展, 情绪跟踪, 自我反省, 心理疗法记录, 日常心情追踪, 创作灵感, 写作进度, 角色发展, 故事情节, 段落构思, 写作灵感捕捉", "zh-TW": "學習進度記錄, 心情日誌, 思維整理, 目標設定, 讀書筆記, 每日總結, 時間管理, 個人目標追蹤, 自我反思, 習慣養成, 日常計劃, 每週回顧, 情緒管理, 治療進展, 情緒追蹤, 自我反省, 心理療法記錄, 日常心情追蹤, 創作靈感, 寫作進度, 角色發展, 故事情節, 段落構思, 寫作靈感捕捉", "en": "Learning progress tracking, Mood diary, Thought organization, Goal setting, Reading notes, Daily summary, Time management, Personal goal tracking, Self-reflection, Habit formation, Daily planning, Weekly review, Emotion management, Therapy progress, Mood tracking, Self-examination, Psychotherapy record, Daily mood tracking, Creative inspiration, Writing progress, Character development, Story plot, Paragraph ideation, Writing inspiration capture", "ja": "学習進捗の追跡, 気分日記, 思考の整理, 目標設定, 読書ノート, 日次サマリー, 時間管理, 個人目標の追跡, 自己反省, 習慣形成, 日常計画, 週次レビュー, 感情管理, セラピーの進捗, 気分追跡, 自己検討, 心理療法記録, 日常の気分追跡, 創造的なインスピレーション, 書き込みの進捗, キャラクターの発展, ストーリープロット, 段落の構想, 書き込みのインスピレーションの捕捉"}, "initMissions": [{"name": {"zh-CN": "填写日记提醒模板使用须知", "en": "Read the guide before using the diary reminder template", "zh-TW": "填寫日記提醒模板使用須知", "ja": "日記リマインダーテンプレートを使用する前にガイドを読んでください"}, "type": "READ_TEMPLATE_README", "templateId": "diary", "time": 10, "beforeText": {"title": {"zh-CN": "恭喜您，模板已经安装完毕", "zh-TW": "恭喜您，模板已經安裝完畢", "ja": "おめでとうございます、テンプレートのインストールが完了しました", "en": "Congratulations, the template has been installed"}, "description": {"zh-CN": "在开始使用之前，请阅读以下使用须知，以便更好地了解如何使用此模板", "zh-TW": "在開始使用之前，請閱讀以下使用須知，以便更好地了解如何使用此模板", "ja": "使用を開始する前に、以下の使用方法をお読みいただき、このテンプレートの使用方法をよりよく理解してください", "en": "Before you start using it, please read the following instructions to better understand how to use this template"}}, "assignType": "DEDICATED", "forcePopup": true, "wizardGuideId": "COMMON_MY_TODO_TUTORIAL", "redirect": {"type": "MY_MISSIONS"}, "to": [{"type": "CURRENT_OPERATOR"}]}, {"name": {"zh-CN": "💡填写日记提醒模板序列初始化任务 #1：测试日记填写提醒", "en": "💡Diary Reminder Template Sequence Initialization Mission #1: Test Diary Reminder", "zh-TW": "💡填寫日記提醒模板序列初始化任務 #1：測試日記填寫提醒", "ja": "💡日記リマインダーテンプレートシーケンス初期化ミッション #1：日記リマインダーのテスト"}, "description": {"zh-CN": "任务描述：\n\n测试自动化是否可以正常发起提醒\n\n任务步骤：\n1. 进入 ‘填写日记提醒’ 自动化节点\n2. 点击‘手动触发’，你应该将收到一个去填写今日日记的提醒", "en": "Task Description:\n\nTest if the automation can initiate the reminder correctly\n\nTask Steps:\n1. Enter the 'Diary Reminder' automation node\n2. Click 'Manual Trigger', you should receive a reminder to fill in today's diary", "zh-TW": "任務描述：\n\n測試自動化是否可以正常發起提醒\n\n任務步驟：\n1. 進入 ‘填寫日記提醒’ 自動化節點\n2. 點擊‘手動觸發’，你應該將收到一個去填寫今日日記的提醒", "ja": "タスク説明：\n\n自動化が正常にリマインダーを発行できるかどうかをテストします\n\nタスク手順：\n1. '日記リマインダー' 自動化ノードに入る\n2. '手動トリガー' をクリックすると、今日の日記を記入するためのリマインダーを受け取るはずです"}, "type": "REDIRECT_SPACE_NODE", "nodeTemplateId": "diary_reminder", "buttonText": {"zh-CN": "查看自动化", "en": "View Automation", "zh-TW": "查看自動化", "ja": "自動化を表示"}, "assignType": "SHARE", "to": [{"type": "ADMIN"}], "canCompleteManually": true}, {"name": {"zh-CN": "💡填写日记提醒模板序列初始化任务 #2：测试每周日记总结汇报", "en": "💡Diary Reminder Template Sequence Initialization Mission #2: Test Weekly Diary Summary Report", "zh-TW": "💡填寫日記提醒模板序列初始化任務 #2：測試每週日記總結報告", "ja": "💡日記リマインダーテンプレートシーケンス初期化ミッション #2：毎週の日記サマリーレポートのテスト"}, "description": {"zh-CN": "任务描述：\n\n测试自动化是否可以正常发起每周的日记填写情况汇总报告，为了便于初次测试，模板初始设置的是当日的汇报\n\n操作步骤：\n1. 进入 ‘日记汇总报告’ 自动化节点\n2. 点击页面底部的‘手动触发’，你应该将收到一封今天日记填写的汇总报告\n3. 在页面左上角目录查看报告，确认内容是否正确\n4. 回到自动化，点击'查找上周的日记记录'的步骤，将筛选条件的'今天'改为‘上周’", "en": "Task Description:\n\nTest if the automation can initiate a weekly summary report of diary entries correctly. For the convenience of the initial test, the template is initially set to report on the current day\n\nTask Steps:\n1. Enter the 'Diary Summary Report' automation node\n2. Click 'Manual Trigger' at the bottom of the page, you should receive a summary report of today's diary entries\n3. Check the report in the directory at the top left of the page to confirm the content is correct\n4. Go back to the automation, click on the step 'Find diary records from the previous week', and change the filter condition 'Today' to 'Last week'", "zh-TW": "任務描述：\n\n測試自動化是否可以正常發起每週的日記填寫情況彙總報告，為了便於初次測試，模板初始設置的是當日的彙報\n\n操作步驟：\n1. 進入 ‘日記彙總報告’ 自動化節點\n2. 點擊頁面底部的‘手動觸發’，你應該將收到一封今天日記填寫的彙總報告\n3. 在頁面左上角目錄查看報告，確認內容是否正確\n4. 回到自動化，點擊'查找上週的日記記錄'的步驟，將篩選條件的'今天'改為‘上週’", "ja": "タスク説明：\n\n自動化が正常に毎週の日記エントリのサマリーレポートを発行できるかどうかをテストします。初回テストのため、テンプレートは初期設定で当日の報告を行います\n\n操作手順：\n1. '日記サマリーレポート' 自動化ノードに入る\n2. ページ下部の '手動トリガー' をクリックすると、今日の日記エントリのサマリーレポートを受け取るはずです\n3. ページ左上のディレクトリでレポートを確認して、内容が正しいかどうかを確認します\n4. 自動化に戻り、ステップ '先週の日記レコードを検索' をクリックし、フィルタ条件 '今日' を '先週'\nに変更します"}, "type": "REDIRECT_SPACE_NODE", "nodeTemplateId": "diary_summary_report", "buttonText": {"zh-CN": "查看自动化", "en": "View Automation", "zh-TW": "查看自動化", "ja": "自動化を表示"}, "assignType": "SHARE", "to": [{"type": "ADMIN"}], "canCompleteManually": true}], "resources": [{"resourceType": "DATABASE", "templateId": "diary_database", "name": {"en": "Diary Database", "ja": "日記データベース", "zh-CN": "日记数据库", "zh-TW": "日記數據庫"}, "description": {"en": "A database to store diary entries", "ja": "日記エントリを保存するデータベース", "zh-CN": "存储日记条目的数据库", "zh-TW": "存儲日記條目的數據庫"}, "databaseType": "DATUM", "views": [{"type": "TABLE", "templateId": "viwrD6Xy99B02uQayT2TzPXI", "name": {"en": "Record View", "ja": "記録ビュー (きろくびゅー)", "zh-CN": "记录视图", "zh-TW": "記錄視圖"}, "filters": {"conjunction": "And", "conditions": []}, "sorts": [], "fields": [{"templateId": "diary_content", "hidden": false, "width": 186}, {"templateId": "diary_date", "hidden": false}]}], "fields": [{"type": "SINGLE_TEXT", "templateId": "diary_content", "privilege": "TYPE_EDIT", "name": {"en": "Diary Content", "ja": "日記内容", "zh-CN": "日记内容", "zh-TW": "日記內容"}, "description": {"en": "The content of the diary entry", "ja": "日記の内容", "zh-CN": "日记的内容", "zh-TW": "日記的內容"}, "primary": true}, {"type": "DATETIME", "templateId": "diary_date", "privilege": "NAME_EDIT", "name": {"en": "Diary Date", "ja": "日記日付", "zh-CN": "日记日期", "zh-TW": "日記日期"}, "description": {"en": "The date of the diary entry", "ja": "日記の日付", "zh-CN": "日记的日期", "zh-TW": "日記的日期"}, "property": {"dateFormat": "YYYY/MM/DD", "includeTime": false}, "primary": false}], "records": [{"templateId": "reciX5q9JkQingryHTVsQb20", "data": {"diary_date": "2025-01-13T00:00:00.000Z", "diary_content": "Today is a very good day!"}, "values": {"diary_date": "2025/01/13", "diary_content": "Today is a very good day!"}}, {"templateId": "recJiXgEZ77t3ogeivGh70h3", "data": {"diary_date": "2025-01-14T00:00:00.000Z", "diary_content": "Today is a good day!"}, "values": {"diary_date": "2025/01/14", "diary_content": "Today is a good day!"}}, {"templateId": "recHWQQScNphKicy27c7oGu9", "data": {"diary_date": "2025-01-20T00:00:00.000Z", "diary_content": "Today is very busy!"}, "values": {"diary_date": "2025/01/20", "diary_content": "Today is very busy!"}}, {"templateId": "recotexJtsYZG0iNzUz0cbsR", "data": {"diary_date": "2025-01-21T00:00:00.000Z", "diary_content": "I feel happy today!"}, "values": {"diary_date": "2025/01/21", "diary_content": "I feel happy today!"}}, {"templateId": "recADbfDigmb6JnjtOeOPRFE", "data": {"diary_date": "2025-01-22T00:00:00.000Z", "diary_content": "I feel sad today!"}, "values": {"diary_date": "2025/01/22", "diary_content": "I feel sad today!"}}]}, {"resourceType": "AUTOMATION", "templateId": "diary_reminder", "name": {"en": "Diary Filling <PERSON>minder", "ja": "日記記入リマインダー", "zh-CN": "填写日记提醒", "zh-TW": "填寫日記提醒"}, "description": {"en": "Remind you to fill in their diaries every night at 9:00 PM", "ja": "毎晩9時にユーザーに日記の記入を促す", "zh-CN": "每晚 9 点提醒用户填写日记", "zh-TW": "每晚 9 點提醒用戶填寫日記"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trg1w0HSaFD14ACIkqGA0sSr", "description": {"en": "Every day at 9:00 PM", "ja": "毎日午後9時", "zh-CN": "每天晚上9点", "zh-TW": "每天晚上9點"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "DAY", "interval": 1}}, "timezone": "AUTO", "datetime": "2024-01-01T21:00:00Z"}}}], "actions": [{"templateId": "create_task", "description": {"en": "Create a task to remind users to fill in their diaries", "ja": "ユーザーに日記の記入を促すタスクを作成します", "zh-CN": "创建提醒用户填写日记的任务", "zh-TW": "創建提醒用戶填寫日記的任務"}, "actionType": "CREATE_MISSION", "input": {"type": "MISSION_BODY", "mission": {"type": "CREATE_RECORD", "name": "Don't forget to write your diary today!", "description": {"en": "Fill in today’s diary and record your thoughts and feelings.", "ja": "今日の日記を記入し、思考や感情を記録してください。", "zh-CN": "填写今天的日记，记录你的想法和感受。", "zh-TW": "填寫今天的日記，記錄你的想法和感受。"}, "canCompleteManually": true, "forcePopup": true, "to": [{"type": "CURRENT_OPERATOR"}], "databaseTemplateId": "diary_database"}}}]}, {"resourceType": "AUTOMATION", "templateId": "diary_summary_report", "name": {"en": "Diary Summary Report", "ja": "日記サマリーレポート", "zh-CN": "日记汇总报告", "zh-TW": "日記彙總報告"}, "description": {"en": "Send you a summary report of diary entries every Monday.", "ja": "毎週月曜日にあなたに日記のエントリーの要約レポートを送ります。", "zh-CN": "每周一向你发送日记条目的汇总报告", "zh-TW": "每週一向你發送日記條目的彙總報告。"}, "triggers": [{"triggerType": "SCHEDULER", "templateId": "trgjYzV6l9sIrXCSh7Owebab", "description": {"en": "Every Monday at 10:00 AM", "ja": "毎週月曜日午前10時", "zh-CN": "每周一上午10点", "zh-TW": "每週一上午10點"}, "input": {"type": "SCHEDULER", "scheduler": {"repeat": {"every": {"type": "WEEK", "interval": 1, "weekdays": ["MON"]}}, "timezone": "AUTO", "datetime": "2024-01-01T10:00:00Z"}}}], "actions": [{"templateId": "find_diary_records", "description": {"en": "Find diary records from the previous week", "ja": "先週の日記レコードを検索します", "zh-CN": "查找上周的日记记录", "zh-TW": "查找上週的日記記錄"}, "actionType": "FIND_RECORDS", "input": {"type": "DATABASE_WITH_FILTER", "filters": {"conjunction": "And", "conditions": [], "conds": [{"fieldTemplateId": "diary_date", "fieldType": "DATETIME", "clause": {"operator": "Is", "value": ["PreviousWeek"]}}]}, "databaseTemplateId": "diary_database"}}, {"templateId": "send_diary_summary_report", "description": {"en": "Send you a summary report of diary entries every Monday.", "ja": "毎週月曜日にあなたに日記のエントリーの要約レポートを送ります。", "zh-CN": "每周一向你发送日记条目的汇总报告", "zh-TW": "每週一向你發送日記條目的彙總報告。"}, "actionType": "SEND_REPORT", "input": {"to": [{"type": "CURRENT_OPERATOR"}], "markdown": "#### <%= _to.name %>, here's your diary summary for the week: \n\nA total of <%= _actions.find_diary_records.records.length %> diaries were recorded this week\n<% _actions.find_diary_records.records.forEach(function(diary) { %> \n  <%= diary.cells.diary_date.value %>\n  <%= diary.cells.diary_content.value %>\n  <% }); %>   ", "subject": "Diary summary for the week", "type": "MARKDOWN"}}]}]}