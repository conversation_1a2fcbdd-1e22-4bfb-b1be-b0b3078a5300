import assert from 'assert';
import type { DatabaseSO } from '@bika/domains/database/server/database-so';
import type { Database, DatabaseField } from '@bika/types/database/bo';
import type { FieldVO } from '@bika/types/database/vo';
import { TestDatabaseContext } from './utils';

// Text
type SingleTextFieldVO = Extract<FieldVO, { type: 'SINGLE_TEXT' }>;
type LongTextFieldVO = Extract<FieldVO, { type: 'LONG_TEXT' }>;
type PhoneFieldVO = Extract<FieldVO, { type: 'PHONE' }>;
type EmailFieldVO = Extract<FieldVO, { type: 'EMAIL' }>;
type UrlFieldVO = Extract<FieldVO, { type: 'URL' }>;
// Number
type NumberFieldVO = Extract<FieldVO, { type: 'NUMBER' }>;
type CurrencyFieldVO = Extract<FieldVO, { type: 'CURRENCY' }>;
type PercentFieldVO = Extract<FieldVO, { type: 'PERCENT' }>;
type RatingFieldVO = Extract<FieldVO, { type: 'RATING' }>;
// Datetime
type DatetimeFieldVO = Extract<FieldVO, { type: 'DATETIME' }>;
type DaterangeFieldVO = Extract<FieldVO, { type: 'DATERANGE' }>;
// Checkbox
type CheckboxFieldVO = Extract<FieldVO, { type: 'CHECKBOX' }>;
// Select
type SingleSelectFieldVO = Extract<FieldVO, { type: 'SINGLE_SELECT' }>;
type MultiSelectFieldVO = Extract<FieldVO, { type: 'MULTI_SELECT' }>;
// Member
type MemberFieldVO = Extract<FieldVO, { type: 'MEMBER' }>;
// Link
type LinkFieldVO = Extract<FieldVO, { type: 'LINK' }>;
// Auto generated
type FormulaFieldVO = Extract<FieldVO, { type: 'FORMULA' }>;
// type LookupFieldVO = Extract<FieldVO, { type: 'LOOKUP' }>;
type CreatedTimeFieldVO = Extract<FieldVO, { type: 'CREATED_TIME' }>;
type ModifiedTimeFieldVO = Extract<FieldVO, { type: 'MODIFIED_TIME' }>;
type CreatedByFieldVO = Extract<FieldVO, { type: 'CREATED_BY' }>;
type ModifiedByFieldVO = Extract<FieldVO, { type: 'MODIFIED_BY' }>;
// type AutoNumberFieldVO = Extract<FieldVO, { type: 'AUTO_NUMBER' }>;

type TestSubDatabaseFieldMapping = {
  // Primary
  primary: SingleTextFieldVO;
  // Link
  link: LinkFieldVO;
};

type TestMainDatabaseFieldMapping = {
  // Primary
  primary: SingleTextFieldVO;
  // Text
  single_text: SingleTextFieldVO;
  long_text: LongTextFieldVO;
  phone: PhoneFieldVO;
  email: EmailFieldVO;
  url: UrlFieldVO;
  // Number
  number: NumberFieldVO;
  currency: CurrencyFieldVO;
  percent: PercentFieldVO;
  rating: RatingFieldVO;
  // Datetime
  datetime: DatetimeFieldVO;
  daterange: DaterangeFieldVO;
  // Checkbox
  checkbox: CheckboxFieldVO;
  // Select
  single_select: SingleSelectFieldVO;
  multi_select: MultiSelectFieldVO;
  // Member
  member: MemberFieldVO;
  // Link
  link: LinkFieldVO;
  // Auto generated
  formula: FormulaFieldVO;
  // lookup: LookupFieldVO;
  created_time: CreatedTimeFieldVO;
  modified_time: ModifiedTimeFieldVO;
  created_by: CreatedByFieldVO;
  modified_by: ModifiedByFieldVO;
  // auto_number: AutoNumberFieldVO;
};

type FilterdMainDatabaseFieldMapping<K extends keyof TestMainDatabaseFieldMapping> = Pick<
  TestMainDatabaseFieldMapping,
  K
> &
  Pick<TestMainDatabaseFieldMapping, 'primary'>;

type MainDatabase<K extends keyof TestMainDatabaseFieldMapping> = {
  db: DatabaseSO;
  f: FilterdMainDatabaseFieldMapping<K>;
};

type SubDatabase = {
  db: DatabaseSO;
  f: TestSubDatabaseFieldMapping;
};

type IncludeSubDatabase<K extends string[]> = 'link' extends K[number]
  ? {
      sub: SubDatabase;
    }
  : {
      sub: undefined;
    };

type TestDatabaseResult<K extends keyof TestMainDatabaseFieldMapping> = {
  ctx: TestDatabaseContext;
  main: MainDatabase<K>;
} & IncludeSubDatabase<K[]>;

/**
 * Create main and sub databases with fields
 *
 * The sub database will auto generate by the main database if contains link field
 *
 * @example
 * ```ts
 * const ctx = await TestDatabaseContext.init();
 *
 * const { mainDb } = await ctx.createDatabasesWithFields(['single_text']);
 * // OR
 * const { mainDb, subDb } = await ctx.createDatabasesWithFields(['single_text', 'link']);
 * ```
 */
export const createDatabasesWithFields = async <K extends keyof TestMainDatabaseFieldMapping>(
  fields: K[],
): Promise<TestDatabaseResult<K>> => {
  const prepareMainFields: DatabaseField[] = [
    {
      templateId: 'primary',
      name: 'primary',
      type: 'SINGLE_TEXT',
    },
  ];

  // Text
  if (fields.includes('single_text' as K)) {
    prepareMainFields.push({
      name: 'single_text',
      type: 'SINGLE_TEXT',
    });
  }
  if (fields.includes('long_text' as K)) {
    prepareMainFields.push({
      name: 'long_text',
      type: 'LONG_TEXT',
    });
  }
  if (fields.includes('phone' as K)) {
    prepareMainFields.push({
      name: 'phone',
      type: 'PHONE',
    });
  }
  if (fields.includes('email' as K)) {
    prepareMainFields.push({
      name: 'email',
      type: 'EMAIL',
    });
  }
  if (fields.includes('url' as K)) {
    prepareMainFields.push({
      name: 'url',
      type: 'URL',
    });
  }

  // Number
  if (fields.includes('number' as K)) {
    prepareMainFields.push({
      name: 'number',
      type: 'NUMBER',
      property: {
        precision: 0,
      },
    });
  }
  if (fields.includes('currency' as K)) {
    prepareMainFields.push({
      name: 'currency',
      type: 'CURRENCY',
      property: {
        precision: 2,
        commaStyle: 'thousand',
        symbol: '$',
        symbolAlign: 'left',
      },
    });
  }
  if (fields.includes('percent' as K)) {
    prepareMainFields.push({
      name: 'percent',
      type: 'PERCENT',
      property: {
        precision: 2,
        commaStyle: 'thousand',
        symbol: '%',
        symbolAlign: 'right',
      },
    });
  }
  if (fields.includes('rating' as K)) {
    prepareMainFields.push({
      name: 'rating',
      type: 'RATING',
      property: {
        max: 5,
        icon: {
          type: 'COLOR',
          color: '#FF0000',
        },
      },
    });
  }

  // Datetime
  if (fields.includes('datetime' as K)) {
    prepareMainFields.push({
      name: 'datetime',
      type: 'DATETIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
      },
    });
  }
  if (fields.includes('daterange' as K)) {
    prepareMainFields.push({
      name: 'daterange',
      type: 'DATERANGE',
      property: {
        dateFormat: 'YYYY-MM-DD',
        includeTime: true,
      },
    });
  }

  // Checkbox
  if (fields.includes('checkbox' as K)) {
    prepareMainFields.push({
      name: 'checkbox',
      type: 'CHECKBOX',
    });
  }

  // Select
  if (fields.includes('single_select' as K)) {
    prepareMainFields.push({
      name: 'single_select',
      type: 'SINGLE_SELECT',
      property: {
        options: [
          {
            id: '1',
            name: 'Option 1',
          },
          {
            id: '2',
            name: 'Option 2',
          },
        ],
      },
    });
  }
  if (fields.includes('multi_select' as K)) {
    prepareMainFields.push({
      name: 'multi_select',
      type: 'MULTI_SELECT',
      property: {
        options: [
          {
            id: '1',
            name: 'Option 1',
          },
          {
            id: '2',
            name: 'Option 2',
          },
        ],
      },
    });
  }

  // Member
  if (fields.includes('member' as K)) {
    prepareMainFields.push({
      name: 'member',
      type: 'MEMBER',
      property: {
        many: true,
      },
    });
  }

  // Auto generate
  if (fields.includes('formula' as K)) {
    prepareMainFields.push({
      name: 'formula',
      type: 'FORMULA',
      property: {
        expression: '"Primary: " + {primary}',
      },
    });
  }
  if (fields.includes('created_time' as K)) {
    prepareMainFields.push({
      name: 'created_time',
      type: 'CREATED_TIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        timeFormat: 'HH:mm',
        includeTime: true,
      },
    });
  }
  if (fields.includes('modified_time' as K)) {
    prepareMainFields.push({
      name: 'modified_time',
      type: 'MODIFIED_TIME',
      property: {
        dateFormat: 'YYYY-MM-DD',
        timeFormat: 'HH:mm',
        includeTime: true,
      },
    });
  }
  if (fields.includes('created_by' as K)) {
    prepareMainFields.push({
      name: 'created_by',
      type: 'CREATED_BY',
    });
  }
  if (fields.includes('modified_by' as K)) {
    prepareMainFields.push({
      name: 'modified_by',
      type: 'MODIFIED_BY',
    });
  }

  const databases: Database[] = [
    {
      resourceType: 'DATABASE',
      templateId: 'main',
      name: 'Main',
      databaseType: 'DATUM',
      fields: prepareMainFields,
    },
  ];

  // Link Field
  if (fields.includes('link' as K)) {
    prepareMainFields.push({
      templateId: 'link',
      name: 'link',
      type: 'LINK',
      property: {
        foreignDatabaseTemplateId: 'sub',
        brotherFieldTemplateId: 'sub:link',
      },
    });

    databases.push({
      resourceType: 'DATABASE',
      templateId: 'sub',
      name: 'Sub',
      databaseType: 'DATUM',
      fields: [
        {
          templateId: 'primary',
          name: 'primary',
          type: 'SINGLE_TEXT',
        },
        {
          templateId: 'link',
          name: 'link',
          type: 'LINK',
          property: {
            foreignDatabaseTemplateId: 'main',
            brotherFieldTemplateId: 'main:link',
          },
        },
      ],
    });
  }

  // Create databases
  const ctx = await TestDatabaseContext.init();
  await ctx.folder.createChildren(ctx.adminSession.user, databases);

  // Main Database
  const main = await ctx.folder.findChildNodeByTemplateId('main');
  assert(main);
  const mainDb = await main.toResourceSO<DatabaseSO>();
  const mainFields = Object.fromEntries(mainDb.getFields().map((field) => [field.name, field.toVO()]));

  // Result
  const mainResult = {
    db: mainDb,
    f: mainFields,
  } as MainDatabase<K>;

  let subResult: SubDatabase | undefined;
  // Sub Database
  if (fields.includes('link' as K)) {
    const sub = await ctx.folder.findChildNodeByTemplateId('sub');
    assert(sub);
    const subDb = await sub.toResourceSO<DatabaseSO>();
    const subFields = Object.fromEntries(subDb.getFields().map((field) => [field.name, field.toVO()]));

    subResult = {
      db: subDb,
      f: subFields,
    } as SubDatabase;
  }

  return {
    ctx,
    main: mainResult,
    sub: subResult,
  } as TestDatabaseResult<K>;
};
